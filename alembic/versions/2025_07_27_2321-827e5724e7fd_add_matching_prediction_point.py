"""add_matching_prediction_point

Revision ID: 827e5724e7fd
Revises: cf6f50b4f325
Create Date: 2025-07-27 23:21:25.077407

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '827e5724e7fd'
down_revision = 'cf6f50b4f325'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('prediction_point_classifications', schema=None) as batch_op:
        batch_op.add_column(sa.Column('matching_prediction_point_id', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('similarity_score', sa.Float(), nullable=True))
        batch_op.create_foreign_key(None, 'prediction_points', ['matching_prediction_point_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('prediction_point_classifications', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('similarity_score')
        batch_op.drop_column('matching_prediction_point_id')

    # ### end Alembic commands ###
