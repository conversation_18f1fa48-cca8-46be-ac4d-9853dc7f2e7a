apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: production
resources:
  # secrets
  - slackbot-secret-sealed.yaml
  - ingest-secret-sealed.yaml
  - veselka-secret-sealed.yaml
  - carbon-analytics-secret-sealed.yaml
  - carbon-panelytics-secret-sealed.yaml
  - comparison-tool-secret-sealed.yaml
  - portal-secret-sealed.yaml
  - robot-syncer-secret-sealed.yaml
  - idm-secret-sealed.yaml
  - carbon-data-secret-sealed.yaml
  - manufacturing-secret-sealed.yaml
  - veselka-analytics-secret-sealed.yaml
  - capp-secret-sealed.yaml
  - web-socket-server-secret-sealed.yaml
  - tableau-bridge-secret-sealed.yaml
  - carbon-bnp-secret-sealed.yaml
  - alexbot-secret-sealed.yaml
  # cron jobs
  - cronjobs/nightly-spatial-export-cronjob.yaml
  # apps
  - ../base/slackbot
  - ../base/idm
  - ../base/ingest
  - ../base/config-service
  - ../base/veselka
  - ../base/portal
  - ../base/robot-syncer
  - ../base/carbon-analytics
  - ../base/carbon-panelytics
  - ../base/query-exporter
  - ../base/version-metadata
  - ../base/infra-mgr
  - ../base/comparison-tool
  - ../base/celery-exporter
  - ../base/web-socket-server
  - ../base/rtc-ui
  - ../base/cloud-image
  - ../base/manufacturing
  - ../base/mechanical
  - ../base/veselka-analytics
  - ../base/capp
  - ../base/tableau-bridge
  - ../base/carbon-bnp
  - ../base/alexbot
  # ingress
  - version-metadata-ingress.yaml
  - slackbot-ingress.yaml # note: also contains portal-integ because http 1.1
  - ingest-ingress.yaml
  - config-service-ingress.yaml
  - rosy-grpc-ingress.yaml
  - veselka-ingress.yaml
  - portal-ingress.yaml
  - portal-grpc-ingress.yaml
  - idm-ingress.yaml
  - rtc-ingress.yaml
  - manufacturing-ingress.yaml
  - capp-ingress.yaml
  - bespoke-ingress.yaml

images:
  - name: ghcr.io/carbonrobotics/cloud/portal
    newTag: portal-v1.25.1
  - name: ghcr.io/carbonrobotics/cloud/robot-syncer
    newTag: rosy-v0.10.0
  - name: ghcr.io/carbonrobotics/manufacturing/manufacturing
    newTag: v0.0.1
  - name: ghcr.io/carbonrobotics/capp/capp
    newTag: master
  - name: ghcr.io/carbonrobotics/cloud/carbon-analytics
    newTag: analytics-v0.1.9
  - name: ghcr.io/carbonrobotics/cloud/carbon-panelytics
    newTag: panelytics-v0.1.2
  - name: ghcr.io/carbonrobotics/cloud/comparison-tool
    newTag: v0.0.293
  - name: ghcr.io/carbonrobotics/cloud/idm
    newTag: v0.0.376
  - name: ghcr.io/carbonrobotics/cloud/infra-mgr
    newTag: master
  - name: ghcr.io/carbonrobotics/cloud/ingest
    newTag: ingest-v0.3.2
  - name: ghcr.io/carbonrobotics/cloud/cloud-image
    newTag: imgsvc-v0.1.1
  - name: ghcr.io/carbonrobotics/cloud/slackbot
    newTag: master
  - name: ghcr.io/carbonrobotics/cloud/version-metadata
    newTag: master
  - name: ghcr.io/carbonrobotics/mechanical/bom
    newTag: production
  - name: ghcr.io/carbonrobotics/robot/common
    newTag: v1.21d.123
  - name: ghcr.io/carbonrobotics/rtc/ui
    newTag: v0.17.1
  - name: ghcr.io/carbonrobotics/rtc/web-socket-server
    newTag: v0.17.1
  - name: ghcr.io/carbonrobotics/veselka/analytics
    newTag: v1.124.0
  - name: ghcr.io/carbonrobotics/veselka/veselka
    newTag: v1.167.0
  - name: ghcr.io/carbonrobotics/cloud/tableau_pg_bridge
    newTag: 2024-12-11

configMapGenerator:
  - name: carbon-data
    literals:
      - GIN_MODE=release

  - name: idm
    literals:
      - GIN_MODE=release
      - AUTH0_ALLOWED_AUDIENCES=https://robot.carbonrobotics.com,https://customer.cloud.carbonrobotics.com,https://ml.carbonrobotics.com,https://datacenter.carbonrobotics.com,https://veselka.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_CLIENT_ID=owEKxih5cjnq3KIASrKFosJKLq86SWS7
      - ALLOWED_ROBOTS_REGEX=^slayer.+|reaper.+|bud\d+|tractor\d+|mvs\d+$
      - CORS_ALLOWED_ORIGINS=https://customer.cloud.carbonrobotics.com
      - CORS_ALLOW_CREDENTIALS=true
      - ROBOT_TOKEN_LIMIT_DURATION=1h
      - STREAM_KEY=s254fk6dqyvm

  - name: ingest
    literals:
      - GIN_MODE=release
      - REDIS_URL=cloud-v1-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - INGEST_SOURCES=s3://carbon-portal/uploads
      - AWS_BUCKET=maka-pono
      - AWS_INGEST_BUCKET=carbon-ingest
      - AWS_INGEST_KEY_PREFIX=production/ingest/upload/
      - AWS_STORAGE_BUCKET=maka-pono
      - AWS_STORAGE_KEY_PREFIX=media/
      - AWS_PREDICT_BURST_STORAGE_BUCKET=carbon-images
      - AWS_FURROWS_STORAGE_BUCKET=carbon-images
      - AWS_FURROWS_STORAGE_KEY_PREFIX=furrows/
      - AWS_CHIP_IMAGE_STORAGE_BUCKET=carbon-images
      - AWS_CHIP_IMAGE_STORAGE_KEY_PREFIX=chip_image/
      - AWS_DIAGNOSTIC_BUCKET=carbon-diagnostics
      - AWS_PLANT_CAPTCHA_BUCKET=carbon-plant-captcha
      - AWS_LOG_BUCKET=carbon-cloud-app
      - VESELKA_URL=http://veselka.production.svc.cluster.local:8080
      - CONFIG_FILE=/config/config.json
      - SECURE="true"
      - PROMETHEUS_TARGET_URL=http://prometheus-robot.monitoring.svc.cluster.local:9090/api/v1/write
      - ROBOT_PROMETHEUS_TARGET_URL=http://prometheus-robot.monitoring.svc.cluster.local:9090/api/v1/write
      - DATACENTER_PROMETHEUS_TARGET_URL=http://prometheus-datacenter.monitoring.svc.cluster.local:9090/api/v1/write
      - AUTHORIZED_DATACENTERS=dc1
      - SQS_JOB_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/job-creator-work-queue.fifo
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DATACENTER_AUDIENCE=https://datacenter.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - GOOGLE_CLIENT_ID=576961730789-hd0vlgcthiaj84k4mb68e7b02l7c2j3k.apps.googleusercontent.com
      - GOOGLE_REDIRECT_URL=https://ingest.cloud.carbonrobotics.com/auth/callback
      - DISABLE_INGESTION=false
    files:
      - config.json=configs/ingest-config.json

  - name: portal
    literals:
      - AUTH0_AUTH_DOMAIN=auth.carbonrobotics.com
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_GRPC_CLIENT_ID=zkuRcsVeAZDnBVOcvPmjKT69iALemQkj
      - AUTH0_REST_AUDIENCE=https://customer.cloud.carbonrobotics.com
      - AUTH0_REST_CALLBACK_URL=https://customer.cloud.carbonrobotics.com/callback/
      - AUTH0_REST_CLIENT_ID=HsMRYnWO4zzckHSy5oKn8vNueRjM7AOT
      - AUTH0_SERVER_ID=2xCVmxAmWtG7vdheT8aKhlw0Tf94gRpu
      - AUTH0_TENANT_DOMAIN=carbonrobotics.us.auth0.com
      - AWS_BUCKET=carbon-portal
      - CONFIG_URL=config.production.svc.cluster.local:443
      - LOG_LEVEL=info
      - NO_CONFIDENCE_CHECKS=true
      - PERF_MODE=true
      - R3_ACCESS_KEY_ID=RQ2HGFNXT4DJJB3RVWAC
      - R3_ACCOUNT_ID=E8B8B878-B754-4112-A239-86D24178C2A9
      - REACT_APP_AUTH0_AUDIENCE=https://customer.cloud.carbonrobotics.com
      - REACT_APP_AUTH0_AUTH_DOMAIN=carbonrobotics.us.auth0.com
      - REACT_APP_AUTH0_REST_CALLBACK_URL=https://customer.cloud.carbonrobotics.com/callback/
      - REACT_APP_AUTH0_REST_CLIENT_ID=HsMRYnWO4zzckHSy5oKn8vNueRjM7AOT
      - REACT_APP_GOOGLE_MEASUREMENT_ID=G-L5X88B8C43
      - REACT_APP_IDM_URL=https://idm.cloud.carbonrobotics.com
      - REACT_APP_MUI_LICENSE_KEY=************************************************************************************************************************
      - REACT_APP_RTC_JOBS_URL=https://rtc-jobs.cloud.carbonrobotics.com
      - REACT_APP_RTC_LOCATOR_URL=https://rtc-locator.cloud.carbonrobotics.com
      - REACT_APP_SENTRY_DSN=https://<EMAIL>/6177154
      - REACT_APP_SENTRY_ENVIRONMENT=production
      - REACT_APP_STREAM_API_KEY=s254fk6dqyvm
      - REACT_APP_S3_CACHE_PROXY_URL=https://cloud-image.cloud.carbonrobotics.com
      - REACT_APP_IMAGE_SERVICE_URL=https://cloud-image.cloud.carbonrobotics.com
      - REACT_APP_USE_ROBOT_SYNCER_TEMPLATES=true
      - REACT_APP_VESELKA_URL=https://veselka.cloud.carbonrobotics.com
      - ROBOT_SYNCER_URL=robot-syncer.production.svc.cluster.local:8080
      - ROOT_URL=https://customer.cloud.carbonrobotics.com
      - RTC_JOBS_TARGET=rtc-jobs-grpc-prod.cloud.carbonrobotics.com:443
      - SERVER_URL=https://customer.cloud.carbonrobotics.com
      - TWILIO_SID=SK1eb3f9e4db027e911675db6202dfe561
      - UPDATE_ROSY_CONFIGS=true
      - USE_ROBOT_SYNCER_TEMPLATES=true
      - USE_ROBOT_SYNCER_CONFIGS=true
      - VESELKA_URL=http://veselka.production.svc.cluster.local:8080
      - INSTANCE=PRODUCTION
      - BUILD=RELEASE
    files:
      - spatial-config.json=configs/portal-spatial-config.json

  - name: robot-syncer
    literals:
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_TENANT_DOMAIN=carbonrobotics.us.auth0.com
      - AWS_BUCKET=carbon-robot-syncer
      - AWS_REGION=us-west-2
      - CONFIG_AUDIT_LOG_GROUP_NAME=robot-config-audit
      - PORTAL_URL=http://portal.production.svc.cluster.local
      - PORTAL_PORT=8080
      - ROBOT_SYNCER_PORT=8080
      - ROBOT_SYNCER_GRPC_PORT=9090
      - SHARED_CONFIG_SCHEMA_PATH=/data/shared_config_schemas

  - name: veselka-cm
    literals:
      - GUNICORN_WORKERS=17
      - VESELKA_IMAGE_SERVICE_URL=cloud-image.production.svc.cluster.local
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_ROLE_LABEL_ID=rol_TAmzHf83PrHlM9Rw
      - AUTH0_ROLE_REVIEW_ID=rol_iS6K7JJfbQzkx4SM
      - AUTH0_ROLE_ADMIN_ID=rol_R22MnaaQRgePAvbX
      - AWS_DEFAULT_REGION=us-west-2
      - CROP_TRANSLATION_VERSION=6
      - POINT_CATEGORY_TRANSLATION_VERSION=6
      - FLASK_ENV=production
      - MODEL_RECOMMENDER_DEFAULT_MAX_CPT=0.5
      - PORT=8080
      - PRELOAD_DISABLED=1
      - REDIS_URL=redis://cloud-v1-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379/0
      - SLACKBOT_URL=http://slackbot.production.svc.cluster.local:8080
      - VESELKA_CLUSTER_JOBS_MANUAL_PRIORITY="70"
      - VESELKA_CLUSTER_JOBS_PREVIEW_PRIORITY="71"
      - VESELKA_COMPARISON_PREDICT_QUEUE=veselka-comparison-predict-queue.fifo
      - VESELKA_DEFAULT_CROP_POINT_CATEGORY_ID="8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
      - VESELKA_GET_TASK_PROBABILITIES_CAPTURED_AT_LIMIT_DAYS="365"
      - VESELKA_IMAGE_QUARANTINE_FOCUS_METRIC_THRESHOLD=0.05
      - VESELKA_JOB_CREATOR_QUEUE=job-creator-work-queue.fifo
      - VESELKA_MODEL_CONVERTER_QUEUE=model-conversion-queue.fifo
      - VESELKA_LIMIT_GET_TASK_PROBABILITIES_CAPTURED_AT="1"
      - VESELKA_MOCK_CLUSTER_JOB_SUBMISSION=0
      - VESELKA_MODE=production
      - VESELKA_MODEL_PD_WEEDS_TARGETED_REGRESSION=-0.1
      - VESELKA_CLUSTER_JOBS_PREVIEW_LIMIT="5"
      - VESELKA_MODEL_RECOMMENDER_ALWAYS_RECOMMEND_PREVIOUS=1
      - VESELKA_MODEL_RECOMMENDER_CACHE_TIME_LIMIT_MINUTES="1440"
      - VESELKA_MODEL_RECOMMENDER_ENABLE_V2="1"
      - VESELKA_MODEL_RECOMMENDER_MODEL_LIMIT=100
      - VESELKA_MODEL_RECOMMENDER_RUNNER_ENABLE_V2="1"
      - VESELKA_MODEL_RECOMMENDER_TIME_INTERVAL_MINUTES=5
      - VESELKA_POPULATE_LABEL_POINTS="1"
      - VESELKA_PORTAL_URL="http://portal.production.svc.cluster.local:8080"
      - VESELKA_PREDICT_RESULT_QUEUE=veselka-predict-result-queue.fifo
      - VESELKA_PREVIEW_DEEPLEARNING_CONTAINER_TAG=c5.1.3
      - VESELKA_PRODUCTION_DEEPLEARNING_CONTAINER_TAG=c5.1.3
      - VESELKA_REVIEW_ONLY=1
      - VESELKA_SENTRY_DSN=https://<EMAIL>/4505172929609728
      - VESELKA_SENTRY_TRACE_SAMPLE_RATE=0.3
      - VESELKA_TASK_SAMPLING_GAMMA=0.5
      - VESELKA_TASK_SAMPLING_GEOHASH_PRECISION=6
      - VESELKA_TASK_SAMPLING_DATE_DROP_OFF=0
      - CLEANUP_KEEP_N_MODELS_PER_PIPELINE_AND_SUBTYPE=5
      - VESELKA_CLUSTER_JOBS_OLDEST_QUEUE_LIMIT=5
      - VESELKA_IMG_S3_BUCKET="carbon-images"
      - VESELKA_IMG_S3_KEY_PREFIX=""
      - VESELKA_BYPASS_LABEL_POINTS="1"
      - VESELKA_CLUSTER_JOBS_EXPIRATION_DAYS="10"
    files:
      - env.js=configs/veselka-env.js

  - name: web-socket-server
    literals:
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - PORTAL_SERVICE_HOST=portal.production.svc.cluster.local:8080
      - PORTAL_SERVICE_SECURE=false
      - ENABLE_AUTH=true
      - ENFORCE_HEART_BEAT=true
      - ENABLE_NEW_RTC_MODE=true
      - ECHO_MODE=false
      - ALLOWED_ROBOTS_BY_PREFIX=slayer
      - RUN_MODE=run-migrate

  - name: rtc-ui
    files:
      - default.conf=configs/rtc-ui-nginx-default.conf
    literals:
      - VITE_AUTH0_AUDIENCE=https://customer.cloud.carbonrobotics.com
      - VITE_AUTH0_AUTH_DOMAIN=carbonrobotics.us.auth0.com
      - VITE_AUTH0_CALLBACK_URL=https://rtc.cloud.carbonrobotics.com/callback/
      - VITE_AUTH0_CLIENT_ID=UrfFXRA0gZchwMGKkvAqCGHaZ7fGOguD
      - VITE_PORTAL_ORIGIN=https://customer.cloud.carbonrobotics.com
      - VITE_WS_ORIGIN=wss://signal-rtc.cloud.carbonrobotics.com

  - name: celery-exporter
    literals:
      - CE_BUCKETS=1,10,60,600,1000
      - CE_BROKER_URL=redis://cloud-v1-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379/0

  - name: slackbot
    literals:
      - MODEL_NOTIFY_CHANNEL=model-notifications
      - TRAINING_FAILURE_NOTIFY_CHANNEL=job-creator-notifications
      - SLACK_MESSAGE_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/slack-message-queue.fifo
      - EQUIPMENT_FILEPATH=/data/equipment.json
      - EQUIPMENT_CHANNEL=robot-checkout
      - INFRA_MGR_BASE_URL=http://infra-mgr.production.svc.cluster.local:8080
      - PRODUCT_SLACK_CHANNEL=C057BVBSDGR
      - PRODUCT_ASANA_PROJECT=1200735241446233
      - CONFIG_FILE=/config/slackbot-config.json
      - IDM_BASE_URL=http://idm.production.svc.cluster.local:8080
      - JIRA_BASE_URL=https://carbonrobotics.atlassian.net/
    files:
      - configs/slackbot-config.json

  - name: version-metadata
    literals:
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - GIN_MODE=release
      - AWS_VERSION_META_BUCKET=carbon-version-metadata

  - name: infra-mgr
    literals:
      - GIN_MODE=release
      - RDS_TARGET_ID_WHITELIST=db-test,db-stage,veselka-db-stage,portal-db-dev,veselka-db-dev,veselka-dev,portal-dev
      - RDS_INSTANCE_CLASS_WHITELIST=db.t4g.medium,db.t3.micro,db.m5.2xlarge,db.r6g.large
      - SLACKBOT_URL=http://slackbot.production.svc.cluster.local:8080/internal/slack/post
      - EVENT_TIMEOUT=4h
      - CARBON_DATA_HOST=carbon-data.production.svc.cluster.local:8080
      - CONFIG_FILE=/config/config.json
    files:
      - config.json=configs/infra-mgr-config.json

  - name: comparison-tool
    literals:
      - S3_KEY_PREFIX=comparison/production
      - ADMIN_MODE=0
      - DEFAULT_DATASET=e7808dfc-972a-49f9-b897-3a841d5d843e
      - VESELKA_URL=http://veselka.production.svc.cluster.local:8080

  - name: carbon-analytics
    literals:
      - VESELKA_URL=http://veselka.production.svc.cluster.local:8080
      - S3_CACHE_PROXY_SERVICE_HOST=cloud-image.production.svc.cluster.local
      - REDIS_URL=redis://cloud-v1-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - DISABLE_USER_PASSWORD=1

  - name: cloud-image
    literals:
      - GIN_MODE=release
      - STORAGE_MODE=s3only
      - SPILLOVER_LAMBDA_FUNCTION_NAME=image-service
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_AUDIENCES=https://veselka.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - TRANSFORM_STORAGE_BUCKET=carbon-image-transforms
      - TRANSFORM_STORAGE_KEY_PREFIX=production/
      - CORS_ALLOWED_ORIGINS=https://veselka.cloud.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - PERF_MODE=false
      - MODE_RATE_LIMIT=s3only:20

  - name: carbon-panelytics
    literals:
      - THANOS_QUERY_FRONTEND=thanos-query-frontend.monitoring.svc.cluster.local

  - name: capp
    literals:
      - NETSUITE_LOG_LEVEL=INFO
      - NETSUITE_ACCOUNT=6693091
      - NETSUITE_TOKEN_ID=****************************************************************
      - NETSUITE_CONSUMER_KEY=****************************************************************

  - name: alexbot
    literals:
      - PROMETHEUS_URL=http://thanos-query-frontend.monitoring.svc:9090
      - ROSY_URL=http://robot-syncer.production.svc:8080
      - R3_ACCESS_KEY_ID=BCOLWNGDX7MJTZXPMPYS

  - name: tableau-bridge
    literals:
      - PAT_TOKEN_ID=BridgeConnector
      - USER_EMAIL=<EMAIL>
      - CLIENT=bridge-worker
      - SITE=carbonrobotics
      - POOL_ID=d55b1f2f-a747-444d-bf10-ad519cae97bd
      - PAT_TOKEN_FILE=/secrets/token.json

  - name: manufacturing
    literals:
      - TARGET_SYSTEM_VERSION=s2.1.3
      - TARGET_BOT_VERSION=v2.0.78

  - name: carbon-bnp
    literals:
      - FROM_EMAIL=<EMAIL>
      - INTERNAL_EMAIL=<EMAIL>
      - SENDGRID_CUSTOMER_TEMPLATE_ID=d-afefdd510d464913b6c752b784177891
      - SENDGRID_INTERNAL_TEMPLATE_ID=d-da88116e5c5542b99aa57d149edf4e37

  - name: query-exporter
    files:
      - config.yaml=configs/query-exporter-config.yaml

  - name: cron-scripts
    files:
      - scripts/spatial-export-script.sh

patches:
  # default affinity patches MUST be first, so all services are scheduled on
  # nodes labeled environment = production unless otherwise specified (by a following patch)
  - path: patches/productionNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: .*
  - path: patches/productionNodeAffinityPatch.yaml
    target:
      kind: StatefulSet
      name: .*
  # END MUST BE FIRST

  - path: patches/defaultNodePodAntiAffinityPatch.yaml
    target:
      kind: Deployment
      name: ingest

  - path: patches/defaultNodePodAntiAffinityPatch.yaml
    target:
      kind: Deployment
      name: cloud-image

  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: carbon-analytics
  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: carbon-panelytics

  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-analytics

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: comparison-tool

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: tableau-bridge

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: comparison-tool

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 9
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: veselka-datasets

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-datasets

  # temporary memory boost while node is doubled
  - patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "80Gi"
    target:
      kind: Deployment
      name: veselka-datasets

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 2
    target:
      kind: Deployment
      name: web-socket-server

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
    target:
      kind: Deployment
      name: cloud-image

  - path: patches/serviceMonitorPatch.yaml
    target:
      kind: ServiceMonitor
#  - path: patches/scaleDownPatch.yaml
#    target:
#      kind: Deployment
#  - path: patches/scaleDownPatch.yaml
#    target:
#      kind: StatefulSet
#  - path: patches/nodeAffinityPatch.yaml
#    target:
#      kind: Deployment
#  - path: patches/nodeAffinityPatch.yaml
#    target:
#      kind: StatefulSet
