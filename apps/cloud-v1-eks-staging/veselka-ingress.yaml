apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/actions.internal-api: |
      {"type":"redirect","redirectConfig":{"host":"veselka-stg.cloud.carbonrobotics.com","path":"/","port":"443","protocol":"HTTPS","statusCode":"HTTP_302"}}
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=600
    external-dns.alpha.kubernetes.io/hostname: veselka-stg.cloud.carbonrobotics.com,analytics-stg.cloud.carbonrobotics.com,comparison-stg.cloud.carbonrobotics.com,panelytics-stg.cloud.carbonrobotics.com,veselka-analytics-stg.cloud.carbonrobotics.com,cloud-image-stg.cloud.carbonrobotics.com
    kubernetes.io/ingress.class: alb
  name: veselka
spec:
  rules:
    - host: veselka-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: internal-api
                port:
                  name: use-annotation
            path: /internal
            pathType: Prefix
          - backend:
              service:
                name: veselka
                port:
                  number: 8080
            path: /
            pathType: Prefix
    - host: analytics-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: carbon-analytics
                port:
                  number: 4180
            path: /
            pathType: Prefix
    - host: comparison-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: comparison-tool
                port:
                  number: 8501
            path: /
            pathType: Prefix
    - host: panelytics-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: carbon-panelytics
                port:
                  number: 4181
            path: /
            pathType: Prefix
    - host: veselka-analytics-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: veselka-analytics
                port:
                  number: 4182
            path: /
            pathType: Prefix
    - host: cloud-image-stg.cloud.carbonrobotics.com
      http:
        paths:
          - backend:
              service:
                name: cloud-image
                port:
                  number: 80
            path: /image
            pathType: Exact
  tls:
    - hosts:
        - veselka-stg.cloud.carbonrobotics.com
        - analytics-stg.cloud.carbonrobotics.com
        - comparison-stg.cloud.carbonrobotics.com
        - panelytics-stg.cloud.carbonrobotics.com
        - veselka-analytics-stg.cloud.carbonrobotics.com
        - cloud-image-stg.cloud.carbonrobotics.com
