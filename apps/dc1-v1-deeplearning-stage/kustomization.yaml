apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: deeplearning-stage
resources:
  - pvcs.yaml
  - ../base/job-creator
  - ../base/veselka_cv


configMapGenerator:
  - name: job-creator
    literals:
#      - WORK_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/job-creator-stage-work-queue.fifo
      - DL_TRAINING_IMAGE=ghcr.io/carbonrobotics/robot/deeplearning:stable
      - DEFAULT_PRETRAINED_MODEL_ID=bad13b84-05e4-44bb-b4eb-f59f66baa70c
      - ML_TOKEN_URL=http://credential-proxy.deeplearning.svc.cluster.local:8080/stg/ml/oauth/token
      - VESELKA_BASE_URL=staging
      - JOB_ENV_VARS=CACHE_HOST_A:***********,CACHE_HOST_B:***********
      - TEST_MODE=true
      - DEBUG=true

  - name: veselka-cv
    literals:
      - PYTHONUNBUFFERED=1
      - MAKA_ROLE=veselka_cv
      - MAKA_ROW=0
      - MAKA_GEN=bud
      - MAKA_ROBOT_NAME=weedy
      - MAKA_USER=maka
      - CREDENTIAL_PROXY_SERVICE_HOST=credential-proxy.deeplearning.svc.cluster.local
      - CREDENTIAL_PROXY_SERVICE_PORT=8080
      # Note this ip is frankenmini02 need to circle back
      - S3_CACHE_PROXY_SERVICE_HOST=***********

images:
  - name: ghcr.io/carbonrobotics/cloud/job-creator
    newTag: dev
  - name: ghcr.io/carbonrobotics/robot/veselka_cv
    newTag: vcv1.10.0-evan-default-crop-id

patches:
  - path: patches/securityContextPatch.yaml
    target:
      kind: Deployment
      name: job-creator
  - path: patches/appNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: job-creator

  # job creator scaling
  - target:
      kind: Deployment
      name: job-creator
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1

  - path: patches/appNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-cv

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
      - op: replace
        path: /spec/template/spec/containers/0/command
        value: ['python', 'veselka/cv/deepweed/server.py', '[{"input_queue": "veselka-predict-queue-test.fifo", "environment": "test" },{"input_queue": "veselka-batch-predict-queue-test.fifo", "environment": "test" },{"input_queue": "veselka-predict-queue-stage.fifo", "environment": "stage" },{"input_queue": "veselka-batch-predict-queue-stage.fifo", "environment": "stage"}]']
    target:
      kind: Deployment
      name: veselka-cv
