import json
import uuid
from http import HTT<PERSON>tatus
from typing import Optional

from celery.result import As<PERSON><PERSON><PERSON><PERSON>
from flask import jsonify, request
from pydantic import BaseModel, field_validator
from werkzeug import Response

from server.apis.frontend.helpers import parse_date_range, parse_multi_select
from server.auth import current_user
from server.constants import ImageArtifactTypes
from server.db.queries.common_types import DateRangeFilter
from server.db.queries.furrow_label import FurrowLabelExploreFilter, FurrowLabelFilters
from server.db.queries.images_types import ImageFilterV2
from server.routes import route
from server.tasks import furrow_label_celery as celery
from server.utils.types import LongRunningJobStatus


class FilterCondsForLabels(BaseModel):
    # image filters - all image filter related fields have a prefix of imgf_
    imgf_captured_at: Optional[DateRangeFilter] = None
    imgf_robot_ids: Optional[list[str]] = None
    imgf_geohashes: Optional[list[str]] = None
    imgf_image_ids: Optional[list[str]] = None
    # Label Filters
    lbf_task_ids: Optional[list[str]] = None
    lbf_user_ids: Optional[list[str]] = None
    lbf_img_ids: Optional[list[str]] = None
    lbf_created_on: Optional[DateRangeFilter] = None
    lbf_updated_on: Optional[DateRangeFilter] = None

    @field_validator("imgf_captured_at", "lbf_created_on", "lbf_updated_on", mode="before")
    def parse_date_range_fields(cls, value: Optional[str]) -> Optional[DateRangeFilter]:
        if value:
            start, end = parse_date_range(value)
            return DateRangeFilter(start=start, end=end)
        return None

    @field_validator(
        "imgf_robot_ids",
        "imgf_geohashes",
        "imgf_image_ids",
        "lbf_task_ids",
        "lbf_user_ids",
        "lbf_img_ids",
        mode="before",
    )
    def parse_multi_select_field(cls, value: Optional[str]) -> Optional[list[str]]:
        if value is not None:
            return parse_multi_select(value)
        return None

    def get_image_filter(self) -> ImageFilterV2:
        return ImageFilterV2(
            captured_at=self.imgf_captured_at,
            robot_ids=self.imgf_robot_ids,
            geohashes=self.imgf_geohashes,
            image_ids=self.imgf_image_ids,
            image_type=(ImageArtifactTypes.FURROW),
        )

    def get_furrow_label_filter(self) -> FurrowLabelFilters:
        return FurrowLabelFilters(
            task_ids=self.lbf_task_ids,
            user_ids=self.lbf_user_ids,
            img_ids=self.lbf_img_ids,
            created_on=self.lbf_created_on,
            updated_on=self.lbf_updated_on,
            is_production=True,
            is_valid=True,
        )

    def get_explore_filter(self) -> FurrowLabelExploreFilter:
        return FurrowLabelExploreFilter(
            image_filter=self.get_image_filter(),
            furrow_filter=self.get_furrow_label_filter(),
        )


class CreateDatasetReq(BaseModel):
    name: str
    filter: FilterCondsForLabels = FilterCondsForLabels()
    split_ratio: celery.SplitSelectorRatio = celery.SplitSelectorRatio()
    fast_run: bool = False


@route("ml", "/furrow/dataset/job", methods=["POST"])
def post_furrow_dataset_v2() -> Response:
    """
    Create a furrow dataset based on the provided filter conditions.

    Request body:
        - "name": <str>
        - "filter": <FilterCondsForTasks>
        - "split_ratio": <SplitSelectorRatio>
        - "fast_run": <bool>
    Response:
        - job_id: <str>
    """
    data = json.loads(request.data)
    try:
        create_dataset_req = CreateDatasetReq(**data)
    except ValueError:
        return Response("Request is not valid", HTTPStatus.BAD_REQUEST)

    celery_job = celery.create_furrow_dataset.delay(
        dataset_id=uuid.uuid4().hex,
        dataset_name=create_dataset_req.name,
        filter_dict=create_dataset_req.filter.get_explore_filter().model_dump(),
        user_id=None,
        split_ratio_dict=create_dataset_req.split_ratio.model_dump(),
        fast_run=create_dataset_req.fast_run,
    )

    return jsonify({"job_id": celery_job.id})


@route("ml", "/furrow/dataset/job/<string:job_id>", methods=["GET"])
def get_furrow_label_job_status(job_id: str) -> Response:
    """
    Get the status of a batch job

    Response:
        - status: [INPROGRESS, COMPLETED, FAILED]
        - message: <str>
        - dataset_id: <str>
    """
    celery_task = AsyncResult(job_id)
    if celery_task.status == "FAILURE":
        return jsonify({"status": LongRunningJobStatus.FAILED.value, "message": str(celery_task.result)})
    elif celery_task.status == "SUCCESS":
        return jsonify({"status": LongRunningJobStatus.SUCCESS.value, "dataset_id": celery_task.result})
    return jsonify({"status": LongRunningJobStatus.PENDING.value})
