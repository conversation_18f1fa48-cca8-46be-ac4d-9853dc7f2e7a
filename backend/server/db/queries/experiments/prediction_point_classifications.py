from typing import Optional, Union

import pydantic
from sqlalchemy.sql.expression import BinaryExpression, and_

from server.db import tables, utils
from server.utils.helpers import IsEmptyCheckPydanticMixin


def create(
    prediction_point_id: str,
    prediction_point_classification_session_id: str,
    classification: str,
    matching_prediction_point_id: Optional[str] = None,
    similarity_score: Optional[float] = None,
    do_commit: bool = True,
) -> tables.PredictionPointClassification:
    with utils.session_scope(do_commit=do_commit) as session:
        prediction_point_classification = tables.PredictionPointClassification(
            prediction_point_id=prediction_point_id,
            prediction_point_classification_session_id=prediction_point_classification_session_id,
            classification=classification,
            matching_prediction_point_id=matching_prediction_point_id,
            similarity_score=similarity_score,
        )
        session.add(prediction_point_classification)

    return prediction_point_classification


class PredictionPointClassificationFilter(IsEmptyCheckPydanticMixin, pydantic.BaseModel):
    session_id: Optional[str] = None
    classifications: Optional[list[str]] = None


def build_filter_conditions(filters: PredictionPointClassificationFilter) -> Union[bool, BinaryExpression]:
    filter_conditions: list[BinaryExpression] = []

    if filters.session_id:
        filter_conditions.append(
            tables.PredictionPointClassification.prediction_point_classification_session_id == filters.session_id
        )

    if filters.classifications:
        filter_conditions.append(tables.PredictionPointClassification.classification.in_(filters.classifications))
    return and_(*filter_conditions) if filter_conditions else True
