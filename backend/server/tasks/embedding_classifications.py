import datetime
import json
import logging
import time

import numpy as np
import sqlalchemy

from server.config import MOD<PERSON>
from server.constants import ACTIVE_SESSION_TIMEOUT_MS
from server.db import queries
from server.db.queries.experiments.prediction_point_classification_sessions import (
    find_session_by_id,
    mark_support_set_ready,
)
from server.predictions import EmbeddingPrediction
from server.types import CategoryCollectionProfile
from server.utils.linear_algebra import cosine_cdist
from server.utils.types import LongRunningJobStatus

from .app import app_task
from .constants import CeleryQueue

LOG = logging.getLogger(__name__)

TIME_OFFSET_MS = 60 * 1000 * 5
PAGE_SIZE = 100
BATCH_SIZE = 100
HIGH_PRIORITY_PREDICTION_POINTS_RATIO = 1 / 5


@app_task(queue=CeleryQueue.MODEL_RATIO_METRICS.value)
def start_embedding_classifications(
    session_id: str, robot_ids: list[str], count_prediction_points_to_load: int
) -> None:
    LOG.info(f"Starting embedding classifications for session {session_id} with robot IDs: {robot_ids}")
    num_high_priority_prediction_points = int(count_prediction_points_to_load * HIGH_PRIORITY_PREDICTION_POINTS_RATIO)
    experiment_session = find_session_by_id(session_id)
    if not experiment_session:
        raise RuntimeError(f"Failed to find session: {session_id}")

    category_collection_profile = CategoryCollectionProfile.parse_obj(experiment_session.category_collection_profile)
    predictions = []
    for category_profile in category_collection_profile.category_profiles:
        for prediction_point_id in category_profile.prediction_point_ids:
            prediction_point = queries.prediction_points.get(prediction_point_id)

            if prediction_point is None:
                raise RuntimeError(f"Failed to find prediction point: {prediction_point_id}")

            deepweed_embedding_prediction = EmbeddingPrediction(
                model_id=experiment_session.model_id,
                image_id=prediction_point.prediction.image_id,
                x=prediction_point.x,
                y=prediction_point.y,
                interactive=True,
                prediction_point_id=prediction_point.id,
            )

            predictions.append(deepweed_embedding_prediction)

    LOG.info(f"Submitted {len(predictions)} - waiting for all support set predictions to complete")

    # Wait for all predictions to complete
    start_time = datetime.datetime.now()
    complete = False
    while not complete:
        LOG.info(f"Waiting for predictions to complete - {len(predictions)}")
        complete = True
        elapsed_time = datetime.datetime.now() - start_time
        if elapsed_time.total_seconds() > 60 * 5:
            LOG.error("Timeout waiting for predictions to complete during embedding classification.")
            raise RuntimeError("Timeout waiting for predictions to complete during embedding classification.")

        for prediction in predictions:
            if prediction.status != LongRunningJobStatus.SUCCESS:
                complete = False
                break
        time.sleep(5)

    LOG.info("Submitting prediction point embedding requests to SQS queue")
    mark_support_set_ready(session_id=session_id)

    batch = []
    for page_index in range(count_prediction_points_to_load // PAGE_SIZE):
        start = datetime.datetime.now()

        image_filter = queries.images.ImageFilterV2(robot_ids=robot_ids)

        prediction_points_explore_filter = queries.prediction_points.PredictionPointsExploreFilter(
            image_filter=image_filter,
        )

        pagination_input = queries.common_types.PaginationInput(
            offset=page_index * PAGE_SIZE,
            limit=PAGE_SIZE,
            sort_input=queries.common_types.SortInput(
                sort_by=queries.common_types.SortByColumn.CAPTURED_AT,
                sort_direction=queries.common_types.SortDirection.DESC,
            ),
        )
        prediction_points_results, _ = queries.prediction_points.explore_prediction_points(
            filter=prediction_points_explore_filter,
            pagination_input=pagination_input,
        )

        query_elapsted_time = datetime.datetime.now() - start

        start = datetime.datetime.now()

        for sub_page_index, (prediction_point, _) in enumerate(prediction_points_results):
            overall_index = page_index * PAGE_SIZE + sub_page_index

            deepweed_embedding_prediction = EmbeddingPrediction(
                model_id=experiment_session.model_id,
                image_id=prediction_point.prediction.image_id,
                x=prediction_point.x,
                y=prediction_point.y,
                interactive=overall_index < num_high_priority_prediction_points,
                prediction_point_id=prediction_point.id,
            )

            if deepweed_embedding_prediction.status == LongRunningJobStatus.SUCCESS:
                batch.append(prediction_point.id)

                if len(batch) >= BATCH_SIZE:
                    run_embedding_classification.delay(batch, experiment_session.id)
                    batch = []

        embedding_prediction_submission_elapsed_time = datetime.datetime.now() - start

        LOG.info(f"Processsed page {page_index + 1} of {count_prediction_points_to_load // PAGE_SIZE}")
        LOG.info(f"Query elapsed time: {query_elapsted_time}")
        LOG.info(f"Embedding prediction submission elapsed time: {embedding_prediction_submission_elapsed_time}")

    if len(batch) > 0:
        run_embedding_classification.delay(batch, experiment_session.id)

    return None


@app_task(queue=CeleryQueue.MODEL_RATIO_METRICS.value)
def run_embedding_classification(
    prediction_point_ids: list[str], prediction_point_classification_session_id: str
) -> None:
    prediction_point_ids = list(set(prediction_point_ids))

    prediction_point_classification_session = queries.prediction_point_classification_sessions.get(
        prediction_point_classification_session_id
    )

    if prediction_point_classification_session is None:
        raise RuntimeError(
            f"Failed to find prediction point classification session: {prediction_point_classification_session_id}"
        )

    category_collection_profile = CategoryCollectionProfile.model_validate_json(
        json.dumps(prediction_point_classification_session.category_collection_profile)
    )

    support_set_prediction_point_ids = []
    support_set_classification_map = {}
    for category_profile in category_collection_profile.category_profiles:
        for category_profile_prediction_point_id in category_profile.prediction_point_ids:
            support_set_prediction_point_ids.append(category_profile_prediction_point_id)
            support_set_classification_map[category_profile_prediction_point_id] = category_profile.id

    support_set_prediction_points = queries.prediction_points.get_points(support_set_prediction_point_ids)

    if len(support_set_prediction_points) != len(support_set_prediction_point_ids):
        missing_points = []
        found_prediction_point_ids = [point.id for point in support_set_prediction_points]
        for prediction_point_id in support_set_prediction_point_ids:
            if prediction_point_id not in found_prediction_point_ids:
                missing_points.append(prediction_point_id)

        LOG.error(f"Failed to find support set prediction points: {missing_points}")
        raise RuntimeError(f"Failed to find support set prediction points: {missing_points}")

    query_set_prediction_points = queries.prediction_points.get_points(prediction_point_ids)

    if len(query_set_prediction_points) != len(prediction_point_ids):
        missing_points = []
        found_prediction_point_ids = [point.id for point in query_set_prediction_points]
        for prediction_point_id in prediction_point_ids:
            if prediction_point_id not in found_prediction_point_ids:
                missing_points.append(prediction_point_id)

        LOG.error(f"Failed to find query set prediction points: {missing_points}")
        raise RuntimeError(f"Failed to find query set prediction points: {missing_points}")

    support_set = []
    for prediction_point in support_set_prediction_points:
        prediction_point_embedding = EmbeddingPrediction(
            model_id=prediction_point_classification_session.model_id,
            image_id=prediction_point.prediction.image_id,
            x=prediction_point.x,
            y=prediction_point.y,
            prediction_point_id=prediction_point.id,
        )

        if prediction_point_embedding.status != LongRunningJobStatus.SUCCESS:
            model_id = prediction_point_classification_session.model_id

            LOG.error(
                f"Failed to get deepweed embedding embedding for prediction point. Model ID: {model_id} Prediction Point ID: {prediction_point.id}"
            )
            raise RuntimeError(
                f"Failed to get deepweed embedding embedding for prediction point. Model ID: {model_id} Prediction Point ID: {prediction_point.id}"
            )

        embedding_data = prediction_point_embedding.data

        if embedding_data is None:
            LOG.error(f"Failed to get embedding data: {prediction_point.id}")
            raise RuntimeError(f"Failed to get embedding data: {prediction_point.id}")

        support_set.append(np.array(embedding_data.embedding))

    if len(support_set) == 0:
        raise RuntimeError("Support set size is less than 1, cannot compute classifications.")

    support_set_array = np.array(support_set)

    query_set = []
    prediction_points_with_embeddings = []
    for prediction_point in query_set_prediction_points:
        prediction_point_embedding = EmbeddingPrediction(
            model_id=prediction_point_classification_session.model_id,
            image_id=prediction_point.prediction.image_id,
            x=prediction_point.x,
            y=prediction_point.y,
            prediction_point_id=prediction_point.id,
        )
        if prediction_point_embedding.status == LongRunningJobStatus.PENDING:
            # Prediction point is still pending, skip classification
            # The prediction point will be classified later when the embedding is ready
            LOG.info(f"Prediction point {prediction_point.id} is still pending, skipping classification for point.")
            continue
        if prediction_point_embedding.status != LongRunningJobStatus.SUCCESS:
            model_id = prediction_point_classification_session.model_id

            LOG.error(
                f"Failed to get deepweed embedding embedding for prediction point. Model ID: {model_id} Prediction Point ID: {prediction_point.id}"
            )
            raise RuntimeError(
                f"Failed to get deepweed embedding embedding for prediction point. Model ID: {model_id} Prediction Point ID: {prediction_point.id}"
            )

        embedding_data = prediction_point_embedding.data

        if embedding_data is None:
            LOG.error(f"Failed to get embedding data: {prediction_point.id}")
            raise RuntimeError(f"Failed to get embedding data: {prediction_point.id}")

        query_set.append(np.array(embedding_data.embedding))
        prediction_points_with_embeddings.append(prediction_point)

    if len(query_set) == 0:
        LOG.info(f"Embedding not yet ready. Skipping classification.")
        return None

    query_set_array = np.array(query_set)

    # Each row should be the distances between a query point and all support points
    scores = cosine_cdist(query_set_array, support_set_array)
    max_score_indices = np.argmax(scores, axis=1)

    for query_set_index, prediction_point in enumerate(prediction_points_with_embeddings):
        max_index = max_score_indices[query_set_index]
        classification = support_set_classification_map[support_set_prediction_points[max_index].id]
        try:
            queries.prediction_point_classifications.create(
                prediction_point.id,
                prediction_point_classification_session.id,
                classification,
                matching_prediction_point_id=support_set_prediction_points[max_index].id,
                similarity_score=(scores[query_set_index, max_index] + 1.0) / 2.0,  # translate from [-1, 1] to [0, 1]
                do_commit=True,
            )
        except sqlalchemy.exc.IntegrityError as e:
            LOG.info(
                f"Classification for {prediction_point.id} in session {prediction_point_classification_session.id} already exists: {e}"
            )
    return None
