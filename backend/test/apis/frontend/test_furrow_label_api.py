import unittest
from copy import deepcopy
from unittest.mock import ANY, Magic<PERSON>ock, patch
from urllib.parse import urlenco<PERSON>

from flask import Flask

from server.apis.ml.furrow_label import Furrow<PERSON>abelExploreFilter, FurrowLabelFilters
from server.apis.frontend.task_v2 import <PERSON>Entry, TaskEntry, UserEntry
from server.app import create_app
from server.constants import ImageArtifactTypes, TaskTypes
from server.db.queries.common_types import QueryStats, ResultsCountType
from server.db.queries.images_types import ImageFilterV2
from server.db.queries.label_types import Confidence, FurrowsLabelData, LineLabel
from server.db.tables import Furrow<PERSON>abel, Image, TaskV2, User
from server.db.utils import PaginationResult
from server.tasks.furrow_label_celery import SplitSelectorRatio
from server.utils.types import LongRunningJobStatus


class Const:
    USER_ID = "user_id"
    USER_EMAIL = "<EMAIL>"

    CELERY_TASK_ID = "celery_task_id"

    LABEL_ID_1 = "label_id_1"
    LABEL_ID_2 = "label_id_2"

    TASK_ID_1: str = "task_id_1"
    TASK_ID_2: str = "task_id_2"

    DATASET_NAME = "dataset_name"

    WORKFLOW_ID_1: str = "workflow_id_1"

    IMG_ID_1: str = "image_id_1"
    IMG_ID_2: str = "image_id_2"
    IMG_URL: str = "image_url"
    IMG_WIDTH: int = 1
    IMG_HEIGHT: int = 2

    ROBOT_ID = "robot_id"
    CROP_ID = "crop_id"
    GEOHASH = "geohash"

    INVALIDATED_BY_ID: str = "invalidated_by"
    INVALIDATED_BY_EMAIL: str = "<EMAIL>"

    CREATED_ON: int = 1
    UPDATED_ON: int = 2
    COMPLETED_ON: int = 3
    INVALIDATED_ON: int = 4

    COUNT = 10
    OFFSET = 20
    LIMIT = 30

    MOCK_USER = User(id="test", admin=True)

    DUMMY_QUERY_STATS = QueryStats(count=COUNT, count_type=ResultsCountType.ESTIMATED, offset=OFFSET, limit=LIMIT)
    DUMMY_PAGINATION_RES = PaginationResult(
        total_records=COUNT, count_type=ResultsCountType.ESTIMATED, current_page=1, total_pages=1
    )

    IMG_1 = Image(
        id=IMG_ID_1,
        url=IMG_URL,
        captured_at=CREATED_ON,
        robot_id=ROBOT_ID,
        crop_id=CROP_ID,
        geohash=GEOHASH,
        width=IMG_WIDTH,
        height=IMG_HEIGHT,
    )

    VALID_CELERY_TASK_RESP = MagicMock(id=CELERY_TASK_ID)

    EXPECTED_IMG_RESPONSE_1 = {
        "id": IMG_ID_1,
        "url": IMG_URL,
        "captured_at": CREATED_ON,
        "robot_id": ROBOT_ID,
        "crop_id": CROP_ID,
        "geohash": GEOHASH,
        "width": IMG_WIDTH,
        "height": IMG_HEIGHT,
    }

    IMG_2 = Image(
        id=IMG_ID_2,
        url=IMG_URL,
        captured_at=CREATED_ON,
        robot_id=ROBOT_ID,
        crop_id=CROP_ID,
        geohash=GEOHASH,
        width=IMG_WIDTH,
        height=IMG_HEIGHT,
    )

    EXPECTED_IMG_RESPONSE_2 = {
        "id": IMG_ID_2,
        "url": IMG_URL,
        "captured_at": CREATED_ON,
        "robot_id": ROBOT_ID,
        "crop_id": CROP_ID,
        "geohash": GEOHASH,
        "width": IMG_WIDTH,
        "height": IMG_HEIGHT,
    }

    TASK_1 = TaskV2(
        id=TASK_ID_1,
        type=TaskTypes.FURROW_LABEL,
        family_id=WORKFLOW_ID_1,
        created=CREATED_ON,
        updated=UPDATED_ON,
        image_id=IMG_ID_1,
        is_done=True,
        completed_on=COMPLETED_ON,
        image=IMG_1,
    )

    EXPECTED_TASK_1_RESPONSE = TaskEntry(
        id=TASK_ID_1,
        type=TaskTypes.FURROW_LABEL,
        workflow_id=WORKFLOW_ID_1,
        created_on=CREATED_ON,
        updated_on=UPDATED_ON,
        image_id=IMG_ID_1,
        is_done=True,
        completed_on=COMPLETED_ON,
    )

    TASK_2 = TaskV2(
        id=TASK_ID_2,
        type=TaskTypes.FURROW_REVIEW,
        family_id=WORKFLOW_ID_1,
        parent_id=TASK_ID_1,
        created=CREATED_ON,
        updated=UPDATED_ON,
        image_id=IMG_ID_2,
        invalidated_on=INVALIDATED_ON,
        invalidated_by=INVALIDATED_BY_ID,
        is_done=True,
        image=IMG_2,
    )

    EXPECTED_TASK_2_RESPONSE = TaskEntry(
        id=TASK_ID_2,
        type=TaskTypes.FURROW_REVIEW,
        workflow_id=WORKFLOW_ID_1,
        parent_task_id=TASK_ID_1,
        created_on=CREATED_ON,
        updated_on=UPDATED_ON,
        image_id=IMG_ID_2,
        invalidated_on=INVALIDATED_ON,
        invalidated_by_user=UserEntry(id=INVALIDATED_BY_ID, email=INVALIDATED_BY_EMAIL),
        is_done=True,
    )

    LINE_LABEL_RAW = {
        "start": {"x": 1, "y": 1},
        "end": {"x": 2, "y": 2},
        "category_id": "category_id",
        "confidence": Confidence.LOW.value,
    }

    LINE_LABEL = LineLabel(**LINE_LABEL_RAW)  # type: ignore

    LABEL_1 = FurrowLabel(id=LABEL_ID_1, user_id=USER_ID, line_labels=[LINE_LABEL.model_dump()])

    EXPECTED_LABEL_RESPONSE_1 = LabelEntry(
        id=LABEL_ID_1, user_id=USER_ID, label_data=FurrowsLabelData(line_labels=[LINE_LABEL])
    )

    LABEL_2 = FurrowLabel(id=LABEL_ID_2, user_id=USER_ID, line_labels=[LINE_LABEL.model_dump()])

    EXPECTED_LABEL_RESPONSE_2 = LabelEntry(
        id=LABEL_ID_2, user_id=USER_ID, label_data=FurrowsLabelData(line_labels=[LINE_LABEL])
    )


class TestCreateFurrowDataset(unittest.TestCase):
    CREATE_DATA_REQUEST = {
        "filter": {"lbf_task_ids": f"{Const.TASK_ID_1},{Const.TASK_ID_2}"},
        "name": Const.DATASET_NAME,
        "split_ratio": {"train": 0.7, "validation": 0.2, "test": 0.1},
    }

    EXPECTED_FILTER_CONDITIONS = FurrowLabelExploreFilter(
        furrow_filter=FurrowLabelFilters(
            task_ids=[Const.TASK_ID_1, Const.TASK_ID_2], is_production=True, is_valid=True
        ),
        image_filter=ImageFilterV2(image_type=ImageArtifactTypes.FURROW),
    )

    EXPECTED_SPLIT_RATIO = SplitSelectorRatio(train=0.7, validation=0.2, test=0.1)

    def setUp(self) -> None:
        self.app: Flask = create_app(True)
        self.api_flask_client = self.app.test_client()

        patch_create_dataset = patch(
            "server.apis.frontend.furrow_label.celery.create_furrow_dataset.delay",
            return_value=Const.VALID_CELERY_TASK_RESP,
        )
        self.mock_create_dataset = patch_create_dataset.start()
        self.addCleanup(patch_create_dataset.stop)

        patch_current_user = patch(
            "server.apis.frontend.furrow_label.current_user", new_callable=lambda: User(id=Const.USER_ID, admin=True)
        )
        self.mock_current_user = patch_current_user.start()
        self.addCleanup(patch_current_user.stop)

    def test_WHEN_create_dataset_request_is_valid_THEN_accurately_submit_celery_task(self) -> None:
        response = self.api_flask_client.post("/internal/furrow/dataset/job", json=self.CREATE_DATA_REQUEST)

        assert response.status_code == 200
        assert response.json == {"job_id": Const.CELERY_TASK_ID}
        self.mock_create_dataset.assert_called_once_with(
            dataset_id=ANY,
            dataset_name=Const.DATASET_NAME,
            filter_dict=self.EXPECTED_FILTER_CONDITIONS.model_dump(),
            user_id=None,
            split_ratio_dict=self.EXPECTED_SPLIT_RATIO.model_dump(),
            fast_run=False,
        )

    def test_WHEN_batch_invalidate_tasks_request_is_invalid_THEN_return_bad_request(self) -> None:
        invalid_request = dict(deepcopy(self.CREATE_DATA_REQUEST))
        del invalid_request["name"]

        response = self.api_flask_client.post("/internal/furrow/dataset/job", json=invalid_request)

        self.mock_create_dataset.assert_not_called()
        assert response.status_code == 400
        assert response.data == b"Request is not valid"


class TestGetFurrowLabelJobStatus(unittest.TestCase):
    JOB_ID = "job_id"

    RESULT = "test_result"

    FAILURE_MSG = "Batch job failed to complete"

    def setUp(self) -> None:
        self.app: Flask = create_app(True)
        self.api_flask_client = self.app.test_client()

        patch_get_job_status = patch(
            "server.apis.frontend.furrow_label.AsyncResult",
            new_callable=lambda: MagicMock(
                return_value=MagicMock(
                    status="SUCCESS",
                    result=self.RESULT,
                )
            ),
        )
        self.mock_get_job_status = patch_get_job_status.start()
        self.addCleanup(patch_get_job_status.stop)

        patch_current_user = patch(
            "server.apis.frontend.furrow_label.current_user", new_callable=lambda: User(id=Const.USER_ID, admin=True)
        )
        self.mock_current_user = patch_current_user.start()
        self.addCleanup(patch_current_user.stop)

    def test_WHEN_batch_job_status_is_requested_and_celery_task_succeeded_THEN_return_success_response(self) -> None:
        response = self.api_flask_client.get(f"/internal/furrow/dataset/job/{self.JOB_ID}")

        assert response.status_code == 200
        assert response.json == {
            "status": LongRunningJobStatus.SUCCESS.value,
            "dataset_id": f"{self.RESULT}",
        }
        self.mock_get_job_status.assert_called_once_with(self.JOB_ID)

    def test_WHEN_batch_job_status_is_requested_and_celery_task_failed_THEN_return_failed_response(self) -> None:
        self.mock_get_job_status.return_value.status = "FAILURE"
        self.mock_get_job_status.return_value.result = self.FAILURE_MSG

        response = self.api_flask_client.get(f"/internal/furrow/dataset/job/{self.JOB_ID}")

        assert response.status_code == 200
        assert response.json == {"status": LongRunningJobStatus.FAILED.value, "message": self.FAILURE_MSG}

    def test_WHEN_batch_job_status_is_requested_and_celery_task_pending_THEN_return_pending_response(self) -> None:
        self.mock_get_job_status.return_value.status = "PENDING"

        response = self.api_flask_client.get(f"/internal/furrow/dataset/job/{self.JOB_ID}")

        assert response.status_code == 200
        assert response.json == {"status": LongRunningJobStatus.PENDING.value}
