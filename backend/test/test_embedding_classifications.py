import json
import unittest
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, call, patch

import numpy as np

from server.db import tables
from server.predictions.embedding_types import EmbeddingPredictionData
from server.tasks.embedding_classifications import run_embedding_classification
from server.types import CategoryCollectionProfile, CategoryProfile
from server.utils.time import epoch_timestamp_ms
from server.utils.types import LongRunningJobStatus


class MockEmbeddingPrediction:
    """Mock for EmbeddingPrediction class"""

    def __init__(
        self,
        model_id: str,
        image_id: str,
        x: int,
        y: int,
        prediction_point_id: Optional[str] = None,
        status: LongRunningJobStatus = LongRunningJobStatus.SUCCESS,
        embedding_data: Optional[List[float]] = None,
    ):
        self._model_id = model_id
        self._image_id = image_id
        self._x = x
        self._y = y
        self._prediction_point_id = prediction_point_id
        self._status = status
        self._data = None

        if embedding_data is not None:
            self._data = EmbeddingPredictionData(
                image_id=image_id, model_id=model_id, embedding=embedding_data, x=x, y=y, metadata={}
            )

    @property
    def status(self) -> LongRunningJobStatus:
        return self._status

    @property
    def data(self) -> Optional[EmbeddingPredictionData]:
        return self._data


class TestEmbeddingClassifications(unittest.TestCase):
    def setUp(self) -> None:
        """Set up test fixtures"""
        self.session_id = "test-session-id"
        self.model_id = "test-model-id"
        self.prediction_point_ids = ["pp1", "pp2", "pp3", "pp4"]

        # Create mock prediction points
        self.support_prediction_points = [
            tables.PredictionPoint(id="support2", x=200, y=200, prediction_id="pred2"),
            tables.PredictionPoint(id="support1", x=100, y=100, prediction_id="pred1"),
        ]

        self.query_prediction_points = [
            tables.PredictionPoint(id="pp1", x=150, y=150, prediction_id="pred3"),
            tables.PredictionPoint(id="pp2", x=250, y=250, prediction_id="pred4"),
            tables.PredictionPoint(id="pp3", x=300, y=300, prediction_id="pred5"),
            tables.PredictionPoint(id="pp4", x=400, y=400, prediction_id="pred6"),
        ]

        # Create mock predictions for support points
        self.support_predictions = [
            tables.Prediction(id="pred1", image_id="img1"),
            tables.Prediction(id="pred2", image_id="img2"),
        ]

        # Create mock predictions for query points
        self.query_predictions = [
            tables.Prediction(id="pred3", image_id="img3"),
            tables.Prediction(id="pred4", image_id="img4"),
            tables.Prediction(id="pred5", image_id="img5"),
            tables.Prediction(id="pred6", image_id="img6"),
        ]

        # Link predictions to prediction points
        for i, pred in enumerate(self.support_predictions):
            self.support_prediction_points[i].prediction = pred

        for i, pred in enumerate(self.query_predictions):
            self.query_prediction_points[i].prediction = pred

        # Create category collection profile
        self.category_collection_profile = CategoryCollectionProfile(
            id="test-collection",
            category_profiles=[
                CategoryProfile(id="category1", prediction_point_ids=["support1"]),
                CategoryProfile(id="category2", prediction_point_ids=["support2"]),
            ],
        )

        # Create mock session
        self.mock_session = tables.PredictionPointClassificationSession(
            id=self.session_id,
            model_id=self.model_id,
            last_accessed=epoch_timestamp_ms(),
            category_collection_profile=self.category_collection_profile.model_dump(),
        )

    @patch("server.tasks.embedding_classifications.queries.prediction_point_classifications.create")
    @patch("server.tasks.embedding_classifications.queries.prediction_points.get_points")
    @patch("server.tasks.embedding_classifications.queries.prediction_point_classification_sessions.get")
    @patch("server.tasks.embedding_classifications.EmbeddingPrediction")
    @patch("server.db.committer")
    def test_run_embedding_classification_success(
        self,
        mock_committer: Mock,
        mock_embedding_prediction_class: Mock,
        mock_get_session: Mock,
        mock_get_points: Mock,
        mock_create_classification: Mock,
    ) -> None:
        """Test successful embedding classification"""

        mock_committer.return_value.__enter__ = Mock()
        mock_committer.return_value.__exit__ = Mock(return_value=None)

        # Setup mocks
        mock_get_session.return_value = self.mock_session

        # Mock get_points to return different results for support and query sets
        def mock_get_points_side_effect(point_ids: List[str]) -> List[tables.PredictionPoint]:
            if "support" in point_ids[0]:
                return self.support_prediction_points
            else:
                return self.query_prediction_points

        mock_get_points.side_effect = mock_get_points_side_effect

        # Create mock embedding predictions with different embeddings
        support_embeddings = [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0]]  # Category 1 embedding  # Category 2 embedding

        query_embeddings = [
            [0.9, 0.1, 0.0],  # Should match category 1
            [0.1, 0.9, 0.0],  # Should match category 2
            [0.6, 0.4, 0.0],  # Should match category 1
        ]

        def mock_embedding_prediction_side_effect(
            model_id: str, image_id: str, x: int, y: int, prediction_point_id: Optional[str] = None
        ) -> MockEmbeddingPrediction:
            # Determine which embedding to return based on prediction point ID
            if prediction_point_id == "support1":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=support_embeddings[0]
                )
            elif prediction_point_id == "support2":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=support_embeddings[1]
                )
            elif prediction_point_id == "pp1":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=query_embeddings[0]
                )
            elif prediction_point_id == "pp2":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=query_embeddings[1]
                )
            elif prediction_point_id == "pp3":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=query_embeddings[2]
                )
            else:
                # Embedding for all other points is still pending - hence it will be skipped from classification
                return MockEmbeddingPrediction(
                    model_id,
                    image_id,
                    x,
                    y,
                    prediction_point_id,
                    embedding_data=[0.0, 0.0, 0.0],
                    status=LongRunningJobStatus.PENDING,
                )

        mock_embedding_prediction_class.side_effect = mock_embedding_prediction_side_effect

        # Call the function
        result = run_embedding_classification(self.prediction_point_ids, self.session_id)

        # Verify results
        self.assertIsNone(result)

        # Verify that classifications were created
        expected_calls = [
            call(
                "pp1",
                self.session_id,
                "category1",
                matching_prediction_point_id="support1",
                similarity_score=0.9969418673368095,
                do_commit=True,
            ),
            call(
                "pp2",
                self.session_id,
                "category2",
                matching_prediction_point_id="support2",
                similarity_score=0.9969418673368095,
                do_commit=True,
            ),
            call(
                "pp3",
                self.session_id,
                "category1",
                matching_prediction_point_id="support1",
                similarity_score=0.9160251471689218,
                do_commit=True,
            ),
        ]

        mock_create_classification.assert_has_calls(expected_calls, any_order=True)
        self.assertEqual(mock_create_classification.call_count, 3)

    @patch("server.tasks.embedding_classifications.queries.prediction_point_classifications.create")
    @patch("server.tasks.embedding_classifications.queries.prediction_points.get_points")
    @patch("server.tasks.embedding_classifications.queries.prediction_point_classification_sessions.get")
    @patch("server.tasks.embedding_classifications.EmbeddingPrediction")
    @patch("server.db.committer")
    def test_WHEN_run_embedding_classification_and_all_embeddings_pending_THEN_skip_classification(
        self,
        mock_committer: Mock,
        mock_embedding_prediction_class: Mock,
        mock_get_session: Mock,
        mock_get_points: Mock,
        mock_create_classification: Mock,
    ) -> None:
        """Test successful embedding classification"""

        mock_committer.return_value.__enter__ = Mock()
        mock_committer.return_value.__exit__ = Mock(return_value=None)

        # Setup mocks
        mock_get_session.return_value = self.mock_session

        # Mock get_points to return different results for support and query sets
        def mock_get_points_side_effect(point_ids: List[str]) -> List[tables.PredictionPoint]:
            if "support" in point_ids[0]:
                return self.support_prediction_points
            else:
                return self.query_prediction_points

        mock_get_points.side_effect = mock_get_points_side_effect

        def mock_embedding_prediction_side_effect(
            model_id: str, image_id: str, x: int, y: int, prediction_point_id: Optional[str] = None
        ) -> MockEmbeddingPrediction:
            if prediction_point_id == "support1":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=[1.0, 0.0, 0.0]
                )
            elif prediction_point_id == "support2":
                return MockEmbeddingPrediction(
                    model_id, image_id, x, y, prediction_point_id, embedding_data=[0.0, 1.0, 0.0]
                )
            return MockEmbeddingPrediction(
                model_id, image_id, x, y, prediction_point_id, status=LongRunningJobStatus.PENDING
            )

        mock_embedding_prediction_class.side_effect = mock_embedding_prediction_side_effect

        # Call the function
        result = run_embedding_classification(self.prediction_point_ids, self.session_id)

        # Verify results
        self.assertIsNone(result)

        mock_create_classification.assert_has_calls([], any_order=True)
        self.assertEqual(mock_create_classification.call_count, 0)


if __name__ == "__main__":
    unittest.main()
