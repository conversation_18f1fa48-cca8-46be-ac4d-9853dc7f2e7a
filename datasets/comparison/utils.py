import os
import logging

from typing import Optional

from .dataset import ComparisonDataset

LOG = logging.getLogger("DL Datasets")


def create(filepath: str, migration_id: Optional[str] = None) -> ComparisonDataset:
    
    LOG.info(f"Creating comparison dataset: {filepath}")
    
    if os.path.exists(filepath):
        raise RuntimeError(f"File already exists: {filepath}")
    
    dataset = ComparisonDataset(filepath, migration_id=migration_id)
    
    return dataset

def load(filepath: str, migration_id: Optional[str] = None) -> ComparisonDataset:
    
    LOG.info(f"Loading comparison dataset: {filepath}")
    
    if not os.path.exists(filepath):
        raise RuntimeError(f"File does not exist: {filepath}")
    
    dataset = ComparisonDataset(filepath, migration_id=migration_id)

    return dataset