# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: almanac/almanac.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'almanac/almanac.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x61lmanac/almanac.proto\x12\x15\x63\x61rbon.aimbot.almanac\"\x91\x01\n\x07\x46ormula\x12\x12\n\nmultiplier\x18\x01 \x01(\x02\x12\x0e\n\x06offset\x18\x02 \x01(\x02\x12\x10\n\x08\x65xponent\x18\x03 \x01(\x02\x12\x1c\n\x14\x66ine_tune_multiplier\x18\x04 \x01(\r\x12\x10\n\x08max_time\x18\x05 \x01(\r\x12 \n\x18\x66ine_tune_multiplier_val\x18\x06 \x01(\x02\")\n\x05Trust\x12\x11\n\tignorable\x18\x01 \x01(\x08\x12\r\n\x05\x61void\x18\x02 \x01(\x08\"o\n\nModelTrust\x12\x0f\n\x07min_doo\x18\x01 \x01(\x02\x12\x19\n\x11weeding_threshold\x18\x02 \x01(\x02\x12\x1a\n\x12thinning_threshold\x18\x03 \x01(\x02\x12\x19\n\x11\x62\x61nding_threshold\x18\x04 \x01(\x02\"g\n\x0cTypeCategory\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\x45\n\x0e\x63lassification\x18\x02 \x01(\x0e\x32-.carbon.aimbot.almanac.CategoryClassification\"\x89\x01\n\x13\x41lmanacTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12\r\n\x05sizes\x18\x02 \x03(\x02\x12\x30\n\x08\x66ormulas\x18\x03 \x03(\x0b\x32\x1e.carbon.aimbot.almanac.Formula\"|\n\x19\x44iscriminatorTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12,\n\x06trusts\x18\x02 \x03(\x0b\x32\x1c.carbon.aimbot.almanac.Trust\"\x7f\n\x17ModelinatorTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12\x31\n\x06trusts\x18\x02 \x03(\x0b\x32!.carbon.aimbot.almanac.ModelTrust\"\x90\x01\n\rAlmanacConfig\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tprotected\x18\x03 \x01(\x08\x12\x12\n\nupdated_ts\x18\x04 \x01(\x04\x12>\n\ncategories\x18\x05 \x03(\x0b\x32*.carbon.aimbot.almanac.AlmanacTypeCategory\"\x9c\x01\n\x13\x44iscriminatorConfig\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tprotected\x18\x03 \x01(\x08\x12\x12\n\nupdated_ts\x18\x04 \x01(\x04\x12\x44\n\ncategories\x18\x05 \x03(\x0b\x32\x30.carbon.aimbot.almanac.DiscriminatorTypeCategory\"\x8c\x01\n\x11ModelinatorConfig\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\x12\x10\n\x08modified\x18\x03 \x01(\x08\x12\x42\n\ncategories\x18\x04 \x03(\x0b\x32..carbon.aimbot.almanac.ModelinatorTypeCategory*>\n\x16\x43\x61tegoryClassification\x12\x11\n\rCATEGORY_WEED\x10\x00\x12\x11\n\rCATEGORY_CROP\x10\x01\x42\x41Z?github.com/carbonrobotics/protos/golang/generated/proto/almanacb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'almanac.almanac_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z?github.com/carbonrobotics/protos/golang/generated/proto/almanac'
  _globals['_CATEGORYCLASSIFICATION']._serialized_start=1301
  _globals['_CATEGORYCLASSIFICATION']._serialized_end=1363
  _globals['_FORMULA']._serialized_start=49
  _globals['_FORMULA']._serialized_end=194
  _globals['_TRUST']._serialized_start=196
  _globals['_TRUST']._serialized_end=237
  _globals['_MODELTRUST']._serialized_start=239
  _globals['_MODELTRUST']._serialized_end=350
  _globals['_TYPECATEGORY']._serialized_start=352
  _globals['_TYPECATEGORY']._serialized_end=455
  _globals['_ALMANACTYPECATEGORY']._serialized_start=458
  _globals['_ALMANACTYPECATEGORY']._serialized_end=595
  _globals['_DISCRIMINATORTYPECATEGORY']._serialized_start=597
  _globals['_DISCRIMINATORTYPECATEGORY']._serialized_end=721
  _globals['_MODELINATORTYPECATEGORY']._serialized_start=723
  _globals['_MODELINATORTYPECATEGORY']._serialized_end=850
  _globals['_ALMANACCONFIG']._serialized_start=853
  _globals['_ALMANACCONFIG']._serialized_end=997
  _globals['_DISCRIMINATORCONFIG']._serialized_start=1000
  _globals['_DISCRIMINATORCONFIG']._serialized_end=1156
  _globals['_MODELINATORCONFIG']._serialized_start=1159
  _globals['_MODELINATORCONFIG']._serialized_end=1299
# @@protoc_insertion_point(module_scope)
