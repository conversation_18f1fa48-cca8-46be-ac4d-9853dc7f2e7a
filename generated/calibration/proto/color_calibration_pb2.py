# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: calibration/proto/color_calibration.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'calibration/proto/color_calibration.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)calibration/proto/color_calibration.proto\x12\x11\x63\x61libration.proto\x1a\x0futil/util.proto\"+\n\x07Request\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x10\n\x08row_type\x18\x02 \x01(\t\"\x1b\n\x08Response\x12\x0f\n\x07success\x18\x01 \x01(\x08\x32\x9d\x03\n\x17\x43olorCalibrationService\x12W\n\x1aInitializeColorCalibration\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12P\n\x13\x43\x65nterTargetCameras\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12P\n\x13SetAutoWhitebalance\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12I\n\x0cSaveToConfig\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12:\n\x05Reset\x12\x12.carbon.util.Empty\x1a\x1b.calibration.proto.Response\"\x00\x42\x45ZCgithub.com/carbonrobotics/protos/golang/generated/proto/calibrationb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'calibration.proto.color_calibration_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZCgithub.com/carbonrobotics/protos/golang/generated/proto/calibration'
  _globals['_REQUEST']._serialized_start=81
  _globals['_REQUEST']._serialized_end=124
  _globals['_RESPONSE']._serialized_start=126
  _globals['_RESPONSE']._serialized_end=153
  _globals['_COLORCALIBRATIONSERVICE']._serialized_start=156
  _globals['_COLORCALIBRATIONSERVICE']._serialized_end=569
# @@protoc_insertion_point(module_scope)
