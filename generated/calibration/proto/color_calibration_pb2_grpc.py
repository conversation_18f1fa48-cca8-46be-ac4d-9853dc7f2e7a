# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.calibration.proto import color_calibration_pb2 as calibration_dot_proto_dot_color__calibration__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in calibration/proto/color_calibration_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ColorCalibrationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.InitializeColorCalibration = channel.unary_unary(
                '/calibration.proto.ColorCalibrationService/InitializeColorCalibration',
                request_serializer=calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
                response_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
                _registered_method=True)
        self.CenterTargetCameras = channel.unary_unary(
                '/calibration.proto.ColorCalibrationService/CenterTargetCameras',
                request_serializer=calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
                response_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
                _registered_method=True)
        self.SetAutoWhitebalance = channel.unary_unary(
                '/calibration.proto.ColorCalibrationService/SetAutoWhitebalance',
                request_serializer=calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
                response_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
                _registered_method=True)
        self.SaveToConfig = channel.unary_unary(
                '/calibration.proto.ColorCalibrationService/SaveToConfig',
                request_serializer=calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
                response_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
                _registered_method=True)
        self.Reset = channel.unary_unary(
                '/calibration.proto.ColorCalibrationService/Reset',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
                _registered_method=True)


class ColorCalibrationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def InitializeColorCalibration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CenterTargetCameras(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAutoWhitebalance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveToConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Reset(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ColorCalibrationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'InitializeColorCalibration': grpc.unary_unary_rpc_method_handler(
                    servicer.InitializeColorCalibration,
                    request_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Request.FromString,
                    response_serializer=calibration_dot_proto_dot_color__calibration__pb2.Response.SerializeToString,
            ),
            'CenterTargetCameras': grpc.unary_unary_rpc_method_handler(
                    servicer.CenterTargetCameras,
                    request_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Request.FromString,
                    response_serializer=calibration_dot_proto_dot_color__calibration__pb2.Response.SerializeToString,
            ),
            'SetAutoWhitebalance': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAutoWhitebalance,
                    request_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Request.FromString,
                    response_serializer=calibration_dot_proto_dot_color__calibration__pb2.Response.SerializeToString,
            ),
            'SaveToConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveToConfig,
                    request_deserializer=calibration_dot_proto_dot_color__calibration__pb2.Request.FromString,
                    response_serializer=calibration_dot_proto_dot_color__calibration__pb2.Response.SerializeToString,
            ),
            'Reset': grpc.unary_unary_rpc_method_handler(
                    servicer.Reset,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=calibration_dot_proto_dot_color__calibration__pb2.Response.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'calibration.proto.ColorCalibrationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('calibration.proto.ColorCalibrationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ColorCalibrationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def InitializeColorCalibration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/calibration.proto.ColorCalibrationService/InitializeColorCalibration',
            calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
            calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CenterTargetCameras(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/calibration.proto.ColorCalibrationService/CenterTargetCameras',
            calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
            calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetAutoWhitebalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/calibration.proto.ColorCalibrationService/SetAutoWhitebalance',
            calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
            calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveToConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/calibration.proto.ColorCalibrationService/SaveToConfig',
            calibration_dot_proto_dot_color__calibration__pb2.Request.SerializeToString,
            calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Reset(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/calibration.proto.ColorCalibrationService/Reset',
            util_dot_util__pb2.Empty.SerializeToString,
            calibration_dot_proto_dot_color__calibration__pb2.Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
