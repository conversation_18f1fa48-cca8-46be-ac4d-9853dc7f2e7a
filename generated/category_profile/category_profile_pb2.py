# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: category_profile/category_profile.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'category_profile/category_profile.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'category_profile/category_profile.proto\x12\x17\x63\x61rbon.category_profile\"\x81\x01\n\x12\x43\x61tegoryCollection\x12\n\n\x02id\x18\x01 \x01(\t\x12\x18\n\x0b\x63ustomer_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x04 \x03(\t\x12\x11\n\tprotected\x18\x05 \x01(\x08\x42\x0e\n\x0c_customer_id\"s\n\x08\x43\x61tegory\x12\n\n\x02id\x18\x01 \x01(\t\x12\x18\n\x0b\x63ustomer_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08\x63hip_ids\x18\x04 \x03(\t\x12\x11\n\tprotected\x18\x05 \x01(\x08\x42\x0e\n\x0c_customer_idBJZHgithub.com/carbonrobotics/protos/golang/generated/proto/category_profileb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'category_profile.category_profile_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/carbonrobotics/protos/golang/generated/proto/category_profile'
  _globals['_CATEGORYCOLLECTION']._serialized_start=69
  _globals['_CATEGORYCOLLECTION']._serialized_end=198
  _globals['_CATEGORY']._serialized_start=200
  _globals['_CATEGORY']._serialized_end=315
# @@protoc_insertion_point(module_scope)
