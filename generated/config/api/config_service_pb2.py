# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/api/config_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'config/api/config_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1f\x63onfig/api/config_service.proto\x12\x13\x63\x61rbon.config.proto\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\x05\"\x19\n\x0cPongResponse\x12\t\n\x01x\x18\x01 \x01(\x05\"\x96\x01\n\x0b\x43onfigValue\x12\x13\n\tint64_val\x18\x01 \x01(\x03H\x00\x12\x14\n\nuint64_val\x18\x02 \x01(\x04H\x00\x12\x12\n\x08\x62ool_val\x18\x03 \x01(\x08H\x00\x12\x13\n\tfloat_val\x18\x04 \x01(\x01H\x00\x12\x14\n\nstring_val\x18\x05 \x01(\tH\x00\x12\x14\n\x0ctimestamp_ms\x18\x06 \x01(\x04\x42\x07\n\x05value\"6\n\x0cIntConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x03\x12\x0b\n\x03max\x18\x02 \x01(\x03\x12\x0c\n\x04step\x18\x03 \x01(\x03\"7\n\rUIntConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x04\x12\x0b\n\x03max\x18\x02 \x01(\x04\x12\x0c\n\x04step\x18\x03 \x01(\x04\"8\n\x0e\x46loatConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x01\x12\x0b\n\x03max\x18\x02 \x01(\x01\x12\x0c\n\x04step\x18\x03 \x01(\x01\"6\n\x0fStringConfigDef\x12\x12\n\nsize_limit\x18\x01 \x01(\r\x12\x0f\n\x07\x63hoices\x18\x03 \x03(\t\"\xd5\x03\n\tConfigDef\x12-\n\x04type\x18\x01 \x01(\x0e\x32\x1f.carbon.config.proto.ConfigType\x12\x39\n\ncomplexity\x18\x02 \x01(\x0e\x32%.carbon.config.proto.ConfigComplexity\x12\x34\n\x07int_def\x18\x03 \x01(\x0b\x32!.carbon.config.proto.IntConfigDefH\x00\x12\x36\n\x08uint_def\x18\x04 \x01(\x0b\x32\".carbon.config.proto.UIntConfigDefH\x00\x12\x38\n\tfloat_def\x18\x05 \x01(\x0b\x32#.carbon.config.proto.FloatConfigDefH\x00\x12:\n\nstring_def\x18\x06 \x01(\x0b\x32$.carbon.config.proto.StringConfigDefH\x00\x12\x0c\n\x04hint\x18\x07 \x01(\t\x12\x1b\n\x13\x64\x65\x66\x61ult_recommended\x18\x08 \x01(\x08\x12\x37\n\rdefault_value\x18\n \x01(\x0b\x32 .carbon.config.proto.ConfigValue\x12\r\n\x05units\x18\t \x01(\tB\x07\n\x05\x65xtra\"\xab\x01\n\nConfigNode\x12\x0c\n\x04name\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\x12+\n\x03\x64\x65\x66\x18\x03 \x01(\x0b\x32\x1e.carbon.config.proto.ConfigDef\x12\x31\n\x08\x63hildren\x18\x04 \x03(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"\xb3\x01\n\nSchemaNode\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x03\x64\x65\x66\x18\x02 \x01(\x0b\x32\x1e.carbon.config.proto.ConfigDef\x12\x32\n\tlist_item\x18\x03 \x01(\x0b\x32\x1f.carbon.config.proto.SchemaNode\x12\x36\n\rnode_children\x18\x04 \x03(\x0b\x32\x1f.carbon.config.proto.SchemaNode\"J\n\nConfigLeaf\x12\x0b\n\x03key\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\"O\n\x0fSetValueRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\"\x12\n\x10SetValueResponse\"\x1d\n\x0eGetTreeRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"@\n\x0fGetTreeResponse\x12-\n\x04node\x18\x01 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"L\n\x0eSetTreeRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x04node\x18\x02 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"\x11\n\x0fSetTreeResponse\"\x1f\n\x10GetLeavesRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"D\n\x11GetLeavesResponse\x12/\n\x06leaves\x18\x01 \x03(\x0b\x32\x1f.carbon.config.proto.ConfigLeaf\"-\n\x10\x41\x64\x64ToListRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x13\n\x11\x41\x64\x64ToListResponse\"2\n\x15RemoveFromListRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x18\n\x16RemoveFromListResponse\"#\n\x13SubscriptionRequest\x12\x0c\n\x04keys\x18\x01 \x03(\t\"I\n\x19SubscriptionNotifyMessage\x12\x18\n\x10subscription_key\x18\x01 \x01(\t\x12\x12\n\nnotify_key\x18\x02 \x01(\t\"V\n\x19UpgradeCloudConfigRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x13\n\x0bto_template\x18\x02 \x01(\t\x12\x15\n\rfrom_template\x18\x03 \x01(\t\"\x1c\n\x1aUpgradeCloudConfigResponse*T\n\nConfigType\x12\x08\n\x04NODE\x10\x00\x12\x08\n\x04LIST\x10\x01\x12\n\n\x06STRING\x10\x02\x12\x07\n\x03INT\x10\x03\x12\x08\n\x04UINT\x10\x04\x12\t\n\x05\x46LOAT\x10\x05\x12\x08\n\x04\x42OOL\x10\x06*E\n\x10\x43onfigComplexity\x12\x08\n\x04USER\x10\x00\x12\x0c\n\x08\x41\x44VANCED\x10\x01\x12\n\n\x06\x45XPERT\x10\x02\x12\r\n\tDEVELOPER\x10\x03\x32\xfb\x05\n\rConfigService\x12K\n\x04Ping\x12 .carbon.config.proto.PingRequest\x1a!.carbon.config.proto.PongResponse\x12W\n\x08SetValue\x12$.carbon.config.proto.SetValueRequest\x1a%.carbon.config.proto.SetValueResponse\x12T\n\x07GetTree\x12#.carbon.config.proto.GetTreeRequest\x1a$.carbon.config.proto.GetTreeResponse\x12T\n\x07SetTree\x12#.carbon.config.proto.SetTreeRequest\x1a$.carbon.config.proto.SetTreeResponse\x12Z\n\tGetLeaves\x12%.carbon.config.proto.GetLeavesRequest\x1a&.carbon.config.proto.GetLeavesResponse\x12Z\n\tAddToList\x12%.carbon.config.proto.AddToListRequest\x1a&.carbon.config.proto.AddToListResponse\x12i\n\x0eRemoveFromList\x12*.carbon.config.proto.RemoveFromListRequest\x1a+.carbon.config.proto.RemoveFromListResponse\x12u\n\x12UpgradeCloudConfig\x12..carbon.config.proto.UpgradeCloudConfigRequest\x1a/.carbon.config.proto.UpgradeCloudConfigResponse2\x84\x01\n\x19\x43onfigNotificationService\x12g\n\tSubscribe\x12(.carbon.config.proto.SubscriptionRequest\x1a..carbon.config.proto.SubscriptionNotifyMessage0\x01\x42HZFgithub.com/carbonrobotics/protos/golang/generated/proto/config_serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.api.config_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZFgithub.com/carbonrobotics/protos/golang/generated/proto/config_service'
  _globals['_CONFIGTYPE']._serialized_start=2167
  _globals['_CONFIGTYPE']._serialized_end=2251
  _globals['_CONFIGCOMPLEXITY']._serialized_start=2253
  _globals['_CONFIGCOMPLEXITY']._serialized_end=2322
  _globals['_PINGREQUEST']._serialized_start=56
  _globals['_PINGREQUEST']._serialized_end=80
  _globals['_PONGRESPONSE']._serialized_start=82
  _globals['_PONGRESPONSE']._serialized_end=107
  _globals['_CONFIGVALUE']._serialized_start=110
  _globals['_CONFIGVALUE']._serialized_end=260
  _globals['_INTCONFIGDEF']._serialized_start=262
  _globals['_INTCONFIGDEF']._serialized_end=316
  _globals['_UINTCONFIGDEF']._serialized_start=318
  _globals['_UINTCONFIGDEF']._serialized_end=373
  _globals['_FLOATCONFIGDEF']._serialized_start=375
  _globals['_FLOATCONFIGDEF']._serialized_end=431
  _globals['_STRINGCONFIGDEF']._serialized_start=433
  _globals['_STRINGCONFIGDEF']._serialized_end=487
  _globals['_CONFIGDEF']._serialized_start=490
  _globals['_CONFIGDEF']._serialized_end=959
  _globals['_CONFIGNODE']._serialized_start=962
  _globals['_CONFIGNODE']._serialized_end=1133
  _globals['_SCHEMANODE']._serialized_start=1136
  _globals['_SCHEMANODE']._serialized_end=1315
  _globals['_CONFIGLEAF']._serialized_start=1317
  _globals['_CONFIGLEAF']._serialized_end=1391
  _globals['_SETVALUEREQUEST']._serialized_start=1393
  _globals['_SETVALUEREQUEST']._serialized_end=1472
  _globals['_SETVALUERESPONSE']._serialized_start=1474
  _globals['_SETVALUERESPONSE']._serialized_end=1492
  _globals['_GETTREEREQUEST']._serialized_start=1494
  _globals['_GETTREEREQUEST']._serialized_end=1523
  _globals['_GETTREERESPONSE']._serialized_start=1525
  _globals['_GETTREERESPONSE']._serialized_end=1589
  _globals['_SETTREEREQUEST']._serialized_start=1591
  _globals['_SETTREEREQUEST']._serialized_end=1667
  _globals['_SETTREERESPONSE']._serialized_start=1669
  _globals['_SETTREERESPONSE']._serialized_end=1686
  _globals['_GETLEAVESREQUEST']._serialized_start=1688
  _globals['_GETLEAVESREQUEST']._serialized_end=1719
  _globals['_GETLEAVESRESPONSE']._serialized_start=1721
  _globals['_GETLEAVESRESPONSE']._serialized_end=1789
  _globals['_ADDTOLISTREQUEST']._serialized_start=1791
  _globals['_ADDTOLISTREQUEST']._serialized_end=1836
  _globals['_ADDTOLISTRESPONSE']._serialized_start=1838
  _globals['_ADDTOLISTRESPONSE']._serialized_end=1857
  _globals['_REMOVEFROMLISTREQUEST']._serialized_start=1859
  _globals['_REMOVEFROMLISTREQUEST']._serialized_end=1909
  _globals['_REMOVEFROMLISTRESPONSE']._serialized_start=1911
  _globals['_REMOVEFROMLISTRESPONSE']._serialized_end=1935
  _globals['_SUBSCRIPTIONREQUEST']._serialized_start=1937
  _globals['_SUBSCRIPTIONREQUEST']._serialized_end=1972
  _globals['_SUBSCRIPTIONNOTIFYMESSAGE']._serialized_start=1974
  _globals['_SUBSCRIPTIONNOTIFYMESSAGE']._serialized_end=2047
  _globals['_UPGRADECLOUDCONFIGREQUEST']._serialized_start=2049
  _globals['_UPGRADECLOUDCONFIGREQUEST']._serialized_end=2135
  _globals['_UPGRADECLOUDCONFIGRESPONSE']._serialized_start=2137
  _globals['_UPGRADECLOUDCONFIGRESPONSE']._serialized_end=2165
  _globals['_CONFIGSERVICE']._serialized_start=2325
  _globals['_CONFIGSERVICE']._serialized_end=3088
  _globals['_CONFIGNOTIFICATIONSERVICE']._serialized_start=3091
  _globals['_CONFIGNOTIFICATIONSERVICE']._serialized_end=3223
# @@protoc_insertion_point(module_scope)
