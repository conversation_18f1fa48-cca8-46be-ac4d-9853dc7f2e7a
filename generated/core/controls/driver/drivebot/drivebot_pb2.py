# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: core/controls/driver/drivebot/drivebot.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'core/controls/driver/drivebot/drivebot.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,core/controls/driver/drivebot/drivebot.proto\"\xc7\x01\n\x0c\x44riveRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\x19\n\x0cvelocity_mph\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12!\n\x14left_wheel_angle_deg\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\"\n\x15right_wheel_angle_deg\x18\x04 \x01(\x02H\x02\x88\x01\x01\x42\x0f\n\r_velocity_mphB\x17\n\x15_left_wheel_angle_degB\x18\n\x16_right_wheel_angle_deg\"\x0f\n\rDriveResponse\"\x12\n\x10GetStatusRequest\"\xb9\x02\n\x11GetStatusResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x15\n\x05state\x18\x02 \x01(\x0e\x32\x06.State\x12\x12\n\npc_control\x18\x03 \x01(\x08\x12#\n\x1btarget_left_wheel_angle_deg\x18\x04 \x01(\x02\x12#\n\x1b\x61\x63tual_left_wheel_angle_deg\x18\x05 \x01(\x02\x12$\n\x1ctarget_right_wheel_angle_deg\x18\x06 \x01(\x02\x12$\n\x1c\x61\x63tual_right_wheel_angle_deg\x18\x07 \x01(\x02\x12\x1b\n\x13target_velocity_mph\x18\x08 \x01(\x02\x12\x1b\n\x13\x61\x63tual_velocity_mph\x18\t \x01(\x02\x12\x13\n\x0bodometer_in\x18\n \x01(\x02\"3\n\x0b\x42uttonPress\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0e\n\x06\x62utton\x18\x02 \x01(\t\"\x19\n\x17GetButtonPressesRequest\"@\n\x18GetButtonPressesResponse\x12$\n\x0e\x62utton_presses\x18\x01 \x03(\x0b\x32\x0c.ButtonPress\"\x92\x01\n\x15SetVelocityPidRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\n\n\x02kp\x18\x02 \x01(\x02\x12\n\n\x02ki\x18\x03 \x01(\x02\x12\n\n\x02kd\x18\x04 \x01(\x02\x12\x16\n\x0estart_drive_ma\x18\x05 \x01(\x02\x12\x14\n\x0cmin_drive_ma\x18\x06 \x01(\x02\x12\x14\n\x0cmax_drive_ma\x18\x07 \x01(\x02\"\x18\n\x16SetVelocityPidResponse\"p\n\x12SetSteerPidRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\n\n\x02kp\x18\x02 \x01(\x02\x12\n\n\x02ki\x18\x03 \x01(\x02\x12\n\n\x02kd\x18\x04 \x01(\x02\x12#\n\x1bsteering_deadband_angle_deg\x18\x05 \x01(\x02\"\x15\n\x13SetSteerPidResponse*/\n\x05State\x12\x0b\n\x07STOPPED\x10\x00\x12\x0c\n\x08STOPPING\x10\x01\x12\x0b\n\x07\x44RIVING\x10\x02\x32\xba\x02\n\x0c\x44riveService\x12(\n\x05\x44rive\x12\r.DriveRequest\x1a\x0e.DriveResponse\"\x00\x12\x34\n\tGetStatus\x12\x11.GetStatusRequest\x1a\x12.GetStatusResponse\"\x00\x12\x43\n\x0eSetVelocityPid\x12\x16.SetVelocityPidRequest\x1a\x17.SetVelocityPidResponse\"\x00\x12:\n\x0bSetSteerPid\x12\x13.SetSteerPidRequest\x1a\x14.SetSteerPidResponse\"\x00\x12I\n\x10GetButtonPresses\x12\x18.GetButtonPressesRequest\x1a\x19.GetButtonPressesResponse\"\x00\x42VZTgithub.com/carbonrobotics/protos/golang/generated/proto/core/controls/drive/drivebotb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'core.controls.driver.drivebot.drivebot_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZTgithub.com/carbonrobotics/protos/golang/generated/proto/core/controls/drive/drivebot'
  _globals['_STATE']._serialized_start=1061
  _globals['_STATE']._serialized_end=1108
  _globals['_DRIVEREQUEST']._serialized_start=49
  _globals['_DRIVEREQUEST']._serialized_end=248
  _globals['_DRIVERESPONSE']._serialized_start=250
  _globals['_DRIVERESPONSE']._serialized_end=265
  _globals['_GETSTATUSREQUEST']._serialized_start=267
  _globals['_GETSTATUSREQUEST']._serialized_end=285
  _globals['_GETSTATUSRESPONSE']._serialized_start=288
  _globals['_GETSTATUSRESPONSE']._serialized_end=601
  _globals['_BUTTONPRESS']._serialized_start=603
  _globals['_BUTTONPRESS']._serialized_end=654
  _globals['_GETBUTTONPRESSESREQUEST']._serialized_start=656
  _globals['_GETBUTTONPRESSESREQUEST']._serialized_end=681
  _globals['_GETBUTTONPRESSESRESPONSE']._serialized_start=683
  _globals['_GETBUTTONPRESSESRESPONSE']._serialized_end=747
  _globals['_SETVELOCITYPIDREQUEST']._serialized_start=750
  _globals['_SETVELOCITYPIDREQUEST']._serialized_end=896
  _globals['_SETVELOCITYPIDRESPONSE']._serialized_start=898
  _globals['_SETVELOCITYPIDRESPONSE']._serialized_end=922
  _globals['_SETSTEERPIDREQUEST']._serialized_start=924
  _globals['_SETSTEERPIDREQUEST']._serialized_end=1036
  _globals['_SETSTEERPIDRESPONSE']._serialized_start=1038
  _globals['_SETSTEERPIDRESPONSE']._serialized_end=1059
  _globals['_DRIVESERVICE']._serialized_start=1111
  _globals['_DRIVESERVICE']._serialized_end=1425
# @@protoc_insertion_point(module_scope)
