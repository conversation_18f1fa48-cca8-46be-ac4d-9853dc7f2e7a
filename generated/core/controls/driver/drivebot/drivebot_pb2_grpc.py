# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.core.controls.driver.drivebot import drivebot_pb2 as core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in core/controls/driver/drivebot/drivebot_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class DriveServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Drive = channel.unary_unary(
                '/DriveService/Drive',
                request_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveResponse.FromString,
                _registered_method=True)
        self.GetStatus = channel.unary_unary(
                '/DriveService/GetStatus',
                request_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusResponse.FromString,
                _registered_method=True)
        self.SetVelocityPid = channel.unary_unary(
                '/DriveService/SetVelocityPid',
                request_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidResponse.FromString,
                _registered_method=True)
        self.SetSteerPid = channel.unary_unary(
                '/DriveService/SetSteerPid',
                request_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidResponse.FromString,
                _registered_method=True)
        self.GetButtonPresses = channel.unary_unary(
                '/DriveService/GetButtonPresses',
                request_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesResponse.FromString,
                _registered_method=True)


class DriveServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Drive(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetVelocityPid(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetSteerPid(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetButtonPresses(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DriveServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Drive': grpc.unary_unary_rpc_method_handler(
                    servicer.Drive,
                    request_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveRequest.FromString,
                    response_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveResponse.SerializeToString,
            ),
            'GetStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStatus,
                    request_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusRequest.FromString,
                    response_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusResponse.SerializeToString,
            ),
            'SetVelocityPid': grpc.unary_unary_rpc_method_handler(
                    servicer.SetVelocityPid,
                    request_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidRequest.FromString,
                    response_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidResponse.SerializeToString,
            ),
            'SetSteerPid': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSteerPid,
                    request_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidRequest.FromString,
                    response_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidResponse.SerializeToString,
            ),
            'GetButtonPresses': grpc.unary_unary_rpc_method_handler(
                    servicer.GetButtonPresses,
                    request_deserializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesRequest.FromString,
                    response_serializer=core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'DriveService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('DriveService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DriveService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Drive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DriveService/Drive',
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveRequest.SerializeToString,
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.DriveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DriveService/GetStatus',
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusRequest.SerializeToString,
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetVelocityPid(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DriveService/SetVelocityPid',
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidRequest.SerializeToString,
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetVelocityPidResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetSteerPid(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DriveService/SetSteerPid',
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidRequest.SerializeToString,
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.SetSteerPidResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetButtonPresses(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DriveService/GetButtonPresses',
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesRequest.SerializeToString,
            core_dot_controls_dot_driver_dot_drivebot_dot_drivebot__pb2.GetButtonPressesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
