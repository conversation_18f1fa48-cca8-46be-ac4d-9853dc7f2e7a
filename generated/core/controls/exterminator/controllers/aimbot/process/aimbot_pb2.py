# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: core/controls/exterminator/controllers/aimbot/process/aimbot.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'core/controls/exterminator/controllers/aimbot/process/aimbot.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nBcore/controls/exterminator/controllers/aimbot/process/aimbot.proto\x12\x06\x61imbot\x1a\x0futil/util.proto\x1a!weed_tracking/weed_tracking.proto\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x16\n\tPongReply\x12\t\n\x01x\x18\x01 \x01(\r\"C\n\x0eTargetingState\x12\x17\n\x0fweeding_enabled\x18\x01 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x02 \x01(\x08\"\xdd\x01\n\x0b\x41imbotState\x12\x11\n\talgorithm\x18\x01 \x01(\t\x12\x0f\n\x07running\x18\x02 \x01(\x08\x12\r\n\x05\x61rmed\x18\x03 \x01(\x08\x12/\n\x0ftargeting_state\x18\x04 \x01(\x0b\x32\x16.aimbot.TargetingState\x12\r\n\x05ready\x18\x05 \x01(\x08\x12:\n\x15safety_override_state\x18\x06 \x01(\x0e\x32\x1b.aimbot.SafetyOverrideState\x12\x1f\n\x17\x61\x63tuation_tasks_running\x18\x07 \x01(\x08\"\x17\n\x15TargetVelocityRequest\"A\n\x13TargetVelocityReply\x12\x14\n\x0cvelocity_min\x18\x01 \x01(\x02\x12\x14\n\x0cvelocity_max\x18\x02 \x01(\x02\"A\n\x16LaserTestActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x13\n\x0b\x64uration_ms\x18\x02 \x01(\r\"@\n\x16ImageDrawActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x12\n\nspeed_mmps\x18\x02 \x01(\x02\"@\n\x16RangeDrawActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x12\n\nduration_s\x18\x02 \x01(\x02\"\xc0\x01\n\x14\x41\x63tuationTaskRequest\x12\x34\n\nlaser_test\x18\x01 \x01(\x0b\x32\x1e.aimbot.LaserTestActuationTaskH\x00\x12\x34\n\nimage_draw\x18\x02 \x01(\x0b\x32\x1e.aimbot.ImageDrawActuationTaskH\x00\x12\x34\n\nrange_draw\x18\x03 \x01(\x0b\x32\x1e.aimbot.RangeDrawActuationTaskH\x00\x42\x06\n\x04task\"3\n\x0eLensSetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"\x0e\n\x0cLensSetReply\"$\n\x0eLensGetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"\x96\x01\n\x0cLensGetReply\x12\r\n\x05value\x18\x01 \x01(\r\x12\x11\n\tmin_value\x18\x02 \x01(\r\x12\x11\n\tmax_value\x18\x03 \x01(\r\x12 \n\x18manual_autofocus_percent\x18\x04 \x01(\x02\x12\x1b\n\x13manual_autofocusing\x18\x05 \x01(\x08\x12\x12\n\nscanner_id\x18\x06 \x01(\r\"<\n\x0fLensGetAllReply\x12)\n\x0blens_status\x18\x01 \x03(\x0b\x32\x14.aimbot.LensGetReply\"*\n\x14LensAutoFocusRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"\x14\n\x12LensAutoFocusReply\".\n\x18StopLensAutoFocusRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"4\n\x0fLaserArmRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05\x61rmed\x18\x02 \x01(\x08\"\x0f\n\rLaserArmReply\"9\n\x12LaserEnableRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"3\n\x10LaserEnableReply\x12\x0e\n\x06status\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"4\n\x10LaserFireRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x0c\n\x04\x66ire\x18\x02 \x01(\x08\"\x10\n\x0eLaserFireReply\"1\n\x0fLaserSetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\n\n\x02on\x18\x02 \x01(\x08\"\x0f\n\rLaserSetReply\"i\n\x1a\x42urnIdividualImagesRequest\x12\x12\n\nscanner_id\x18\x01 \x03(\r\x12\x12\n\nspeed_mmps\x18\x02 \x01(\x02\x12\x11\n\tintensity\x18\x03 \x01(\x02\x12\x10\n\x08json_img\x18\x04 \x01(\t\"\xc7\x01\n\nLaserState\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x0e\n\x06\x66iring\x18\x02 \x01(\x08\x12\r\n\x05\x65rror\x18\x03 \x01(\x08\x12\x16\n\nerror_code\x18\x04 \x01(\tB\x02\x18\x01\x12\x19\n\rerror_message\x18\x05 \x01(\tB\x02\x18\x01\x12\r\n\x05power\x18\x06 \x01(\x08\x12\x12\n\ndelta_temp\x18\x07 \x01(\x02\x12\x0f\n\x07\x63urrent\x18\x08 \x01(\x02\x12\r\n\x05\x61rced\x18\t \x01(\x08\x12\x13\n\x0bpower_level\x18\n \x01(\x02\"\xb7\x01\n\x10ServoGoToRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x36\n\nservo_type\x18\x02 \x01(\x0e\x32\".aimbot.ServoGoToRequest.ServoType\x12\x10\n\x08position\x18\x03 \x01(\x05\x12\x0f\n\x07time_ms\x18\x04 \x01(\r\x12\x14\n\x0c\x61wait_settle\x18\x05 \x01(\x08\"\x1e\n\tServoType\x12\x07\n\x03PAN\x10\x00\x12\x08\n\x04TILT\x10\x01\"\x10\n\x0eServoGoToReply\"+\n\x15ServoGetPosVelRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"o\n\x13ServoGetPosVelReply\x12\x14\n\x0cpan_position\x18\x01 \x01(\x05\x12\x15\n\rtilt_position\x18\x02 \x01(\x05\x12\x14\n\x0cpan_velocity\x18\x03 \x01(\r\x12\x15\n\rtilt_velocity\x18\x04 \x01(\r\"+\n\x15ServoGetLimitsRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"[\n\x13ServoGetLimitsReply\x12\x0f\n\x07pan_min\x18\x01 \x01(\x05\x12\x0f\n\x07pan_max\x18\x02 \x01(\x05\x12\x10\n\x08tilt_min\x18\x03 \x01(\x05\x12\x10\n\x08tilt_max\x18\x04 \x01(\x05\"\x80\x01\n\x0bTuningParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x06v_uint\x18\x02 \x01(\rH\x00\x12\x0f\n\x05v_int\x18\x03 \x01(\x05H\x00\x12\x10\n\x06v_bool\x18\x04 \x01(\x08H\x00\x12\x11\n\x07v_float\x18\x05 \x01(\x02H\x00\x12\x12\n\x08v_string\x18\x06 \x01(\tH\x00\x42\x07\n\x05value\"@\n\x19TuningParamsUpdateRequest\x12#\n\x06params\x18\x01 \x03(\x0b\x32\x13.aimbot.TuningParam\"\x19\n\x17TuningParamsUpdateReply\"&\n\x16TuningParamsGetRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\";\n\x14TuningParamsGetReply\x12#\n\x06params\x18\x01 \x03(\x0b\x32\x13.aimbot.TuningParam\"\x18\n\x16GetLoadEstimateRequest\"A\n\x14GetLoadEstimateReply\x12\x14\n\x0c\x63urrent_load\x18\x01 \x01(\x02\x12\x13\n\x0btarget_load\x18\x02 \x01(\x02\"\x16\n\x14GetDiagnosticRequest\"(\n\x12GetDiagnosticReply\x12\x12\n\ndiagnostic\x18\x01 \x01(\t\"(\n\x13ResetDevicesRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\"\x13\n\x11ResetDevicesReply\"?\n\x13ResetScannerRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x14\n\x0cmetrics_only\x18\x02 \x01(\x08\"\x13\n\x11ResetScannerReply\"\x1f\n\x11ScannerDescriptor\x12\n\n\x02id\x18\x01 \x01(\r\"d\n\x15ScannerTargetPosition\x12\x35\n\x12scanner_descriptor\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\t\n\x01x\x18\x02 \x01(\r\x12\t\n\x01y\x18\x03 \x01(\r\"W\n\x0e\x43rosshairState\x12\t\n\x01x\x18\x01 \x01(\r\x12\t\n\x01y\x18\x02 \x01(\r\x12\x13\n\x0b\x63\x61librating\x18\x03 \x01(\x08\x12\x1a\n\x12\x63\x61libration_failed\x18\x04 \x01(\x08\"\xaa\x02\n\x0cScannerState\x12\x35\n\x12scanner_descriptor\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\'\n\x0blaser_state\x18\x02 \x01(\x0b\x32\x12.aimbot.LaserState\x12/\n\x0f\x63rosshair_state\x18\x03 \x01(\x0b\x32\x16.aimbot.CrosshairState\x12\x15\n\rscanner_error\x18\x04 \x01(\x08\x12\x12\n\nerror_code\x18\x05 \x01(\t\x12\x15\n\rerror_message\x18\x06 \x01(\t\x12\x1c\n\x14target_trajectory_id\x18\x07 \x01(\r\x12\x13\n\x0bpan_failure\x18\x08 \x01(\x08\x12\x14\n\x0ctilt_failure\x18\t \x01(\x08\"E\n\x1c\x41utoXHairCalibrationProgress\x12\x13\n\x0bin_progress\x18\x01 \x01(\x08\x12\x10\n\x08progress\x18\x02 \x01(\x02\"y\n\x12ScannerStatusReply\x12$\n\x06states\x18\x01 \x03(\x0b\x32\x14.aimbot.ScannerState\x12=\n\x0fx_hair_progress\x18\x02 \x01(\x0b\x32$.aimbot.AutoXHairCalibrationProgress\"\x1d\n\x0b\x42ootedReply\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08\"a\n\x0cTrackerState\x12\n\n\x02id\x18\x01 \x01(\r\x12\x15\n\rat_weed_limit\x18\x02 \x01(\x08\x12\x16\n\x0erotary_timeout\x18\x03 \x01(\x08\x12\x16\n\x0e\x64\x65\x65pweed_error\x18\x04 \x01(\x08\"\'\n\x0eSchedulerState\x12\x15\n\rover_capacity\x18\x01 \x01(\x08\"f\n\rTrackingState\x12$\n\x06states\x18\x01 \x03(\x0b\x32\x14.aimbot.TrackerState\x12/\n\x0fscheduler_state\x18\x02 \x01(\x0b\x32\x16.aimbot.SchedulerState\"`\n\x13\x42\x65\x64topHeightProfile\x12\x1b\n\x13weed_height_columns\x18\x01 \x03(\x01\x12\x1b\n\x13\x63rop_height_columns\x18\x02 \x03(\x01\x12\x0f\n\x07pcam_id\x18\x03 \x01(\t\"b\n\x1aTrackerBedtopHeightProfile\x12-\n\x08profiles\x18\x01 \x03(\x0b\x32\x1b.aimbot.BedtopHeightProfile\x12\x15\n\rbbh_offset_mm\x18\x02 \x01(\x02\"t\n\x15GetDimensionsResponse\x12\x10\n\x08min_x_mm\x18\x01 \x01(\x01\x12\x10\n\x08max_x_mm\x18\x02 \x01(\x01\x12\x10\n\x08min_y_mm\x18\x03 \x01(\x01\x12\x10\n\x08max_y_mm\x18\x04 \x01(\x01\x12\x13\n\x0b\x63\x65nter_x_mm\x18\x05 \x01(\x01\"*\n\x15GetTargetCamSNRequest\x12\x11\n\tcamera_id\x18\x01 \x01(\t\"/\n\x16GetTargetCamSNResponse\x12\x15\n\rserial_number\x18\x01 \x01(\t\"\x1b\n\x19ReloadThinningConfRequest\"\x1a\n\x18ReloadAlmanacConfRequest\" \n\x1eReloadDiscriminatorConfRequest\"\x1e\n\x1cReloadModelinatorConfRequest\"\x19\n\x17ReloadTVEProfileRequest\"z\n\x16\x46ixLaserMetricsRequest\x12*\n\x07scanner\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\x18\n\x10total_fire_count\x18\x02 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x03 \x01(\x03\"%\n\x13TrackedItemsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"W\n\x12TrackedItemHistory\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12+\n\tdetection\x18\x02 \x01(\x0b\x32\x18.weed_tracking.Detection\"F\n\x0bTrackedItem\x12\n\n\x02id\x18\x01 \x01(\x03\x12+\n\x07history\x18\x02 \x03(\x0b\x32\x1a.aimbot.TrackedItemHistory\"B\n\x14TrackedItemsResponse\x12*\n\rtracked_items\x18\x01 \x03(\x0b\x32\x13.aimbot.TrackedItem\"2\n\x15ParticipationResponse\x12\x19\n\x11running_as_leader\x18\x01 \x01(\x08*M\n\x13SafetyOverrideState\x12\x16\n\x12SafetyOverrideNone\x10\x00\x12\x1e\n\x1aSafetyOverrideVelocityStop\x10\x01\x32\x87\x1b\n\rAimbotService\x12\x30\n\x04Ping\x12\x13.aimbot.PingRequest\x1a\x11.aimbot.PongReply\"\x00\x12\x36\n\tGetBooted\x12\x12.carbon.util.Empty\x1a\x13.aimbot.BootedReply\"\x00\x12\x35\n\tArmLasers\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\"\x00\x12\x38\n\x0c\x44isarmLasers\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\"\x00\x12;\n\x0eGetAimbotState\x12\x12.carbon.util.Empty\x1a\x13.aimbot.AimbotState\"\x00\x12\x41\n\x11SetTargetingState\x12\x16.aimbot.TargetingState\x1a\x12.carbon.util.Empty\"\x00\x12H\n\x12StartActuationTask\x12\x1c.aimbot.ActuationTaskRequest\x1a\x12.carbon.util.Empty\"\x00\x12?\n\x13\x43\x61ncelActuationTask\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\"\x00\x12\x39\n\x07LensSet\x12\x16.aimbot.LensSetRequest\x1a\x14.aimbot.LensSetReply\"\x00\x12\x39\n\x07LensGet\x12\x16.aimbot.LensGetRequest\x1a\x14.aimbot.LensGetReply\"\x00\x12;\n\nLensGetAll\x12\x12.carbon.util.Empty\x1a\x17.aimbot.LensGetAllReply\"\x00\x12K\n\rLensAutoFocus\x12\x1c.aimbot.LensAutoFocusRequest\x1a\x1a.aimbot.LensAutoFocusReply\"\x00\x12K\n\x11StopLensAutoFocus\x12 .aimbot.StopLensAutoFocusRequest\x1a\x12.carbon.util.Empty\"\x00\x12<\n\x08LaserArm\x12\x17.aimbot.LaserArmRequest\x1a\x15.aimbot.LaserArmReply\"\x00\x12<\n\x08LaserSet\x12\x17.aimbot.LaserSetRequest\x1a\x15.aimbot.LaserSetReply\"\x00\x12\x45\n\x0bLaserEnable\x12\x1a.aimbot.LaserEnableRequest\x1a\x18.aimbot.LaserEnableReply\"\x00\x12?\n\tLaserFire\x12\x18.aimbot.LaserFireRequest\x1a\x16.aimbot.LaserFireReply\"\x00\x12\x44\n\x11ResetLaserMetrics\x12\x19.aimbot.ScannerDescriptor\x1a\x12.carbon.util.Empty\"\x00\x12G\n\x0f\x46ixLaserMetrics\x12\x1e.aimbot.FixLaserMetricsRequest\x1a\x12.carbon.util.Empty\"\x00\x12N\n\x12\x42urnIdividualImage\x12\".aimbot.BurnIdividualImagesRequest\x1a\x12.carbon.util.Empty\"\x00\x12?\n\tServoGoTo\x12\x18.aimbot.ServoGoToRequest\x1a\x16.aimbot.ServoGoToReply\"\x00\x12N\n\x0eServoGetPosVel\x12\x1d.aimbot.ServoGetPosVelRequest\x1a\x1b.aimbot.ServoGetPosVelReply\"\x00\x12N\n\x0eServoGetLimits\x12\x1d.aimbot.ServoGetLimitsRequest\x1a\x1b.aimbot.ServoGetLimitsReply\"\x00\x12Z\n\x12TuningParamsUpdate\x12!.aimbot.TuningParamsUpdateRequest\x1a\x1f.aimbot.TuningParamsUpdateReply\"\x00\x12Q\n\x0fTuningParamsGet\x12\x1e.aimbot.TuningParamsGetRequest\x1a\x1c.aimbot.TuningParamsGetReply\"\x00\x12Q\n\x0fGetLoadEstimate\x12\x1e.aimbot.GetLoadEstimateRequest\x1a\x1c.aimbot.GetLoadEstimateReply\"\x00\x12K\n\rGetDiagnostic\x12\x1c.aimbot.GetDiagnosticRequest\x1a\x1a.aimbot.GetDiagnosticReply\"\x00\x12H\n\x0cResetDevices\x12\x1b.aimbot.ResetDevicesRequest\x1a\x19.aimbot.ResetDevicesReply\"\x00\x12H\n\x0cResetScanner\x12\x1b.aimbot.ResetScannerRequest\x1a\x19.aimbot.ResetScannerReply\"\x00\x12L\n\x1bStartAutoCalibrateCrosshair\x12\x19.aimbot.ScannerDescriptor\x1a\x12.carbon.util.Empty\x12I\n\x1fStartAutoCalibrateAllCrosshairs\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12;\n\x11StopAutoCalibrate\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12I\n\x14SetCrosshairPosition\x12\x1d.aimbot.ScannerTargetPosition\x1a\x12.carbon.util.Empty\x12@\n\x0bMoveScanner\x12\x1d.aimbot.ScannerTargetPosition\x1a\x12.carbon.util.Empty\x12V\n\x1aGetAutoCalibrationProgress\x12\x12.carbon.util.Empty\x1a$.aimbot.AutoXHairCalibrationProgress\x12\x42\n\x10GetScannerStatus\x12\x12.carbon.util.Empty\x1a\x1a.aimbot.ScannerStatusReply\x12O\n\x11GetTargetVelocity\x12\x1d.aimbot.TargetVelocityRequest\x1a\x1b.aimbot.TargetVelocityReply\x12=\n\x10GetTrackingState\x12\x12.carbon.util.Empty\x1a\x15.aimbot.TrackingState\x12P\n\x16GetBedtopHeightProfile\x12\x12.carbon.util.Empty\x1a\".aimbot.TrackerBedtopHeightProfile\x12\x42\n\rGetDimensions\x12\x12.carbon.util.Empty\x1a\x1d.aimbot.GetDimensionsResponse\x12O\n\x0eGetTargetCamSN\x12\x1d.aimbot.GetTargetCamSNRequest\x1a\x1e.aimbot.GetTargetCamSNResponse\x12K\n\x12ReloadThinningConf\x12!.aimbot.ReloadThinningConfRequest\x1a\x12.carbon.util.Empty\x12I\n\x11ReloadAlmanacConf\x12 .aimbot.ReloadAlmanacConfRequest\x1a\x12.carbon.util.Empty\x12U\n\x17ReloadDiscriminatorConf\x12&.aimbot.ReloadDiscriminatorConfRequest\x1a\x12.carbon.util.Empty\x12Q\n\x15ReloadModelinatorConf\x12$.aimbot.ReloadModelinatorConfRequest\x1a\x12.carbon.util.Empty\x12G\n\x10ReloadTVEProfile\x12\x1f.aimbot.ReloadTVEProfileRequest\x1a\x12.carbon.util.Empty\x12T\n\x17GetDistanceTrackedItems\x12\x1b.aimbot.TrackedItemsRequest\x1a\x1c.aimbot.TrackedItemsResponse\x12\x45\n\x10GetParticipation\x12\x12.carbon.util.Empty\x1a\x1d.aimbot.ParticipationResponseB@Z>github.com/carbonrobotics/protos/golang/generated/proto/aimbotb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'core.controls.exterminator.controllers.aimbot.process.aimbot_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/aimbot'
  _globals['_LASERSTATE'].fields_by_name['error_code']._loaded_options = None
  _globals['_LASERSTATE'].fields_by_name['error_code']._serialized_options = b'\030\001'
  _globals['_LASERSTATE'].fields_by_name['error_message']._loaded_options = None
  _globals['_LASERSTATE'].fields_by_name['error_message']._serialized_options = b'\030\001'
  _globals['_SAFETYOVERRIDESTATE']._serialized_start=5157
  _globals['_SAFETYOVERRIDESTATE']._serialized_end=5234
  _globals['_PINGREQUEST']._serialized_start=130
  _globals['_PINGREQUEST']._serialized_end=154
  _globals['_PONGREPLY']._serialized_start=156
  _globals['_PONGREPLY']._serialized_end=178
  _globals['_TARGETINGSTATE']._serialized_start=180
  _globals['_TARGETINGSTATE']._serialized_end=247
  _globals['_AIMBOTSTATE']._serialized_start=250
  _globals['_AIMBOTSTATE']._serialized_end=471
  _globals['_TARGETVELOCITYREQUEST']._serialized_start=473
  _globals['_TARGETVELOCITYREQUEST']._serialized_end=496
  _globals['_TARGETVELOCITYREPLY']._serialized_start=498
  _globals['_TARGETVELOCITYREPLY']._serialized_end=563
  _globals['_LASERTESTACTUATIONTASK']._serialized_start=565
  _globals['_LASERTESTACTUATIONTASK']._serialized_end=630
  _globals['_IMAGEDRAWACTUATIONTASK']._serialized_start=632
  _globals['_IMAGEDRAWACTUATIONTASK']._serialized_end=696
  _globals['_RANGEDRAWACTUATIONTASK']._serialized_start=698
  _globals['_RANGEDRAWACTUATIONTASK']._serialized_end=762
  _globals['_ACTUATIONTASKREQUEST']._serialized_start=765
  _globals['_ACTUATIONTASKREQUEST']._serialized_end=957
  _globals['_LENSSETREQUEST']._serialized_start=959
  _globals['_LENSSETREQUEST']._serialized_end=1010
  _globals['_LENSSETREPLY']._serialized_start=1012
  _globals['_LENSSETREPLY']._serialized_end=1026
  _globals['_LENSGETREQUEST']._serialized_start=1028
  _globals['_LENSGETREQUEST']._serialized_end=1064
  _globals['_LENSGETREPLY']._serialized_start=1067
  _globals['_LENSGETREPLY']._serialized_end=1217
  _globals['_LENSGETALLREPLY']._serialized_start=1219
  _globals['_LENSGETALLREPLY']._serialized_end=1279
  _globals['_LENSAUTOFOCUSREQUEST']._serialized_start=1281
  _globals['_LENSAUTOFOCUSREQUEST']._serialized_end=1323
  _globals['_LENSAUTOFOCUSREPLY']._serialized_start=1325
  _globals['_LENSAUTOFOCUSREPLY']._serialized_end=1345
  _globals['_STOPLENSAUTOFOCUSREQUEST']._serialized_start=1347
  _globals['_STOPLENSAUTOFOCUSREQUEST']._serialized_end=1393
  _globals['_LASERARMREQUEST']._serialized_start=1395
  _globals['_LASERARMREQUEST']._serialized_end=1447
  _globals['_LASERARMREPLY']._serialized_start=1449
  _globals['_LASERARMREPLY']._serialized_end=1464
  _globals['_LASERENABLEREQUEST']._serialized_start=1466
  _globals['_LASERENABLEREQUEST']._serialized_end=1523
  _globals['_LASERENABLEREPLY']._serialized_start=1525
  _globals['_LASERENABLEREPLY']._serialized_end=1576
  _globals['_LASERFIREREQUEST']._serialized_start=1578
  _globals['_LASERFIREREQUEST']._serialized_end=1630
  _globals['_LASERFIREREPLY']._serialized_start=1632
  _globals['_LASERFIREREPLY']._serialized_end=1648
  _globals['_LASERSETREQUEST']._serialized_start=1650
  _globals['_LASERSETREQUEST']._serialized_end=1699
  _globals['_LASERSETREPLY']._serialized_start=1701
  _globals['_LASERSETREPLY']._serialized_end=1716
  _globals['_BURNIDIVIDUALIMAGESREQUEST']._serialized_start=1718
  _globals['_BURNIDIVIDUALIMAGESREQUEST']._serialized_end=1823
  _globals['_LASERSTATE']._serialized_start=1826
  _globals['_LASERSTATE']._serialized_end=2025
  _globals['_SERVOGOTOREQUEST']._serialized_start=2028
  _globals['_SERVOGOTOREQUEST']._serialized_end=2211
  _globals['_SERVOGOTOREQUEST_SERVOTYPE']._serialized_start=2181
  _globals['_SERVOGOTOREQUEST_SERVOTYPE']._serialized_end=2211
  _globals['_SERVOGOTOREPLY']._serialized_start=2213
  _globals['_SERVOGOTOREPLY']._serialized_end=2229
  _globals['_SERVOGETPOSVELREQUEST']._serialized_start=2231
  _globals['_SERVOGETPOSVELREQUEST']._serialized_end=2274
  _globals['_SERVOGETPOSVELREPLY']._serialized_start=2276
  _globals['_SERVOGETPOSVELREPLY']._serialized_end=2387
  _globals['_SERVOGETLIMITSREQUEST']._serialized_start=2389
  _globals['_SERVOGETLIMITSREQUEST']._serialized_end=2432
  _globals['_SERVOGETLIMITSREPLY']._serialized_start=2434
  _globals['_SERVOGETLIMITSREPLY']._serialized_end=2525
  _globals['_TUNINGPARAM']._serialized_start=2528
  _globals['_TUNINGPARAM']._serialized_end=2656
  _globals['_TUNINGPARAMSUPDATEREQUEST']._serialized_start=2658
  _globals['_TUNINGPARAMSUPDATEREQUEST']._serialized_end=2722
  _globals['_TUNINGPARAMSUPDATEREPLY']._serialized_start=2724
  _globals['_TUNINGPARAMSUPDATEREPLY']._serialized_end=2749
  _globals['_TUNINGPARAMSGETREQUEST']._serialized_start=2751
  _globals['_TUNINGPARAMSGETREQUEST']._serialized_end=2789
  _globals['_TUNINGPARAMSGETREPLY']._serialized_start=2791
  _globals['_TUNINGPARAMSGETREPLY']._serialized_end=2850
  _globals['_GETLOADESTIMATEREQUEST']._serialized_start=2852
  _globals['_GETLOADESTIMATEREQUEST']._serialized_end=2876
  _globals['_GETLOADESTIMATEREPLY']._serialized_start=2878
  _globals['_GETLOADESTIMATEREPLY']._serialized_end=2943
  _globals['_GETDIAGNOSTICREQUEST']._serialized_start=2945
  _globals['_GETDIAGNOSTICREQUEST']._serialized_end=2967
  _globals['_GETDIAGNOSTICREPLY']._serialized_start=2969
  _globals['_GETDIAGNOSTICREPLY']._serialized_end=3009
  _globals['_RESETDEVICESREQUEST']._serialized_start=3011
  _globals['_RESETDEVICESREQUEST']._serialized_end=3051
  _globals['_RESETDEVICESREPLY']._serialized_start=3053
  _globals['_RESETDEVICESREPLY']._serialized_end=3072
  _globals['_RESETSCANNERREQUEST']._serialized_start=3074
  _globals['_RESETSCANNERREQUEST']._serialized_end=3137
  _globals['_RESETSCANNERREPLY']._serialized_start=3139
  _globals['_RESETSCANNERREPLY']._serialized_end=3158
  _globals['_SCANNERDESCRIPTOR']._serialized_start=3160
  _globals['_SCANNERDESCRIPTOR']._serialized_end=3191
  _globals['_SCANNERTARGETPOSITION']._serialized_start=3193
  _globals['_SCANNERTARGETPOSITION']._serialized_end=3293
  _globals['_CROSSHAIRSTATE']._serialized_start=3295
  _globals['_CROSSHAIRSTATE']._serialized_end=3382
  _globals['_SCANNERSTATE']._serialized_start=3385
  _globals['_SCANNERSTATE']._serialized_end=3683
  _globals['_AUTOXHAIRCALIBRATIONPROGRESS']._serialized_start=3685
  _globals['_AUTOXHAIRCALIBRATIONPROGRESS']._serialized_end=3754
  _globals['_SCANNERSTATUSREPLY']._serialized_start=3756
  _globals['_SCANNERSTATUSREPLY']._serialized_end=3877
  _globals['_BOOTEDREPLY']._serialized_start=3879
  _globals['_BOOTEDREPLY']._serialized_end=3908
  _globals['_TRACKERSTATE']._serialized_start=3910
  _globals['_TRACKERSTATE']._serialized_end=4007
  _globals['_SCHEDULERSTATE']._serialized_start=4009
  _globals['_SCHEDULERSTATE']._serialized_end=4048
  _globals['_TRACKINGSTATE']._serialized_start=4050
  _globals['_TRACKINGSTATE']._serialized_end=4152
  _globals['_BEDTOPHEIGHTPROFILE']._serialized_start=4154
  _globals['_BEDTOPHEIGHTPROFILE']._serialized_end=4250
  _globals['_TRACKERBEDTOPHEIGHTPROFILE']._serialized_start=4252
  _globals['_TRACKERBEDTOPHEIGHTPROFILE']._serialized_end=4350
  _globals['_GETDIMENSIONSRESPONSE']._serialized_start=4352
  _globals['_GETDIMENSIONSRESPONSE']._serialized_end=4468
  _globals['_GETTARGETCAMSNREQUEST']._serialized_start=4470
  _globals['_GETTARGETCAMSNREQUEST']._serialized_end=4512
  _globals['_GETTARGETCAMSNRESPONSE']._serialized_start=4514
  _globals['_GETTARGETCAMSNRESPONSE']._serialized_end=4561
  _globals['_RELOADTHINNINGCONFREQUEST']._serialized_start=4563
  _globals['_RELOADTHINNINGCONFREQUEST']._serialized_end=4590
  _globals['_RELOADALMANACCONFREQUEST']._serialized_start=4592
  _globals['_RELOADALMANACCONFREQUEST']._serialized_end=4618
  _globals['_RELOADDISCRIMINATORCONFREQUEST']._serialized_start=4620
  _globals['_RELOADDISCRIMINATORCONFREQUEST']._serialized_end=4652
  _globals['_RELOADMODELINATORCONFREQUEST']._serialized_start=4654
  _globals['_RELOADMODELINATORCONFREQUEST']._serialized_end=4684
  _globals['_RELOADTVEPROFILEREQUEST']._serialized_start=4686
  _globals['_RELOADTVEPROFILEREQUEST']._serialized_end=4711
  _globals['_FIXLASERMETRICSREQUEST']._serialized_start=4713
  _globals['_FIXLASERMETRICSREQUEST']._serialized_end=4835
  _globals['_TRACKEDITEMSREQUEST']._serialized_start=4837
  _globals['_TRACKEDITEMSREQUEST']._serialized_end=4874
  _globals['_TRACKEDITEMHISTORY']._serialized_start=4876
  _globals['_TRACKEDITEMHISTORY']._serialized_end=4963
  _globals['_TRACKEDITEM']._serialized_start=4965
  _globals['_TRACKEDITEM']._serialized_end=5035
  _globals['_TRACKEDITEMSRESPONSE']._serialized_start=5037
  _globals['_TRACKEDITEMSRESPONSE']._serialized_end=5103
  _globals['_PARTICIPATIONRESPONSE']._serialized_start=5105
  _globals['_PARTICIPATIONRESPONSE']._serialized_end=5155
  _globals['_AIMBOTSERVICE']._serialized_start=5237
  _globals['_AIMBOTSERVICE']._serialized_end=8700
# @@protoc_insertion_point(module_scope)
