# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.core.controls.exterminator.controllers.aimbot.process import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in core/controls/exterminator/controllers/aimbot/process/aimbot_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AimbotServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/aimbot.AimbotService/Ping',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PingRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PongReply.FromString,
                _registered_method=True)
        self.GetBooted = channel.unary_unary(
                '/aimbot.AimbotService/GetBooted',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BootedReply.FromString,
                _registered_method=True)
        self.ArmLasers = channel.unary_unary(
                '/aimbot.AimbotService/ArmLasers',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.DisarmLasers = channel.unary_unary(
                '/aimbot.AimbotService/DisarmLasers',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetAimbotState = channel.unary_unary(
                '/aimbot.AimbotService/GetAimbotState',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AimbotState.FromString,
                _registered_method=True)
        self.SetTargetingState = channel.unary_unary(
                '/aimbot.AimbotService/SetTargetingState',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetingState.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartActuationTask = channel.unary_unary(
                '/aimbot.AimbotService/StartActuationTask',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ActuationTaskRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.CancelActuationTask = channel.unary_unary(
                '/aimbot.AimbotService/CancelActuationTask',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.LensSet = channel.unary_unary(
                '/aimbot.AimbotService/LensSet',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetReply.FromString,
                _registered_method=True)
        self.LensGet = channel.unary_unary(
                '/aimbot.AimbotService/LensGet',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetReply.FromString,
                _registered_method=True)
        self.LensGetAll = channel.unary_unary(
                '/aimbot.AimbotService/LensGetAll',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetAllReply.FromString,
                _registered_method=True)
        self.LensAutoFocus = channel.unary_unary(
                '/aimbot.AimbotService/LensAutoFocus',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusReply.FromString,
                _registered_method=True)
        self.StopLensAutoFocus = channel.unary_unary(
                '/aimbot.AimbotService/StopLensAutoFocus',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.StopLensAutoFocusRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.LaserArm = channel.unary_unary(
                '/aimbot.AimbotService/LaserArm',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmReply.FromString,
                _registered_method=True)
        self.LaserSet = channel.unary_unary(
                '/aimbot.AimbotService/LaserSet',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetReply.FromString,
                _registered_method=True)
        self.LaserEnable = channel.unary_unary(
                '/aimbot.AimbotService/LaserEnable',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableReply.FromString,
                _registered_method=True)
        self.LaserFire = channel.unary_unary(
                '/aimbot.AimbotService/LaserFire',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireReply.FromString,
                _registered_method=True)
        self.ResetLaserMetrics = channel.unary_unary(
                '/aimbot.AimbotService/ResetLaserMetrics',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.FixLaserMetrics = channel.unary_unary(
                '/aimbot.AimbotService/FixLaserMetrics',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.FixLaserMetricsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.BurnIdividualImage = channel.unary_unary(
                '/aimbot.AimbotService/BurnIdividualImage',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BurnIdividualImagesRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ServoGoTo = channel.unary_unary(
                '/aimbot.AimbotService/ServoGoTo',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToReply.FromString,
                _registered_method=True)
        self.ServoGetPosVel = channel.unary_unary(
                '/aimbot.AimbotService/ServoGetPosVel',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelReply.FromString,
                _registered_method=True)
        self.ServoGetLimits = channel.unary_unary(
                '/aimbot.AimbotService/ServoGetLimits',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsReply.FromString,
                _registered_method=True)
        self.TuningParamsUpdate = channel.unary_unary(
                '/aimbot.AimbotService/TuningParamsUpdate',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateReply.FromString,
                _registered_method=True)
        self.TuningParamsGet = channel.unary_unary(
                '/aimbot.AimbotService/TuningParamsGet',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetReply.FromString,
                _registered_method=True)
        self.GetLoadEstimate = channel.unary_unary(
                '/aimbot.AimbotService/GetLoadEstimate',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateReply.FromString,
                _registered_method=True)
        self.GetDiagnostic = channel.unary_unary(
                '/aimbot.AimbotService/GetDiagnostic',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticReply.FromString,
                _registered_method=True)
        self.ResetDevices = channel.unary_unary(
                '/aimbot.AimbotService/ResetDevices',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesReply.FromString,
                _registered_method=True)
        self.ResetScanner = channel.unary_unary(
                '/aimbot.AimbotService/ResetScanner',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerReply.FromString,
                _registered_method=True)
        self.StartAutoCalibrateCrosshair = channel.unary_unary(
                '/aimbot.AimbotService/StartAutoCalibrateCrosshair',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartAutoCalibrateAllCrosshairs = channel.unary_unary(
                '/aimbot.AimbotService/StartAutoCalibrateAllCrosshairs',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopAutoCalibrate = channel.unary_unary(
                '/aimbot.AimbotService/StopAutoCalibrate',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.SetCrosshairPosition = channel.unary_unary(
                '/aimbot.AimbotService/SetCrosshairPosition',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.MoveScanner = channel.unary_unary(
                '/aimbot.AimbotService/MoveScanner',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetAutoCalibrationProgress = channel.unary_unary(
                '/aimbot.AimbotService/GetAutoCalibrationProgress',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AutoXHairCalibrationProgress.FromString,
                _registered_method=True)
        self.GetScannerStatus = channel.unary_unary(
                '/aimbot.AimbotService/GetScannerStatus',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerStatusReply.FromString,
                _registered_method=True)
        self.GetTargetVelocity = channel.unary_unary(
                '/aimbot.AimbotService/GetTargetVelocity',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityReply.FromString,
                _registered_method=True)
        self.GetTrackingState = channel.unary_unary(
                '/aimbot.AimbotService/GetTrackingState',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackingState.FromString,
                _registered_method=True)
        self.GetBedtopHeightProfile = channel.unary_unary(
                '/aimbot.AimbotService/GetBedtopHeightProfile',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackerBedtopHeightProfile.FromString,
                _registered_method=True)
        self.GetDimensions = channel.unary_unary(
                '/aimbot.AimbotService/GetDimensions',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDimensionsResponse.FromString,
                _registered_method=True)
        self.GetTargetCamSN = channel.unary_unary(
                '/aimbot.AimbotService/GetTargetCamSN',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNResponse.FromString,
                _registered_method=True)
        self.ReloadThinningConf = channel.unary_unary(
                '/aimbot.AimbotService/ReloadThinningConf',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadThinningConfRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ReloadAlmanacConf = channel.unary_unary(
                '/aimbot.AimbotService/ReloadAlmanacConf',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadAlmanacConfRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ReloadDiscriminatorConf = channel.unary_unary(
                '/aimbot.AimbotService/ReloadDiscriminatorConf',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadDiscriminatorConfRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ReloadModelinatorConf = channel.unary_unary(
                '/aimbot.AimbotService/ReloadModelinatorConf',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadModelinatorConfRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ReloadTVEProfile = channel.unary_unary(
                '/aimbot.AimbotService/ReloadTVEProfile',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadTVEProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetDistanceTrackedItems = channel.unary_unary(
                '/aimbot.AimbotService/GetDistanceTrackedItems',
                request_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsResponse.FromString,
                _registered_method=True)
        self.GetParticipation = channel.unary_unary(
                '/aimbot.AimbotService/GetParticipation',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ParticipationResponse.FromString,
                _registered_method=True)


class AimbotServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBooted(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ArmLasers(self, request, context):
        """Aimbot
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisarmLasers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAimbotState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTargetingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartActuationTask(self, request, context):
        """Actuation Tasks
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelActuationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LensSet(self, request, context):
        """Lens
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LensGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LensGetAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LensAutoFocus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopLensAutoFocus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LaserArm(self, request, context):
        """Laser
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LaserSet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LaserEnable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LaserFire(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetLaserMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FixLaserMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BurnIdividualImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ServoGoTo(self, request, context):
        """Servo
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ServoGetPosVel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ServoGetLimits(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TuningParamsUpdate(self, request, context):
        """Tuning Params
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TuningParamsGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLoadEstimate(self, request, context):
        """Load Estimate
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDiagnostic(self, request, context):
        """Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetDevices(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetScanner(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartAutoCalibrateCrosshair(self, request, context):
        """Crosshair
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartAutoCalibrateAllCrosshairs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopAutoCalibrate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCrosshairPosition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoveScanner(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAutoCalibrationProgress(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetScannerStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTargetVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTrackingState(self, request, context):
        """Tracking
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBedtopHeightProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDimensions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTargetCamSN(self, request, context):
        """TargetCam
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadThinningConf(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadAlmanacConf(self, request, context):
        """Almanac
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadDiscriminatorConf(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadModelinatorConf(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadTVEProfile(self, request, context):
        """Target Velocity Estimator
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDistanceTrackedItems(self, request, context):
        """Tracked Items
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetParticipation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AimbotServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PingRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PongReply.SerializeToString,
            ),
            'GetBooted': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBooted,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BootedReply.SerializeToString,
            ),
            'ArmLasers': grpc.unary_unary_rpc_method_handler(
                    servicer.ArmLasers,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'DisarmLasers': grpc.unary_unary_rpc_method_handler(
                    servicer.DisarmLasers,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetAimbotState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAimbotState,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AimbotState.SerializeToString,
            ),
            'SetTargetingState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTargetingState,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetingState.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartActuationTask,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ActuationTaskRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'CancelActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelActuationTask,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'LensSet': grpc.unary_unary_rpc_method_handler(
                    servicer.LensSet,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetReply.SerializeToString,
            ),
            'LensGet': grpc.unary_unary_rpc_method_handler(
                    servicer.LensGet,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetReply.SerializeToString,
            ),
            'LensGetAll': grpc.unary_unary_rpc_method_handler(
                    servicer.LensGetAll,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetAllReply.SerializeToString,
            ),
            'LensAutoFocus': grpc.unary_unary_rpc_method_handler(
                    servicer.LensAutoFocus,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusReply.SerializeToString,
            ),
            'StopLensAutoFocus': grpc.unary_unary_rpc_method_handler(
                    servicer.StopLensAutoFocus,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.StopLensAutoFocusRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'LaserArm': grpc.unary_unary_rpc_method_handler(
                    servicer.LaserArm,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmReply.SerializeToString,
            ),
            'LaserSet': grpc.unary_unary_rpc_method_handler(
                    servicer.LaserSet,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetReply.SerializeToString,
            ),
            'LaserEnable': grpc.unary_unary_rpc_method_handler(
                    servicer.LaserEnable,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableReply.SerializeToString,
            ),
            'LaserFire': grpc.unary_unary_rpc_method_handler(
                    servicer.LaserFire,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireReply.SerializeToString,
            ),
            'ResetLaserMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetLaserMetrics,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'FixLaserMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.FixLaserMetrics,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.FixLaserMetricsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'BurnIdividualImage': grpc.unary_unary_rpc_method_handler(
                    servicer.BurnIdividualImage,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BurnIdividualImagesRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ServoGoTo': grpc.unary_unary_rpc_method_handler(
                    servicer.ServoGoTo,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToReply.SerializeToString,
            ),
            'ServoGetPosVel': grpc.unary_unary_rpc_method_handler(
                    servicer.ServoGetPosVel,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelReply.SerializeToString,
            ),
            'ServoGetLimits': grpc.unary_unary_rpc_method_handler(
                    servicer.ServoGetLimits,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsReply.SerializeToString,
            ),
            'TuningParamsUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.TuningParamsUpdate,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateReply.SerializeToString,
            ),
            'TuningParamsGet': grpc.unary_unary_rpc_method_handler(
                    servicer.TuningParamsGet,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetReply.SerializeToString,
            ),
            'GetLoadEstimate': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLoadEstimate,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateReply.SerializeToString,
            ),
            'GetDiagnostic': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDiagnostic,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticReply.SerializeToString,
            ),
            'ResetDevices': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetDevices,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesReply.SerializeToString,
            ),
            'ResetScanner': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetScanner,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerReply.SerializeToString,
            ),
            'StartAutoCalibrateCrosshair': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateCrosshair,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartAutoCalibrateAllCrosshairs': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateAllCrosshairs,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopAutoCalibrate': grpc.unary_unary_rpc_method_handler(
                    servicer.StopAutoCalibrate,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetCrosshairPosition': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCrosshairPosition,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'MoveScanner': grpc.unary_unary_rpc_method_handler(
                    servicer.MoveScanner,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetAutoCalibrationProgress': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAutoCalibrationProgress,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AutoXHairCalibrationProgress.SerializeToString,
            ),
            'GetScannerStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetScannerStatus,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerStatusReply.SerializeToString,
            ),
            'GetTargetVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTargetVelocity,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityReply.SerializeToString,
            ),
            'GetTrackingState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTrackingState,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackingState.SerializeToString,
            ),
            'GetBedtopHeightProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBedtopHeightProfile,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackerBedtopHeightProfile.SerializeToString,
            ),
            'GetDimensions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDimensions,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDimensionsResponse.SerializeToString,
            ),
            'GetTargetCamSN': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTargetCamSN,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNResponse.SerializeToString,
            ),
            'ReloadThinningConf': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadThinningConf,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadThinningConfRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ReloadAlmanacConf': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadAlmanacConf,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadAlmanacConfRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ReloadDiscriminatorConf': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadDiscriminatorConf,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadDiscriminatorConfRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ReloadModelinatorConf': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadModelinatorConf,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadModelinatorConfRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ReloadTVEProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadTVEProfile,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadTVEProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetDistanceTrackedItems': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDistanceTrackedItems,
                    request_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsResponse.SerializeToString,
            ),
            'GetParticipation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetParticipation,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ParticipationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'aimbot.AimbotService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('aimbot.AimbotService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AimbotService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/Ping',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PingRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.PongReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBooted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetBooted',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BootedReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ArmLasers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ArmLasers',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DisarmLasers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/DisarmLasers',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAimbotState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetAimbotState',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AimbotState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetTargetingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/SetTargetingState',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetingState.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/StartActuationTask',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ActuationTaskRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/CancelActuationTask',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LensSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LensSet',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensSetReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LensGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LensGet',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LensGetAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LensGetAll',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensGetAllReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LensAutoFocus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LensAutoFocus',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LensAutoFocusReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopLensAutoFocus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/StopLensAutoFocus',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.StopLensAutoFocusRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LaserArm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LaserArm',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserArmReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LaserSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LaserSet',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserSetReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LaserEnable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LaserEnable',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserEnableReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LaserFire(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/LaserFire',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.LaserFireReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResetLaserMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ResetLaserMetrics',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FixLaserMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/FixLaserMetrics',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.FixLaserMetricsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BurnIdividualImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/BurnIdividualImage',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.BurnIdividualImagesRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ServoGoTo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ServoGoTo',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGoToReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ServoGetPosVel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ServoGetPosVel',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetPosVelReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ServoGetLimits(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ServoGetLimits',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ServoGetLimitsReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TuningParamsUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/TuningParamsUpdate',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsUpdateReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TuningParamsGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/TuningParamsGet',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TuningParamsGetReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLoadEstimate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetLoadEstimate',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetLoadEstimateReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDiagnostic(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetDiagnostic',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDiagnosticReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResetDevices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ResetDevices',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetDevicesReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResetScanner(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ResetScanner',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ResetScannerReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartAutoCalibrateCrosshair(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/StartAutoCalibrateCrosshair',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerDescriptor.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartAutoCalibrateAllCrosshairs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/StartAutoCalibrateAllCrosshairs',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopAutoCalibrate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/StopAutoCalibrate',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCrosshairPosition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/SetCrosshairPosition',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MoveScanner(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/MoveScanner',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerTargetPosition.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAutoCalibrationProgress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetAutoCalibrationProgress',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.AutoXHairCalibrationProgress.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetScannerStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetScannerStatus',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ScannerStatusReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTargetVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetTargetVelocity',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TargetVelocityReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTrackingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetTrackingState',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackingState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBedtopHeightProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetBedtopHeightProfile',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackerBedtopHeightProfile.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDimensions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetDimensions',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetDimensionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTargetCamSN(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetTargetCamSN',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.GetTargetCamSNResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReloadThinningConf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ReloadThinningConf',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadThinningConfRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReloadAlmanacConf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ReloadAlmanacConf',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadAlmanacConfRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReloadDiscriminatorConf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ReloadDiscriminatorConf',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadDiscriminatorConfRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReloadModelinatorConf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ReloadModelinatorConf',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadModelinatorConfRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReloadTVEProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/ReloadTVEProfile',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ReloadTVEProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDistanceTrackedItems(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetDistanceTrackedItems',
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.TrackedItemsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetParticipation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aimbot.AimbotService/GetParticipation',
            util_dot_util__pb2.Empty.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2.ParticipationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
