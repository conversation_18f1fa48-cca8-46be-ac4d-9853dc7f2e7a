# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: cv/cv.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'cv/cv.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0b\x63v/cv.proto\x12\x10\x63\x61rbon.aimbot.cv*\x86\x01\n\x10P2PCaptureReason\x12\x19\n\x15P2PCaptureReason_MISS\x10\x00\x12\x1c\n\x18P2PCaptureReason_SUCCESS\x10\x01\x12\x19\n\x15P2PCaptureReason_JUMP\x10\x02\x12\x1e\n\x1aP2PCaptureReason_First_P2P\x10\x03\x42<Z:github.com/carbonrobotics/protos/golang/generated/proto/cvb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'cv.cv_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z:github.com/carbonrobotics/protos/golang/generated/proto/cv'
  _globals['_P2PCAPTUREREASON']._serialized_start=34
  _globals['_P2PCAPTUREREASON']._serialized_end=168
# @@protoc_insertion_point(module_scope)
