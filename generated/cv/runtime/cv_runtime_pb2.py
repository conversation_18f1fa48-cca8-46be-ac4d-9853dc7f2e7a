# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: cv/runtime/cv_runtime.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'cv/runtime/cv_runtime.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.camera import camera_pb2 as lib_dot_common_dot_camera_dot_camera__pb2
from generated.cv import cv_pb2 as cv_dot_cv__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x63v/runtime/cv_runtime.proto\x12\x10\x63v.runtime.proto\x1a\x1elib/common/camera/camera.proto\x1a\x0b\x63v/cv.proto\x1a!weed_tracking/weed_tracking.proto\"B\n\x0fMaskExpressions\x12/\n\x05\x65xprs\x18\x01 \x03(\x0b\x32 .cv.runtime.proto.MaskExpression\"I\n\x10TargetSafetyZone\x12\n\n\x02up\x18\x01 \x01(\x02\x12\x0c\n\x04\x64own\x18\x02 \x01(\x02\x12\x0c\n\x04left\x18\x03 \x01(\x02\x12\r\n\x05right\x18\x04 \x01(\x02\"t\n\nP2PContext\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\x12\x1c\n\x14predict_timestamp_ms\x18\x02 \x01(\x03\x12\x17\n\x0fpredict_coord_x\x18\x03 \x01(\x02\x12\x17\n\x0fpredict_coord_y\x18\x04 \x01(\x02\"\xf1\x01\n\x14SetP2PContextRequest\x12\x15\n\rtarget_cam_id\x18\x01 \x01(\t\x12\x35\n\x0fprimary_context\x18\x02 \x01(\x0b\x32\x1c.cv.runtime.proto.P2PContext\x12<\n\x11secondary_context\x18\x03 \x01(\x0b\x32\x1c.cv.runtime.proto.P2PContextH\x00\x88\x01\x01\x12\x37\n\x0bsafety_zone\x18\x04 \x01(\x0b\x32\".cv.runtime.proto.TargetSafetyZoneB\x14\n\x12_secondary_context\"\x17\n\x15SetP2PContextResponse\"Y\n\x14GetBufferNameRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x31\n\x08use_case\x18\x02 \x01(\x0e\x32\x1f.cv.runtime.proto.BufferUseCase\",\n\x15GetBufferNameResponse\x12\x13\n\x0b\x62uffer_name\x18\x01 \x01(\t\",\n\x1aGetCameraDimensionsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"O\n\x1bGetCameraDimensionsResponse\x12\r\n\x05width\x18\x01 \x01(\x03\x12\x0e\n\x06height\x18\x02 \x01(\x03\x12\x11\n\ttranspose\x18\x03 \x01(\x08\"\xb7\x01\n\x1aStartP2PDataCaptureRequest\x12\x15\n\rtarget_cam_id\x18\x01 \x01(\t\x12\x19\n\x11\x63\x61pture_miss_rate\x18\x02 \x01(\x02\x12\x1c\n\x14\x63\x61pture_success_rate\x18\x03 \x01(\x02\x12\x17\n\x0f\x63\x61pture_enabled\x18\x04 \x01(\x08\x12\x14\n\x0c\x63\x61pture_path\x18\x05 \x01(\t\x12\x1a\n\x12\x61\x66ter_timestamp_ms\x18\x06 \x01(\x03\"\x1d\n\x1bStartP2PDataCaptureResponse\"2\n\x19StopP2PDataCaptureRequest\x12\x15\n\rtarget_cam_id\x18\x01 \x01(\t\"\x1c\n\x1aStopP2PDataCaptureResponse\"=\n\x16PointDetectionCategory\x12\x11\n\tthreshold\x18\x01 \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\t\"^\n\x1dSegmentationDetectionCategory\x12\x11\n\tthreshold\x18\x01 \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\t\x12\x18\n\x10safety_radius_in\x18\x03 \x01(\x02\"\xf4\x01\n DeepweedDetectionCriteriaSetting\x12\x42\n\x10point_categories\x18\x01 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12\x1c\n\x14weed_point_threshold\x18\x02 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x03 \x01(\x02\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"\xf7\x01\n#SetDeepweedDetectionCriteriaRequest\x12\x1c\n\x14weed_point_threshold\x18\x01 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x02 \x01(\x02\x12\x42\n\x10point_categories\x18\x03 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"&\n$SetDeepweedDetectionCriteriaResponse\"%\n#GetDeepweedDetectionCriteriaRequest\"\xf8\x01\n$GetDeepweedDetectionCriteriaResponse\x12\x1c\n\x14weed_point_threshold\x18\x01 \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\x02 \x01(\x02\x12\x42\n\x10point_categories\x18\x03 \x03(\x0b\x32(.cv.runtime.proto.PointDetectionCategory\x12P\n\x17segmentation_categories\x18\x04 \x03(\x0b\x32/.cv.runtime.proto.SegmentationDetectionCategory\"G\n%GetDeepweedSupportedCategoriesRequest\x12\x13\n\x06\x63\x61m_id\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\t\n\x07_cam_id\"c\n&GetDeepweedSupportedCategoriesResponse\x12\x1f\n\x17segmentation_categories\x18\x01 \x03(\t\x12\x18\n\x10point_categories\x18\x02 \x03(\t\"\x96\x01\n SetDeeplabMaskExpressionsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x31\n\x08use_case\x18\x02 \x01(\x0e\x32\x1f.cv.runtime.proto.BufferUseCase\x12/\n\x05\x65xprs\x18\x03 \x03(\x0b\x32 .cv.runtime.proto.MaskExpression\"#\n!SetDeeplabMaskExpressionsResponse\"3\n!GetDeepweedIndexToCategoryRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\xdd\x04\n\"GetDeepweedIndexToCategoryResponse\x12x\n\x1cweed_point_index_to_category\x18\x01 \x03(\x0b\x32R.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry\x12x\n\x1c\x63rop_point_index_to_category\x18\x02 \x03(\x0b\x32R.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry\x12}\n\x1eintersection_index_to_category\x18\x03 \x03(\x0b\x32U.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry\x1a?\n\x1dWeedPointIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a?\n\x1d\x43ropPointIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x42\n IntersectionIndexToCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"4\n\x1aGetPredictCamMatrixRequest\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\"D\n*GetPredictCamDistortionCoefficientsRequest\x12\x16\n\x0epredict_cam_id\x18\x01 \x01(\t\"\x87\x05\n\x18SetCameraSettingsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x18\n\x0b\x65xposure_us\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05gamma\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x14\n\x07gain_db\x18\x04 \x01(\x02H\x02\x88\x01\x01\x12\x46\n\x13light_source_preset\x18\x05 \x01(\x0e\x32$.lib.common.camera.LightSourcePresetH\x03\x88\x01\x01\x12\x19\n\x0cwb_ratio_red\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x1b\n\x0ewb_ratio_green\x18\x07 \x01(\x02H\x05\x88\x01\x01\x12\x1a\n\rwb_ratio_blue\x18\x08 \x01(\x02H\x06\x88\x01\x01\x12\x19\n\x0croi_offset_x\x18\t \x01(\x03H\x07\x88\x01\x01\x12\x19\n\x0croi_offset_y\x18\n \x01(\x03H\x08\x88\x01\x01\x12\x13\n\x06mirror\x18\x0b \x01(\x08H\t\x88\x01\x01\x12\x11\n\x04\x66lip\x18\x0c \x01(\x08H\n\x88\x01\x01\x12\x15\n\x08strobing\x18\r \x01(\x08H\x0b\x88\x01\x01\x12\x10\n\x03ptp\x18\x0e \x01(\x08H\x0c\x88\x01\x01\x12\x1e\n\x11\x61uto_whitebalance\x18\x0f \x01(\x08H\r\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x08\n\x06_gammaB\n\n\x08_gain_dbB\x16\n\x14_light_source_presetB\x0f\n\r_wb_ratio_redB\x11\n\x0f_wb_ratio_greenB\x10\n\x0e_wb_ratio_blueB\x0f\n\r_roi_offset_xB\x0f\n\r_roi_offset_yB\t\n\x07_mirrorB\x07\n\x05_flipB\x0b\n\t_strobingB\x06\n\x04_ptpB\x14\n\x12_auto_whitebalance\",\n\x19SetCameraSettingsResponse\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\"=\n\x1aSetAutoWhitebalanceRequest\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x0f\n\x07\x63\x61m_ids\x18\x02 \x03(\t\"\x1d\n\x1bSetAutoWhitebalanceResponse\"+\n\x18GetCameraSettingsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\"\xb5\x05\n\x16\x43\x61meraSettingsResponse\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x18\n\x0b\x65xposure_us\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05gamma\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x14\n\x07gain_db\x18\x04 \x01(\x02H\x02\x88\x01\x01\x12\x46\n\x13light_source_preset\x18\x05 \x01(\x0e\x32$.lib.common.camera.LightSourcePresetH\x03\x88\x01\x01\x12\x19\n\x0cwb_ratio_red\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x1b\n\x0ewb_ratio_green\x18\x07 \x01(\x02H\x05\x88\x01\x01\x12\x1a\n\rwb_ratio_blue\x18\x08 \x01(\x02H\x06\x88\x01\x01\x12\x16\n\troi_width\x18\t \x01(\x03H\x07\x88\x01\x01\x12\x17\n\nroi_height\x18\n \x01(\x03H\x08\x88\x01\x01\x12\x19\n\x0croi_offset_x\x18\x0b \x01(\x03H\t\x88\x01\x01\x12\x19\n\x0croi_offset_y\x18\x0c \x01(\x03H\n\x88\x01\x01\x12\x13\n\x06gpu_id\x18\r \x01(\x03H\x0b\x88\x01\x01\x12\x0e\n\x06mirror\x18\x0e \x01(\x08\x12\x0c\n\x04\x66lip\x18\x0f \x01(\x08\x12\x10\n\x08strobing\x18\x10 \x01(\x08\x12\x0b\n\x03ptp\x18\x11 \x01(\x08\x12\x1e\n\x11\x61uto_whitebalance\x18\x12 \x01(\x08H\x0c\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x08\n\x06_gammaB\n\n\x08_gain_dbB\x16\n\x14_light_source_presetB\x0f\n\r_wb_ratio_redB\x11\n\x0f_wb_ratio_greenB\x10\n\x0e_wb_ratio_blueB\x0c\n\n_roi_widthB\r\n\x0b_roi_heightB\x0f\n\r_roi_offset_xB\x0f\n\r_roi_offset_yB\t\n\x07_gpu_idB\x14\n\x12_auto_whitebalance\"g\n\x19GetCameraSettingsResponse\x12J\n\x18\x63\x61mera_settings_response\x18\x01 \x03(\x0b\x32(.cv.runtime.proto.CameraSettingsResponse\"\x91\x01\n\x1dStartBurstRecordFramesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64uration_ms\x18\x02 \x01(\x03\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x19\n\x11\x64ownsample_factor\x18\x05 \x01(\x05\" \n\x1eStartBurstRecordFramesResponse\"p\n\x1cStopBurstRecordFramesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12$\n\x17last_frame_timestamp_ms\x18\x02 \x01(\x03H\x00\x88\x01\x01\x42\x1a\n\x18_last_frame_timestamp_ms\"\x1f\n\x1dStopBurstRecordFramesResponse\"\x9a\x01\n\x0eP2POutputProto\x12\x0f\n\x07matched\x18\x01 \x01(\x08\x12\x16\n\x0etarget_coord_x\x18\x02 \x01(\x02\x12\x16\n\x0etarget_coord_y\x18\x03 \x01(\x02\x12\x1b\n\x13target_timestamp_ms\x18\x04 \x01(\x03\x12\x1c\n\x14predict_timestamp_ms\x18\x05 \x01(\x03\x12\x0c\n\x04safe\x18\x06 \x01(\x08\"X\n\x17GetNextP2POutputRequest\x12\x13\n\x0b\x62uffer_name\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x12\n\ntimeout_ms\x18\x03 \x01(\x03\">\n\x14GetConnectorsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x15\n\rconnector_ids\x18\x02 \x03(\t\"f\n\x11\x43onnectorResponse\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0c\x63onnector_id\x18\x02 \x01(\t\x12\x12\n\nis_enabled\x18\x03 \x01(\x08\x12\x17\n\x0freduction_ratio\x18\x04 \x01(\x03\"X\n\x15GetConnectorsResponse\x12?\n\x12\x63onnector_response\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.ConnectorResponse\"\x98\x01\n\x14SetConnectorsRequest\x12\x0f\n\x07\x63\x61m_ids\x18\x01 \x03(\t\x12\x15\n\rconnector_ids\x18\x02 \x03(\t\x12\x17\n\nis_enabled\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0freduction_ratio\x18\x04 \x01(\x03H\x01\x88\x01\x01\x42\r\n\x0b_is_enabledB\x12\n\x10_reduction_ratio\"\x17\n\x15SetConnectorsResponse\"\xfd\x01\n\nNodeTiming\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08\x66ps_mean\x18\x02 \x01(\x02\x12\x11\n\tfps_99pct\x18\x03 \x01(\x02\x12\x17\n\x0flatency_ms_mean\x18\x04 \x01(\x02\x12\x18\n\x10latency_ms_99pct\x18\x05 \x01(\x02\x12\r\n\x05state\x18\x06 \x01(\t\x12\x45\n\rstate_timings\x18\x07 \x03(\x0b\x32..cv.runtime.proto.NodeTiming.StateTimingsEntry\x1a\x33\n\x11StateTimingsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x12\n\x10GetTimingRequest\"F\n\x11GetTimingResponse\x12\x31\n\x0bnode_timing\x18\x01 \x03(\x0b\x32\x1c.cv.runtime.proto.NodeTiming\"K\n\x0ePredictRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nfile_paths\x18\x02 \x03(\t\x12\x15\n\rtimestamps_ms\x18\x03 \x03(\x03\"\x11\n\x0fPredictResponse\"P\n\x13LoadAndQueueRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nfile_paths\x18\x02 \x03(\t\x12\x15\n\rtimestamps_ms\x18\x03 \x03(\x03\"\x16\n\x14LoadAndQueueResponse\"4\n\x0fSetImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\"\x12\n\x10SetImageResponse\"#\n\x11UnsetImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\x14\n\x12UnsetImageResponse\"\x16\n\x14GetModelPathsRequest\"w\n\x15GetModelPathsResponse\x12\x10\n\x03p2p\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08\x64\x65\x65pweed\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07\x66urrows\x18\x03 \x01(\tH\x02\x88\x01\x01\x42\x06\n\x04_p2pB\x0b\n\t_deepweedB\n\n\x08_furrows\"\x1e\n\x1cGetCameraTemperaturesRequest\"8\n\x11\x43\x61meraTemperature\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x13\n\x0btemperature\x18\x02 \x01(\x01\"Y\n\x1dGetCameraTemperaturesResponse\x12\x38\n\x0btemperature\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.CameraTemperature\"\x82\x01\n\x06GeoLLA\x12\x10\n\x03lat\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x10\n\x03lng\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x10\n\x03\x61lt\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x19\n\x0ctimestamp_ms\x18\x04 \x01(\x03H\x03\x88\x01\x01\x42\x06\n\x04_latB\x06\n\x04_lngB\x06\n\x04_altB\x0f\n\r_timestamp_ms\"w\n\x07GeoECEF\x12\x0e\n\x01x\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x0e\n\x01y\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x0e\n\x01z\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x19\n\x0ctimestamp_ms\x18\x04 \x01(\x03H\x03\x88\x01\x01\x42\x04\n\x02_xB\x04\n\x02_yB\x04\n\x02_zB\x0f\n\r_timestamp_ms\"g\n\x15SetGPSLocationRequest\x12%\n\x03lla\x18\x01 \x01(\x0b\x32\x18.cv.runtime.proto.GeoLLA\x12\'\n\x04\x65\x63\x65\x66\x18\x02 \x01(\x0b\x32\x19.cv.runtime.proto.GeoECEF\"\x18\n\x16SetGPSLocationResponse\"=\n\x19SetImplementStatusRequest\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\"\x1c\n\x1aSetImplementStatusResponse\"\xd4\x03\n\x0eMaskExpression\x12\x38\n\x08\x63\x61tegory\x18\x01 \x01(\x0b\x32$.cv.runtime.proto.CategoryExpressionH\x00\x12\x34\n\x06\x64ilate\x18\x02 \x01(\x0b\x32\".cv.runtime.proto.DilateExpressionH\x00\x12\x32\n\x05\x65rode\x18\x03 \x01(\x0b\x32!.cv.runtime.proto.ErodeExpressionH\x00\x12\x37\n\nunion_expr\x18\x04 \x01(\x0b\x32!.cv.runtime.proto.UnionExpressionH\x00\x12:\n\tintersect\x18\x05 \x01(\x0b\x32%.cv.runtime.proto.IntersectExpressionH\x00\x12\x34\n\x06negate\x18\x06 \x01(\x0b\x32\".cv.runtime.proto.NegateExpressionH\x00\x12\x30\n\x04line\x18\x07 \x01(\x0b\x32 .cv.runtime.proto.LineExpressionH\x00\x12\x39\n\tall_lines\x18\x08 \x01(\x0b\x32$.cv.runtime.proto.AllLinesExpressionH\x00\x42\x06\n\x04type\"9\n\x12\x43\x61tegoryExpression\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\x11\n\tthreshold\x18\x02 \x01(\x02\"8\n\x0eLineExpression\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\"&\n\x12\x41llLinesExpression\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\"e\n\x10\x44ilateExpression\x12.\n\x04\x65xpr\x18\x01 \x01(\x0b\x32 .cv.runtime.proto.MaskExpression\x12\x0c\n\x04size\x18\x02 \x01(\x05\x12\x13\n\x0bgranularity\x18\x03 \x01(\x05\"d\n\x0f\x45rodeExpression\x12.\n\x04\x65xpr\x18\x01 \x01(\x0b\x32 .cv.runtime.proto.MaskExpression\x12\x0c\n\x04size\x18\x02 \x01(\x05\x12\x13\n\x0bgranularity\x18\x03 \x01(\x05\"B\n\x0fUnionExpression\x12/\n\x05\x65xprs\x18\x01 \x03(\x0b\x32 .cv.runtime.proto.MaskExpression\"F\n\x13IntersectExpression\x12/\n\x05\x65xprs\x18\x01 \x03(\x0b\x32 .cv.runtime.proto.MaskExpression\"B\n\x10NegateExpression\x12.\n\x04\x65xpr\x18\x01 \x01(\x0b\x32 .cv.runtime.proto.MaskExpression\"\x8d\x01\n\x14SetImageScoreRequest\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\t\x12@\n\x13\x64\x65\x65pweed_detections\x18\x04 \x03(\x0b\x32#.cv.runtime.proto.DeepweedDetection\"\x17\n\x15SetImageScoreResponse\"*\n\x14GetScoreQueueRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"B\n\x0bScoreObject\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\t\"L\n\x15GetScoreQueueResponse\x12\x33\n\x0cscore_object\x18\x01 \x03(\x0b\x32\x1d.cv.runtime.proto.ScoreObject\"-\n\x17GetMaxImageScoreRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"7\n\x18GetMaxImageScoreResponse\x12\r\n\x05score\x18\x01 \x01(\x01\x12\x0c\n\x04type\x18\x02 \x01(\t\".\n\x18GetMaxScoredImageRequest\x12\x12\n\nscore_type\x18\x01 \x01(\t\"f\n\x18GetLatestP2PImageRequest\x12\x16\n\nscore_type\x18\x01 \x01(\tB\x02\x18\x01\x12\x32\n\x06reason\x18\x02 \x01(\x0e\x32\".carbon.aimbot.cv.P2PCaptureReason\"\'\n\x15GetLatestImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"(\n\x12\x46lushQueuesRequest\x12\x12\n\nscore_type\x18\x01 \x03(\t\"\x15\n\x13\x46lushQueuesResponse\"\x9f\x06\n\x18ImageAndMetadataResponse\x12\r\n\x05\x62ytes\x18\x01 \x01(\x0c\x12\r\n\x05width\x18\x02 \x01(\x05\x12\x0e\n\x06height\x18\x03 \x01(\x05\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\x12\r\n\x05score\x18\x05 \x01(\x01\x12\x0e\n\x06\x63\x61m_id\x18\x06 \x01(\t\x12\x1a\n\x12iso_formatted_time\x18\x07 \x01(\t\x12\x0f\n\x07lla_lat\x18\x08 \x01(\x01\x12\x0f\n\x07lla_lng\x18\t \x01(\x01\x12\x0f\n\x07lla_alt\x18\n \x01(\x01\x12\x18\n\x10lla_timestamp_ms\x18\x0b \x01(\x03\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x0c \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\r \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x0e \x01(\x01\x12\x19\n\x11\x65\x63\x65\x66_timestamp_ms\x18\x0f \x01(\x03\x12\x0b\n\x03ppi\x18\x10 \x01(\x02\x12\x17\n\nscore_type\x18\x11 \x01(\tH\x00\x88\x01\x01\x12\x12\n\nimage_type\x18\x12 \x01(\t\x12\x11\n\tmodel_url\x18\x13 \x01(\t\x12\x0c\n\x04\x63rop\x18\x14 \x01(\t\x12\x1b\n\x13weed_height_columns\x18\x15 \x03(\x01\x12\x1b\n\x13\x63rop_height_columns\x18\x16 \x03(\x01\x12\x15\n\rbbh_offset_mm\x18\x17 \x01(\x01\x12\x14\n\x0c\x66ocus_metric\x18\x18 \x01(\x01\x12\x13\n\x0b\x65xposure_us\x18\x19 \x01(\x01\x12\x1c\n\x14\x63rop_point_threshold\x18\x1a \x01(\x01\x12\x1c\n\x14weed_point_threshold\x18\x1b \x01(\x01\x12\x17\n\x0fweeding_enabled\x18\x1c \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x1d \x01(\x08\x12\x13\n\x0b\x64\x65\x65pweed_id\x18\x1e \x01(\t\x12\x0e\n\x06p2p_id\x18\x1f \x01(\t\x12\x35\n\x13\x64\x65\x65pweed_detections\x18  \x03(\x0b\x32\x18.weed_tracking.Detection\x12\x1e\n\x16segmentation_threshold\x18! \x01(\x01\x12\x1b\n\x13simulator_generated\x18\" \x01(\x08\x42\r\n\x0b_score_type\"\xda\x05\n\x1bP2PImageAndMetadataResponse\x12\x14\n\x0ctarget_bytes\x18\x01 \x01(\x0c\x12\x14\n\x0ctarget_width\x18\x02 \x01(\x05\x12\x15\n\rtarget_height\x18\x03 \x01(\x05\x12\x19\n\x11perspective_bytes\x18\x04 \x01(\x0c\x12\x19\n\x11perspective_width\x18\x05 \x01(\x05\x12\x1a\n\x12perspective_height\x18\x06 \x01(\x05\x12\x1e\n\x16\x61nnotated_target_bytes\x18\x07 \x01(\x0c\x12\x1e\n\x16\x61nnotated_target_width\x18\x08 \x01(\x05\x12\x1f\n\x17\x61nnotated_target_height\x18\t \x01(\x05\x12\x14\n\x0ctimestamp_ms\x18\n \x01(\x03\x12\r\n\x05score\x18\x0b \x01(\x01\x12\x0e\n\x06\x63\x61m_id\x18\x0c \x01(\t\x12\x1a\n\x12iso_formatted_time\x18\r \x01(\t\x12\x0f\n\x07lla_lat\x18\x0e \x01(\x01\x12\x0f\n\x07lla_lng\x18\x0f \x01(\x01\x12\x0f\n\x07lla_alt\x18\x10 \x01(\x01\x12\x18\n\x10lla_timestamp_ms\x18\x11 \x01(\x03\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x12 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\x13 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x14 \x01(\x01\x12\x19\n\x11\x65\x63\x65\x66_timestamp_ms\x18\x15 \x01(\x03\x12\x0b\n\x03ppi\x18\x16 \x01(\x02\x12\x17\n\x0fperspective_ppi\x18\x17 \x01(\x02\x12\x12\n\nimage_type\x18\x19 \x01(\t\x12\x11\n\tmodel_url\x18\x1a \x01(\t\x12\x0c\n\x04\x63rop\x18\x1b \x01(\t\x12\x14\n\x0c\x66ocus_metric\x18\x1c \x01(\x01\x12\x13\n\x0b\x65xposure_us\x18\x1d \x01(\x01\x12\x17\n\x0fweeding_enabled\x18\x1e \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x1f \x01(\x08\x12\x13\n\x0b\x64\x65\x65pweed_id\x18  \x01(\t\x12\x0e\n\x06p2p_id\x18! \x01(\t\"\x16\n\x14GetCameraInfoRequest\"\xc7\x01\n\nCameraInfo\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x17\n\nip_address\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x1a\n\rserial_number\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\r\n\x05model\x18\x04 \x01(\t\x12\r\n\x05width\x18\x05 \x01(\r\x12\x0e\n\x06height\x18\x06 \x01(\r\x12\x11\n\tconnected\x18\x07 \x01(\x08\x12\x12\n\nlink_speed\x18\x08 \x01(\x04\x42\r\n\x0b_ip_addressB\x10\n\x0e_serial_number\"J\n\x15GetCameraInfoResponse\x12\x31\n\x0b\x63\x61mera_info\x18\x01 \x03(\x0b\x32\x1c.cv.runtime.proto.CameraInfo\"\"\n GetLightweightBurstRecordRequest\"L\n!GetLightweightBurstRecordResponse\x12\x10\n\x08zip_file\x18\x01 \x01(\x0c\x12\x15\n\rmetadata_file\x18\x02 \x01(\x0c\"\x12\n\x10GetBootedRequest\"#\n\x11GetBootedResponse\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08\"\x11\n\x0fGetReadyRequest\"!\n\x10GetReadyResponse\x12\r\n\x05ready\x18\x01 \x01(\x08\"\xd5\x02\n\x11\x44\x65\x65pweedDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\r\n\x05score\x18\x04 \x01(\x02\x12]\n\x16\x64\x65tection_class_scores\x18\x05 \x03(\x0b\x32=.cv.runtime.proto.DeepweedDetection.DetectionClassScoresEntry\x12-\n\thit_class\x18\x06 \x01(\x0e\x32\x1a.cv.runtime.proto.HitClass\x12\x1a\n\x12mask_intersections\x18\x07 \x03(\t\x12\x12\n\nweed_score\x18\x08 \x01(\x02\x12\x12\n\ncrop_score\x18\t \x01(\x02\x1a;\n\x19\x44\x65tectionClassScoresEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\xef\x01\n\x0e\x44\x65\x65pweedOutput\x12\x37\n\ndetections\x18\x01 \x03(\x0b\x32#.cv.runtime.proto.DeepweedDetection\x12\x12\n\nmask_width\x18\x02 \x01(\r\x12\x13\n\x0bmask_height\x18\x03 \x01(\r\x12\x15\n\rmask_channels\x18\x04 \x01(\r\x12\x0c\n\x04mask\x18\x05 \x03(\x05\x12\x1c\n\x14mask_channel_classes\x18\x06 \x03(\t\x12\"\n\x1apredict_in_distance_buffer\x18\x07 \x01(\x08\x12\x14\n\x0ctimestamp_ms\x18\x08 \x01(\x03\"K\n#GetDeepweedOutputByTimestampRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"%\n#GetRecommendedStrobeSettingsRequest\"d\n$GetRecommendedStrobeSettingsResponse\x12\x19\n\x11target_camera_fps\x18\x01 \x01(\x02\x12!\n\x19targets_per_predict_ratio\x18\x02 \x01(\x05\"/\n\x19StartP2PBufferringRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t:\x02\x18\x01\" \n\x1aStartP2PBufferringResponse:\x02\x18\x01\"\xaa\x01\n\x18StopP2PBufferringRequest\x12\x12\n\nsave_burst\x18\x01 \x01(\x08\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x1a\n\x12start_timestamp_ms\x18\x05 \x01(\x03\x12\x18\n\x10\x65nd_timestamp_ms\x18\x06 \x01(\x03:\x02\x18\x01\"\x1f\n\x19StopP2PBufferringResponse:\x02\x18\x01\"\x92\x01\n\x11P2PCaptureRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x03\x12\x15\n\rwrite_to_disk\x18\x04 \x01(\x08\x12\x32\n\x06reason\x18\x05 \x01(\x0e\x32\".carbon.aimbot.cv.P2PCaptureReason\"\x14\n\x12P2PCaptureResponse\"\x9a\x01\n P2PBufferringBurstCaptureRequest\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\"\n\x1a\x64ont_capture_predict_image\x18\x04 \x01(\x08\x12\x1a\n\x12start_timestamp_ms\x18\x05 \x01(\x03\x12\x18\n\x10\x65nd_timestamp_ms\x18\x06 \x01(\x03\"#\n!P2PBufferringBurstCaptureResponse\"]\n\x1cGetNextDeepweedOutputRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x13\n\x0b\x62uffer_name\x18\x02 \x01(\t\x12\x12\n\ntimeout_ms\x18\x03 \x01(\x03\"M\n\x18SetTargetingStateRequest\x12\x17\n\x0fweeding_enabled\x18\x01 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x02 \x01(\x08\"\x1b\n\x19SetTargetingStateResponse*A\n\rBufferUseCase\x12\x07\n\x03P2P\x10\x00\x12\x0f\n\x0bOpticalFlow\x10\x01\x12\x0b\n\x07Predict\x10\x02\x12\t\n\x05\x44rive\x10\x03*\xa2\x02\n\x0e\x44\x65tectionClass\x12\x0c\n\x08ORGANICS\x10\x00\x12\x0c\n\x08\x44RIPTAPE\x10\x01\x12\x0b\n\x07SPINACH\x10\x02\x12\r\n\tSTICK_RED\x10\x03\x12\x10\n\x0cSTICK_YELLOW\x10\x04\x12\t\n\x05ONION\x10\x05\x12\t\n\x05\x43ROWN\x10\x06\x12\t\n\x05GRASS\x10\x07\x12\n\n\x06\x46URROW\x10\x08\x12\x0c\n\x08PURSLANE\x10\t\x12\r\n\tHORSETAIL\x10\n\x12\n\n\x06KOCHIA\x10\x0b\x12\x10\n\x0cLAMBSQUARTER\x10\x0c\x12\n\n\x06MALLOW\x10\r\x12\x0c\n\x08NUTSEDGE\x10\x0e\x12\x0e\n\nLEAFY_WEED\x10\x0f\x12\x12\n\x0eINFRASTRUCTURE\x10\x10\x12\x11\n\rLONDON_ROCKET\x10\x11\x12\r\n\tBABY_WEED\x10\x12*)\n\x08HitClass\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\x12\t\n\x05PLANT\x10\x02\x32\xc0&\n\x10\x43VRuntimeService\x12\x62\n\rSetP2PContext\x12&.cv.runtime.proto.SetP2PContextRequest\x1a\'.cv.runtime.proto.SetP2PContextResponse\"\x00\x12\x62\n\rGetBufferName\x12&.cv.runtime.proto.GetBufferNameRequest\x1a\'.cv.runtime.proto.GetBufferNameResponse\"\x00\x12t\n\x13GetCameraDimensions\x12,.cv.runtime.proto.GetCameraDimensionsRequest\x1a-.cv.runtime.proto.GetCameraDimensionsResponse\"\x00\x12\x62\n\rGetCameraInfo\x12&.cv.runtime.proto.GetCameraInfoRequest\x1a\'.cv.runtime.proto.GetCameraInfoResponse\"\x00\x12t\n\x13StartP2PDataCapture\x12,.cv.runtime.proto.StartP2PDataCaptureRequest\x1a-.cv.runtime.proto.StartP2PDataCaptureResponse\"\x00\x12q\n\x12StopP2PDataCapture\x12+.cv.runtime.proto.StopP2PDataCaptureRequest\x1a,.cv.runtime.proto.StopP2PDataCaptureResponse\"\x00\x12\x86\x01\n\x19SetDeeplabMaskExpressions\x12\x32.cv.runtime.proto.SetDeeplabMaskExpressionsRequest\x1a\x33.cv.runtime.proto.SetDeeplabMaskExpressionsResponse\"\x00\x12\x89\x01\n\x1aGetDeepweedIndexToCategory\x12\x33.cv.runtime.proto.GetDeepweedIndexToCategoryRequest\x1a\x34.cv.runtime.proto.GetDeepweedIndexToCategoryResponse\"\x00\x12\x8f\x01\n\x1cSetDeepweedDetectionCriteria\x12\x35.cv.runtime.proto.SetDeepweedDetectionCriteriaRequest\x1a\x36.cv.runtime.proto.SetDeepweedDetectionCriteriaResponse\"\x00\x12\x8f\x01\n\x1cGetDeepweedDetectionCriteria\x12\x35.cv.runtime.proto.GetDeepweedDetectionCriteriaRequest\x1a\x36.cv.runtime.proto.GetDeepweedDetectionCriteriaResponse\"\x00\x12\x95\x01\n\x1eGetDeepweedSupportedCategories\x12\x37.cv.runtime.proto.GetDeepweedSupportedCategoriesRequest\x1a\x38.cv.runtime.proto.GetDeepweedSupportedCategoriesResponse\"\x00\x12z\n\x15GetCameraTemperatures\x12..cv.runtime.proto.GetCameraTemperaturesRequest\x1a/.cv.runtime.proto.GetCameraTemperaturesResponse\"\x00\x12n\n\x11SetCameraSettings\x12*.cv.runtime.proto.SetCameraSettingsRequest\x1a+.cv.runtime.proto.SetCameraSettingsResponse\"\x00\x12n\n\x11GetCameraSettings\x12*.cv.runtime.proto.GetCameraSettingsRequest\x1a+.cv.runtime.proto.GetCameraSettingsResponse\"\x00\x12}\n\x16StartBurstRecordFrames\x12/.cv.runtime.proto.StartBurstRecordFramesRequest\x1a\x30.cv.runtime.proto.StartBurstRecordFramesResponse\"\x00\x12z\n\x15StopBurstRecordFrames\x12..cv.runtime.proto.StopBurstRecordFramesRequest\x1a/.cv.runtime.proto.StopBurstRecordFramesResponse\"\x00\x12\x62\n\rGetConnectors\x12&.cv.runtime.proto.GetConnectorsRequest\x1a\'.cv.runtime.proto.GetConnectorsResponse\"\x00\x12\x62\n\rSetConnectors\x12&.cv.runtime.proto.SetConnectorsRequest\x1a\'.cv.runtime.proto.SetConnectorsResponse\"\x00\x12V\n\tGetTiming\x12\".cv.runtime.proto.GetTimingRequest\x1a#.cv.runtime.proto.GetTimingResponse\"\x00\x12P\n\x07Predict\x12 .cv.runtime.proto.PredictRequest\x1a!.cv.runtime.proto.PredictResponse\"\x00\x12_\n\x0cLoadAndQueue\x12%.cv.runtime.proto.LoadAndQueueRequest\x1a&.cv.runtime.proto.LoadAndQueueResponse\"\x00\x12S\n\x08SetImage\x12!.cv.runtime.proto.SetImageRequest\x1a\".cv.runtime.proto.SetImageResponse\"\x00\x12Y\n\nUnsetImage\x12#.cv.runtime.proto.UnsetImageRequest\x1a$.cv.runtime.proto.UnsetImageResponse\"\x00\x12\x62\n\rGetModelPaths\x12&.cv.runtime.proto.GetModelPathsRequest\x1a\'.cv.runtime.proto.GetModelPathsResponse\"\x00\x12\x65\n\x0eSetGPSLocation\x12\'.cv.runtime.proto.SetGPSLocationRequest\x1a(.cv.runtime.proto.SetGPSLocationResponse\"\x00\x12q\n\x12SetImplementStatus\x12+.cv.runtime.proto.SetImplementStatusRequest\x1a,.cv.runtime.proto.SetImplementStatusResponse\"\x00\x12\x62\n\rSetImageScore\x12&.cv.runtime.proto.SetImageScoreRequest\x1a\'.cv.runtime.proto.SetImageScoreResponse\"\x00\x12\x62\n\rGetScoreQueue\x12&.cv.runtime.proto.GetScoreQueueRequest\x1a\'.cv.runtime.proto.GetScoreQueueResponse\"\x00\x12k\n\x10GetMaxImageScore\x12).cv.runtime.proto.GetMaxImageScoreRequest\x1a*.cv.runtime.proto.GetMaxImageScoreResponse\"\x00\x12m\n\x11GetMaxScoredImage\x12*.cv.runtime.proto.GetMaxScoredImageRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x12p\n\x11GetLatestP2PImage\x12*.cv.runtime.proto.GetLatestP2PImageRequest\x1a-.cv.runtime.proto.P2PImageAndMetadataResponse\"\x00\x12\\\n\x0b\x46lushQueues\x12$.cv.runtime.proto.FlushQueuesRequest\x1a%.cv.runtime.proto.FlushQueuesResponse\"\x00\x12g\n\x0eGetLatestImage\x12\'.cv.runtime.proto.GetLatestImageRequest\x1a*.cv.runtime.proto.ImageAndMetadataResponse\"\x00\x12\x86\x01\n\x19GetLightweightBurstRecord\x12\x32.cv.runtime.proto.GetLightweightBurstRecordRequest\x1a\x33.cv.runtime.proto.GetLightweightBurstRecordResponse\"\x00\x12V\n\tGetBooted\x12\".cv.runtime.proto.GetBootedRequest\x1a#.cv.runtime.proto.GetBootedResponse\"\x00\x12S\n\x08GetReady\x12!.cv.runtime.proto.GetReadyRequest\x1a\".cv.runtime.proto.GetReadyResponse\"\x00\x12y\n\x1cGetDeepweedOutputByTimestamp\x12\x35.cv.runtime.proto.GetDeepweedOutputByTimestampRequest\x1a .cv.runtime.proto.DeepweedOutput\"\x00\x12\x8f\x01\n\x1cGetRecommendedStrobeSettings\x12\x35.cv.runtime.proto.GetRecommendedStrobeSettingsRequest\x1a\x36.cv.runtime.proto.GetRecommendedStrobeSettingsResponse\"\x00\x12Y\n\nP2PCapture\x12#.cv.runtime.proto.P2PCaptureRequest\x1a$.cv.runtime.proto.P2PCaptureResponse\"\x00\x12t\n\x13SetAutoWhitebalance\x12,.cv.runtime.proto.SetAutoWhitebalanceRequest\x1a-.cv.runtime.proto.SetAutoWhitebalanceResponse\"\x00\x12k\n\x15GetNextDeepweedOutput\x12..cv.runtime.proto.GetNextDeepweedOutputRequest\x1a .cv.runtime.proto.DeepweedOutput\"\x00\x12\x61\n\x10GetNextP2POutput\x12).cv.runtime.proto.GetNextP2POutputRequest\x1a .cv.runtime.proto.P2POutputProto\"\x00\x12n\n\x11SetTargetingState\x12*.cv.runtime.proto.SetTargetingStateRequest\x1a+.cv.runtime.proto.SetTargetingStateResponse\"\x00\x12\x86\x01\n\x19P2PBufferringBurstCapture\x12\x32.cv.runtime.proto.P2PBufferringBurstCaptureRequest\x1a\x33.cv.runtime.proto.P2PBufferringBurstCaptureResponse\"\x00\x42<Z:github.com/carbonrobotics/protos/golang/generated/proto/cvb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'cv.runtime.cv_runtime_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z:github.com/carbonrobotics/protos/golang/generated/proto/cv'
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY']._loaded_options = None
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY']._serialized_options = b'8\001'
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY']._loaded_options = None
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY']._serialized_options = b'8\001'
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY']._loaded_options = None
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY']._serialized_options = b'8\001'
  _globals['_NODETIMING_STATETIMINGSENTRY']._loaded_options = None
  _globals['_NODETIMING_STATETIMINGSENTRY']._serialized_options = b'8\001'
  _globals['_GETLATESTP2PIMAGEREQUEST'].fields_by_name['score_type']._loaded_options = None
  _globals['_GETLATESTP2PIMAGEREQUEST'].fields_by_name['score_type']._serialized_options = b'\030\001'
  _globals['_DEEPWEEDDETECTION_DETECTIONCLASSSCORESENTRY']._loaded_options = None
  _globals['_DEEPWEEDDETECTION_DETECTIONCLASSSCORESENTRY']._serialized_options = b'8\001'
  _globals['_STARTP2PBUFFERRINGREQUEST']._loaded_options = None
  _globals['_STARTP2PBUFFERRINGREQUEST']._serialized_options = b'\030\001'
  _globals['_STARTP2PBUFFERRINGRESPONSE']._loaded_options = None
  _globals['_STARTP2PBUFFERRINGRESPONSE']._serialized_options = b'\030\001'
  _globals['_STOPP2PBUFFERRINGREQUEST']._loaded_options = None
  _globals['_STOPP2PBUFFERRINGREQUEST']._serialized_options = b'\030\001'
  _globals['_STOPP2PBUFFERRINGRESPONSE']._loaded_options = None
  _globals['_STOPP2PBUFFERRINGRESPONSE']._serialized_options = b'\030\001'
  _globals['_BUFFERUSECASE']._serialized_start=12979
  _globals['_BUFFERUSECASE']._serialized_end=13044
  _globals['_DETECTIONCLASS']._serialized_start=13047
  _globals['_DETECTIONCLASS']._serialized_end=13337
  _globals['_HITCLASS']._serialized_start=13339
  _globals['_HITCLASS']._serialized_end=13380
  _globals['_MASKEXPRESSIONS']._serialized_start=129
  _globals['_MASKEXPRESSIONS']._serialized_end=195
  _globals['_TARGETSAFETYZONE']._serialized_start=197
  _globals['_TARGETSAFETYZONE']._serialized_end=270
  _globals['_P2PCONTEXT']._serialized_start=272
  _globals['_P2PCONTEXT']._serialized_end=388
  _globals['_SETP2PCONTEXTREQUEST']._serialized_start=391
  _globals['_SETP2PCONTEXTREQUEST']._serialized_end=632
  _globals['_SETP2PCONTEXTRESPONSE']._serialized_start=634
  _globals['_SETP2PCONTEXTRESPONSE']._serialized_end=657
  _globals['_GETBUFFERNAMEREQUEST']._serialized_start=659
  _globals['_GETBUFFERNAMEREQUEST']._serialized_end=748
  _globals['_GETBUFFERNAMERESPONSE']._serialized_start=750
  _globals['_GETBUFFERNAMERESPONSE']._serialized_end=794
  _globals['_GETCAMERADIMENSIONSREQUEST']._serialized_start=796
  _globals['_GETCAMERADIMENSIONSREQUEST']._serialized_end=840
  _globals['_GETCAMERADIMENSIONSRESPONSE']._serialized_start=842
  _globals['_GETCAMERADIMENSIONSRESPONSE']._serialized_end=921
  _globals['_STARTP2PDATACAPTUREREQUEST']._serialized_start=924
  _globals['_STARTP2PDATACAPTUREREQUEST']._serialized_end=1107
  _globals['_STARTP2PDATACAPTURERESPONSE']._serialized_start=1109
  _globals['_STARTP2PDATACAPTURERESPONSE']._serialized_end=1138
  _globals['_STOPP2PDATACAPTUREREQUEST']._serialized_start=1140
  _globals['_STOPP2PDATACAPTUREREQUEST']._serialized_end=1190
  _globals['_STOPP2PDATACAPTURERESPONSE']._serialized_start=1192
  _globals['_STOPP2PDATACAPTURERESPONSE']._serialized_end=1220
  _globals['_POINTDETECTIONCATEGORY']._serialized_start=1222
  _globals['_POINTDETECTIONCATEGORY']._serialized_end=1283
  _globals['_SEGMENTATIONDETECTIONCATEGORY']._serialized_start=1285
  _globals['_SEGMENTATIONDETECTIONCATEGORY']._serialized_end=1379
  _globals['_DEEPWEEDDETECTIONCRITERIASETTING']._serialized_start=1382
  _globals['_DEEPWEEDDETECTIONCRITERIASETTING']._serialized_end=1626
  _globals['_SETDEEPWEEDDETECTIONCRITERIAREQUEST']._serialized_start=1629
  _globals['_SETDEEPWEEDDETECTIONCRITERIAREQUEST']._serialized_end=1876
  _globals['_SETDEEPWEEDDETECTIONCRITERIARESPONSE']._serialized_start=1878
  _globals['_SETDEEPWEEDDETECTIONCRITERIARESPONSE']._serialized_end=1916
  _globals['_GETDEEPWEEDDETECTIONCRITERIAREQUEST']._serialized_start=1918
  _globals['_GETDEEPWEEDDETECTIONCRITERIAREQUEST']._serialized_end=1955
  _globals['_GETDEEPWEEDDETECTIONCRITERIARESPONSE']._serialized_start=1958
  _globals['_GETDEEPWEEDDETECTIONCRITERIARESPONSE']._serialized_end=2206
  _globals['_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST']._serialized_start=2208
  _globals['_GETDEEPWEEDSUPPORTEDCATEGORIESREQUEST']._serialized_end=2279
  _globals['_GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE']._serialized_start=2281
  _globals['_GETDEEPWEEDSUPPORTEDCATEGORIESRESPONSE']._serialized_end=2380
  _globals['_SETDEEPLABMASKEXPRESSIONSREQUEST']._serialized_start=2383
  _globals['_SETDEEPLABMASKEXPRESSIONSREQUEST']._serialized_end=2533
  _globals['_SETDEEPLABMASKEXPRESSIONSRESPONSE']._serialized_start=2535
  _globals['_SETDEEPLABMASKEXPRESSIONSRESPONSE']._serialized_end=2570
  _globals['_GETDEEPWEEDINDEXTOCATEGORYREQUEST']._serialized_start=2572
  _globals['_GETDEEPWEEDINDEXTOCATEGORYREQUEST']._serialized_end=2623
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE']._serialized_start=2626
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE']._serialized_end=3231
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY']._serialized_start=3035
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_WEEDPOINTINDEXTOCATEGORYENTRY']._serialized_end=3098
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY']._serialized_start=3100
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_CROPPOINTINDEXTOCATEGORYENTRY']._serialized_end=3163
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY']._serialized_start=3165
  _globals['_GETDEEPWEEDINDEXTOCATEGORYRESPONSE_INTERSECTIONINDEXTOCATEGORYENTRY']._serialized_end=3231
  _globals['_GETPREDICTCAMMATRIXREQUEST']._serialized_start=3233
  _globals['_GETPREDICTCAMMATRIXREQUEST']._serialized_end=3285
  _globals['_GETPREDICTCAMDISTORTIONCOEFFICIENTSREQUEST']._serialized_start=3287
  _globals['_GETPREDICTCAMDISTORTIONCOEFFICIENTSREQUEST']._serialized_end=3355
  _globals['_SETCAMERASETTINGSREQUEST']._serialized_start=3358
  _globals['_SETCAMERASETTINGSREQUEST']._serialized_end=4005
  _globals['_SETCAMERASETTINGSRESPONSE']._serialized_start=4007
  _globals['_SETCAMERASETTINGSRESPONSE']._serialized_end=4051
  _globals['_SETAUTOWHITEBALANCEREQUEST']._serialized_start=4053
  _globals['_SETAUTOWHITEBALANCEREQUEST']._serialized_end=4114
  _globals['_SETAUTOWHITEBALANCERESPONSE']._serialized_start=4116
  _globals['_SETAUTOWHITEBALANCERESPONSE']._serialized_end=4145
  _globals['_GETCAMERASETTINGSREQUEST']._serialized_start=4147
  _globals['_GETCAMERASETTINGSREQUEST']._serialized_end=4190
  _globals['_CAMERASETTINGSRESPONSE']._serialized_start=4193
  _globals['_CAMERASETTINGSRESPONSE']._serialized_end=4886
  _globals['_GETCAMERASETTINGSRESPONSE']._serialized_start=4888
  _globals['_GETCAMERASETTINGSRESPONSE']._serialized_end=4991
  _globals['_STARTBURSTRECORDFRAMESREQUEST']._serialized_start=4994
  _globals['_STARTBURSTRECORDFRAMESREQUEST']._serialized_end=5139
  _globals['_STARTBURSTRECORDFRAMESRESPONSE']._serialized_start=5141
  _globals['_STARTBURSTRECORDFRAMESRESPONSE']._serialized_end=5173
  _globals['_STOPBURSTRECORDFRAMESREQUEST']._serialized_start=5175
  _globals['_STOPBURSTRECORDFRAMESREQUEST']._serialized_end=5287
  _globals['_STOPBURSTRECORDFRAMESRESPONSE']._serialized_start=5289
  _globals['_STOPBURSTRECORDFRAMESRESPONSE']._serialized_end=5320
  _globals['_P2POUTPUTPROTO']._serialized_start=5323
  _globals['_P2POUTPUTPROTO']._serialized_end=5477
  _globals['_GETNEXTP2POUTPUTREQUEST']._serialized_start=5479
  _globals['_GETNEXTP2POUTPUTREQUEST']._serialized_end=5567
  _globals['_GETCONNECTORSREQUEST']._serialized_start=5569
  _globals['_GETCONNECTORSREQUEST']._serialized_end=5631
  _globals['_CONNECTORRESPONSE']._serialized_start=5633
  _globals['_CONNECTORRESPONSE']._serialized_end=5735
  _globals['_GETCONNECTORSRESPONSE']._serialized_start=5737
  _globals['_GETCONNECTORSRESPONSE']._serialized_end=5825
  _globals['_SETCONNECTORSREQUEST']._serialized_start=5828
  _globals['_SETCONNECTORSREQUEST']._serialized_end=5980
  _globals['_SETCONNECTORSRESPONSE']._serialized_start=5982
  _globals['_SETCONNECTORSRESPONSE']._serialized_end=6005
  _globals['_NODETIMING']._serialized_start=6008
  _globals['_NODETIMING']._serialized_end=6261
  _globals['_NODETIMING_STATETIMINGSENTRY']._serialized_start=6210
  _globals['_NODETIMING_STATETIMINGSENTRY']._serialized_end=6261
  _globals['_GETTIMINGREQUEST']._serialized_start=6263
  _globals['_GETTIMINGREQUEST']._serialized_end=6281
  _globals['_GETTIMINGRESPONSE']._serialized_start=6283
  _globals['_GETTIMINGRESPONSE']._serialized_end=6353
  _globals['_PREDICTREQUEST']._serialized_start=6355
  _globals['_PREDICTREQUEST']._serialized_end=6430
  _globals['_PREDICTRESPONSE']._serialized_start=6432
  _globals['_PREDICTRESPONSE']._serialized_end=6449
  _globals['_LOADANDQUEUEREQUEST']._serialized_start=6451
  _globals['_LOADANDQUEUEREQUEST']._serialized_end=6531
  _globals['_LOADANDQUEUERESPONSE']._serialized_start=6533
  _globals['_LOADANDQUEUERESPONSE']._serialized_end=6555
  _globals['_SETIMAGEREQUEST']._serialized_start=6557
  _globals['_SETIMAGEREQUEST']._serialized_end=6609
  _globals['_SETIMAGERESPONSE']._serialized_start=6611
  _globals['_SETIMAGERESPONSE']._serialized_end=6629
  _globals['_UNSETIMAGEREQUEST']._serialized_start=6631
  _globals['_UNSETIMAGEREQUEST']._serialized_end=6666
  _globals['_UNSETIMAGERESPONSE']._serialized_start=6668
  _globals['_UNSETIMAGERESPONSE']._serialized_end=6688
  _globals['_GETMODELPATHSREQUEST']._serialized_start=6690
  _globals['_GETMODELPATHSREQUEST']._serialized_end=6712
  _globals['_GETMODELPATHSRESPONSE']._serialized_start=6714
  _globals['_GETMODELPATHSRESPONSE']._serialized_end=6833
  _globals['_GETCAMERATEMPERATURESREQUEST']._serialized_start=6835
  _globals['_GETCAMERATEMPERATURESREQUEST']._serialized_end=6865
  _globals['_CAMERATEMPERATURE']._serialized_start=6867
  _globals['_CAMERATEMPERATURE']._serialized_end=6923
  _globals['_GETCAMERATEMPERATURESRESPONSE']._serialized_start=6925
  _globals['_GETCAMERATEMPERATURESRESPONSE']._serialized_end=7014
  _globals['_GEOLLA']._serialized_start=7017
  _globals['_GEOLLA']._serialized_end=7147
  _globals['_GEOECEF']._serialized_start=7149
  _globals['_GEOECEF']._serialized_end=7268
  _globals['_SETGPSLOCATIONREQUEST']._serialized_start=7270
  _globals['_SETGPSLOCATIONREQUEST']._serialized_end=7373
  _globals['_SETGPSLOCATIONRESPONSE']._serialized_start=7375
  _globals['_SETGPSLOCATIONRESPONSE']._serialized_end=7399
  _globals['_SETIMPLEMENTSTATUSREQUEST']._serialized_start=7401
  _globals['_SETIMPLEMENTSTATUSREQUEST']._serialized_end=7462
  _globals['_SETIMPLEMENTSTATUSRESPONSE']._serialized_start=7464
  _globals['_SETIMPLEMENTSTATUSRESPONSE']._serialized_end=7492
  _globals['_MASKEXPRESSION']._serialized_start=7495
  _globals['_MASKEXPRESSION']._serialized_end=7963
  _globals['_CATEGORYEXPRESSION']._serialized_start=7965
  _globals['_CATEGORYEXPRESSION']._serialized_end=8022
  _globals['_LINEEXPRESSION']._serialized_start=8024
  _globals['_LINEEXPRESSION']._serialized_end=8080
  _globals['_ALLLINESEXPRESSION']._serialized_start=8082
  _globals['_ALLLINESEXPRESSION']._serialized_end=8120
  _globals['_DILATEEXPRESSION']._serialized_start=8122
  _globals['_DILATEEXPRESSION']._serialized_end=8223
  _globals['_ERODEEXPRESSION']._serialized_start=8225
  _globals['_ERODEEXPRESSION']._serialized_end=8325
  _globals['_UNIONEXPRESSION']._serialized_start=8327
  _globals['_UNIONEXPRESSION']._serialized_end=8393
  _globals['_INTERSECTEXPRESSION']._serialized_start=8395
  _globals['_INTERSECTEXPRESSION']._serialized_end=8465
  _globals['_NEGATEEXPRESSION']._serialized_start=8467
  _globals['_NEGATEEXPRESSION']._serialized_end=8533
  _globals['_SETIMAGESCOREREQUEST']._serialized_start=8536
  _globals['_SETIMAGESCOREREQUEST']._serialized_end=8677
  _globals['_SETIMAGESCORERESPONSE']._serialized_start=8679
  _globals['_SETIMAGESCORERESPONSE']._serialized_end=8702
  _globals['_GETSCOREQUEUEREQUEST']._serialized_start=8704
  _globals['_GETSCOREQUEUEREQUEST']._serialized_end=8746
  _globals['_SCOREOBJECT']._serialized_start=8748
  _globals['_SCOREOBJECT']._serialized_end=8814
  _globals['_GETSCOREQUEUERESPONSE']._serialized_start=8816
  _globals['_GETSCOREQUEUERESPONSE']._serialized_end=8892
  _globals['_GETMAXIMAGESCOREREQUEST']._serialized_start=8894
  _globals['_GETMAXIMAGESCOREREQUEST']._serialized_end=8939
  _globals['_GETMAXIMAGESCORERESPONSE']._serialized_start=8941
  _globals['_GETMAXIMAGESCORERESPONSE']._serialized_end=8996
  _globals['_GETMAXSCOREDIMAGEREQUEST']._serialized_start=8998
  _globals['_GETMAXSCOREDIMAGEREQUEST']._serialized_end=9044
  _globals['_GETLATESTP2PIMAGEREQUEST']._serialized_start=9046
  _globals['_GETLATESTP2PIMAGEREQUEST']._serialized_end=9148
  _globals['_GETLATESTIMAGEREQUEST']._serialized_start=9150
  _globals['_GETLATESTIMAGEREQUEST']._serialized_end=9189
  _globals['_FLUSHQUEUESREQUEST']._serialized_start=9191
  _globals['_FLUSHQUEUESREQUEST']._serialized_end=9231
  _globals['_FLUSHQUEUESRESPONSE']._serialized_start=9233
  _globals['_FLUSHQUEUESRESPONSE']._serialized_end=9254
  _globals['_IMAGEANDMETADATARESPONSE']._serialized_start=9257
  _globals['_IMAGEANDMETADATARESPONSE']._serialized_end=10056
  _globals['_P2PIMAGEANDMETADATARESPONSE']._serialized_start=10059
  _globals['_P2PIMAGEANDMETADATARESPONSE']._serialized_end=10789
  _globals['_GETCAMERAINFOREQUEST']._serialized_start=10791
  _globals['_GETCAMERAINFOREQUEST']._serialized_end=10813
  _globals['_CAMERAINFO']._serialized_start=10816
  _globals['_CAMERAINFO']._serialized_end=11015
  _globals['_GETCAMERAINFORESPONSE']._serialized_start=11017
  _globals['_GETCAMERAINFORESPONSE']._serialized_end=11091
  _globals['_GETLIGHTWEIGHTBURSTRECORDREQUEST']._serialized_start=11093
  _globals['_GETLIGHTWEIGHTBURSTRECORDREQUEST']._serialized_end=11127
  _globals['_GETLIGHTWEIGHTBURSTRECORDRESPONSE']._serialized_start=11129
  _globals['_GETLIGHTWEIGHTBURSTRECORDRESPONSE']._serialized_end=11205
  _globals['_GETBOOTEDREQUEST']._serialized_start=11207
  _globals['_GETBOOTEDREQUEST']._serialized_end=11225
  _globals['_GETBOOTEDRESPONSE']._serialized_start=11227
  _globals['_GETBOOTEDRESPONSE']._serialized_end=11262
  _globals['_GETREADYREQUEST']._serialized_start=11264
  _globals['_GETREADYREQUEST']._serialized_end=11281
  _globals['_GETREADYRESPONSE']._serialized_start=11283
  _globals['_GETREADYRESPONSE']._serialized_end=11316
  _globals['_DEEPWEEDDETECTION']._serialized_start=11319
  _globals['_DEEPWEEDDETECTION']._serialized_end=11660
  _globals['_DEEPWEEDDETECTION_DETECTIONCLASSSCORESENTRY']._serialized_start=11601
  _globals['_DEEPWEEDDETECTION_DETECTIONCLASSSCORESENTRY']._serialized_end=11660
  _globals['_DEEPWEEDOUTPUT']._serialized_start=11663
  _globals['_DEEPWEEDOUTPUT']._serialized_end=11902
  _globals['_GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST']._serialized_start=11904
  _globals['_GETDEEPWEEDOUTPUTBYTIMESTAMPREQUEST']._serialized_end=11979
  _globals['_GETRECOMMENDEDSTROBESETTINGSREQUEST']._serialized_start=11981
  _globals['_GETRECOMMENDEDSTROBESETTINGSREQUEST']._serialized_end=12018
  _globals['_GETRECOMMENDEDSTROBESETTINGSRESPONSE']._serialized_start=12020
  _globals['_GETRECOMMENDEDSTROBESETTINGSRESPONSE']._serialized_end=12120
  _globals['_STARTP2PBUFFERRINGREQUEST']._serialized_start=12122
  _globals['_STARTP2PBUFFERRINGREQUEST']._serialized_end=12169
  _globals['_STARTP2PBUFFERRINGRESPONSE']._serialized_start=12171
  _globals['_STARTP2PBUFFERRINGRESPONSE']._serialized_end=12203
  _globals['_STOPP2PBUFFERRINGREQUEST']._serialized_start=12206
  _globals['_STOPP2PBUFFERRINGREQUEST']._serialized_end=12376
  _globals['_STOPP2PBUFFERRINGRESPONSE']._serialized_start=12378
  _globals['_STOPP2PBUFFERRINGRESPONSE']._serialized_end=12409
  _globals['_P2PCAPTUREREQUEST']._serialized_start=12412
  _globals['_P2PCAPTUREREQUEST']._serialized_end=12558
  _globals['_P2PCAPTURERESPONSE']._serialized_start=12560
  _globals['_P2PCAPTURERESPONSE']._serialized_end=12580
  _globals['_P2PBUFFERRINGBURSTCAPTUREREQUEST']._serialized_start=12583
  _globals['_P2PBUFFERRINGBURSTCAPTUREREQUEST']._serialized_end=12737
  _globals['_P2PBUFFERRINGBURSTCAPTURERESPONSE']._serialized_start=12739
  _globals['_P2PBUFFERRINGBURSTCAPTURERESPONSE']._serialized_end=12774
  _globals['_GETNEXTDEEPWEEDOUTPUTREQUEST']._serialized_start=12776
  _globals['_GETNEXTDEEPWEEDOUTPUTREQUEST']._serialized_end=12869
  _globals['_SETTARGETINGSTATEREQUEST']._serialized_start=12871
  _globals['_SETTARGETINGSTATEREQUEST']._serialized_end=12948
  _globals['_SETTARGETINGSTATERESPONSE']._serialized_start=12950
  _globals['_SETTARGETINGSTATERESPONSE']._serialized_end=12977
  _globals['_CVRUNTIMESERVICE']._serialized_start=13383
  _globals['_CVRUNTIMESERVICE']._serialized_end=18311
# @@protoc_insertion_point(module_scope)
