# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.cv.runtime import cv_runtime_pb2 as cv_dot_runtime_dot_cv__runtime__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in cv/runtime/cv_runtime_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class CVRuntimeServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetP2PContext = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetP2PContext',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextResponse.FromString,
                _registered_method=True)
        self.GetBufferName = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetBufferName',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameResponse.FromString,
                _registered_method=True)
        self.GetCameraDimensions = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraDimensions',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsResponse.FromString,
                _registered_method=True)
        self.GetCameraInfo = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraInfo',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoResponse.FromString,
                _registered_method=True)
        self.StartP2PDataCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StartP2PDataCapture',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureResponse.FromString,
                _registered_method=True)
        self.StopP2PDataCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StopP2PDataCapture',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureResponse.FromString,
                _registered_method=True)
        self.SetDeeplabMaskExpressions = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetDeeplabMaskExpressions',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsResponse.FromString,
                _registered_method=True)
        self.GetDeepweedIndexToCategory = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.FromString,
                _registered_method=True)
        self.SetDeepweedDetectionCriteria = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.FromString,
                _registered_method=True)
        self.GetDeepweedDetectionCriteria = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.FromString,
                _registered_method=True)
        self.GetDeepweedSupportedCategories = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.FromString,
                _registered_method=True)
        self.GetCameraTemperatures = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.FromString,
                _registered_method=True)
        self.SetCameraSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetCameraSettings',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsResponse.FromString,
                _registered_method=True)
        self.GetCameraSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetCameraSettings',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsResponse.FromString,
                _registered_method=True)
        self.StartBurstRecordFrames = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.FromString,
                _registered_method=True)
        self.StopBurstRecordFrames = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.FromString,
                _registered_method=True)
        self.GetConnectors = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetConnectors',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsResponse.FromString,
                _registered_method=True)
        self.SetConnectors = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetConnectors',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsResponse.FromString,
                _registered_method=True)
        self.GetTiming = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetTiming',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetTimingRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetTimingResponse.FromString,
                _registered_method=True)
        self.Predict = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/Predict',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.PredictRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.PredictResponse.FromString,
                _registered_method=True)
        self.LoadAndQueue = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/LoadAndQueue',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueResponse.FromString,
                _registered_method=True)
        self.SetImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImage',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageResponse.FromString,
                _registered_method=True)
        self.UnsetImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/UnsetImage',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageResponse.FromString,
                _registered_method=True)
        self.GetModelPaths = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetModelPaths',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsResponse.FromString,
                _registered_method=True)
        self.SetGPSLocation = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetGPSLocation',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationResponse.FromString,
                _registered_method=True)
        self.SetImplementStatus = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImplementStatus',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusResponse.FromString,
                _registered_method=True)
        self.SetImageScore = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetImageScore',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreResponse.FromString,
                _registered_method=True)
        self.GetScoreQueue = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetScoreQueue',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueResponse.FromString,
                _registered_method=True)
        self.GetMaxImageScore = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetMaxImageScore',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreResponse.FromString,
                _registered_method=True)
        self.GetMaxScoredImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxScoredImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                _registered_method=True)
        self.GetLatestP2PImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLatestP2PImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.FromString,
                _registered_method=True)
        self.FlushQueues = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/FlushQueues',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesResponse.FromString,
                _registered_method=True)
        self.GetLatestImage = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLatestImage',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLatestImageRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
                _registered_method=True)
        self.GetLightweightBurstRecord = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.FromString,
                _registered_method=True)
        self.GetBooted = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetBooted',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBootedRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBootedResponse.FromString,
                _registered_method=True)
        self.GetReady = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetReady',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetReadyRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetReadyResponse.FromString,
                _registered_method=True)
        self.GetDeepweedOutputByTimestamp = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.FromString,
                _registered_method=True)
        self.GetRecommendedStrobeSettings = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.FromString,
                _registered_method=True)
        self.P2PCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/P2PCapture',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureResponse.FromString,
                _registered_method=True)
        self.SetAutoWhitebalance = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.FromString,
                _registered_method=True)
        self.GetNextDeepweedOutput = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.FromString,
                _registered_method=True)
        self.GetNextP2POutput = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/GetNextP2POutput',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetNextP2POutputRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2POutputProto.FromString,
                _registered_method=True)
        self.SetTargetingState = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/SetTargetingState',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateResponse.FromString,
                _registered_method=True)
        self.P2PBufferringBurstCapture = channel.unary_unary(
                '/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture',
                request_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.SerializeToString,
                response_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.FromString,
                _registered_method=True)


class CVRuntimeServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetP2PContext(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBufferName(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraDimensions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartP2PDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopP2PDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDeeplabMaskExpressions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedIndexToCategory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDeepweedDetectionCriteria(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedDetectionCriteria(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedSupportedCategories(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraTemperatures(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCameraSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCameraSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartBurstRecordFrames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopBurstRecordFrames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConnectors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConnectors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTiming(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Predict(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadAndQueue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UnsetImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelPaths(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGPSLocation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImplementStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImageScore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetScoreQueue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMaxImageScore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMaxScoredImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLatestP2PImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FlushQueues(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLatestImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLightweightBurstRecord(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBooted(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReady(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedOutputByTimestamp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRecommendedStrobeSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def P2PCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAutoWhitebalance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDeepweedOutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextP2POutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTargetingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def P2PBufferringBurstCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CVRuntimeServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetP2PContext': grpc.unary_unary_rpc_method_handler(
                    servicer.SetP2PContext,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextResponse.SerializeToString,
            ),
            'GetBufferName': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBufferName,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameResponse.SerializeToString,
            ),
            'GetCameraDimensions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraDimensions,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsResponse.SerializeToString,
            ),
            'GetCameraInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraInfo,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoResponse.SerializeToString,
            ),
            'StartP2PDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.StartP2PDataCapture,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureResponse.SerializeToString,
            ),
            'StopP2PDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.StopP2PDataCapture,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureResponse.SerializeToString,
            ),
            'SetDeeplabMaskExpressions': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDeeplabMaskExpressions,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsResponse.SerializeToString,
            ),
            'GetDeepweedIndexToCategory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedIndexToCategory,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.SerializeToString,
            ),
            'SetDeepweedDetectionCriteria': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDeepweedDetectionCriteria,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.SerializeToString,
            ),
            'GetDeepweedDetectionCriteria': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedDetectionCriteria,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.SerializeToString,
            ),
            'GetDeepweedSupportedCategories': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedSupportedCategories,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.SerializeToString,
            ),
            'GetCameraTemperatures': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraTemperatures,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.SerializeToString,
            ),
            'SetCameraSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCameraSettings,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsResponse.SerializeToString,
            ),
            'GetCameraSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraSettings,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsResponse.SerializeToString,
            ),
            'StartBurstRecordFrames': grpc.unary_unary_rpc_method_handler(
                    servicer.StartBurstRecordFrames,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.SerializeToString,
            ),
            'StopBurstRecordFrames': grpc.unary_unary_rpc_method_handler(
                    servicer.StopBurstRecordFrames,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.SerializeToString,
            ),
            'GetConnectors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConnectors,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsResponse.SerializeToString,
            ),
            'SetConnectors': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConnectors,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsResponse.SerializeToString,
            ),
            'GetTiming': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTiming,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetTimingRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetTimingResponse.SerializeToString,
            ),
            'Predict': grpc.unary_unary_rpc_method_handler(
                    servicer.Predict,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.PredictRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.PredictResponse.SerializeToString,
            ),
            'LoadAndQueue': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadAndQueue,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueResponse.SerializeToString,
            ),
            'SetImage': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImage,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageResponse.SerializeToString,
            ),
            'UnsetImage': grpc.unary_unary_rpc_method_handler(
                    servicer.UnsetImage,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageResponse.SerializeToString,
            ),
            'GetModelPaths': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelPaths,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsResponse.SerializeToString,
            ),
            'SetGPSLocation': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGPSLocation,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationResponse.SerializeToString,
            ),
            'SetImplementStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImplementStatus,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusResponse.SerializeToString,
            ),
            'SetImageScore': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImageScore,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreResponse.SerializeToString,
            ),
            'GetScoreQueue': grpc.unary_unary_rpc_method_handler(
                    servicer.GetScoreQueue,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueResponse.SerializeToString,
            ),
            'GetMaxImageScore': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMaxImageScore,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreResponse.SerializeToString,
            ),
            'GetMaxScoredImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMaxScoredImage,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetMaxScoredImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetLatestP2PImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLatestP2PImage,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLatestP2PImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.SerializeToString,
            ),
            'FlushQueues': grpc.unary_unary_rpc_method_handler(
                    servicer.FlushQueues,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesResponse.SerializeToString,
            ),
            'GetLatestImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLatestImage,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLatestImageRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.SerializeToString,
            ),
            'GetLightweightBurstRecord': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLightweightBurstRecord,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.SerializeToString,
            ),
            'GetBooted': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBooted,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBootedRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetBootedResponse.SerializeToString,
            ),
            'GetReady': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReady,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetReadyRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetReadyResponse.SerializeToString,
            ),
            'GetDeepweedOutputByTimestamp': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedOutputByTimestamp,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.SerializeToString,
            ),
            'GetRecommendedStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRecommendedStrobeSettings,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.SerializeToString,
            ),
            'P2PCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.P2PCapture,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureResponse.SerializeToString,
            ),
            'SetAutoWhitebalance': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAutoWhitebalance,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.SerializeToString,
            ),
            'GetNextDeepweedOutput': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDeepweedOutput,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.SerializeToString,
            ),
            'GetNextP2POutput': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextP2POutput,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.GetNextP2POutputRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2POutputProto.SerializeToString,
            ),
            'SetTargetingState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTargetingState,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateResponse.SerializeToString,
            ),
            'P2PBufferringBurstCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.P2PBufferringBurstCapture,
                    request_deserializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.FromString,
                    response_serializer=cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'cv.runtime.proto.CVRuntimeService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('cv.runtime.proto.CVRuntimeService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CVRuntimeService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetP2PContext(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetP2PContext',
            cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetP2PContextResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBufferName(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetBufferName',
            cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetBufferNameResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCameraDimensions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetCameraDimensions',
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraDimensionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCameraInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetCameraInfo',
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartP2PDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/StartP2PDataCapture',
            cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.StartP2PDataCaptureResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopP2PDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/StopP2PDataCapture',
            cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.StopP2PDataCaptureResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetDeeplabMaskExpressions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetDeeplabMaskExpressions',
            cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetDeeplabMaskExpressionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedIndexToCategory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory',
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedIndexToCategoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetDeepweedDetectionCriteria(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria',
            cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetDeepweedDetectionCriteriaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedDetectionCriteria(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria',
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedDetectionCriteriaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedSupportedCategories(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories',
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedSupportedCategoriesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCameraTemperatures(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures',
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraTemperaturesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCameraSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetCameraSettings',
            cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetCameraSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCameraSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetCameraSettings',
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetCameraSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartBurstRecordFrames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames',
            cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.StartBurstRecordFramesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopBurstRecordFrames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames',
            cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.StopBurstRecordFramesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetConnectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetConnectors',
            cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetConnectorsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetConnectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetConnectors',
            cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetConnectorsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTiming(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetTiming',
            cv_dot_runtime_dot_cv__runtime__pb2.GetTimingRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetTimingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Predict(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/Predict',
            cv_dot_runtime_dot_cv__runtime__pb2.PredictRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.PredictResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadAndQueue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/LoadAndQueue',
            cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.LoadAndQueueResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetImage',
            cv_dot_runtime_dot_cv__runtime__pb2.SetImageRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UnsetImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/UnsetImage',
            cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.UnsetImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModelPaths(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetModelPaths',
            cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetModelPathsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetGPSLocation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetGPSLocation',
            cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetGPSLocationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetImplementStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetImplementStatus',
            cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetImplementStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetImageScore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetImageScore',
            cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetImageScoreResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetScoreQueue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetScoreQueue',
            cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetScoreQueueResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMaxImageScore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetMaxImageScore',
            cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetMaxImageScoreResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMaxScoredImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage',
            cv_dot_runtime_dot_cv__runtime__pb2.GetMaxScoredImageRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLatestP2PImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage',
            cv_dot_runtime_dot_cv__runtime__pb2.GetLatestP2PImageRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.P2PImageAndMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FlushQueues(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/FlushQueues',
            cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.FlushQueuesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLatestImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetLatestImage',
            cv_dot_runtime_dot_cv__runtime__pb2.GetLatestImageRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.ImageAndMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLightweightBurstRecord(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord',
            cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetLightweightBurstRecordResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBooted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetBooted',
            cv_dot_runtime_dot_cv__runtime__pb2.GetBootedRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetBootedResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetReady',
            cv_dot_runtime_dot_cv__runtime__pb2.GetReadyRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetReadyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedOutputByTimestamp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp',
            cv_dot_runtime_dot_cv__runtime__pb2.GetDeepweedOutputByTimestampRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRecommendedStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings',
            cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.GetRecommendedStrobeSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def P2PCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/P2PCapture',
            cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.P2PCaptureResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetAutoWhitebalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance',
            cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetAutoWhitebalanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextDeepweedOutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput',
            cv_dot_runtime_dot_cv__runtime__pb2.GetNextDeepweedOutputRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.DeepweedOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextP2POutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/GetNextP2POutput',
            cv_dot_runtime_dot_cv__runtime__pb2.GetNextP2POutputRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.P2POutputProto.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetTargetingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/SetTargetingState',
            cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.SetTargetingStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def P2PBufferringBurstCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture',
            cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureRequest.SerializeToString,
            cv_dot_runtime_dot_cv__runtime__pb2.P2PBufferringBurstCaptureResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
