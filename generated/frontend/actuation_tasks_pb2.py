# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/actuation_tasks.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/actuation_tasks.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.core.controls.exterminator.controllers.aimbot.process import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1e\x66rontend/actuation_tasks.proto\x12\x1f\x63\x61rbon.frontend.actuation_tasks\x1a\x42\x63ore/controls/exterminator/controllers/aimbot/process/aimbot.proto\x1a\x0futil/util.proto\"^\n GlobalAimbotActuationTaskRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\x12*\n\x04task\x18\x02 \x01(\x0b\x32\x1c.aimbot.ActuationTaskRequest\"\x82\x01\n\x18GlobalActuationTaskState\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07running\x18\x02 \x01(\x08\x12\x17\n\x0f\x65lapsed_time_ms\x18\x03 \x01(\r\x12\x18\n\x10\x65xpected_time_ms\x18\x04 \x01(\r2\xd1\x02\n\x15\x41\x63tuationTasksService\x12t\n\x1fGetNextGlobalActuationTaskState\x12\x16.carbon.util.Timestamp\x1a\x39.carbon.frontend.actuation_tasks.GlobalActuationTaskState\x12w\n\x1eStartGlobalAimbotActuationTask\x12\x41.carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest\x1a\x12.carbon.util.Empty\x12I\n\x1f\x43\x61ncelGlobalAimbotActuationTask\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.actuation_tasks_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GLOBALAIMBOTACTUATIONTASKREQUEST']._serialized_start=152
  _globals['_GLOBALAIMBOTACTUATIONTASKREQUEST']._serialized_end=246
  _globals['_GLOBALACTUATIONTASKSTATE']._serialized_start=249
  _globals['_GLOBALACTUATIONTASKSTATE']._serialized_end=379
  _globals['_ACTUATIONTASKSSERVICE']._serialized_start=382
  _globals['_ACTUATIONTASKSSERVICE']._serialized_end=719
# @@protoc_insertion_point(module_scope)
