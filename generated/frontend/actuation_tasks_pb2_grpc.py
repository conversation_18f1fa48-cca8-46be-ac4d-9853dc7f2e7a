# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import actuation_tasks_pb2 as frontend_dot_actuation__tasks__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/actuation_tasks_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ActuationTasksServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextGlobalActuationTaskState = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_actuation__tasks__pb2.GlobalActuationTaskState.FromString,
                _registered_method=True)
        self.StartGlobalAimbotActuationTask = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask',
                request_serializer=frontend_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.CancelGlobalAimbotActuationTask = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class ActuationTasksServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextGlobalActuationTaskState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartGlobalAimbotActuationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelGlobalAimbotActuationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ActuationTasksServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextGlobalActuationTaskState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextGlobalActuationTaskState,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_actuation__tasks__pb2.GlobalActuationTaskState.SerializeToString,
            ),
            'StartGlobalAimbotActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartGlobalAimbotActuationTask,
                    request_deserializer=frontend_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'CancelGlobalAimbotActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelGlobalAimbotActuationTask,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.actuation_tasks.ActuationTasksService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.actuation_tasks.ActuationTasksService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ActuationTasksService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextGlobalActuationTaskState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_actuation__tasks__pb2.GlobalActuationTaskState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartGlobalAimbotActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask',
            frontend_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelGlobalAimbotActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
