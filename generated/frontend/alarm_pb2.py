# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/alarm.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/alarm.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import translation_pb2 as frontend_dot_translation__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/alarm.proto\x12\x15\x63\x61rbon.frontend.alarm\x1a\x1a\x66rontend/translation.proto\x1a\x0futil/util.proto\"\xc7\x03\n\x08\x41larmRow\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x12\n\nalarm_code\x18\x02 \x01(\t\x12\x11\n\tsubsystem\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x30\n\x05level\x18\x05 \x01(\x0e\x32!.carbon.frontend.alarm.AlarmLevel\x12\x12\n\nidentifier\x18\x06 \x01(\t\x12\x14\n\x0c\x61\x63knowledged\x18\x07 \x01(\x08\x12\x32\n\x06impact\x18\x08 \x01(\x0e\x32\".carbon.frontend.alarm.AlarmImpact\x12\x19\n\x11stop_timestamp_ms\x18\t \x01(\x03\x12\x19\n\x11\x61utofix_available\x18\n \x01(\x08\x12\x19\n\x11\x61utofix_attempted\x18\x0b \x01(\x08\x12\x1c\n\x14\x61utofix_duration_sec\x18\x0c \x01(\r\x12\x17\n\x0f\x64\x65scription_key\x18\r \x01(\t\x12Q\n\x16translation_parameters\x18\x0e \x03(\x0b\x32\x31.carbon.frontend.translation.TranslationParameter\"a\n\nAlarmTable\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12/\n\x06\x61larms\x18\x02 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\"?\n\nAlarmCount\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"(\n\x12\x41\x63knowledgeRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"t\n\x16GetNextAlarmLogRequest\x12\x10\n\x08\x66rom_idx\x18\x01 \x01(\x05\x12\x0e\n\x06to_idx\x18\x02 \x01(\x05\x12\"\n\x02ts\x18\x03 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x14\n\x0cvisible_only\x18\x04 \x01(\x08\"n\n\x17GetNextAlarmLogResponse\x12/\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"W\n\x1bGetNextAlarmLogCountRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x14\n\x0cvisible_only\x18\x02 \x01(\x08\"V\n\x1cGetNextAlarmLogCountResponse\x12\x12\n\nnum_alarms\x18\x01 \x01(\x05\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"0\n\x1a\x41ttemptAutofixAlarmRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"F\n GetNextAutofixAlarmStatusRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"q\n!GetNextAutofixAlarmStatusResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x11\n\tcompleted\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t*d\n\nAlarmLevel\x12\x0e\n\nAL_UNKNOWN\x10\x00\x12\x0f\n\x0b\x41L_CRITICAL\x10\x01\x12\x0b\n\x07\x41L_HIGH\x10\x02\x12\r\n\tAL_MEDIUM\x10\x03\x12\n\n\x06\x41L_LOW\x10\x04\x12\r\n\tAL_HIDDEN\x10\x05*\\\n\x0b\x41larmImpact\x12\x0e\n\nAI_UNKNOWN\x10\x00\x12\x0f\n\x0b\x41I_CRITICAL\x10\x01\x12\x0e\n\nAI_OFFLINE\x10\x02\x12\x0f\n\x0b\x41I_DEGRADED\x10\x03\x12\x0b\n\x07\x41I_NONE\x10\x04\x32\xeb\x06\n\x0c\x41larmService\x12M\n\x10GetNextAlarmList\x12\x16.carbon.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmTable\x12N\n\x11GetNextAlarmCount\x12\x16.carbon.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmCount\x12P\n\x13GetNextNewAlarmList\x12\x16.carbon.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmTable\x12Q\n\x10\x41\x63knowledgeAlarm\x12).carbon.frontend.alarm.AcknowledgeRequest\x1a\x12.carbon.util.Empty\x12\x35\n\x0bResetAlarms\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12p\n\x0fGetNextAlarmLog\x12-.carbon.frontend.alarm.GetNextAlarmLogRequest\x1a..carbon.frontend.alarm.GetNextAlarmLogResponse\x12\x7f\n\x14GetNextAlarmLogCount\x12\x32.carbon.frontend.alarm.GetNextAlarmLogCountRequest\x1a\x33.carbon.frontend.alarm.GetNextAlarmLogCountResponse\x12\\\n\x13\x41ttemptAutofixAlarm\x12\x31.carbon.frontend.alarm.AttemptAutofixAlarmRequest\x1a\x12.carbon.util.Empty\x12\x8e\x01\n\x19GetNextAutofixAlarmStatus\x12\x37.carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.alarm_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_ALARMLEVEL']._serialized_start=1400
  _globals['_ALARMLEVEL']._serialized_end=1500
  _globals['_ALARMIMPACT']._serialized_start=1502
  _globals['_ALARMIMPACT']._serialized_end=1594
  _globals['_ALARMROW']._serialized_start=93
  _globals['_ALARMROW']._serialized_end=548
  _globals['_ALARMTABLE']._serialized_start=550
  _globals['_ALARMTABLE']._serialized_end=647
  _globals['_ALARMCOUNT']._serialized_start=649
  _globals['_ALARMCOUNT']._serialized_end=712
  _globals['_ACKNOWLEDGEREQUEST']._serialized_start=714
  _globals['_ACKNOWLEDGEREQUEST']._serialized_end=754
  _globals['_GETNEXTALARMLOGREQUEST']._serialized_start=756
  _globals['_GETNEXTALARMLOGREQUEST']._serialized_end=872
  _globals['_GETNEXTALARMLOGRESPONSE']._serialized_start=874
  _globals['_GETNEXTALARMLOGRESPONSE']._serialized_end=984
  _globals['_GETNEXTALARMLOGCOUNTREQUEST']._serialized_start=986
  _globals['_GETNEXTALARMLOGCOUNTREQUEST']._serialized_end=1073
  _globals['_GETNEXTALARMLOGCOUNTRESPONSE']._serialized_start=1075
  _globals['_GETNEXTALARMLOGCOUNTRESPONSE']._serialized_end=1161
  _globals['_ATTEMPTAUTOFIXALARMREQUEST']._serialized_start=1163
  _globals['_ATTEMPTAUTOFIXALARMREQUEST']._serialized_end=1211
  _globals['_GETNEXTAUTOFIXALARMSTATUSREQUEST']._serialized_start=1213
  _globals['_GETNEXTAUTOFIXALARMSTATUSREQUEST']._serialized_end=1283
  _globals['_GETNEXTAUTOFIXALARMSTATUSRESPONSE']._serialized_start=1285
  _globals['_GETNEXTAUTOFIXALARMSTATUSRESPONSE']._serialized_end=1398
  _globals['_ALARMSERVICE']._serialized_start=1597
  _globals['_ALARMSERVICE']._serialized_end=2472
# @@protoc_insertion_point(module_scope)
