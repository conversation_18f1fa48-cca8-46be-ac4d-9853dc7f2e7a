# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import almanac_pb2 as frontend_dot_almanac__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/almanac_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AlmanacConfigServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetConfigData = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/GetConfigData',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.GetConfigDataResponse.FromString,
                _registered_method=True)
        self.GetNextConfigData = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/GetNextConfigData',
                request_serializer=frontend_dot_almanac__pb2.GetNextConfigDataRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.GetNextConfigDataResponse.FromString,
                _registered_method=True)
        self.LoadAlmanacConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/LoadAlmanacConfig',
                request_serializer=frontend_dot_almanac__pb2.LoadAlmanacConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.LoadAlmanacConfigResponse.FromString,
                _registered_method=True)
        self.SaveAlmanacConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/SaveAlmanacConfig',
                request_serializer=frontend_dot_almanac__pb2.SaveAlmanacConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.SaveAlmanacConfigResponse.FromString,
                _registered_method=True)
        self.SetActiveAlmanacConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/SetActiveAlmanacConfig',
                request_serializer=frontend_dot_almanac__pb2.SetActiveAlmanacConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.DeleteAlmanacConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/DeleteAlmanacConfig',
                request_serializer=frontend_dot_almanac__pb2.DeleteAlmanacConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextAlmanacConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/GetNextAlmanacConfig',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.GetNextAlmanacConfigResponse.FromString,
                _registered_method=True)
        self.LoadDiscriminatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/LoadDiscriminatorConfig',
                request_serializer=frontend_dot_almanac__pb2.LoadDiscriminatorConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.LoadDiscriminatorConfigResponse.FromString,
                _registered_method=True)
        self.SaveDiscriminatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/SaveDiscriminatorConfig',
                request_serializer=frontend_dot_almanac__pb2.SaveDiscriminatorConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.SaveDiscriminatorConfigResponse.FromString,
                _registered_method=True)
        self.SetActiveDiscriminatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/SetActiveDiscriminatorConfig',
                request_serializer=frontend_dot_almanac__pb2.SetActiveDiscriminatorConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.DeleteDiscriminatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/DeleteDiscriminatorConfig',
                request_serializer=frontend_dot_almanac__pb2.DeleteDiscriminatorConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextDiscriminatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/GetNextDiscriminatorConfig',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.GetNextDiscriminatorConfigResponse.FromString,
                _registered_method=True)
        self.GetNextModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/GetNextModelinatorConfig',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.GetNextModelinatorConfigResponse.FromString,
                _registered_method=True)
        self.SaveModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/SaveModelinatorConfig',
                request_serializer=frontend_dot_almanac__pb2.SaveModelinatorConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.FetchModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/FetchModelinatorConfig',
                request_serializer=frontend_dot_almanac__pb2.FetchModelinatorConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_almanac__pb2.FetchModelinatorConfigResponse.FromString,
                _registered_method=True)
        self.ResetModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.almanac.AlmanacConfigService/ResetModelinatorConfig',
                request_serializer=frontend_dot_almanac__pb2.ResetModelinatorConfigRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class AlmanacConfigServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetConfigData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextConfigData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadAlmanacConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveAlmanacConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActiveAlmanacConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAlmanacConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAlmanacConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadDiscriminatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveDiscriminatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActiveDiscriminatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteDiscriminatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDiscriminatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AlmanacConfigServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetConfigData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfigData,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_almanac__pb2.GetConfigDataResponse.SerializeToString,
            ),
            'GetNextConfigData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextConfigData,
                    request_deserializer=frontend_dot_almanac__pb2.GetNextConfigDataRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.GetNextConfigDataResponse.SerializeToString,
            ),
            'LoadAlmanacConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadAlmanacConfig,
                    request_deserializer=frontend_dot_almanac__pb2.LoadAlmanacConfigRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.LoadAlmanacConfigResponse.SerializeToString,
            ),
            'SaveAlmanacConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveAlmanacConfig,
                    request_deserializer=frontend_dot_almanac__pb2.SaveAlmanacConfigRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.SaveAlmanacConfigResponse.SerializeToString,
            ),
            'SetActiveAlmanacConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActiveAlmanacConfig,
                    request_deserializer=frontend_dot_almanac__pb2.SetActiveAlmanacConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'DeleteAlmanacConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAlmanacConfig,
                    request_deserializer=frontend_dot_almanac__pb2.DeleteAlmanacConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextAlmanacConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAlmanacConfig,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_almanac__pb2.GetNextAlmanacConfigResponse.SerializeToString,
            ),
            'LoadDiscriminatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadDiscriminatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.LoadDiscriminatorConfigRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.LoadDiscriminatorConfigResponse.SerializeToString,
            ),
            'SaveDiscriminatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveDiscriminatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.SaveDiscriminatorConfigRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.SaveDiscriminatorConfigResponse.SerializeToString,
            ),
            'SetActiveDiscriminatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActiveDiscriminatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.SetActiveDiscriminatorConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'DeleteDiscriminatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteDiscriminatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.DeleteDiscriminatorConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextDiscriminatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDiscriminatorConfig,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_almanac__pb2.GetNextDiscriminatorConfigResponse.SerializeToString,
            ),
            'GetNextModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModelinatorConfig,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_almanac__pb2.GetNextModelinatorConfigResponse.SerializeToString,
            ),
            'SaveModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveModelinatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.SaveModelinatorConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'FetchModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchModelinatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.FetchModelinatorConfigRequest.FromString,
                    response_serializer=frontend_dot_almanac__pb2.FetchModelinatorConfigResponse.SerializeToString,
            ),
            'ResetModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetModelinatorConfig,
                    request_deserializer=frontend_dot_almanac__pb2.ResetModelinatorConfigRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.almanac.AlmanacConfigService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.almanac.AlmanacConfigService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AlmanacConfigService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetConfigData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/GetConfigData',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_almanac__pb2.GetConfigDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextConfigData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/GetNextConfigData',
            frontend_dot_almanac__pb2.GetNextConfigDataRequest.SerializeToString,
            frontend_dot_almanac__pb2.GetNextConfigDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadAlmanacConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/LoadAlmanacConfig',
            frontend_dot_almanac__pb2.LoadAlmanacConfigRequest.SerializeToString,
            frontend_dot_almanac__pb2.LoadAlmanacConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveAlmanacConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/SaveAlmanacConfig',
            frontend_dot_almanac__pb2.SaveAlmanacConfigRequest.SerializeToString,
            frontend_dot_almanac__pb2.SaveAlmanacConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetActiveAlmanacConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/SetActiveAlmanacConfig',
            frontend_dot_almanac__pb2.SetActiveAlmanacConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteAlmanacConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/DeleteAlmanacConfig',
            frontend_dot_almanac__pb2.DeleteAlmanacConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextAlmanacConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/GetNextAlmanacConfig',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_almanac__pb2.GetNextAlmanacConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadDiscriminatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/LoadDiscriminatorConfig',
            frontend_dot_almanac__pb2.LoadDiscriminatorConfigRequest.SerializeToString,
            frontend_dot_almanac__pb2.LoadDiscriminatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveDiscriminatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/SaveDiscriminatorConfig',
            frontend_dot_almanac__pb2.SaveDiscriminatorConfigRequest.SerializeToString,
            frontend_dot_almanac__pb2.SaveDiscriminatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetActiveDiscriminatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/SetActiveDiscriminatorConfig',
            frontend_dot_almanac__pb2.SetActiveDiscriminatorConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteDiscriminatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/DeleteDiscriminatorConfig',
            frontend_dot_almanac__pb2.DeleteDiscriminatorConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextDiscriminatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/GetNextDiscriminatorConfig',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_almanac__pb2.GetNextDiscriminatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/GetNextModelinatorConfig',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_almanac__pb2.GetNextModelinatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/SaveModelinatorConfig',
            frontend_dot_almanac__pb2.SaveModelinatorConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/FetchModelinatorConfig',
            frontend_dot_almanac__pb2.FetchModelinatorConfigRequest.SerializeToString,
            frontend_dot_almanac__pb2.FetchModelinatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResetModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.almanac.AlmanacConfigService/ResetModelinatorConfig',
            frontend_dot_almanac__pb2.ResetModelinatorConfigRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
