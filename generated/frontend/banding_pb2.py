# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/banding.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/banding.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.core.controls.exterminator.controllers.aimbot.process import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x66rontend/banding.proto\x12\x17\x63\x61rbon.frontend.banding\x1a\x0futil/util.proto\x1a\x42\x63ore/controls/exterminator/controllers/aimbot/process/aimbot.proto\x1a!weed_tracking/weed_tracking.proto\"J\n\nBandingRow\x12\x0e\n\x06row_id\x18\x01 \x01(\x05\x12,\n\x05\x62\x61nds\x18\x02 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\"[\n\nBandingDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x31\n\x04rows\x18\x02 \x03(\x0b\x32#.carbon.frontend.banding.BandingRow\x12\x0c\n\x04uuid\x18\x03 \x01(\t\"c\n\x15SaveBandingDefRequest\x12\x37\n\nbandingDef\x18\x01 \x01(\x0b\x32#.carbon.frontend.banding.BandingDef\x12\x11\n\tsetActive\x18\x02 \x01(\x08\"\x81\x01\n\x17LoadBandingDefsResponse\x12\x38\n\x0b\x62\x61ndingDefs\x18\x01 \x03(\x0b\x32#.carbon.frontend.banding.BandingDef\x12\x15\n\tactiveDef\x18\x02 \x01(\tB\x02\x18\x01\x12\x15\n\ractiveDefUUID\x18\x03 \x01(\t\"<\n\x1aSetActiveBandingDefRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"=\n\x1bGetActiveBandingDefResponse\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"9\n\x17\x44\x65leteBandingDefRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"N\n\x11VisualizationData\x12\x0c\n\x04x_mm\x18\x01 \x01(\x05\x12\x0c\n\x04y_mm\x18\x02 \x01(\x05\x12\x0c\n\x04z_mm\x18\x03 \x01(\x05\x12\x0f\n\x07is_weed\x18\x04 \x01(\x08\"\xea\x01\n\x1fGetNextVisualizationDataRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0e\n\x06row_id\x18\x02 \x01(\x05\x12M\n\x10types_to_include\x18\x03 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x12\x44\n\x11threshold_filters\x18\x04 \x01(\x0b\x32).carbon.frontend.banding.ThresholdFilters\"\xae\x01\n GetNextVisualizationDataResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x38\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32*.carbon.frontend.banding.VisualizationData\x12,\n\x05\x62\x61nds\x18\x03 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\"y\n!GetNextVisualizationData2Response\x12\x30\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"&\n\x14GetDimensionsRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x05\"+\n\x18SetBandingEnabledRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"+\n\x18IsBandingEnabledResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\xe3\x01\n GetVisualizationMetadataResponse\x12~\n\x1d\x63rop_safety_radius_mm_per_row\x18\x01 \x03(\x0b\x32W.carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry\x1a?\n\x1d\x43ropSafetyRadiusMmPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\xc0\x01\n\x0fThresholdFilter\x12\x38\n\x07weeding\x18\x01 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\x12\x39\n\x08thinning\x18\x02 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\x12\x38\n\x07\x62\x61nding\x18\x03 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\"\x82\x01\n\x10ThresholdFilters\x12\x36\n\x04\x63rop\x18\x01 \x01(\x0b\x32(.carbon.frontend.banding.ThresholdFilter\x12\x36\n\x04weed\x18\x02 \x01(\x0b\x32(.carbon.frontend.banding.ThresholdFilter\"\xe4\x01\n)GetNextVisualizationDataForAllRowsRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12M\n\x10types_to_include\x18\x02 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x12\x44\n\x11threshold_filters\x18\x03 \x01(\x0b\x32).carbon.frontend.banding.ThresholdFilters\"\xe1\x02\n*GetNextVisualizationDataForAllRowsResponse\x12i\n\x0c\x64\x61ta_per_row\x18\x01 \x03(\x0b\x32S.carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12M\n\x10types_to_include\x18\x03 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x1aU\n\x0f\x44\x61taPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot:\x02\x38\x01*\x94\x02\n\x1aVisualizationTypeToInclude\x12\x12\n\x0e\x44UPLICATE_WEED\x10\x00\x12\x12\n\x0e\x44UPLICATE_CROP\x10\x01\x12\x0f\n\x0bKILLED_WEED\x10\x02\x12\x0f\n\x0bKILLED_CROP\x10\x03\x12\x10\n\x0cKILLING_WEED\x10\x04\x12\x10\n\x0cIGNORED_WEED\x10\x05\x12\x10\n\x0cKILLING_CROP\x10\x06\x12\x0e\n\nERROR_WEED\x10\x07\x12\x0e\n\nERROR_CROP\x10\x08\x12\x10\n\x0cIGNORED_CROP\x10\t\x12\x08\n\x04WEED\x10\n\x12\x08\n\x04\x43ROP\x10\x0b\x12\x0f\n\x0b\x43ROP_RADIUS\x10\x0c\x12\r\n\tCROP_KEPT\x10\r\x12\x10\n\x0cTHINNING_BOX\x10\x0e*-\n\x0eThresholdState\x12\x07\n\x03\x41NY\x10\x00\x12\x08\n\x04PASS\x10\x01\x12\x08\n\x04\x46\x41IL\x10\x02\x32\xf6\x0b\n\x0e\x42\x61ndingService\x12W\n\x0fLoadBandingDefs\x12\x12.carbon.util.Empty\x1a\x30.carbon.frontend.banding.LoadBandingDefsResponse\x12T\n\x0eSaveBandingDef\x12..carbon.frontend.banding.SaveBandingDefRequest\x1a\x12.carbon.util.Empty\x12X\n\x10\x44\x65leteBandingDef\x12\x30.carbon.frontend.banding.DeleteBandingDefRequest\x1a\x12.carbon.util.Empty\x12^\n\x13SetActiveBandingDef\x12\x33.carbon.frontend.banding.SetActiveBandingDefRequest\x1a\x12.carbon.util.Empty\x12_\n\x13GetActiveBandingDef\x12\x12.carbon.util.Empty\x1a\x34.carbon.frontend.banding.GetActiveBandingDefResponse\x12\x8f\x01\n\x18GetNextVisualizationData\x12\x38.carbon.frontend.banding.GetNextVisualizationDataRequest\x1a\x39.carbon.frontend.banding.GetNextVisualizationDataResponse\x12\x91\x01\n\x19GetNextVisualizationData2\x12\x38.carbon.frontend.banding.GetNextVisualizationDataRequest\x1a:.carbon.frontend.banding.GetNextVisualizationData2Response\x12\xad\x01\n\"GetNextVisualizationDataForAllRows\x12\x42.carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest\x1a\x43.carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse\x12]\n\rGetDimensions\x12-.carbon.frontend.banding.GetDimensionsRequest\x1a\x1d.aimbot.GetDimensionsResponse\x12Z\n\x11SetBandingEnabled\x12\x31.carbon.frontend.banding.SetBandingEnabledRequest\x1a\x12.carbon.util.Empty\x12Y\n\x10IsBandingEnabled\x12\x12.carbon.util.Empty\x1a\x31.carbon.frontend.banding.IsBandingEnabledResponse\x12\x61\n\x18SetDynamicBandingEnabled\x12\x31.carbon.frontend.banding.SetBandingEnabledRequest\x1a\x12.carbon.util.Empty\x12`\n\x17IsDynamicBandingEnabled\x12\x12.carbon.util.Empty\x1a\x31.carbon.frontend.banding.IsBandingEnabledResponse\x12i\n\x18GetVisualizationMetadata\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.banding_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_LOADBANDINGDEFSRESPONSE'].fields_by_name['activeDef']._loaded_options = None
  _globals['_LOADBANDINGDEFSRESPONSE'].fields_by_name['activeDef']._serialized_options = b'\030\001'
  _globals['_SETACTIVEBANDINGDEFREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_SETACTIVEBANDINGDEFREQUEST'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_GETACTIVEBANDINGDEFRESPONSE'].fields_by_name['name']._loaded_options = None
  _globals['_GETACTIVEBANDINGDEFRESPONSE'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_DELETEBANDINGDEFREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_DELETEBANDINGDEFREQUEST'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY']._loaded_options = None
  _globals['_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_options = b'8\001'
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY']._loaded_options = None
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY']._serialized_options = b'8\001'
  _globals['_VISUALIZATIONTYPETOINCLUDE']._serialized_start=2650
  _globals['_VISUALIZATIONTYPETOINCLUDE']._serialized_end=2926
  _globals['_THRESHOLDSTATE']._serialized_start=2928
  _globals['_THRESHOLDSTATE']._serialized_end=2973
  _globals['_BANDINGROW']._serialized_start=171
  _globals['_BANDINGROW']._serialized_end=245
  _globals['_BANDINGDEF']._serialized_start=247
  _globals['_BANDINGDEF']._serialized_end=338
  _globals['_SAVEBANDINGDEFREQUEST']._serialized_start=340
  _globals['_SAVEBANDINGDEFREQUEST']._serialized_end=439
  _globals['_LOADBANDINGDEFSRESPONSE']._serialized_start=442
  _globals['_LOADBANDINGDEFSRESPONSE']._serialized_end=571
  _globals['_SETACTIVEBANDINGDEFREQUEST']._serialized_start=573
  _globals['_SETACTIVEBANDINGDEFREQUEST']._serialized_end=633
  _globals['_GETACTIVEBANDINGDEFRESPONSE']._serialized_start=635
  _globals['_GETACTIVEBANDINGDEFRESPONSE']._serialized_end=696
  _globals['_DELETEBANDINGDEFREQUEST']._serialized_start=698
  _globals['_DELETEBANDINGDEFREQUEST']._serialized_end=755
  _globals['_VISUALIZATIONDATA']._serialized_start=757
  _globals['_VISUALIZATIONDATA']._serialized_end=835
  _globals['_GETNEXTVISUALIZATIONDATAREQUEST']._serialized_start=838
  _globals['_GETNEXTVISUALIZATIONDATAREQUEST']._serialized_end=1072
  _globals['_GETNEXTVISUALIZATIONDATARESPONSE']._serialized_start=1075
  _globals['_GETNEXTVISUALIZATIONDATARESPONSE']._serialized_end=1249
  _globals['_GETNEXTVISUALIZATIONDATA2RESPONSE']._serialized_start=1251
  _globals['_GETNEXTVISUALIZATIONDATA2RESPONSE']._serialized_end=1372
  _globals['_GETDIMENSIONSREQUEST']._serialized_start=1374
  _globals['_GETDIMENSIONSREQUEST']._serialized_end=1412
  _globals['_SETBANDINGENABLEDREQUEST']._serialized_start=1414
  _globals['_SETBANDINGENABLEDREQUEST']._serialized_end=1457
  _globals['_ISBANDINGENABLEDRESPONSE']._serialized_start=1459
  _globals['_ISBANDINGENABLEDRESPONSE']._serialized_end=1502
  _globals['_GETVISUALIZATIONMETADATARESPONSE']._serialized_start=1505
  _globals['_GETVISUALIZATIONMETADATARESPONSE']._serialized_end=1732
  _globals['_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_start=1669
  _globals['_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_end=1732
  _globals['_THRESHOLDFILTER']._serialized_start=1735
  _globals['_THRESHOLDFILTER']._serialized_end=1927
  _globals['_THRESHOLDFILTERS']._serialized_start=1930
  _globals['_THRESHOLDFILTERS']._serialized_end=2060
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST']._serialized_start=2063
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST']._serialized_end=2291
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE']._serialized_start=2294
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE']._serialized_end=2647
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY']._serialized_start=2562
  _globals['_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY']._serialized_end=2647
  _globals['_BANDINGSERVICE']._serialized_start=2976
  _globals['_BANDINGSERVICE']._serialized_end=4502
# @@protoc_insertion_point(module_scope)
