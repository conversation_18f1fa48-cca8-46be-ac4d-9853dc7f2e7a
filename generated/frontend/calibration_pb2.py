# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/calibration.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/calibration.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import camera_pb2 as frontend_dot_camera__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1a\x66rontend/calibration.proto\x12!carbon.frontend.color_calibration\x1a\x15\x66rontend/camera.proto\x1a\x0futil/util.proto\"R\n\x16\x43olorCalibrationValues\x12\x0b\n\x03red\x18\x01 \x01(\x02\x12\r\n\x05green\x18\x02 \x01(\x02\x12\x0c\n\x04\x62lue\x18\x03 \x01(\x02\x12\x0e\n\x06\x63\x61m_id\x18\x04 \x01(\t2\xf6\x01\n\x12\x43\x61librationService\x12y\n\x15StartColorCalibration\x12%.carbon.frontend.camera.CameraRequest\x1a\x39.carbon.frontend.color_calibration.ColorCalibrationValues\x12\x65\n\x14SaveColorCalibration\x12\x39.carbon.frontend.color_calibration.ColorCalibrationValues\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.calibration_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_COLORCALIBRATIONVALUES']._serialized_start=105
  _globals['_COLORCALIBRATIONVALUES']._serialized_end=187
  _globals['_CALIBRATIONSERVICE']._serialized_start=190
  _globals['_CALIBRATIONSERVICE']._serialized_end=436
# @@protoc_insertion_point(module_scope)
