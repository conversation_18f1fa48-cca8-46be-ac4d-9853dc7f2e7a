# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/camera.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/camera.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x66rontend/camera.proto\x12\x16\x63\x61rbon.frontend.camera\x1a\x0futil/util.proto\"\x1f\n\rCameraRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\x9b\x02\n\x06\x43\x61mera\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x11\n\tcamera_id\x18\x02 \x01(\t\x12\x30\n\x04type\x18\x03 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12\x16\n\x0e\x61uto_focusable\x18\x04 \x01(\x08\x12\x13\n\x0bstream_host\x18\x05 \x01(\t\x12\x13\n\x0bstream_port\x18\x06 \x01(\r\x12\r\n\x05width\x18\x07 \x01(\r\x12\x0e\n\x06height\x18\x08 \x01(\r\x12\x11\n\ttranspose\x18\t \x01(\x08\x12\x11\n\tconnected\x18\n \x01(\x08\x12\x31\n\x08rtc_info\x18\x0b \x01(\x0b\x32\x1f.carbon.frontend.camera.RTCInfo\"-\n\x07RTCInfo\x12\x0f\n\x07host_id\x18\x01 \x01(\t\x12\x11\n\tstream_id\x18\x02 \x01(\t\"a\n\nCameraList\x12/\n\x07\x63\x61meras\x18\x01 \x03(\x0b\x32\x1e.carbon.frontend.camera.Camera\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"c\n\x11\x43\x61meraListRequest\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12\x1c\n\x14include_disconnected\x18\x02 \x01(\x08\"\x8b\x01\n\x15NextCameraListRequest\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x1c\n\x14include_disconnected\x18\x03 \x01(\x08*.\n\nCameraType\x12\x07\n\x03\x41NY\x10\x00\x12\x0b\n\x07PREDICT\x10\x01\x12\n\n\x06TARGET\x10\x02\x32\xd7\x01\n\rCameraService\x12^\n\rGetCameraList\x12).carbon.frontend.camera.CameraListRequest\x1a\".carbon.frontend.camera.CameraList\x12\x66\n\x11GetNextCameraList\x12-.carbon.frontend.camera.NextCameraListRequest\x1a\".<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.camera_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_CAMERATYPE']._serialized_start=774
  _globals['_CAMERATYPE']._serialized_end=820
  _globals['_CAMERAREQUEST']._serialized_start=66
  _globals['_CAMERAREQUEST']._serialized_end=97
  _globals['_CAMERA']._serialized_start=100
  _globals['_CAMERA']._serialized_end=383
  _globals['_RTCINFO']._serialized_start=385
  _globals['_RTCINFO']._serialized_end=430
  _globals['_CAMERALIST']._serialized_start=432
  _globals['_CAMERALIST']._serialized_end=529
  _globals['_CAMERALISTREQUEST']._serialized_start=531
  _globals['_CAMERALISTREQUEST']._serialized_end=630
  _globals['_NEXTCAMERALISTREQUEST']._serialized_start=633
  _globals['_NEXTCAMERALISTREQUEST']._serialized_end=772
  _globals['_CAMERASERVICE']._serialized_start=823
  _globals['_CAMERASERVICE']._serialized_end=1038
# @@protoc_insertion_point(module_scope)
