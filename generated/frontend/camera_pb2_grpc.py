# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import camera_pb2 as frontend_dot_camera__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/camera_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class CameraServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCameraList = channel.unary_unary(
                '/carbon.frontend.camera.CameraService/GetCameraList',
                request_serializer=frontend_dot_camera__pb2.CameraListRequest.SerializeToString,
                response_deserializer=frontend_dot_camera__pb2.CameraList.FromString,
                _registered_method=True)
        self.GetNextCameraList = channel.unary_unary(
                '/carbon.frontend.camera.CameraService/GetNextCameraList',
                request_serializer=frontend_dot_camera__pb2.NextCameraListRequest.SerializeToString,
                response_deserializer=frontend_dot_camera__pb2.CameraList.FromString,
                _registered_method=True)


class CameraServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetCameraList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextCameraList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CameraServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCameraList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraList,
                    request_deserializer=frontend_dot_camera__pb2.CameraListRequest.FromString,
                    response_serializer=frontend_dot_camera__pb2.CameraList.SerializeToString,
            ),
            'GetNextCameraList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCameraList,
                    request_deserializer=frontend_dot_camera__pb2.NextCameraListRequest.FromString,
                    response_serializer=frontend_dot_camera__pb2.CameraList.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.camera.CameraService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.camera.CameraService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CameraService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetCameraList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.camera.CameraService/GetCameraList',
            frontend_dot_camera__pb2.CameraListRequest.SerializeToString,
            frontend_dot_camera__pb2.CameraList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextCameraList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.camera.CameraService/GetNextCameraList',
            frontend_dot_camera__pb2.NextCameraListRequest.SerializeToString,
            frontend_dot_camera__pb2.CameraList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
