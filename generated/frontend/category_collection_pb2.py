# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/category_collection.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/category_collection.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.category_profile import category_profile_pb2 as category__profile_dot_category__profile__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"frontend/category_collection.proto\x12#carbon.frontend.category_collection\x1a\x0futil/util.proto\x1a\'category_profile/category_profile.proto\"K\n%GetNextCategoryCollectionsDataRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x97\x01\n&GetNextCategoryCollectionsDataResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12I\n\x14\x63\x61tegory_collections\x18\x03 \x03(\x0b\x32+.carbon.category_profile.CategoryCollection\"N\n(GetNextActiveCategoryCollectionIdRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x99\x01\n)GetNextActiveCategoryCollectionIdResponse\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x17\n\x0freload_required\x18\x03 \x01(\x08\x12!\n\x19last_updated_timestamp_ms\x18\x04 \x01(\x03\"X\n$SetActiveCategoryCollectionIdRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp2\xe0\x04\n\x19\x43\x61tegoryCollectionService\x12\xb9\x01\n\x1eGetNextCategoryCollectionsData\x12J.carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest\x1aK.carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse\x12\xc2\x01\n!GetNextActiveCategoryCollectionId\x12M.carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest\x1aN.carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse\x12~\n\x1dSetActiveCategoryCollectionId\x12I.carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest\x1a\x12.carbon.util.Empty\x12\x42\n\x18ReloadCategoryCollection\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.category_collection_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GETNEXTCATEGORYCOLLECTIONSDATAREQUEST']._serialized_start=133
  _globals['_GETNEXTCATEGORYCOLLECTIONSDATAREQUEST']._serialized_end=208
  _globals['_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE']._serialized_start=211
  _globals['_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE']._serialized_end=362
  _globals['_GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST']._serialized_start=364
  _globals['_GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST']._serialized_end=442
  _globals['_GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE']._serialized_start=445
  _globals['_GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE']._serialized_end=598
  _globals['_SETACTIVECATEGORYCOLLECTIONIDREQUEST']._serialized_start=600
  _globals['_SETACTIVECATEGORYCOLLECTIONIDREQUEST']._serialized_end=688
  _globals['_CATEGORYCOLLECTIONSERVICE']._serialized_start=691
  _globals['_CATEGORYCOLLECTIONSERVICE']._serialized_end=1299
# @@protoc_insertion_point(module_scope)
