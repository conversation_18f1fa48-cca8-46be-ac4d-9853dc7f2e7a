# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/crosshair.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/crosshair.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import camera_pb2 as frontend_dot_camera__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x66rontend/crosshair.proto\x12\x19\x63\x61rbon.frontend.crosshair\x1a\x15\x66rontend/camera.proto\x1a\x0futil/util.proto\")\n\x11\x43rosshairPosition\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\"\xa8\x01\n\x16\x43rosshairPositionState\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x39\n\x03pos\x18\x02 \x01(\x0b\x32,.carbon.frontend.crosshair.CrosshairPosition\x12\x13\n\x0b\x63\x61librating\x18\x03 \x01(\x08\x12\x1a\n\x12\x63\x61libration_failed\x18\x04 \x01(\x08\"N\n\x18\x43rosshairPositionRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"h\n\x1bSetCrosshairPositionRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x39\n\x03pos\x18\x02 \x01(\x0b\x32,.carbon.frontend.crosshair.CrosshairPosition\":\n\x12MoveScannerRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\"B\n\x1c\x41utoCrossHairCalStateRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"z\n\x1d\x41utoCrossHairCalStateResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x13\n\x0bin_progress\x18\x02 \x01(\x08\x12\x10\n\x08progress\x18\x03 \x01(\x02\x12\x0e\n\x06\x66\x61iled\x18\x04 \x03(\t2\xbf\x05\n\x10\x43rosshairService\x12X\n\x1bStartAutoCalibrateCrosshair\x12%.carbon.frontend.camera.CameraRequest\x1a\x12.carbon.util.Empty\x12I\n\x1fStartAutoCalibrateAllCrosshairs\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12;\n\x11StopAutoCalibrate\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x7f\n\x15GetNextCrosshairState\x12\x33.carbon.frontend.crosshair.CrosshairPositionRequest\x1a\x31.carbon.frontend.crosshair.CrosshairPositionState\x12\x62\n\x14SetCrosshairPosition\x12\x36.carbon.frontend.crosshair.SetCrosshairPositionRequest\x1a\x12.carbon.util.Empty\x12P\n\x0bMoveScanner\x12-.carbon.frontend.crosshair.MoveScannerRequest\x1a\x12.carbon.util.Empty\x12\x91\x01\n\x1cGetNextAutoCrossHairCalState\x12\x37.carbon.frontend.crosshair.AutoCrossHairCalStateRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.crosshair_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_CROSSHAIRPOSITION']._serialized_start=95
  _globals['_CROSSHAIRPOSITION']._serialized_end=136
  _globals['_CROSSHAIRPOSITIONSTATE']._serialized_start=139
  _globals['_CROSSHAIRPOSITIONSTATE']._serialized_end=307
  _globals['_CROSSHAIRPOSITIONREQUEST']._serialized_start=309
  _globals['_CROSSHAIRPOSITIONREQUEST']._serialized_end=387
  _globals['_SETCROSSHAIRPOSITIONREQUEST']._serialized_start=389
  _globals['_SETCROSSHAIRPOSITIONREQUEST']._serialized_end=493
  _globals['_MOVESCANNERREQUEST']._serialized_start=495
  _globals['_MOVESCANNERREQUEST']._serialized_end=553
  _globals['_AUTOCROSSHAIRCALSTATEREQUEST']._serialized_start=555
  _globals['_AUTOCROSSHAIRCALSTATEREQUEST']._serialized_end=621
  _globals['_AUTOCROSSHAIRCALSTATERESPONSE']._serialized_start=623
  _globals['_AUTOCROSSHAIRCALSTATERESPONSE']._serialized_end=745
  _globals['_CROSSHAIRSERVICE']._serialized_start=748
  _globals['_CROSSHAIRSERVICE']._serialized_end=1451
# @@protoc_insertion_point(module_scope)
