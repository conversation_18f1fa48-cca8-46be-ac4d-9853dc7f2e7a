# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import camera_pb2 as frontend_dot_camera__pb2
from generated.frontend import crosshair_pb2 as frontend_dot_crosshair__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/crosshair_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class CrosshairServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartAutoCalibrateCrosshair = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair',
                request_serializer=frontend_dot_camera__pb2.CameraRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartAutoCalibrateAllCrosshairs = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopAutoCalibrate = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextCrosshairState = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState',
                request_serializer=frontend_dot_crosshair__pb2.CrosshairPositionRequest.SerializeToString,
                response_deserializer=frontend_dot_crosshair__pb2.CrosshairPositionState.FromString,
                _registered_method=True)
        self.SetCrosshairPosition = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition',
                request_serializer=frontend_dot_crosshair__pb2.SetCrosshairPositionRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.MoveScanner = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/MoveScanner',
                request_serializer=frontend_dot_crosshair__pb2.MoveScannerRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextAutoCrossHairCalState = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState',
                request_serializer=frontend_dot_crosshair__pb2.AutoCrossHairCalStateRequest.SerializeToString,
                response_deserializer=frontend_dot_crosshair__pb2.AutoCrossHairCalStateResponse.FromString,
                _registered_method=True)


class CrosshairServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartAutoCalibrateCrosshair(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartAutoCalibrateAllCrosshairs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopAutoCalibrate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextCrosshairState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCrosshairPosition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoveScanner(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAutoCrossHairCalState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CrosshairServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartAutoCalibrateCrosshair': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateCrosshair,
                    request_deserializer=frontend_dot_camera__pb2.CameraRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartAutoCalibrateAllCrosshairs': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateAllCrosshairs,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopAutoCalibrate': grpc.unary_unary_rpc_method_handler(
                    servicer.StopAutoCalibrate,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextCrosshairState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCrosshairState,
                    request_deserializer=frontend_dot_crosshair__pb2.CrosshairPositionRequest.FromString,
                    response_serializer=frontend_dot_crosshair__pb2.CrosshairPositionState.SerializeToString,
            ),
            'SetCrosshairPosition': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCrosshairPosition,
                    request_deserializer=frontend_dot_crosshair__pb2.SetCrosshairPositionRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'MoveScanner': grpc.unary_unary_rpc_method_handler(
                    servicer.MoveScanner,
                    request_deserializer=frontend_dot_crosshair__pb2.MoveScannerRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextAutoCrossHairCalState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAutoCrossHairCalState,
                    request_deserializer=frontend_dot_crosshair__pb2.AutoCrossHairCalStateRequest.FromString,
                    response_serializer=frontend_dot_crosshair__pb2.AutoCrossHairCalStateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.crosshair.CrosshairService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.crosshair.CrosshairService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CrosshairService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartAutoCalibrateCrosshair(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair',
            frontend_dot_camera__pb2.CameraRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartAutoCalibrateAllCrosshairs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopAutoCalibrate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextCrosshairState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState',
            frontend_dot_crosshair__pb2.CrosshairPositionRequest.SerializeToString,
            frontend_dot_crosshair__pb2.CrosshairPositionState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCrosshairPosition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition',
            frontend_dot_crosshair__pb2.SetCrosshairPositionRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MoveScanner(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/MoveScanner',
            frontend_dot_crosshair__pb2.MoveScannerRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextAutoCrossHairCalState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState',
            frontend_dot_crosshair__pb2.AutoCrossHairCalStateRequest.SerializeToString,
            frontend_dot_crosshair__pb2.AutoCrossHairCalStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
