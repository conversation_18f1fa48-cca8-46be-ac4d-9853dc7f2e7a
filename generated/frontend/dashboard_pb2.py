# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/dashboard.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/dashboard.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import translation_pb2 as frontend_dot_translation__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x66rontend/dashboard.proto\x12\x19\x63\x61rbon.frontend.dashboard\x1a\x1a\x66rontend/translation.proto\x1a\x0futil/util.proto\"\xca\x01\n\x0b\x45xtraStatus\x12\r\n\x05title\x18\x01 \x01(\t\x12\x11\n\ticon_name\x18\x02 \x01(\t\x12\x12\n\nicon_color\x18\x03 \x01(\t\x12\x13\n\x0bstatus_text\x18\x04 \x01(\t\x12\x14\n\x0cstatus_color\x18\x05 \x01(\t\x12\x10\n\x08group_id\x18\x06 \x01(\t\x12\x12\n\nsection_id\x18\x07 \x01(\t\x12\x10\n\x08progress\x18\x08 \x01(\x01\x12\r\n\x05width\x18\t \x01(\r\x12\x13\n\x0b\x62ottom_text\x18\n \x01(\t\" \n\rWeedTargeting\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\";\n\x11ThinningTargeting\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x15\n\talgorithm\x18\x02 \x01(\x04\x42\x02\x18\x01\"\xaf\x02\n\x0eTargetingState\x12<\n\nweed_state\x18\x01 \x01(\x0b\x32(.carbon.frontend.dashboard.WeedTargeting\x12\x44\n\x0ethinning_state\x18\x02 \x01(\x0b\x32,.carbon.frontend.dashboard.ThinningTargeting\x12\x13\n\x07\x65nabled\x18\x03 \x03(\x08\x42\x02\x18\x01\x12P\n\x0c\x65nabled_rows\x18\x04 \x03(\x0b\x32:.carbon.frontend.dashboard.TargetingState.EnabledRowsEntry\x1a\x32\n\x10\x45nabledRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\xb7\x01\n\x0f\x45xtraConclusion\x12\r\n\x05title\x18\x01 \x01(\t\x12\x17\n\x0f\x66lip_thresholds\x18\x02 \x01(\x08\x12\x1e\n\x16good_threshold_percent\x18\x03 \x01(\r\x12 \n\x18medium_threshold_percent\x18\x04 \x01(\r\x12:\n\x07percent\x18\x05 \x01(\x0b\x32).carbon.frontend.translation.PercentValue\"\x9f\x01\n\x0fRowStateMessage\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x1d\n\x15target_state_mismatch\x18\x02 \x01(\x08\x12\r\n\x05ready\x18\x03 \x01(\x08\x12M\n\x15safety_override_state\x18\x04 \x01(\x0e\x32..carbon.frontend.dashboard.SafetyOverrideState\"\xb1\r\n\x15\x44\x61shboardStateMessage\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x16\n\x0elasers_enabled\x18\x02 \x01(\x08\x12\x17\n\x0brow_enabled\x18\x03 \x03(\x08\x42\x02\x18\x01\x12\x36\n\x06\x65xtras\x18\x04 \x03(\x0b\x32&.carbon.frontend.dashboard.ExtraStatus\x12<\n\x0eselected_model\x18\x05 \x01(\x0b\x32$.carbon.frontend.dashboard.CropModel\x12\x42\n\x0ftargeting_state\x18\x06 \x01(\x0b\x32).carbon.frontend.dashboard.TargetingState\x12!\n\x15target_state_mismatch\x18\x07 \x03(\x08\x42\x02\x18\x01\x12\x15\n\trow_ready\x18\x08 \x03(\x08\x42\x02\x18\x01\x12\x16\n\nrow_exists\x18\t \x03(\x08\x42\x02\x18\x01\x12\x14\n\x0crow_width_in\x18\n \x01(\x01\x12Q\n\x15safety_override_state\x18\x0b \x03(\x0e\x32..carbon.frontend.dashboard.SafetyOverrideStateB\x02\x18\x01\x12@\n\x0einternet_state\x18\x0c \x01(\x0e\x32(.carbon.frontend.dashboard.InternetState\x12\x42\n\x0fimplement_state\x18\r \x01(\x0e\x32).carbon.frontend.dashboard.ImplementState\x12\x1a\n\x12\x65\x66\x66iciency_enabled\x18\x0e \x01(\x08\x12\x45\n\x12\x65\x66\x66iciency_percent\x18\x0f \x01(\x0b\x32).carbon.frontend.translation.PercentValue\x12\x1a\n\x12\x65rror_rate_enabled\x18\x10 \x01(\x08\x12=\n\nerror_rate\x18\x11 \x01(\x0b\x32).carbon.frontend.translation.PercentValue\x12\x45\n\x11\x65xtra_conclusions\x18\x12 \x03(\x0b\x32*.carbon.frontend.dashboard.ExtraConclusion\x12\x41\n\x11\x61rea_weeded_today\x18\x13 \x01(\x0b\x32&.carbon.frontend.translation.AreaValue\x12\x41\n\x11\x61rea_weeded_total\x18\x14 \x01(\x0b\x32&.carbon.frontend.translation.AreaValue\x12\x45\n\x12weeds_killed_today\x18\x15 \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x12weeds_killed_total\x18\x16 \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x11time_weeded_today\x18\x17 \x01(\x0b\x32*.carbon.frontend.translation.DurationValue\x12\x45\n\x11time_weeded_total\x18\x18 \x01(\x0b\x32*.carbon.frontend.translation.DurationValue\x12\x17\n\x0fweeding_enabled\x18\x19 \x01(\x08\x12\x12\n\ndebug_mode\x18\x1a \x01(\x08\x12\x45\n\x12\x63rops_killed_today\x18\x1b \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x12\x63rops_killed_total\x18\x1c \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x16\n\x0e\x63ruise_enabled\x18\x1d \x01(\x08\x12S\n\nrow_states\x18\x1e \x03(\x0b\x32?.carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry\x12\x1b\n\x13\x63ruise_allow_enable\x18\x1f \x01(\x08\x1a\\\n\x0eRowStatesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.carbon.frontend.dashboard.RowStateMessage:\x02\x38\x01\"T\n\tCropModel\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12\x11\n\thas_model\x18\x02 \x01(\x08\x12\x11\n\tpreferred\x18\x03 \x01(\x08\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\"H\n\x10\x43ropModelOptions\x12\x34\n\x06models\x18\x01 \x03(\x0b\x32$.carbon.frontend.dashboard.CropModel\"\x1b\n\x05RowId\x12\x12\n\nrow_number\x18\x01 \x01(\r\"\xda\x02\n\x0fWeedingVelocity\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x1c\n\x14\x63urrent_velocity_mph\x18\x02 \x01(\x01\x12\x1b\n\x13target_velocity_mph\x18\x03 \x01(\x01\x12\x15\n\rtolerance_mph\x18\x04 \x01(\x01\x12\'\n\x1fprimary_target_velocity_top_mph\x18\x05 \x01(\x01\x12*\n\"primary_target_velocity_bottom_mph\x18\x06 \x01(\x01\x12)\n!secondary_target_velocity_top_mph\x18\x07 \x01(\x01\x12,\n$secondary_target_velocity_bottom_mph\x18\x08 \x01(\x01\x12#\n\x1b\x63ruise_control_velocity_mph\x18\t \x01(\x01\"\x1b\n\nRowSpacing\x12\r\n\x05width\x18\x01 \x01(\x01\"\x1f\n\x0c\x43ruiseEnable\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08*M\n\x13SafetyOverrideState\x12\x16\n\x12SafetyOverrideNone\x10\x00\x12\x1e\n\x1aSafetyOverrideVelocityStop\x10\x01*L\n\rInternetState\x12\x10\n\x0c\x44ISCONNECTED\x10\x00\x12\t\n\x05LOW_1\x10\x01\x12\t\n\x05LOW_2\x10\x02\x12\t\n\x05LOW_3\x10\x03\x12\x08\n\x04HIGH\x10\x04*)\n\x0eImplementState\x12\n\n\x06RAISED\x10\x00\x12\x0b\n\x07LOWERED\x10\x01\x32\xeb\x05\n\x10\x44\x61shboardService\x12\x41\n\tToggleRow\x12 .carbon.frontend.dashboard.RowId\x1a\x12.carbon.util.Empty\x12\x36\n\x0cToggleLasers\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x61\n\x15GetNextDashboardState\x12\x16.carbon.util.Timestamp\x1a\x30.carbon.frontend.dashboard.DashboardStateMessage\x12[\n\x13GetCropModelOptions\x12\x12.carbon.util.Empty\x1a+.carbon.frontend.dashboard.CropModelOptions\"\x03\x88\x02\x01\x12M\n\x0cSetCropModel\x12$.carbon.frontend.dashboard.CropModel\x1a\x12.carbon.util.Empty\"\x03\x88\x02\x01\x12\\\n\x16GetNextWeedingVelocity\x12\x16.carbon.util.Timestamp\x1a*.carbon.frontend.dashboard.WeedingVelocity\x12R\n\x11SetTargetingState\x12).carbon.frontend.dashboard.TargetingState\x1a\x12.carbon.util.Empty\x12J\n\rSetRowSpacing\x12%.carbon.frontend.dashboard.RowSpacing\x1a\x12.carbon.util.Empty\x12O\n\x10SetCruiseEnabled\x12\'.carbon.frontend.dashboard.CruiseEnable\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.dashboard_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_THINNINGTARGETING'].fields_by_name['algorithm']._loaded_options = None
  _globals['_THINNINGTARGETING'].fields_by_name['algorithm']._serialized_options = b'\030\001'
  _globals['_TARGETINGSTATE_ENABLEDROWSENTRY']._loaded_options = None
  _globals['_TARGETINGSTATE_ENABLEDROWSENTRY']._serialized_options = b'8\001'
  _globals['_TARGETINGSTATE'].fields_by_name['enabled']._loaded_options = None
  _globals['_TARGETINGSTATE'].fields_by_name['enabled']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY']._serialized_options = b'8\001'
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_enabled']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_enabled']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['target_state_mismatch']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['target_state_mismatch']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_ready']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_ready']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_exists']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['row_exists']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['safety_override_state']._loaded_options = None
  _globals['_DASHBOARDSTATEMESSAGE'].fields_by_name['safety_override_state']._serialized_options = b'\030\001'
  _globals['_CROPMODEL'].fields_by_name['crop']._loaded_options = None
  _globals['_CROPMODEL'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_DASHBOARDSERVICE'].methods_by_name['GetCropModelOptions']._loaded_options = None
  _globals['_DASHBOARDSERVICE'].methods_by_name['GetCropModelOptions']._serialized_options = b'\210\002\001'
  _globals['_DASHBOARDSERVICE'].methods_by_name['SetCropModel']._loaded_options = None
  _globals['_DASHBOARDSERVICE'].methods_by_name['SetCropModel']._serialized_options = b'\210\002\001'
  _globals['_SAFETYOVERRIDESTATE']._serialized_start=3370
  _globals['_SAFETYOVERRIDESTATE']._serialized_end=3447
  _globals['_INTERNETSTATE']._serialized_start=3449
  _globals['_INTERNETSTATE']._serialized_end=3525
  _globals['_IMPLEMENTSTATE']._serialized_start=3527
  _globals['_IMPLEMENTSTATE']._serialized_end=3568
  _globals['_EXTRASTATUS']._serialized_start=101
  _globals['_EXTRASTATUS']._serialized_end=303
  _globals['_WEEDTARGETING']._serialized_start=305
  _globals['_WEEDTARGETING']._serialized_end=337
  _globals['_THINNINGTARGETING']._serialized_start=339
  _globals['_THINNINGTARGETING']._serialized_end=398
  _globals['_TARGETINGSTATE']._serialized_start=401
  _globals['_TARGETINGSTATE']._serialized_end=704
  _globals['_TARGETINGSTATE_ENABLEDROWSENTRY']._serialized_start=654
  _globals['_TARGETINGSTATE_ENABLEDROWSENTRY']._serialized_end=704
  _globals['_EXTRACONCLUSION']._serialized_start=707
  _globals['_EXTRACONCLUSION']._serialized_end=890
  _globals['_ROWSTATEMESSAGE']._serialized_start=893
  _globals['_ROWSTATEMESSAGE']._serialized_end=1052
  _globals['_DASHBOARDSTATEMESSAGE']._serialized_start=1055
  _globals['_DASHBOARDSTATEMESSAGE']._serialized_end=2768
  _globals['_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY']._serialized_start=2676
  _globals['_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY']._serialized_end=2768
  _globals['_CROPMODEL']._serialized_start=2770
  _globals['_CROPMODEL']._serialized_end=2854
  _globals['_CROPMODELOPTIONS']._serialized_start=2856
  _globals['_CROPMODELOPTIONS']._serialized_end=2928
  _globals['_ROWID']._serialized_start=2930
  _globals['_ROWID']._serialized_end=2957
  _globals['_WEEDINGVELOCITY']._serialized_start=2960
  _globals['_WEEDINGVELOCITY']._serialized_end=3306
  _globals['_ROWSPACING']._serialized_start=3308
  _globals['_ROWSPACING']._serialized_end=3335
  _globals['_CRUISEENABLE']._serialized_start=3337
  _globals['_CRUISEENABLE']._serialized_end=3368
  _globals['_DASHBOARDSERVICE']._serialized_start=3571
  _globals['_DASHBOARDSERVICE']._serialized_end=4318
# @@protoc_insertion_point(module_scope)
