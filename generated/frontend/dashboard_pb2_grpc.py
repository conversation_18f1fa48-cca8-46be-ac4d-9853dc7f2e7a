# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import dashboard_pb2 as frontend_dot_dashboard__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/dashboard_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class DashboardServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ToggleRow = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/ToggleRow',
                request_serializer=frontend_dot_dashboard__pb2.RowId.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ToggleLasers = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/ToggleLasers',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextDashboardState = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetNextDashboardState',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_dashboard__pb2.DashboardStateMessage.FromString,
                _registered_method=True)
        self.GetCropModelOptions = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetCropModelOptions',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_dashboard__pb2.CropModelOptions.FromString,
                _registered_method=True)
        self.SetCropModel = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetCropModel',
                request_serializer=frontend_dot_dashboard__pb2.CropModel.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextWeedingVelocity = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_dashboard__pb2.WeedingVelocity.FromString,
                _registered_method=True)
        self.SetTargetingState = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetTargetingState',
                request_serializer=frontend_dot_dashboard__pb2.TargetingState.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.SetRowSpacing = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetRowSpacing',
                request_serializer=frontend_dot_dashboard__pb2.RowSpacing.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.SetCruiseEnabled = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled',
                request_serializer=frontend_dot_dashboard__pb2.CruiseEnable.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class DashboardServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ToggleRow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToggleLasers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDashboardState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCropModelOptions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCropModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextWeedingVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTargetingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetRowSpacing(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCruiseEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DashboardServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ToggleRow': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleRow,
                    request_deserializer=frontend_dot_dashboard__pb2.RowId.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ToggleLasers': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleLasers,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextDashboardState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDashboardState,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_dashboard__pb2.DashboardStateMessage.SerializeToString,
            ),
            'GetCropModelOptions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCropModelOptions,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_dashboard__pb2.CropModelOptions.SerializeToString,
            ),
            'SetCropModel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCropModel,
                    request_deserializer=frontend_dot_dashboard__pb2.CropModel.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextWeedingVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextWeedingVelocity,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_dashboard__pb2.WeedingVelocity.SerializeToString,
            ),
            'SetTargetingState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTargetingState,
                    request_deserializer=frontend_dot_dashboard__pb2.TargetingState.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetRowSpacing': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRowSpacing,
                    request_deserializer=frontend_dot_dashboard__pb2.RowSpacing.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetCruiseEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCruiseEnabled,
                    request_deserializer=frontend_dot_dashboard__pb2.CruiseEnable.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.dashboard.DashboardService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.dashboard.DashboardService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DashboardService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ToggleRow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/ToggleRow',
            frontend_dot_dashboard__pb2.RowId.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToggleLasers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/ToggleLasers',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextDashboardState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/GetNextDashboardState',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_dashboard__pb2.DashboardStateMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCropModelOptions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/GetCropModelOptions',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_dashboard__pb2.CropModelOptions.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCropModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/SetCropModel',
            frontend_dot_dashboard__pb2.CropModel.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextWeedingVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_dashboard__pb2.WeedingVelocity.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetTargetingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/SetTargetingState',
            frontend_dot_dashboard__pb2.TargetingState.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetRowSpacing(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/SetRowSpacing',
            frontend_dot_dashboard__pb2.RowSpacing.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCruiseEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled',
            frontend_dot_dashboard__pb2.CruiseEnable.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
