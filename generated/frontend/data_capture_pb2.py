# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/data_capture.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/data_capture.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x66rontend/data_capture.proto\x12\x1c\x63\x61rbon.frontend.data_capture\x1a\x0futil/util.proto\"\x1f\n\x0f\x44\x61taCaptureRate\x12\x0c\n\x04rate\x18\x01 \x01(\x01\"\xb0\x04\n\x10\x44\x61taCaptureState\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x14\n\x0cimages_taken\x18\x02 \x01(\r\x12\x1b\n\x13target_images_taken\x18\x03 \x01(\r\x12+\n#estimated_capture_remaining_time_ms\x18\x04 \x01(\x04\x12\x17\n\x0fimages_uploaded\x18\x05 \x01(\r\x12\x1e\n\x16target_images_uploaded\x18\x06 \x01(\r\x12*\n\"estimated_upload_remaining_time_ms\x18\x07 \x01(\x04\x12;\n\x04rate\x18\x08 \x01(\x0b\x32-.carbon.frontend.data_capture.DataCaptureRate\x12!\n\x19wireless_upload_available\x18\t \x01(\x08\x12\x1d\n\x15usb_storage_connected\x18\n \x01(\x08\x12\x16\n\x0e\x63\x61pture_status\x18\x0b \x01(\t\x12\x15\n\rupload_status\x18\x0c \x01(\t\x12\x14\n\x0csession_name\x18\r \x01(\t\x12\x39\n\x04step\x18\x0e \x01(\x0e\x32+.carbon.frontend.data_capture.ProcedureStep\x12\x0c\n\x04\x63rop\x18\x0f \x01(\t\x12\x15\n\rerror_message\x18\x10 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x11 \x01(\t\"\"\n\x12\x44\x61taCaptureSession\x12\x0c\n\x04name\x18\x01 \x01(\t\"j\n\x17StartDataCaptureRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04rate\x18\x02 \x01(\x01\x12\x0c\n\x04\x63rop\x18\x03 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x14\n\x0csnap_capture\x18\x05 \x01(\x08\"2\n\x11SnapImagesRequest\x12\x0c\n\x04\x63rop\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\"t\n\x07Session\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x18\n\x10images_remaining\x18\x02 \x01(\r\x12\x14\n\x0cis_uploading\x18\x03 \x01(\x08\x12\x15\n\rhas_completed\x18\x04 \x01(\x08\x12\x14\n\x0cis_capturing\x18\x05 \x01(\x08\"S\n\x18\x41vailableSessionResponse\x12\x37\n\x08sessions\x18\x01 \x03(\x0b\x32%.carbon.frontend.data_capture.Session\"\x1b\n\x0bSessionName\x12\x0c\n\x04name\x18\x01 \x01(\t\"W\n\x14RegularCaptureStatus\x12\x10\n\x08uploaded\x18\x01 \x01(\r\x12\x0e\n\x06\x62udget\x18\x02 \x01(\r\x12\x1d\n\x15last_upload_timestamp\x18\x03 \x01(\x03*%\n\x0cUploadMethod\x12\x0c\n\x08WIRELESS\x10\x00\x12\x07\n\x03USB\x10\x01*\xcd\x01\n\rProcedureStep\x12\x07\n\x03NEW\x10\x00\x12\r\n\tCAPTURING\x10\x01\x12\x12\n\x0e\x43\x41PTURE_PAUSED\x10\x02\x12\x14\n\x10\x43\x41PTURE_COMPLETE\x10\x03\x12\x16\n\x12UPLOADING_WIRELESS\x10\x04\x12\x1d\n\x19UPLOADING_WIRELESS_PAUSED\x10\x05\x12\x11\n\rUPLOADING_USB\x10\x06\x12\x18\n\x14UPLOADING_USB_PAUSED\x10\x07\x12\x16\n\x12UPLOADING_COMPLETE\x10\x08\x32\xa9\x0c\n\x12\x44\x61taCaptureService\x12]\n\x10StartDataCapture\x12\x35.carbon.frontend.data_capture.StartDataCaptureRequest\x1a\x12.carbon.util.Empty\x12:\n\x10PauseDataCapture\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x39\n\x0fStopDataCapture\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12;\n\x11ResumeDataCapture\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12=\n\x13\x43ompleteDataCapture\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12H\n\x1eStartDataCaptureWirelessUpload\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x43\n\x19StartDataCaptureUSBUpload\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12?\n\x15StopDataCaptureUpload\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12@\n\x16PauseDataCaptureUpload\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x41\n\x17ResumeDataCaptureUpload\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12i\n(StartBackgroundDataCaptureWirelessUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x12.carbon.util.Empty\x12\x64\n#StartBackgroundDataCaptureUSBUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x12.carbon.util.Empty\x12`\n\x1fStopBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x12.carbon.util.Empty\x12\x61\n PauseBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x12.carbon.util.Empty\x12\x62\n!ResumeBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x12.carbon.util.Empty\x12\x61\n\x17GetNextDataCaptureState\x12\x16.carbon.util.Timestamp\x1a..carbon.frontend.data_capture.DataCaptureState\x12Q\n\nSnapImages\x12/.carbon.frontend.data_capture.SnapImagesRequest\x1a\x12.carbon.util.Empty\x12Y\n\x0bGetSessions\x12\x12.carbon.util.Empty\x1a\x36.carbon.frontend.data_capture.AvailableSessionResponse\x12\x61\n\x17GetRegularCaptureStatus\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.data_capture_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_UPLOADMETHOD']._serialized_start=1191
  _globals['_UPLOADMETHOD']._serialized_end=1228
  _globals['_PROCEDURESTEP']._serialized_start=1231
  _globals['_PROCEDURESTEP']._serialized_end=1436
  _globals['_DATACAPTURERATE']._serialized_start=78
  _globals['_DATACAPTURERATE']._serialized_end=109
  _globals['_DATACAPTURESTATE']._serialized_start=112
  _globals['_DATACAPTURESTATE']._serialized_end=672
  _globals['_DATACAPTURESESSION']._serialized_start=674
  _globals['_DATACAPTURESESSION']._serialized_end=708
  _globals['_STARTDATACAPTUREREQUEST']._serialized_start=710
  _globals['_STARTDATACAPTUREREQUEST']._serialized_end=816
  _globals['_SNAPIMAGESREQUEST']._serialized_start=818
  _globals['_SNAPIMAGESREQUEST']._serialized_end=868
  _globals['_SESSION']._serialized_start=870
  _globals['_SESSION']._serialized_end=986
  _globals['_AVAILABLESESSIONRESPONSE']._serialized_start=988
  _globals['_AVAILABLESESSIONRESPONSE']._serialized_end=1071
  _globals['_SESSIONNAME']._serialized_start=1073
  _globals['_SESSIONNAME']._serialized_end=1100
  _globals['_REGULARCAPTURESTATUS']._serialized_start=1102
  _globals['_REGULARCAPTURESTATUS']._serialized_end=1189
  _globals['_DATACAPTURESERVICE']._serialized_start=1439
  _globals['_DATACAPTURESERVICE']._serialized_end=3016
# @@protoc_insertion_point(module_scope)
