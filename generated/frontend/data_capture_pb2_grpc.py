# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import data_capture_pb2 as frontend_dot_data__capture__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/data_capture_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class DataCaptureServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartDataCapture = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StartDataCapture',
                request_serializer=frontend_dot_data__capture__pb2.StartDataCaptureRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.PauseDataCapture = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/PauseDataCapture',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopDataCapture = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StopDataCapture',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ResumeDataCapture = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/ResumeDataCapture',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.CompleteDataCapture = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/CompleteDataCapture',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartDataCaptureWirelessUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureWirelessUpload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartDataCaptureUSBUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureUSBUpload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StopDataCaptureUpload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.PauseDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/PauseDataCaptureUpload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ResumeDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/ResumeDataCaptureUpload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartBackgroundDataCaptureWirelessUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureWirelessUpload',
                request_serializer=frontend_dot_data__capture__pb2.SessionName.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartBackgroundDataCaptureUSBUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureUSBUpload',
                request_serializer=frontend_dot_data__capture__pb2.SessionName.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopBackgroundDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/StopBackgroundDataCaptureUpload',
                request_serializer=frontend_dot_data__capture__pb2.SessionName.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.PauseBackgroundDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/PauseBackgroundDataCaptureUpload',
                request_serializer=frontend_dot_data__capture__pb2.SessionName.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ResumeBackgroundDataCaptureUpload = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/ResumeBackgroundDataCaptureUpload',
                request_serializer=frontend_dot_data__capture__pb2.SessionName.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextDataCaptureState = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/GetNextDataCaptureState',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_data__capture__pb2.DataCaptureState.FromString,
                _registered_method=True)
        self.SnapImages = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/SnapImages',
                request_serializer=frontend_dot_data__capture__pb2.SnapImagesRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetSessions = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/GetSessions',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_data__capture__pb2.AvailableSessionResponse.FromString,
                _registered_method=True)
        self.GetRegularCaptureStatus = channel.unary_unary(
                '/carbon.frontend.data_capture.DataCaptureService/GetRegularCaptureStatus',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_data__capture__pb2.RegularCaptureStatus.FromString,
                _registered_method=True)


class DataCaptureServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PauseDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResumeDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CompleteDataCapture(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartDataCaptureWirelessUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartDataCaptureUSBUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PauseDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResumeDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartBackgroundDataCaptureWirelessUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartBackgroundDataCaptureUSBUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopBackgroundDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PauseBackgroundDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResumeBackgroundDataCaptureUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDataCaptureState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SnapImages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSessions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRegularCaptureStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DataCaptureServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.StartDataCapture,
                    request_deserializer=frontend_dot_data__capture__pb2.StartDataCaptureRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'PauseDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.PauseDataCapture,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.StopDataCapture,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ResumeDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.ResumeDataCapture,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'CompleteDataCapture': grpc.unary_unary_rpc_method_handler(
                    servicer.CompleteDataCapture,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartDataCaptureWirelessUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartDataCaptureWirelessUpload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartDataCaptureUSBUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartDataCaptureUSBUpload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StopDataCaptureUpload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'PauseDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.PauseDataCaptureUpload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ResumeDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.ResumeDataCaptureUpload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartBackgroundDataCaptureWirelessUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartBackgroundDataCaptureWirelessUpload,
                    request_deserializer=frontend_dot_data__capture__pb2.SessionName.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartBackgroundDataCaptureUSBUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartBackgroundDataCaptureUSBUpload,
                    request_deserializer=frontend_dot_data__capture__pb2.SessionName.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopBackgroundDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StopBackgroundDataCaptureUpload,
                    request_deserializer=frontend_dot_data__capture__pb2.SessionName.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'PauseBackgroundDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.PauseBackgroundDataCaptureUpload,
                    request_deserializer=frontend_dot_data__capture__pb2.SessionName.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ResumeBackgroundDataCaptureUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.ResumeBackgroundDataCaptureUpload,
                    request_deserializer=frontend_dot_data__capture__pb2.SessionName.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextDataCaptureState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDataCaptureState,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_data__capture__pb2.DataCaptureState.SerializeToString,
            ),
            'SnapImages': grpc.unary_unary_rpc_method_handler(
                    servicer.SnapImages,
                    request_deserializer=frontend_dot_data__capture__pb2.SnapImagesRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetSessions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSessions,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_data__capture__pb2.AvailableSessionResponse.SerializeToString,
            ),
            'GetRegularCaptureStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRegularCaptureStatus,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_data__capture__pb2.RegularCaptureStatus.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.data_capture.DataCaptureService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.data_capture.DataCaptureService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DataCaptureService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StartDataCapture',
            frontend_dot_data__capture__pb2.StartDataCaptureRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PauseDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/PauseDataCapture',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StopDataCapture',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResumeDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/ResumeDataCapture',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CompleteDataCapture(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/CompleteDataCapture',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartDataCaptureWirelessUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureWirelessUpload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartDataCaptureUSBUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureUSBUpload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StopDataCaptureUpload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PauseDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/PauseDataCaptureUpload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResumeDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/ResumeDataCaptureUpload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartBackgroundDataCaptureWirelessUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureWirelessUpload',
            frontend_dot_data__capture__pb2.SessionName.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartBackgroundDataCaptureUSBUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureUSBUpload',
            frontend_dot_data__capture__pb2.SessionName.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopBackgroundDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/StopBackgroundDataCaptureUpload',
            frontend_dot_data__capture__pb2.SessionName.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PauseBackgroundDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/PauseBackgroundDataCaptureUpload',
            frontend_dot_data__capture__pb2.SessionName.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResumeBackgroundDataCaptureUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/ResumeBackgroundDataCaptureUpload',
            frontend_dot_data__capture__pb2.SessionName.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextDataCaptureState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/GetNextDataCaptureState',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_data__capture__pb2.DataCaptureState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SnapImages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/SnapImages',
            frontend_dot_data__capture__pb2.SnapImagesRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSessions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/GetSessions',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_data__capture__pb2.AvailableSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRegularCaptureStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.data_capture.DataCaptureService/GetRegularCaptureStatus',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_data__capture__pb2.RegularCaptureStatus.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
