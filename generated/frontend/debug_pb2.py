# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/debug.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/debug.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import alarm_pb2 as frontend_dot_alarm__pb2
from generated.util import util_pb2 as util_dot_util__pb2
from generated.logging import logging_pb2 as logging_dot_logging__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/debug.proto\x12\x15\x63\x61rbon.frontend.debug\x1a\x14\x66rontend/alarm.proto\x1a\x0futil/util.proto\x1a\x15logging/logging.proto\x1a!weed_tracking/weed_tracking.proto\"O\n\x0cRobotMessage\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12/\n\x06\x61larms\x18\x02 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\"a\n\x12SetLogLevelRequest\x12\'\n\x05level\x18\x01 \x01(\x0e\x32\x18.carbon.logging.LogLevel\x12\x11\n\tcomponent\x18\x02 \x01(\t\x12\x0f\n\x07row_num\x18\x03 \x01(\x05\x32\xf7\x03\n\x0c\x44\x65\x62ugService\x12\x43\n\x08GetRobot\x12\x12.carbon.util.Empty\x1a#.carbon.frontend.debug.RobotMessage\x12L\n\x0bSetLogLevel\x12).carbon.frontend.debug.SetLogLevelRequest\x1a\x12.carbon.util.Empty\x12r\n\"StartSavingCropLineDetectionReplay\x12\x38.weed_tracking.StartSavingCropLineDetectionReplayRequest\x1a\x12.carbon.util.Empty\x12Y\n\x1aStartRecordingAimbotInputs\x12\'.weed_tracking.RecordAimbotInputRequest\x1a\x12.carbon.util.Empty\x12\x44\n\x1a\x41\x64\x64MockSpatialMetricsBlock\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12?\n\x15\x44\x65leteProfileSyncData\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.EmptyBDH\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.debug_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'H\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_ROBOTMESSAGE']._serialized_start=144
  _globals['_ROBOTMESSAGE']._serialized_end=223
  _globals['_SETLOGLEVELREQUEST']._serialized_start=225
  _globals['_SETLOGLEVELREQUEST']._serialized_end=322
  _globals['_DEBUGSERVICE']._serialized_start=325
  _globals['_DEBUGSERVICE']._serialized_end=828
# @@protoc_insertion_point(module_scope)
