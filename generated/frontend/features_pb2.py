# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/features.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/features.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x66rontend/features.proto\x12\x18\x63\x61rbon.frontend.features\x1a\x0futil/util.proto\"=\n\x10RowConfiguration\x12\x14\n\x0cnum_predicts\x18\x01 \x01(\x05\x12\x13\n\x0bnum_targets\x18\x02 \x01(\x05\"\xa4\x02\n\x12RobotConfiguration\x12\x10\n\x08num_rows\x18\x01 \x01(\x05\x12]\n\x11row_configuration\x18\x02 \x03(\x0b\x32\x42.carbon.frontend.features.RobotConfiguration.RowConfigurationEntry\x12\x38\n\ngeneration\x18\x03 \x01(\x0e\x32$.carbon.frontend.features.Generation\x1a\x63\n\x15RowConfigurationEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.carbon.frontend.features.RowConfiguration:\x02\x38\x01*3\n\nGeneration\x12\r\n\tUndefined\x10\x00\x12\n\n\x06Slayer\x10\x01\x12\n\n\x06Reaper\x10\x02\x32\xb5\x01\n\x0e\x46\x65\x61tureService\x12H\n\x13GetNextFeatureFlags\x12\x16.carbon.util.Timestamp\x1a\x19.carbon.util.FeatureFlags\x12Y\n\x15GetRobotConfiguration\x12\x12.carbon.util.Empty\x1a,.<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.features_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY']._loaded_options = None
  _globals['_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY']._serialized_options = b'8\001'
  _globals['_GENERATION']._serialized_start=428
  _globals['_GENERATION']._serialized_end=479
  _globals['_ROWCONFIGURATION']._serialized_start=70
  _globals['_ROWCONFIGURATION']._serialized_end=131
  _globals['_ROBOTCONFIGURATION']._serialized_start=134
  _globals['_ROBOTCONFIGURATION']._serialized_end=426
  _globals['_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY']._serialized_start=327
  _globals['_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY']._serialized_end=426
  _globals['_FEATURESERVICE']._serialized_start=482
  _globals['_FEATURESERVICE']._serialized_end=663
# @@protoc_insertion_point(module_scope)
