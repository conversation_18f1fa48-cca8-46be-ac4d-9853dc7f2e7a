# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import features_pb2 as frontend_dot_features__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/features_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class FeatureServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextFeatureFlags = channel.unary_unary(
                '/carbon.frontend.features.FeatureService/GetNextFeatureFlags',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=util_dot_util__pb2.FeatureFlags.FromString,
                _registered_method=True)
        self.GetRobotConfiguration = channel.unary_unary(
                '/carbon.frontend.features.FeatureService/GetRobotConfiguration',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_features__pb2.RobotConfiguration.FromString,
                _registered_method=True)


class FeatureServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextFeatureFlags(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRobotConfiguration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FeatureServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextFeatureFlags': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextFeatureFlags,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=util_dot_util__pb2.FeatureFlags.SerializeToString,
            ),
            'GetRobotConfiguration': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRobotConfiguration,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_features__pb2.RobotConfiguration.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.features.FeatureService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.features.FeatureService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FeatureService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextFeatureFlags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.features.FeatureService/GetNextFeatureFlags',
            util_dot_util__pb2.Timestamp.SerializeToString,
            util_dot_util__pb2.FeatureFlags.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRobotConfiguration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.features.FeatureService/GetRobotConfiguration',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_features__pb2.RobotConfiguration.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
