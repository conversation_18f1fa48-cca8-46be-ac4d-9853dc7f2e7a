# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/focus.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/focus.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import camera_pb2 as frontend_dot_camera__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/focus.proto\x12\x15\x63\x61rbon.frontend.focus\x1a\x15\x66rontend/camera.proto\x1a\x0futil/util.proto\"\x94\x01\n\x10TargetFocusState\x12\x19\n\x11liquid_lens_value\x18\x01 \x01(\r\x12\x1a\n\x12\x66ocus_progress_pct\x18\x02 \x01(\x01\x12\x16\n\x0emax_lens_value\x18\x03 \x01(\r\x12\x16\n\x0emin_lens_value\x18\x04 \x01(\r\x12\x19\n\x11\x66ocus_in_progress\x18\x05 \x01(\x08\"\x13\n\x11PredictFocusState\"\x8f\x02\n\nFocusState\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x39\n\x06target\x18\x02 \x01(\x0b\x32\'.carbon.frontend.focus.TargetFocusStateH\x00\x12;\n\x07predict\x18\x03 \x01(\x0b\x32(.carbon.frontend.focus.PredictFocusStateH\x00\x12!\n\x19global_focus_progress_pct\x18\x04 \x01(\x01\x12\x19\n\x11grid_view_enabled\x18\x05 \x01(\x08\x12\x19\n\x11\x66ocus_in_progress\x18\x06 \x01(\x08\x42\x0c\n\ntype_state\"4\n\x0eLensSetRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nlens_value\x18\x02 \x01(\r\"G\n\x11\x46ocusStateRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp2\xc7\x03\n\x0c\x46ocusService\x12?\n\x15TogglePredictGridView\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12`\n\x11GetNextFocusState\x12(.carbon.frontend.focus.FocusStateRequest\x1a!.carbon.frontend.focus.FocusState\x12S\n\x16StartAutoFocusSpecific\x12%.carbon.frontend.camera.CameraRequest\x1a\x12.carbon.util.Empty\x12;\n\x11StartAutoFocusAll\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x37\n\rStopAutoFocus\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12I\n\x0cSetLensValue\x12%.carbon.frontend.focus.LensSetRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.focus_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_TARGETFOCUSSTATE']._serialized_start=88
  _globals['_TARGETFOCUSSTATE']._serialized_end=236
  _globals['_PREDICTFOCUSSTATE']._serialized_start=238
  _globals['_PREDICTFOCUSSTATE']._serialized_end=257
  _globals['_FOCUSSTATE']._serialized_start=260
  _globals['_FOCUSSTATE']._serialized_end=531
  _globals['_LENSSETREQUEST']._serialized_start=533
  _globals['_LENSSETREQUEST']._serialized_end=585
  _globals['_FOCUSSTATEREQUEST']._serialized_start=587
  _globals['_FOCUSSTATEREQUEST']._serialized_end=658
  _globals['_FOCUSSERVICE']._serialized_start=661
  _globals['_FOCUSSERVICE']._serialized_end=1116
# @@protoc_insertion_point(module_scope)
