# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/image_stream.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/image_stream.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x66rontend/image_stream.proto\x12\x1c\x63\x61rbon.frontend.image_stream\x1a\x0futil/util.proto\x1a!weed_tracking/weed_tracking.proto\"\x8b\x01\n\x0b\x41nnotations\x12-\n\ndetections\x18\x01 \x01(\x0b\x32\x19.weed_tracking.Detections\x12#\n\x05\x62\x61nds\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Bands\x12\x13\n\x0b\x63rosshair_x\x18\x03 \x01(\x05\x12\x13\n\x0b\x63rosshair_y\x18\x04 \x01(\x05\"\xa7\x01\n\x05Image\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\r\n\x05width\x18\x02 \x01(\r\x12\x0e\n\x06height\x18\x03 \x01(\r\x12\r\n\x05\x66ocus\x18\x04 \x01(\x01\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\x12>\n\x0b\x61nnotations\x18\x06 \x01(\x0b\x32).carbon.frontend.image_stream.Annotations\"_\n\nImageEvent\x12\x15\n\tcamera_id\x18\x01 \x01(\tB\x02\x18\x01\x12\x36\n\x05image\x18\x02 \x01(\x0b\x32#.carbon.frontend.image_stream.ImageB\x02\x18\x01:\x02\x18\x01\"\xc8\x01\n\x12\x43\x61meraImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x11\n\tannotated\x18\x03 \x01(\x08\x12$\n\x1cinclude_annotations_metadata\x18\x04 \x01(\x08\x12\x17\n\x0f\x64ont_downsample\x18\x05 \x01(\x08\x12\x15\n\rencode_as_png\x18\x06 \x01(\x08\x12\x15\n\rencode_as_raw\x18\x07 \x01(\x08\"\x85\x01\n!GetPredictImageByTimestampRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x15\n\rcrop_around_x\x18\x03 \x01(\x05\x12\x15\n\rcrop_around_y\x18\x04 \x01(\x05\"V\n\"GetPredictImageByTimestampResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x10\n\x08\x63\x65nter_x\x18\x02 \x01(\x05\x12\x10\n\x08\x63\x65nter_y\x18\x03 \x01(\x05\"g\n\x13PossiblePerspective\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x15\n\rcrop_around_x\x18\x02 \x01(\x05\x12\x15\n\rcrop_around_y\x18\x03 \x01(\x05\"\x9d\x01\n\"GetMultiPredictPerspectivesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12G\n\x0cperspectives\x18\x02 \x03(\x0b\x32\x31.carbon.frontend.image_stream.PossiblePerspective\x12\x1e\n\x16requested_perspectives\x18\x03 \x01(\x05\"k\n\x13\x43\x65ntroidPerspective\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x10\n\x08\x63\x65nter_x\x18\x02 \x01(\x05\x12\x10\n\x08\x63\x65nter_y\x18\x03 \x01(\x05\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\"n\n#GetMultiPredictPerspectivesResponse\x12G\n\x0cperspectives\x18\x01 \x03(\x0b\x32\x31.carbon.frontend.image_stream.CentroidPerspective2\xc8\x03\n\x12ImageStreamService\x12k\n\x12GetNextCameraImage\x12\x30.carbon.frontend.image_stream.CameraImageRequest\x1a#.carbon.frontend.image_stream.Image\x12\x9f\x01\n\x1aGetPredictImageByTimestamp\x12?.carbon.frontend.image_stream.GetPredictImageByTimestampRequest\<EMAIL>.image_stream.GetPredictImageByTimestampResponse\x12\xa2\x01\n\x1bGetMultiPredictPerspectives\<EMAIL>.image_stream.GetMultiPredictPerspectivesRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.image_stream_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_IMAGEEVENT'].fields_by_name['camera_id']._loaded_options = None
  _globals['_IMAGEEVENT'].fields_by_name['camera_id']._serialized_options = b'\030\001'
  _globals['_IMAGEEVENT'].fields_by_name['image']._loaded_options = None
  _globals['_IMAGEEVENT'].fields_by_name['image']._serialized_options = b'\030\001'
  _globals['_IMAGEEVENT']._loaded_options = None
  _globals['_IMAGEEVENT']._serialized_options = b'\030\001'
  _globals['_ANNOTATIONS']._serialized_start=114
  _globals['_ANNOTATIONS']._serialized_end=253
  _globals['_IMAGE']._serialized_start=256
  _globals['_IMAGE']._serialized_end=423
  _globals['_IMAGEEVENT']._serialized_start=425
  _globals['_IMAGEEVENT']._serialized_end=520
  _globals['_CAMERAIMAGEREQUEST']._serialized_start=523
  _globals['_CAMERAIMAGEREQUEST']._serialized_end=723
  _globals['_GETPREDICTIMAGEBYTIMESTAMPREQUEST']._serialized_start=726
  _globals['_GETPREDICTIMAGEBYTIMESTAMPREQUEST']._serialized_end=859
  _globals['_GETPREDICTIMAGEBYTIMESTAMPRESPONSE']._serialized_start=861
  _globals['_GETPREDICTIMAGEBYTIMESTAMPRESPONSE']._serialized_end=947
  _globals['_POSSIBLEPERSPECTIVE']._serialized_start=949
  _globals['_POSSIBLEPERSPECTIVE']._serialized_end=1052
  _globals['_GETMULTIPREDICTPERSPECTIVESREQUEST']._serialized_start=1055
  _globals['_GETMULTIPREDICTPERSPECTIVESREQUEST']._serialized_end=1212
  _globals['_CENTROIDPERSPECTIVE']._serialized_start=1214
  _globals['_CENTROIDPERSPECTIVE']._serialized_end=1321
  _globals['_GETMULTIPREDICTPERSPECTIVESRESPONSE']._serialized_start=1323
  _globals['_GETMULTIPREDICTPERSPECTIVESRESPONSE']._serialized_end=1433
  _globals['_IMAGESTREAMSERVICE']._serialized_start=1436
  _globals['_IMAGESTREAMSERVICE']._serialized_end=1892
# @@protoc_insertion_point(module_scope)
