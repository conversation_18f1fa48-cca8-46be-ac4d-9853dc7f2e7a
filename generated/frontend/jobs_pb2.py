# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/jobs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/jobs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.frontend import weeding_diagnostics_pb2 as frontend_dot_weeding__diagnostics__pb2
from generated.metrics import metrics_aggregator_service_pb2 as metrics_dot_metrics__aggregator__service__pb2
from generated.frontend import profile_sync_pb2 as frontend_dot_profile__sync__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x66rontend/jobs.proto\x12\x14\x63\x61rbon.frontend.jobs\x1a\x0futil/util.proto\x1a\"frontend/weeding_diagnostics.proto\x1a(metrics/metrics_aggregator_service.proto\x1a\x1b\x66rontend/profile_sync.proto\"B\n\x0eJobDescription\x12\r\n\x05jobId\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0btimestampMs\x18\x03 \x01(\x03\"j\n\rActiveProfile\x12?\n\x0cprofile_type\x18\x01 \x01(\x0e\x32).carbon.frontend.profile_sync.ProfileType\x12\n\n\x02id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xba\x04\n\x03Job\x12<\n\x0ejobDescription\x18\x01 \x01(\x0b\x32$.carbon.frontend.jobs.JobDescription\x12\x16\n\x0e\x62\x61ndingProfile\x18\x02 \x01(\t\x12\x17\n\x0fthinningProfile\x18\x03 \x01(\t\x12\x12\n\nstopTimeMs\x18\x04 \x01(\x03\x12\x18\n\x10lastUpdateTimeMs\x18\x05 \x01(\x03\x12\x17\n\x0f\x65xpectedAcreage\x18\x06 \x01(\x02\x12\x11\n\tcompleted\x18\x07 \x01(\x08\x12\x0f\n\x07\x61lmanac\x18\x08 \x01(\t\x12\x15\n\rdiscriminator\x18\t \x01(\t\x12\x0f\n\x07\x63rop_id\x18\n \x01(\t\x12\x1a\n\x12\x62\x61ndingProfileUUID\x18\x0b \x01(\t\x12\x1b\n\x13thinningProfileUUID\x18\x0c \x01(\t\x12\x1a\n\x12\x61lmanacProfileUUID\x18\r \x01(\t\x12 \n\x18\x64iscriminatorProfileUUID\x18\x0e \x01(\t\x12\x46\n\x0f\x61\x63tive_profiles\x18\x0f \x03(\x0b\x32-.carbon.frontend.jobs.Job.ActiveProfilesEntry\x12\x16\n\x0elastUsedTimeMs\x18\x10 \x01(\x03\x1aZ\n\x13\x41\x63tiveProfilesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x32\n\x05value\x18\x02 \x01(\x0b\x32#.carbon.frontend.jobs.ActiveProfile:\x02\x38\x01\"I\n\x10\x43reateJobRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\x08\x12\x17\n\x0f\x65xpectedAcreage\x18\x03 \x01(\x02\"\"\n\x11\x43reateJobResponse\x12\r\n\x05jobId\x18\x01 \x01(\t\"i\n\x10UpdateJobRequest\x12<\n\x0ejobDescription\x18\x01 \x01(\x0b\x32$.carbon.frontend.jobs.JobDescription\x12\x17\n\x0f\x65xpectedAcreage\x18\x02 \x01(\x02\"?\n\x12GetNextJobsRequest\x12)\n\ttimestamp\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"f\n\x0eJobWithMetrics\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\x12,\n\x07metrics\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics\"\x89\x01\n\x13GetNextJobsResponse\x12\x32\n\x04jobs\x18\x01 \x03(\x0b\x32$.carbon.frontend.jobs.JobWithMetrics\x12\x13\n\x0b\x61\x63tiveJobId\x18\x02 \x01(\t\x12)\n\ttimestamp\x18\x03 \x01(\x0b\x32\x16.carbon.util.Timestamp\"F\n\x19GetNextActiveJobIdRequest\x12)\n\ttimestamp\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\\\n\x1aGetNextActiveJobIdResponse\x12\x13\n\x0b\x61\x63tiveJobId\x18\x01 \x01(\t\x12)\n\ttimestamp\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x1e\n\rGetJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"8\n\x0eGetJobResponse\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\" \n\x0fStartJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"%\n\x14GetConfigDumpRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"d\n\x15GetConfigDumpResponse\x12K\n\nrootConfig\x18\x01 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\"N\n\x1bGetActiveJobMetricsResponse\x12/\n\njobMetrics\x18\x01 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics\"!\n\x10\x44\x65leteJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"(\n\x17MarkJobCompletedRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\")\n\x18MarkJobIncompleteRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"F\n\x11GetNextJobRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\r\n\x05jobId\x18\x02 \x01(\t\"k\n\x12GetNextJobResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x31\n\x03job\x18\x02 \x01(\x0b\x32$.carbon.frontend.jobs.JobWithMetrics2\x88\t\n\x0bJobsService\x12\x62\n\x0bGetNextJobs\x12(.carbon.frontend.jobs.GetNextJobsRequest\x1a).carbon.frontend.jobs.GetNextJobsResponse\x12\\\n\tCreateJob\x12&.carbon.frontend.jobs.CreateJobRequest\x1a\'.carbon.frontend.jobs.CreateJobResponse\x12G\n\tUpdateJob\x12&.carbon.frontend.jobs.UpdateJobRequest\x1a\x12.carbon.util.Empty\x12\x45\n\x08StartJob\x12%.carbon.frontend.jobs.StartJobRequest\x1a\x12.carbon.util.Empty\x12\x37\n\rStopActiveJob\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12w\n\x12GetNextActiveJobId\x12/.carbon.frontend.jobs.GetNextActiveJobIdRequest\x1a\x30.carbon.frontend.jobs.GetNextActiveJobIdResponse\x12S\n\x06GetJob\x12#.carbon.frontend.jobs.GetJobRequest\x1a$.carbon.frontend.jobs.GetJobResponse\x12h\n\rGetConfigDump\x12*.carbon.frontend.jobs.GetConfigDumpRequest\x1a+.carbon.frontend.jobs.GetConfigDumpResponse\x12\\\n\x13GetActiveJobMetrics\x12\x12.carbon.util.Empty\x1a\x31.carbon.frontend.jobs.GetActiveJobMetricsResponse\x12G\n\tDeleteJob\x12&.carbon.frontend.jobs.DeleteJobRequest\x1a\x12.carbon.util.Empty\x12U\n\x10MarkJobCompleted\x12-.carbon.frontend.jobs.MarkJobCompletedRequest\x1a\x12.carbon.util.Empty\x12W\n\x11MarkJobIncomplete\x12..carbon.frontend.jobs.MarkJobIncompleteRequest\x1a\x12.carbon.util.Empty\x12_\n\nGetNextJob\x12\'.carbon.frontend.jobs.GetNextJobRequest\x1a(.<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.jobs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_JOB_ACTIVEPROFILESENTRY']._loaded_options = None
  _globals['_JOB_ACTIVEPROFILESENTRY']._serialized_options = b'8\001'
  _globals['_JOBDESCRIPTION']._serialized_start=169
  _globals['_JOBDESCRIPTION']._serialized_end=235
  _globals['_ACTIVEPROFILE']._serialized_start=237
  _globals['_ACTIVEPROFILE']._serialized_end=343
  _globals['_JOB']._serialized_start=346
  _globals['_JOB']._serialized_end=916
  _globals['_JOB_ACTIVEPROFILESENTRY']._serialized_start=826
  _globals['_JOB_ACTIVEPROFILESENTRY']._serialized_end=916
  _globals['_CREATEJOBREQUEST']._serialized_start=918
  _globals['_CREATEJOBREQUEST']._serialized_end=991
  _globals['_CREATEJOBRESPONSE']._serialized_start=993
  _globals['_CREATEJOBRESPONSE']._serialized_end=1027
  _globals['_UPDATEJOBREQUEST']._serialized_start=1029
  _globals['_UPDATEJOBREQUEST']._serialized_end=1134
  _globals['_GETNEXTJOBSREQUEST']._serialized_start=1136
  _globals['_GETNEXTJOBSREQUEST']._serialized_end=1199
  _globals['_JOBWITHMETRICS']._serialized_start=1201
  _globals['_JOBWITHMETRICS']._serialized_end=1303
  _globals['_GETNEXTJOBSRESPONSE']._serialized_start=1306
  _globals['_GETNEXTJOBSRESPONSE']._serialized_end=1443
  _globals['_GETNEXTACTIVEJOBIDREQUEST']._serialized_start=1445
  _globals['_GETNEXTACTIVEJOBIDREQUEST']._serialized_end=1515
  _globals['_GETNEXTACTIVEJOBIDRESPONSE']._serialized_start=1517
  _globals['_GETNEXTACTIVEJOBIDRESPONSE']._serialized_end=1609
  _globals['_GETJOBREQUEST']._serialized_start=1611
  _globals['_GETJOBREQUEST']._serialized_end=1641
  _globals['_GETJOBRESPONSE']._serialized_start=1643
  _globals['_GETJOBRESPONSE']._serialized_end=1699
  _globals['_STARTJOBREQUEST']._serialized_start=1701
  _globals['_STARTJOBREQUEST']._serialized_end=1733
  _globals['_GETCONFIGDUMPREQUEST']._serialized_start=1735
  _globals['_GETCONFIGDUMPREQUEST']._serialized_end=1772
  _globals['_GETCONFIGDUMPRESPONSE']._serialized_start=1774
  _globals['_GETCONFIGDUMPRESPONSE']._serialized_end=1874
  _globals['_GETACTIVEJOBMETRICSRESPONSE']._serialized_start=1876
  _globals['_GETACTIVEJOBMETRICSRESPONSE']._serialized_end=1954
  _globals['_DELETEJOBREQUEST']._serialized_start=1956
  _globals['_DELETEJOBREQUEST']._serialized_end=1989
  _globals['_MARKJOBCOMPLETEDREQUEST']._serialized_start=1991
  _globals['_MARKJOBCOMPLETEDREQUEST']._serialized_end=2031
  _globals['_MARKJOBINCOMPLETEREQUEST']._serialized_start=2033
  _globals['_MARKJOBINCOMPLETEREQUEST']._serialized_end=2074
  _globals['_GETNEXTJOBREQUEST']._serialized_start=2076
  _globals['_GETNEXTJOBREQUEST']._serialized_end=2146
  _globals['_GETNEXTJOBRESPONSE']._serialized_start=2148
  _globals['_GETNEXTJOBRESPONSE']._serialized_end=2255
  _globals['_JOBSSERVICE']._serialized_start=2258
  _globals['_JOBSSERVICE']._serialized_end=3418
# @@protoc_insertion_point(module_scope)
