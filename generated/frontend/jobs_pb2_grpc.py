# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import jobs_pb2 as frontend_dot_jobs__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/jobs_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class JobsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextJobs = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetNextJobs',
                request_serializer=frontend_dot_jobs__pb2.GetNextJobsRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetNextJobsResponse.FromString,
                _registered_method=True)
        self.CreateJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/CreateJob',
                request_serializer=frontend_dot_jobs__pb2.CreateJobRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.CreateJobResponse.FromString,
                _registered_method=True)
        self.UpdateJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/UpdateJob',
                request_serializer=frontend_dot_jobs__pb2.UpdateJobRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/StartJob',
                request_serializer=frontend_dot_jobs__pb2.StartJobRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StopActiveJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/StopActiveJob',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextActiveJobId = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetNextActiveJobId',
                request_serializer=frontend_dot_jobs__pb2.GetNextActiveJobIdRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetNextActiveJobIdResponse.FromString,
                _registered_method=True)
        self.GetJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetJob',
                request_serializer=frontend_dot_jobs__pb2.GetJobRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetJobResponse.FromString,
                _registered_method=True)
        self.GetConfigDump = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetConfigDump',
                request_serializer=frontend_dot_jobs__pb2.GetConfigDumpRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetConfigDumpResponse.FromString,
                _registered_method=True)
        self.GetActiveJobMetrics = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetActiveJobMetrics',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetActiveJobMetricsResponse.FromString,
                _registered_method=True)
        self.DeleteJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/DeleteJob',
                request_serializer=frontend_dot_jobs__pb2.DeleteJobRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.MarkJobCompleted = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/MarkJobCompleted',
                request_serializer=frontend_dot_jobs__pb2.MarkJobCompletedRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.MarkJobIncomplete = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/MarkJobIncomplete',
                request_serializer=frontend_dot_jobs__pb2.MarkJobIncompleteRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextJob = channel.unary_unary(
                '/carbon.frontend.jobs.JobsService/GetNextJob',
                request_serializer=frontend_dot_jobs__pb2.GetNextJobRequest.SerializeToString,
                response_deserializer=frontend_dot_jobs__pb2.GetNextJobResponse.FromString,
                _registered_method=True)


class JobsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextJobs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopActiveJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveJobId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConfigDump(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActiveJobMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MarkJobCompleted(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MarkJobIncomplete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JobsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextJobs': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextJobs,
                    request_deserializer=frontend_dot_jobs__pb2.GetNextJobsRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetNextJobsResponse.SerializeToString,
            ),
            'CreateJob': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateJob,
                    request_deserializer=frontend_dot_jobs__pb2.CreateJobRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.CreateJobResponse.SerializeToString,
            ),
            'UpdateJob': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateJob,
                    request_deserializer=frontend_dot_jobs__pb2.UpdateJobRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartJob': grpc.unary_unary_rpc_method_handler(
                    servicer.StartJob,
                    request_deserializer=frontend_dot_jobs__pb2.StartJobRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopActiveJob': grpc.unary_unary_rpc_method_handler(
                    servicer.StopActiveJob,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextActiveJobId': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveJobId,
                    request_deserializer=frontend_dot_jobs__pb2.GetNextActiveJobIdRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetNextActiveJobIdResponse.SerializeToString,
            ),
            'GetJob': grpc.unary_unary_rpc_method_handler(
                    servicer.GetJob,
                    request_deserializer=frontend_dot_jobs__pb2.GetJobRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetJobResponse.SerializeToString,
            ),
            'GetConfigDump': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfigDump,
                    request_deserializer=frontend_dot_jobs__pb2.GetConfigDumpRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetConfigDumpResponse.SerializeToString,
            ),
            'GetActiveJobMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActiveJobMetrics,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetActiveJobMetricsResponse.SerializeToString,
            ),
            'DeleteJob': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteJob,
                    request_deserializer=frontend_dot_jobs__pb2.DeleteJobRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'MarkJobCompleted': grpc.unary_unary_rpc_method_handler(
                    servicer.MarkJobCompleted,
                    request_deserializer=frontend_dot_jobs__pb2.MarkJobCompletedRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'MarkJobIncomplete': grpc.unary_unary_rpc_method_handler(
                    servicer.MarkJobIncomplete,
                    request_deserializer=frontend_dot_jobs__pb2.MarkJobIncompleteRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextJob': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextJob,
                    request_deserializer=frontend_dot_jobs__pb2.GetNextJobRequest.FromString,
                    response_serializer=frontend_dot_jobs__pb2.GetNextJobResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.jobs.JobsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.jobs.JobsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class JobsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextJobs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetNextJobs',
            frontend_dot_jobs__pb2.GetNextJobsRequest.SerializeToString,
            frontend_dot_jobs__pb2.GetNextJobsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/CreateJob',
            frontend_dot_jobs__pb2.CreateJobRequest.SerializeToString,
            frontend_dot_jobs__pb2.CreateJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/UpdateJob',
            frontend_dot_jobs__pb2.UpdateJobRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/StartJob',
            frontend_dot_jobs__pb2.StartJobRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopActiveJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/StopActiveJob',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextActiveJobId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetNextActiveJobId',
            frontend_dot_jobs__pb2.GetNextActiveJobIdRequest.SerializeToString,
            frontend_dot_jobs__pb2.GetNextActiveJobIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetJob',
            frontend_dot_jobs__pb2.GetJobRequest.SerializeToString,
            frontend_dot_jobs__pb2.GetJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetConfigDump(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetConfigDump',
            frontend_dot_jobs__pb2.GetConfigDumpRequest.SerializeToString,
            frontend_dot_jobs__pb2.GetConfigDumpResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActiveJobMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetActiveJobMetrics',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_jobs__pb2.GetActiveJobMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/DeleteJob',
            frontend_dot_jobs__pb2.DeleteJobRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MarkJobCompleted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/MarkJobCompleted',
            frontend_dot_jobs__pb2.MarkJobCompletedRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MarkJobIncomplete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/MarkJobIncomplete',
            frontend_dot_jobs__pb2.MarkJobIncompleteRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.jobs.JobsService/GetNextJob',
            frontend_dot_jobs__pb2.GetNextJobRequest.SerializeToString,
            frontend_dot_jobs__pb2.GetNextJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
