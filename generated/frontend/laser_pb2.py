# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/laser.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/laser.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import camera_pb2 as frontend_dot_camera__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/laser.proto\x12\x15\x63\x61rbon.frontend.laser\x1a\x15\x66rontend/camera.proto\x1a\x0futil/util.proto\"Z\n\x0fLaserDescriptor\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x10\n\x08laser_id\x18\x02 \x01(\r\x12\x11\n\tcamera_id\x18\x03 \x01(\t\x12\x0e\n\x06serial\x18\x04 \x01(\t\"\xa2\x02\n\nLaserState\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x0e\n\x06\x66iring\x18\x02 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x03 \x01(\x08\x12\r\n\x05\x65rror\x18\x04 \x01(\x08\x12\x18\n\x10total_fire_count\x18\x05 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x06 \x01(\x03\x12\x12\n\ndelta_temp\x18\x07 \x01(\x02\x12\x0f\n\x07\x63urrent\x18\t \x01(\x02\x12\x1c\n\x14target_trajectory_id\x18\n \x01(\r\x12\x14\n\x0clifetime_sec\x18\x0b \x01(\x04\x12\x13\n\x0bpower_level\x18\x0c \x01(\x02\"g\n\x0eLaserStateList\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x31\n\x06lasers\x18\x02 \x03(\x0b\x32!.carbon.frontend.laser.LaserState\" \n\nRowRequest\x12\x12\n\nrow_number\x18\x01 \x01(\r\"m\n\x14SetLaserPowerRequest\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x13\n\x0bpower_level\x18\x02 \x01(\x02\"\xa6\x01\n\x16\x46ixLaserMetricsRequest\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x18\n\x10total_fire_count\x18\x02 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x03 \x01(\x03\x12\x14\n\x0clifetime_sec\x18\x04 \x01(\x04\x32\x80\x05\n\x0cLaserService\x12H\n\tFireLaser\x12%.carbon.frontend.camera.CameraRequest\x1a\x12.carbon.util.Empty(\x01\x12R\n\x11GetNextLaserState\x12\x16.carbon.util.Timestamp\x1a%.carbon.frontend.laser.LaserStateList\x12P\n\x12ToggleLaserEnabled\x12&.carbon.frontend.laser.LaserDescriptor\x1a\x12.carbon.util.Empty\x12\x42\n\tEnableRow\x12!.carbon.frontend.laser.RowRequest\x1a\x12.carbon.util.Empty\x12\x43\n\nDisableRow\x12!.carbon.frontend.laser.RowRequest\x1a\x12.carbon.util.Empty\x12O\n\x11ResetLaserMetrics\x12&.carbon.frontend.laser.LaserDescriptor\x1a\x12.carbon.util.Empty\x12T\n\x0f\x46ixLaserMetrics\x12-.carbon.frontend.laser.FixLaserMetricsRequest\x1a\x12.carbon.util.Empty\x12P\n\rSetLaserPower\x12+.carbon.frontend.laser.SetLaserPowerRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.laser_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_LASERDESCRIPTOR']._serialized_start=87
  _globals['_LASERDESCRIPTOR']._serialized_end=177
  _globals['_LASERSTATE']._serialized_start=180
  _globals['_LASERSTATE']._serialized_end=470
  _globals['_LASERSTATELIST']._serialized_start=472
  _globals['_LASERSTATELIST']._serialized_end=575
  _globals['_ROWREQUEST']._serialized_start=577
  _globals['_ROWREQUEST']._serialized_end=609
  _globals['_SETLASERPOWERREQUEST']._serialized_start=611
  _globals['_SETLASERPOWERREQUEST']._serialized_end=720
  _globals['_FIXLASERMETRICSREQUEST']._serialized_start=723
  _globals['_FIXLASERMETRICSREQUEST']._serialized_end=889
  _globals['_LASERSERVICE']._serialized_start=892
  _globals['_LASERSERVICE']._serialized_end=1532
# @@protoc_insertion_point(module_scope)
