# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/messages.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/messages.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x66rontend/messages.proto\x12\x15\x63\x61rbon.frontend.debug\x1a\x0futil/util.proto\"n\n\x07Message\x12\n\n\x02id\x18\x01 \x01(\x03\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x14\n\x0c\x66rom_support\x18\x04 \x01(\x08\x12\x0c\n\x04read\x18\x05 \x01(\x08\"!\n\x0eMessageRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\"5\n\x0fMessagesRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"h\n\x10MessagesResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x30\n\x08messages\x18\x02 \x03(\x0b\x32\x1e.carbon.frontend.debug.Message\"\x19\n\x0bReadRequest\x12\n\n\x02id\x18\x01 \x01(\x03\x32\xf6\x01\n\x0fMessagesService\x12\x45\n\x0bReadMessage\x12\".carbon.frontend.debug.ReadRequest\x1a\x12.carbon.util.Empty\x12H\n\x0bSendMessage\x12%.carbon.frontend.debug.MessageRequest\x1a\x12.carbon.util.Empty\x12R\n\x0fGetNextMessages\x12\x16.carbon.util.Timestamp\x1a\'.<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.messages_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_MESSAGE']._serialized_start=67
  _globals['_MESSAGE']._serialized_end=177
  _globals['_MESSAGEREQUEST']._serialized_start=179
  _globals['_MESSAGEREQUEST']._serialized_end=212
  _globals['_MESSAGESREQUEST']._serialized_start=214
  _globals['_MESSAGESREQUEST']._serialized_end=267
  _globals['_MESSAGESRESPONSE']._serialized_start=269
  _globals['_MESSAGESRESPONSE']._serialized_end=373
  _globals['_READREQUEST']._serialized_start=375
  _globals['_READREQUEST']._serialized_end=400
  _globals['_MESSAGESSERVICE']._serialized_start=403
  _globals['_MESSAGESSERVICE']._serialized_end=649
# @@protoc_insertion_point(module_scope)
