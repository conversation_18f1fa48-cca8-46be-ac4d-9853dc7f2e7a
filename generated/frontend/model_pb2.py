# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/model.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/model.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/model.proto\x12\x15\x63\x61rbon.frontend.model\x1a\x0futil/util.proto\"\xce\x03\n\x05Model\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04\x63rop\x18\x02 \x01(\t\x12\"\n\x02ts\x18\x03 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0e\n\x06\x63ustom\x18\x04 \x01(\x08\x12\x0e\n\x06pinned\x18\x05 \x01(\x08\x12\x0e\n\x06\x61\x63tive\x18\x06 \x01(\x08\x12\x0e\n\x06synced\x18\x07 \x01(\x08\x12\x16\n\x0esynced_to_rows\x18\x08 \x03(\x08\x12\x13\n\x0b\x64ownloading\x18\t \x01(\x08\x12\x0c\n\x04type\x18\n \x01(\t\x12\x33\n\x13last_used_timestamp\x18\x0b \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x1c\n\x14\x64ownloading_progress\x18\x0c \x01(\x02\x12/\n\'estimated_downloading_remaining_time_ms\x18\r \x01(\x04\x12\x34\n\x14\x64ownloaded_timestamp\x18\x0e \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x13\n\x0brecommended\x18\x0f \x01(\x08\x12\x17\n\x0fviable_crop_ids\x18\x10 \x03(\t\x12\x12\n\nmaintained\x18\x11 \x01(\x08\x12\x10\n\x08nickname\x18\x12 \x01(\t\"$\n\x11SelectCropRequest\x12\x0f\n\x07\x63rop_id\x18\x01 \x01(\t\"\"\n\x12ListCropParameters\x12\x0c\n\x04lang\x18\x01 \x01(\t\"\xac\x01\n\x0b\x45nabledCrop\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07\x63reated\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x61rbon_name\x18\x03 \x01(\t\x12\x13\n\x0b\x63ommon_name\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\r\n\x05notes\x18\x06 \x01(\t\x12\x17\n\x0fpinned_model_id\x18\x07 \x01(\t\x12\x19\n\x11recommended_model\x18\x08 \x01(\t\"K\n\x0f\x45nabledCropList\x12\x38\n\x0c\x65nabledCrops\x18\x01 \x03(\x0b\x32\".carbon.frontend.model.EnabledCrop\"T\n\x1dGetNextSelectedCropIDResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\"q\n\x0fPinModelRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x04\x63rop\x18\x02 \x01(\tB\x02\x18\x01\x12\"\n\x1a\x61llow_pinned_crop_override\x18\x03 \x01(\x08\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x0b\n\x03p2p\x18\x05 \x01(\x08\"C\n\x11UnpinModelRequest\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x0b\n\x03p2p\x18\x05 \x01(\x08\"a\n\x18GetNextModelStateRequest\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07\x63rop_id\x18\x03 \x01(\t\"\xae\x01\n\x19GetNextModelStateResponse\x12,\n\x06models\x18\x01 \x03(\x0b\x32\x1c.carbon.frontend.model.Model\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x1c\n\x14\x63urrent_p2p_model_id\x18\x03 \x01(\t\x12!\n\x19\x63urrent_deepweed_model_id\x18\x04 \x01(\t\"(\n\x14\x44ownloadModelRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\"\xf5\x01\n\x13ModelHistoryRequest\x12\x17\n\x0fstart_timestamp\x18\x01 \x01(\x03\x12\r\n\x05\x63ount\x18\x02 \x01(\x03\x12\x0f\n\x07reverse\x18\x03 \x01(\x08\x12\x37\n\x0cmatch_filter\x18\x04 \x01(\x0b\x32!.carbon.frontend.model.ModelEvent\x12\"\n\x02ts\x18\x05 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12H\n\x12\x65vent_type_matcher\x18\x06 \x01(\x0b\x32,.carbon.frontend.model.ModelEventTypeMatcher\"\xa3\x01\n\nModelEvent\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x12\n\nmodel_type\x18\x03 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x10\n\x08job_name\x18\x05 \x01(\t\x12\x0c\n\x04time\x18\x06 \x01(\x03\x12\x16\n\x0emodel_nickname\x18\x07 \x01(\t\x12\x18\n\x10model_parameters\x18\x08 \x01(\t\"\xe4\x01\n\x15ModelEventTypeMatcher\x12\x13\n\x0brobot_start\x18\x01 \x01(\x08\x12\x0e\n\x06pinned\x18\x02 \x01(\x08\x12\x10\n\x08unpinned\x18\x03 \x01(\x08\x12\x13\n\x0brecommended\x18\x04 \x01(\x08\x12\x11\n\tactivated\x18\x05 \x01(\x08\x12\x17\n\x0fnickname_change\x18\x06 \x01(\x08\x12\x17\n\x0fnickname_delete\x18\x07 \x01(\x08\x12 \n\x18\x64\x65\x66\x61ult_parameter_change\x18\x08 \x01(\x08\x12\x18\n\x10parameter_change\x18\t \x01(\x08\"m\n\x14ModelHistoryResponse\x12\x31\n\x06\x65vents\x18\x01 \x03(\x0b\x32!.carbon.frontend.model.ModelEvent\x12\"\n\x02ts\x18\x05 \x01(\x0b\x32\x16.carbon.util.Timestamp\"Q\n\x18GetModelNicknamesRequest\x12\x11\n\tmodel_ids\x18\x01 \x03(\t\x12\"\n\x02ts\x18\x05 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\xd5\x01\n\x19GetModelNicknamesResponse\x12]\n\x0fmodel_nicknames\x18\x01 \x03(\x0b\x32\x44.carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry\x12\"\n\x02ts\x18\x05 \x01(\x0b\x32\x16.carbon.util.Timestamp\x1a\x35\n\x13ModelNicknamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"C\n\x17SetModelNicknameRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x16\n\x0emodel_nickname\x18\x02 \x01(\t\"2\n\rCropModelPair\x12\x0f\n\x07\x63rop_id\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\"d\n$RefreshDefaultModelParametersRequest\x12<\n\x0e\x63ropModelPairs\x18\x01 \x03(\x0b\x32$.carbon.frontend.model.CropModelPair\"1\n\x12SyncCropIDsRequest\x12\x1b\n\x13\x66orce_cache_refresh\x18\x01 \x01(\x08\x32\xc2\r\n\x0cModelService\x12\x46\n\x08PinModel\x12&.carbon.frontend.model.PinModelRequest\x1a\x12.carbon.util.Empty\x12J\n\nUnpinModel\x12(.carbon.frontend.model.UnpinModelRequest\x1a\x12.carbon.util.Empty\x12v\n\x11GetNextModelState\x12/.carbon.frontend.model.GetNextModelStateRequest\x1a\x30.carbon.frontend.model.GetNextModelStateResponse\x12y\n\x14GetNextAllModelState\x12/.carbon.frontend.model.GetNextModelStateRequest\x1a\x30.carbon.frontend.model.GetNextModelStateResponse\x12\x35\n\x0bUpdateModel\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x65\n\x10ListEnabledCrops\x12).carbon.frontend.model.ListCropParameters\x1a&.carbon.frontend.model.EnabledCropList\x12\x65\n\x10ListCaptureCrops\x12).carbon.frontend.model.ListCropParameters\x1a&.carbon.frontend.model.EnabledCropList\x12\x65\n\x15GetNextSelectedCropID\x12\x16.carbon.util.Timestamp\x1a\x34.carbon.frontend.model.GetNextSelectedCropIDResponse\x12J\n\nSelectCrop\x12(.carbon.frontend.model.SelectCropRequest\x1a\x12.carbon.util.Empty\x12P\n\rDownloadModel\x12+.carbon.frontend.model.DownloadModelRequest\x1a\x12.carbon.util.Empty\x12n\n\x13GetNextModelHistory\x12*.carbon.frontend.model.ModelHistoryRequest\x1a+.carbon.frontend.model.ModelHistoryResponse\x12j\n\x0fGetModelHistory\x12*.carbon.frontend.model.ModelHistoryRequest\x1a+.carbon.frontend.model.ModelHistoryResponse\x12v\n\x11GetModelNicknames\x12/.carbon.frontend.model.GetModelNicknamesRequest\x1a\x30.carbon.frontend.model.GetModelNicknamesResponse\x12z\n\x15GetNextModelNicknames\x12/.carbon.frontend.model.GetModelNicknamesRequest\x1a\x30.carbon.frontend.model.GetModelNicknamesResponse\x12V\n\x10SetModelNickname\x12..carbon.frontend.model.SetModelNicknameRequest\x1a\x12.carbon.util.Empty\x12p\n\x1dRefreshDefaultModelParameters\x12;.carbon.frontend.model.RefreshDefaultModelParametersRequest\x1a\x12.carbon.util.Empty\x12L\n\x0bSyncCropIDs\x12).carbon.frontend.model.SyncCropIDsRequest\x1a\x12.carbon.util.Empty\x12\x39\n\x0fTriggerDownload\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_PINMODELREQUEST'].fields_by_name['crop']._loaded_options = None
  _globals['_PINMODELREQUEST'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_UNPINMODELREQUEST'].fields_by_name['crop']._loaded_options = None
  _globals['_UNPINMODELREQUEST'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_GETNEXTMODELSTATEREQUEST'].fields_by_name['crop']._loaded_options = None
  _globals['_GETNEXTMODELSTATEREQUEST'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY']._loaded_options = None
  _globals['_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY']._serialized_options = b'8\001'
  _globals['_MODEL']._serialized_start=65
  _globals['_MODEL']._serialized_end=527
  _globals['_SELECTCROPREQUEST']._serialized_start=529
  _globals['_SELECTCROPREQUEST']._serialized_end=565
  _globals['_LISTCROPPARAMETERS']._serialized_start=567
  _globals['_LISTCROPPARAMETERS']._serialized_end=601
  _globals['_ENABLEDCROP']._serialized_start=604
  _globals['_ENABLEDCROP']._serialized_end=776
  _globals['_ENABLEDCROPLIST']._serialized_start=778
  _globals['_ENABLEDCROPLIST']._serialized_end=853
  _globals['_GETNEXTSELECTEDCROPIDRESPONSE']._serialized_start=855
  _globals['_GETNEXTSELECTEDCROPIDRESPONSE']._serialized_end=939
  _globals['_PINMODELREQUEST']._serialized_start=941
  _globals['_PINMODELREQUEST']._serialized_end=1054
  _globals['_UNPINMODELREQUEST']._serialized_start=1056
  _globals['_UNPINMODELREQUEST']._serialized_end=1123
  _globals['_GETNEXTMODELSTATEREQUEST']._serialized_start=1125
  _globals['_GETNEXTMODELSTATEREQUEST']._serialized_end=1222
  _globals['_GETNEXTMODELSTATERESPONSE']._serialized_start=1225
  _globals['_GETNEXTMODELSTATERESPONSE']._serialized_end=1399
  _globals['_DOWNLOADMODELREQUEST']._serialized_start=1401
  _globals['_DOWNLOADMODELREQUEST']._serialized_end=1441
  _globals['_MODELHISTORYREQUEST']._serialized_start=1444
  _globals['_MODELHISTORYREQUEST']._serialized_end=1689
  _globals['_MODELEVENT']._serialized_start=1692
  _globals['_MODELEVENT']._serialized_end=1855
  _globals['_MODELEVENTTYPEMATCHER']._serialized_start=1858
  _globals['_MODELEVENTTYPEMATCHER']._serialized_end=2086
  _globals['_MODELHISTORYRESPONSE']._serialized_start=2088
  _globals['_MODELHISTORYRESPONSE']._serialized_end=2197
  _globals['_GETMODELNICKNAMESREQUEST']._serialized_start=2199
  _globals['_GETMODELNICKNAMESREQUEST']._serialized_end=2280
  _globals['_GETMODELNICKNAMESRESPONSE']._serialized_start=2283
  _globals['_GETMODELNICKNAMESRESPONSE']._serialized_end=2496
  _globals['_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY']._serialized_start=2443
  _globals['_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY']._serialized_end=2496
  _globals['_SETMODELNICKNAMEREQUEST']._serialized_start=2498
  _globals['_SETMODELNICKNAMEREQUEST']._serialized_end=2565
  _globals['_CROPMODELPAIR']._serialized_start=2567
  _globals['_CROPMODELPAIR']._serialized_end=2617
  _globals['_REFRESHDEFAULTMODELPARAMETERSREQUEST']._serialized_start=2619
  _globals['_REFRESHDEFAULTMODELPARAMETERSREQUEST']._serialized_end=2719
  _globals['_SYNCCROPIDSREQUEST']._serialized_start=2721
  _globals['_SYNCCROPIDSREQUEST']._serialized_end=2770
  _globals['_MODELSERVICE']._serialized_start=2773
  _globals['_MODELSERVICE']._serialized_end=4503
# @@protoc_insertion_point(module_scope)
