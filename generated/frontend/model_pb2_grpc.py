# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import model_pb2 as frontend_dot_model__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/model_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ModelServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.PinModel = channel.unary_unary(
                '/carbon.frontend.model.ModelService/PinModel',
                request_serializer=frontend_dot_model__pb2.PinModelRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.UnpinModel = channel.unary_unary(
                '/carbon.frontend.model.ModelService/UnpinModel',
                request_serializer=frontend_dot_model__pb2.UnpinModelRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextModelState = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetNextModelState',
                request_serializer=frontend_dot_model__pb2.GetNextModelStateRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.GetNextModelStateResponse.FromString,
                _registered_method=True)
        self.GetNextAllModelState = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetNextAllModelState',
                request_serializer=frontend_dot_model__pb2.GetNextModelStateRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.GetNextModelStateResponse.FromString,
                _registered_method=True)
        self.UpdateModel = channel.unary_unary(
                '/carbon.frontend.model.ModelService/UpdateModel',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ListEnabledCrops = channel.unary_unary(
                '/carbon.frontend.model.ModelService/ListEnabledCrops',
                request_serializer=frontend_dot_model__pb2.ListCropParameters.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.EnabledCropList.FromString,
                _registered_method=True)
        self.ListCaptureCrops = channel.unary_unary(
                '/carbon.frontend.model.ModelService/ListCaptureCrops',
                request_serializer=frontend_dot_model__pb2.ListCropParameters.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.EnabledCropList.FromString,
                _registered_method=True)
        self.GetNextSelectedCropID = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetNextSelectedCropID',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.GetNextSelectedCropIDResponse.FromString,
                _registered_method=True)
        self.SelectCrop = channel.unary_unary(
                '/carbon.frontend.model.ModelService/SelectCrop',
                request_serializer=frontend_dot_model__pb2.SelectCropRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.DownloadModel = channel.unary_unary(
                '/carbon.frontend.model.ModelService/DownloadModel',
                request_serializer=frontend_dot_model__pb2.DownloadModelRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextModelHistory = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetNextModelHistory',
                request_serializer=frontend_dot_model__pb2.ModelHistoryRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.ModelHistoryResponse.FromString,
                _registered_method=True)
        self.GetModelHistory = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetModelHistory',
                request_serializer=frontend_dot_model__pb2.ModelHistoryRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.ModelHistoryResponse.FromString,
                _registered_method=True)
        self.GetModelNicknames = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetModelNicknames',
                request_serializer=frontend_dot_model__pb2.GetModelNicknamesRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.GetModelNicknamesResponse.FromString,
                _registered_method=True)
        self.GetNextModelNicknames = channel.unary_unary(
                '/carbon.frontend.model.ModelService/GetNextModelNicknames',
                request_serializer=frontend_dot_model__pb2.GetModelNicknamesRequest.SerializeToString,
                response_deserializer=frontend_dot_model__pb2.GetModelNicknamesResponse.FromString,
                _registered_method=True)
        self.SetModelNickname = channel.unary_unary(
                '/carbon.frontend.model.ModelService/SetModelNickname',
                request_serializer=frontend_dot_model__pb2.SetModelNicknameRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.RefreshDefaultModelParameters = channel.unary_unary(
                '/carbon.frontend.model.ModelService/RefreshDefaultModelParameters',
                request_serializer=frontend_dot_model__pb2.RefreshDefaultModelParametersRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.SyncCropIDs = channel.unary_unary(
                '/carbon.frontend.model.ModelService/SyncCropIDs',
                request_serializer=frontend_dot_model__pb2.SyncCropIDsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.TriggerDownload = channel.unary_unary(
                '/carbon.frontend.model.ModelService/TriggerDownload',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class ModelServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def PinModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UnpinModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextModelState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAllModelState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListEnabledCrops(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListCaptureCrops(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextSelectedCropID(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SelectCrop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DownloadModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextModelHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModelNicknames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextModelNicknames(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetModelNickname(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RefreshDefaultModelParameters(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SyncCropIDs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TriggerDownload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModelServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'PinModel': grpc.unary_unary_rpc_method_handler(
                    servicer.PinModel,
                    request_deserializer=frontend_dot_model__pb2.PinModelRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'UnpinModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UnpinModel,
                    request_deserializer=frontend_dot_model__pb2.UnpinModelRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextModelState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModelState,
                    request_deserializer=frontend_dot_model__pb2.GetNextModelStateRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.GetNextModelStateResponse.SerializeToString,
            ),
            'GetNextAllModelState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAllModelState,
                    request_deserializer=frontend_dot_model__pb2.GetNextModelStateRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.GetNextModelStateResponse.SerializeToString,
            ),
            'UpdateModel': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateModel,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ListEnabledCrops': grpc.unary_unary_rpc_method_handler(
                    servicer.ListEnabledCrops,
                    request_deserializer=frontend_dot_model__pb2.ListCropParameters.FromString,
                    response_serializer=frontend_dot_model__pb2.EnabledCropList.SerializeToString,
            ),
            'ListCaptureCrops': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCaptureCrops,
                    request_deserializer=frontend_dot_model__pb2.ListCropParameters.FromString,
                    response_serializer=frontend_dot_model__pb2.EnabledCropList.SerializeToString,
            ),
            'GetNextSelectedCropID': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextSelectedCropID,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_model__pb2.GetNextSelectedCropIDResponse.SerializeToString,
            ),
            'SelectCrop': grpc.unary_unary_rpc_method_handler(
                    servicer.SelectCrop,
                    request_deserializer=frontend_dot_model__pb2.SelectCropRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'DownloadModel': grpc.unary_unary_rpc_method_handler(
                    servicer.DownloadModel,
                    request_deserializer=frontend_dot_model__pb2.DownloadModelRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextModelHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModelHistory,
                    request_deserializer=frontend_dot_model__pb2.ModelHistoryRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.ModelHistoryResponse.SerializeToString,
            ),
            'GetModelHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelHistory,
                    request_deserializer=frontend_dot_model__pb2.ModelHistoryRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.ModelHistoryResponse.SerializeToString,
            ),
            'GetModelNicknames': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModelNicknames,
                    request_deserializer=frontend_dot_model__pb2.GetModelNicknamesRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.GetModelNicknamesResponse.SerializeToString,
            ),
            'GetNextModelNicknames': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModelNicknames,
                    request_deserializer=frontend_dot_model__pb2.GetModelNicknamesRequest.FromString,
                    response_serializer=frontend_dot_model__pb2.GetModelNicknamesResponse.SerializeToString,
            ),
            'SetModelNickname': grpc.unary_unary_rpc_method_handler(
                    servicer.SetModelNickname,
                    request_deserializer=frontend_dot_model__pb2.SetModelNicknameRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'RefreshDefaultModelParameters': grpc.unary_unary_rpc_method_handler(
                    servicer.RefreshDefaultModelParameters,
                    request_deserializer=frontend_dot_model__pb2.RefreshDefaultModelParametersRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'SyncCropIDs': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncCropIDs,
                    request_deserializer=frontend_dot_model__pb2.SyncCropIDsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'TriggerDownload': grpc.unary_unary_rpc_method_handler(
                    servicer.TriggerDownload,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.model.ModelService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.model.ModelService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModelService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def PinModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/PinModel',
            frontend_dot_model__pb2.PinModelRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UnpinModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/UnpinModel',
            frontend_dot_model__pb2.UnpinModelRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextModelState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetNextModelState',
            frontend_dot_model__pb2.GetNextModelStateRequest.SerializeToString,
            frontend_dot_model__pb2.GetNextModelStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextAllModelState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetNextAllModelState',
            frontend_dot_model__pb2.GetNextModelStateRequest.SerializeToString,
            frontend_dot_model__pb2.GetNextModelStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/UpdateModel',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListEnabledCrops(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/ListEnabledCrops',
            frontend_dot_model__pb2.ListCropParameters.SerializeToString,
            frontend_dot_model__pb2.EnabledCropList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListCaptureCrops(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/ListCaptureCrops',
            frontend_dot_model__pb2.ListCropParameters.SerializeToString,
            frontend_dot_model__pb2.EnabledCropList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextSelectedCropID(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetNextSelectedCropID',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_model__pb2.GetNextSelectedCropIDResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SelectCrop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/SelectCrop',
            frontend_dot_model__pb2.SelectCropRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DownloadModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/DownloadModel',
            frontend_dot_model__pb2.DownloadModelRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextModelHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetNextModelHistory',
            frontend_dot_model__pb2.ModelHistoryRequest.SerializeToString,
            frontend_dot_model__pb2.ModelHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModelHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetModelHistory',
            frontend_dot_model__pb2.ModelHistoryRequest.SerializeToString,
            frontend_dot_model__pb2.ModelHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModelNicknames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetModelNicknames',
            frontend_dot_model__pb2.GetModelNicknamesRequest.SerializeToString,
            frontend_dot_model__pb2.GetModelNicknamesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextModelNicknames(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/GetNextModelNicknames',
            frontend_dot_model__pb2.GetModelNicknamesRequest.SerializeToString,
            frontend_dot_model__pb2.GetModelNicknamesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetModelNickname(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/SetModelNickname',
            frontend_dot_model__pb2.SetModelNicknameRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RefreshDefaultModelParameters(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/RefreshDefaultModelParameters',
            frontend_dot_model__pb2.RefreshDefaultModelParametersRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SyncCropIDs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/SyncCropIDs',
            frontend_dot_model__pb2.SyncCropIDsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TriggerDownload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.model.ModelService/TriggerDownload',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
