# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/module.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/module.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x66rontend/module.proto\x12\x16\x63\x61rbon.frontend.module\x1a\x0futil/util.proto\",\n\x0eModuleIdentity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06serial\x18\x02 \x01(\t\"?\n\x19GetNextModulesListRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x8c\x02\n\x1aGetNextModulesListResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12@\n\x10\x61ssigned_modules\x18\x02 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x42\n\x12unassigned_modules\x18\x03 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x44\n\x14unset_serial_modules\x18\x04 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"X\n\x15IdentifyModuleRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"V\n\x13\x41ssignModuleRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"_\n\x1c\x43learModuleAssignmentRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"m\n\x16SetModuleSerialRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x12\n\nnew_serial\x18\x02 \x01(\t\"R\n\x10ModuleDefinition\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x19\n\x11module_spacing_mm\x18\x02 \x01(\x02\x12\x10\n\x08\x64isabled\x18\x03 \x01(\x08\"r\n\rRowDefinition\x12\x0e\n\x06row_id\x18\x01 \x01(\r\x12\x39\n\x07modules\x18\x02 \x03(\x0b\x32(.carbon.frontend.module.ModuleDefinition\x12\x16\n\x0erow_spacing_mm\x18\x03 \x01(\x02\"7\n\rBarDefinition\x12\x15\n\rbar_length_mm\x18\x01 \x01(\r\x12\x0f\n\x07\x66olding\x18\x02 \x01(\x08\"\x85\x01\n\x0fRobotDefinition\x12\x33\n\x04rows\x18\x01 \x03(\x0b\x32%.carbon.frontend.module.RowDefinition\x12=\n\x0e\x62\x61r_definition\x18\x03 \x01(\x0b\x32%.carbon.frontend.module.BarDefinition\"i\n\x06Preset\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12;\n\ndefinition\x18\x03 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition\")\n\x15GetPresetsListRequest\x12\x10\n\x08language\x18\x01 \x01(\t\"I\n\x16GetPresetsListResponse\x12/\n\x07presets\x18\x01 \x03(\x0b\x32\x1e.carbon.frontend.module.Preset\"\x84\x01\n!GetCurrentRobotDefinitionResponse\x12H\n\x12\x63urrent_definition\x18\x01 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinitionH\x00\x88\x01\x01\x42\x15\n\x13_current_definition\"g\n SetCurrentRobotDefinitionRequest\x12\x43\n\x12\x63urrent_definition\x18\x01 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition\"D\n\x1eGetNextValidationStatusRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x8f\x02\n\x1fGetNextValidationStatusResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x36\n\x06module\x18\x02 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x38\n\x06status\x18\x03 \x01(\x0e\x32(.carbon.frontend.module.ValidationStatus\x12\x11\n\tstep_name\x18\x04 \x01(\t\x12\x19\n\x11state_description\x18\x05 \x01(\t\x12\x13\n\x0bstep_number\x18\x06 \x01(\x05\x12\x13\n\x0btotal_steps\x18\x07 \x01(\x05\"P\n\x16StartValidationRequest\x12\x36\n\x06module\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"D\n\x1c\x43onfirmValidationStepRequest\x12\x13\n\x0bstep_number\x18\x01 \x01(\x05\x12\x0f\n\x07success\x18\x02 \x01(\x08*O\n\x10ValidationStatus\x12\t\n\x05READY\x10\x00\x12\x0b\n\x07RUNNING\x10\x01\x12\x0b\n\x07WAITING\x10\x02\x12\n\n\x06\x46\x41ILED\x10\x03\x12\n\n\x06PASSED\x10\x04\x32\xbe\x06\n\x17ModuleAssignmentService\x12{\n\x12GetNextModulesList\x12\x31.carbon.frontend.module.GetNextModulesListRequest\x1a\x32.carbon.frontend.module.GetNextModulesListResponse\x12S\n\x0eIdentifyModule\x12-.carbon.frontend.module.IdentifyModuleRequest\x1a\x12.carbon.util.Empty\x12O\n\x0c\x41ssignModule\x12+.carbon.frontend.module.AssignModuleRequest\x1a\x12.carbon.util.Empty\x12\x61\n\x15\x43learModuleAssignment\x12\x34.carbon.frontend.module.ClearModuleAssignmentRequest\x1a\x12.carbon.util.Empty\x12U\n\x0fSetModuleSerial\x12..carbon.frontend.module.SetModuleSerialRequest\x1a\x12.carbon.util.Empty\x12o\n\x0eGetPresetsList\x12-.carbon.frontend.module.GetPresetsListRequest\x1a..carbon.frontend.module.GetPresetsListResponse\x12j\n\x19GetCurrentRobotDefinition\x12\x12.carbon.util.Empty\x1a\x39.carbon.frontend.module.GetCurrentRobotDefinitionResponse\x12i\n\x19SetCurrentRobotDefinition\x12\x38.carbon.frontend.module.SetCurrentRobotDefinitionRequest\x1a\x12.carbon.util.Empty2\xe0\x02\n\x17ModuleValidationService\x12\x8a\x01\n\x17GetNextValidationStatus\x12\x36.carbon.frontend.module.GetNextValidationStatusRequest\x1a\x37.carbon.frontend.module.GetNextValidationStatusResponse\x12U\n\x0fStartValidation\x12..carbon.frontend.module.StartValidationRequest\x1a\x12.carbon.util.Empty\x12\x61\n\x15\x43onfirmValidationStep\x12\x34.carbon.frontend.module.ConfirmValidationStepRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.module_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_VALIDATIONSTATUS']._serialized_start=2188
  _globals['_VALIDATIONSTATUS']._serialized_end=2267
  _globals['_MODULEIDENTITY']._serialized_start=66
  _globals['_MODULEIDENTITY']._serialized_end=110
  _globals['_GETNEXTMODULESLISTREQUEST']._serialized_start=112
  _globals['_GETNEXTMODULESLISTREQUEST']._serialized_end=175
  _globals['_GETNEXTMODULESLISTRESPONSE']._serialized_start=178
  _globals['_GETNEXTMODULESLISTRESPONSE']._serialized_end=446
  _globals['_IDENTIFYMODULEREQUEST']._serialized_start=448
  _globals['_IDENTIFYMODULEREQUEST']._serialized_end=536
  _globals['_ASSIGNMODULEREQUEST']._serialized_start=538
  _globals['_ASSIGNMODULEREQUEST']._serialized_end=624
  _globals['_CLEARMODULEASSIGNMENTREQUEST']._serialized_start=626
  _globals['_CLEARMODULEASSIGNMENTREQUEST']._serialized_end=721
  _globals['_SETMODULESERIALREQUEST']._serialized_start=723
  _globals['_SETMODULESERIALREQUEST']._serialized_end=832
  _globals['_MODULEDEFINITION']._serialized_start=834
  _globals['_MODULEDEFINITION']._serialized_end=916
  _globals['_ROWDEFINITION']._serialized_start=918
  _globals['_ROWDEFINITION']._serialized_end=1032
  _globals['_BARDEFINITION']._serialized_start=1034
  _globals['_BARDEFINITION']._serialized_end=1089
  _globals['_ROBOTDEFINITION']._serialized_start=1092
  _globals['_ROBOTDEFINITION']._serialized_end=1225
  _globals['_PRESET']._serialized_start=1227
  _globals['_PRESET']._serialized_end=1332
  _globals['_GETPRESETSLISTREQUEST']._serialized_start=1334
  _globals['_GETPRESETSLISTREQUEST']._serialized_end=1375
  _globals['_GETPRESETSLISTRESPONSE']._serialized_start=1377
  _globals['_GETPRESETSLISTRESPONSE']._serialized_end=1450
  _globals['_GETCURRENTROBOTDEFINITIONRESPONSE']._serialized_start=1453
  _globals['_GETCURRENTROBOTDEFINITIONRESPONSE']._serialized_end=1585
  _globals['_SETCURRENTROBOTDEFINITIONREQUEST']._serialized_start=1587
  _globals['_SETCURRENTROBOTDEFINITIONREQUEST']._serialized_end=1690
  _globals['_GETNEXTVALIDATIONSTATUSREQUEST']._serialized_start=1692
  _globals['_GETNEXTVALIDATIONSTATUSREQUEST']._serialized_end=1760
  _globals['_GETNEXTVALIDATIONSTATUSRESPONSE']._serialized_start=1763
  _globals['_GETNEXTVALIDATIONSTATUSRESPONSE']._serialized_end=2034
  _globals['_STARTVALIDATIONREQUEST']._serialized_start=2036
  _globals['_STARTVALIDATIONREQUEST']._serialized_end=2116
  _globals['_CONFIRMVALIDATIONSTEPREQUEST']._serialized_start=2118
  _globals['_CONFIRMVALIDATIONSTEPREQUEST']._serialized_end=2186
  _globals['_MODULEASSIGNMENTSERVICE']._serialized_start=2270
  _globals['_MODULEASSIGNMENTSERVICE']._serialized_end=3100
  _globals['_MODULEVALIDATIONSERVICE']._serialized_start=3103
  _globals['_MODULEVALIDATIONSERVICE']._serialized_end=3455
# @@protoc_insertion_point(module_scope)
