# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import module_pb2 as frontend_dot_module__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/module_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ModuleAssignmentServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextModulesList = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList',
                request_serializer=frontend_dot_module__pb2.GetNextModulesListRequest.SerializeToString,
                response_deserializer=frontend_dot_module__pb2.GetNextModulesListResponse.FromString,
                _registered_method=True)
        self.IdentifyModule = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/IdentifyModule',
                request_serializer=frontend_dot_module__pb2.IdentifyModuleRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.AssignModule = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/AssignModule',
                request_serializer=frontend_dot_module__pb2.AssignModuleRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ClearModuleAssignment = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment',
                request_serializer=frontend_dot_module__pb2.ClearModuleAssignmentRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.SetModuleSerial = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial',
                request_serializer=frontend_dot_module__pb2.SetModuleSerialRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetPresetsList = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetPresetsList',
                request_serializer=frontend_dot_module__pb2.GetPresetsListRequest.SerializeToString,
                response_deserializer=frontend_dot_module__pb2.GetPresetsListResponse.FromString,
                _registered_method=True)
        self.GetCurrentRobotDefinition = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_module__pb2.GetCurrentRobotDefinitionResponse.FromString,
                _registered_method=True)
        self.SetCurrentRobotDefinition = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition',
                request_serializer=frontend_dot_module__pb2.SetCurrentRobotDefinitionRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class ModuleAssignmentServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextModulesList(self, request, context):
        """Module Identity 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IdentifyModule(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AssignModule(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearModuleAssignment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetModuleSerial(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPresetsList(self, request, context):
        """Robot Definition 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCurrentRobotDefinition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCurrentRobotDefinition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModuleAssignmentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextModulesList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModulesList,
                    request_deserializer=frontend_dot_module__pb2.GetNextModulesListRequest.FromString,
                    response_serializer=frontend_dot_module__pb2.GetNextModulesListResponse.SerializeToString,
            ),
            'IdentifyModule': grpc.unary_unary_rpc_method_handler(
                    servicer.IdentifyModule,
                    request_deserializer=frontend_dot_module__pb2.IdentifyModuleRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'AssignModule': grpc.unary_unary_rpc_method_handler(
                    servicer.AssignModule,
                    request_deserializer=frontend_dot_module__pb2.AssignModuleRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ClearModuleAssignment': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearModuleAssignment,
                    request_deserializer=frontend_dot_module__pb2.ClearModuleAssignmentRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetModuleSerial': grpc.unary_unary_rpc_method_handler(
                    servicer.SetModuleSerial,
                    request_deserializer=frontend_dot_module__pb2.SetModuleSerialRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPresetsList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPresetsList,
                    request_deserializer=frontend_dot_module__pb2.GetPresetsListRequest.FromString,
                    response_serializer=frontend_dot_module__pb2.GetPresetsListResponse.SerializeToString,
            ),
            'GetCurrentRobotDefinition': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCurrentRobotDefinition,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_module__pb2.GetCurrentRobotDefinitionResponse.SerializeToString,
            ),
            'SetCurrentRobotDefinition': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCurrentRobotDefinition,
                    request_deserializer=frontend_dot_module__pb2.SetCurrentRobotDefinitionRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.module.ModuleAssignmentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.module.ModuleAssignmentService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModuleAssignmentService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextModulesList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList',
            frontend_dot_module__pb2.GetNextModulesListRequest.SerializeToString,
            frontend_dot_module__pb2.GetNextModulesListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def IdentifyModule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/IdentifyModule',
            frontend_dot_module__pb2.IdentifyModuleRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AssignModule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/AssignModule',
            frontend_dot_module__pb2.AssignModuleRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ClearModuleAssignment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment',
            frontend_dot_module__pb2.ClearModuleAssignmentRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetModuleSerial(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial',
            frontend_dot_module__pb2.SetModuleSerialRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPresetsList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/GetPresetsList',
            frontend_dot_module__pb2.GetPresetsListRequest.SerializeToString,
            frontend_dot_module__pb2.GetPresetsListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCurrentRobotDefinition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_module__pb2.GetCurrentRobotDefinitionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCurrentRobotDefinition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition',
            frontend_dot_module__pb2.SetCurrentRobotDefinitionRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ModuleValidationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextValidationStatus = channel.unary_unary(
                '/carbon.frontend.module.ModuleValidationService/GetNextValidationStatus',
                request_serializer=frontend_dot_module__pb2.GetNextValidationStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_module__pb2.GetNextValidationStatusResponse.FromString,
                _registered_method=True)
        self.StartValidation = channel.unary_unary(
                '/carbon.frontend.module.ModuleValidationService/StartValidation',
                request_serializer=frontend_dot_module__pb2.StartValidationRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.ConfirmValidationStep = channel.unary_unary(
                '/carbon.frontend.module.ModuleValidationService/ConfirmValidationStep',
                request_serializer=frontend_dot_module__pb2.ConfirmValidationStepRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class ModuleValidationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextValidationStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartValidation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ConfirmValidationStep(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModuleValidationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextValidationStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextValidationStatus,
                    request_deserializer=frontend_dot_module__pb2.GetNextValidationStatusRequest.FromString,
                    response_serializer=frontend_dot_module__pb2.GetNextValidationStatusResponse.SerializeToString,
            ),
            'StartValidation': grpc.unary_unary_rpc_method_handler(
                    servicer.StartValidation,
                    request_deserializer=frontend_dot_module__pb2.StartValidationRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'ConfirmValidationStep': grpc.unary_unary_rpc_method_handler(
                    servicer.ConfirmValidationStep,
                    request_deserializer=frontend_dot_module__pb2.ConfirmValidationStepRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.module.ModuleValidationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.module.ModuleValidationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModuleValidationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextValidationStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleValidationService/GetNextValidationStatus',
            frontend_dot_module__pb2.GetNextValidationStatusRequest.SerializeToString,
            frontend_dot_module__pb2.GetNextValidationStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartValidation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleValidationService/StartValidation',
            frontend_dot_module__pb2.StartValidationRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ConfirmValidationStep(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.module.ModuleValidationService/ConfirmValidationStep',
            frontend_dot_module__pb2.ConfirmValidationStepRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
