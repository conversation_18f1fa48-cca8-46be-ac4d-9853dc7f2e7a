# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/plant_captcha.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/plant_captcha.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2
from generated.almanac import almanac_pb2 as almanac_dot_almanac__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1c\x66rontend/plant_captcha.proto\x12\x1d\x63\x61rbon.frontend.plant_captcha\x1a\x0futil/util.proto\x1a!weed_tracking/weed_tracking.proto\x1a\x15\x61lmanac/almanac.proto\"|\n\x0cPlantCaptcha\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x03 \x01(\t\x12\x11\n\tcrop_name\x18\x04 \x01(\t\x12\x15\n\rstart_time_ms\x18\x05 \x01(\x03\x12\x11\n\trows_used\x18\x06 \x03(\x05\"^\n\x18StartPlantCaptchaRequest\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\"\x1b\n\x19StartPlantCaptchaResponse\"F\n GetNextPlantCaptchaStatusRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\xbe\x01\n!GetNextPlantCaptchaStatusResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x31\n\x06status\x18\x02 \x01(\x0e\x32!.weed_tracking.PlantCaptchaStatus\x12\x14\n\x0ctotal_images\x18\x03 \x01(\x05\x12\x14\n\x0cimages_taken\x18\x04 \x01(\x05\x12\x16\n\x0emetadata_taken\x18\x05 \x01(\x05\"E\n\x1fGetNextPlantCaptchasListRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\x8a\x01\n\x14PlantCaptchaListItem\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\x12\x14\n\x0cimages_taken\x18\x02 \x01(\x05\x12\x18\n\x10images_processed\x18\x03 \x01(\x05\"\x93\x01\n GetNextPlantCaptchasListResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12K\n\x0eplant_captchas\x18\x02 \x03(\x0b\x32\x33.carbon.frontend.plant_captcha.PlantCaptchaListItem\")\n\x19\x44\x65letePlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"&\n\x16GetPlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xc6\x01\n\x10PlantCaptchaItem\x12\x11\n\timage_url\x18\x01 \x01(\t\x12\x39\n\x08metadata\x18\x02 \x01(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\x12\x1d\n\x15\x61\x64\x64itional_image_urls\x18\x03 \x03(\t\x12\x45\n\x14\x61\x64\x64itional_metadatas\x18\x04 \x03(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\"\x9d\x01\n\x17GetPlantCaptchaResponse\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\x12>\n\x05items\x18\x02 \x03(\x0b\x32/.carbon.frontend.plant_captcha.PlantCaptchaItem\".\n\x1eStartPlantCaptchaUploadRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"Y\n%GetNextPlantCaptchaUploadStateRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xab\x01\n&GetNextPlantCaptchaUploadStateResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12L\n\x0cupload_state\x18\x02 \x01(\x0e\x32\x36.carbon.frontend.plant_captcha.PlantCaptchaUploadState\x12\x0f\n\x07percent\x18\x03 \x01(\x05\"h\n\x16PlantCaptchaItemResult\x12\n\n\x02id\x18\x01 \x01(\t\x12\x42\n\x0fuser_prediction\x18\x02 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\"x\n SubmitPlantCaptchaResultsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x46\n\x07results\x18\x02 \x03(\x0b\x32\x35.carbon.frontend.plant_captcha.PlantCaptchaItemResult\"=\n!GetPlantCaptchaItemResultsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x03(\t\"l\n\"GetPlantCaptchaItemResultsResponse\x12\x46\n\x07results\x18\x01 \x03(\x0b\x32\x35.carbon.frontend.plant_captcha.PlantCaptchaItemResult\",\n\x1c\x43\x61lculatePlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xd1\x01\n\x1d\x43\x61lculatePlantCaptchaResponse\x12\x44\n\x12modelinator_config\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12\x11\n\tsucceeded\x18\x02 \x01(\x08\x12W\n\x0e\x66\x61ilure_reason\x18\x03 \x01(\x0e\x32?.carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason\"\x89\x01\n\x12PlantCaptchaResult\x12\x38\n\x05label\x18\x01 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\x12\x39\n\x08metadata\x18\x02 \x01(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\"\x88\x06\n\x13PlantCaptchaResults\x12\x44\n\x12\x63urrent_parameters\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12J\n\x0f\x63\x61ptcha_results\x18\x02 \x03(\x0b\x32\x31.carbon.frontend.plant_captcha.PlantCaptchaResult\x12\x11\n\talgorithm\x18\x03 \x01(\t\x12\x1b\n\x13goal_crops_targeted\x18\x04 \x01(\x02\x12\x1b\n\x13goal_weeds_targeted\x18\x05 \x01(\x02\x12\x1d\n\x15goal_unknown_targeted\x18\x06 \x01(\x02\x12\x35\n\x07\x61lmanac\x18\x07 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\x12\x1e\n\x16max_recommended_mindoo\x18\x08 \x01(\x02\x12$\n\x1cmin_items_for_recommendation\x18\t \x01(\x05\x12+\n#use_weed_categories_for_weed_labels\x18\n \x01(\x08\x12\x1e\n\x16min_recommended_mindoo\x18\x0b \x01(\x02\x12&\n\x1emin_recommended_weed_threshold\x18\x0c \x01(\x02\x12&\n\x1emax_recommended_weed_threshold\x18\r \x01(\x02\x12&\n\x1emin_recommended_crop_threshold\x18\x0e \x01(\x02\x12&\n\x1emax_recommended_crop_threshold\x18\x0f \x01(\x02\x12\"\n\x1amin_doo_for_recommendation\x18\x10 \x01(\x02\x12\x1f\n\x17use_other_as_tiebreaker\x18\x11 \x01(\x08\x12\x1d\n\x15limit_by_crops_missed\x18\x12 \x01(\x08\x12%\n\x1dnumber_of_crop_configurations\x18\x13 \x01(\x05\"x\n\x1bVeselkaPlantCaptchaResponse\x12\x46\n\x14new_model_parameters\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12\x11\n\tsucceeded\x18\x02 \x01(\x08\"3\n#GetOriginalModelinatorConfigRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"l\n$GetOriginalModelinatorConfigResponse\x12\x44\n\x12modelinator_config\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig*>\n\x17PlantCaptchaUploadState\x12\x08\n\x04NONE\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x08\n\x04\x44ONE\x10\x02*]\n PlantLabelAlgorithmFailureReason\x12\x0e\n\nNO_FAILURE\x10\x00\x12\x13\n\x0fMETRICS_NOT_MET\x10\x01\x12\x14\n\x10NOT_ENOUGH_ITEMS\x10\x02\x32\xf5\x0c\n\x13PlantCaptchaService\x12\x86\x01\n\x11StartPlantCaptcha\x12\x37.carbon.frontend.plant_captcha.StartPlantCaptchaRequest\x1a\x38.carbon.frontend.plant_captcha.StartPlantCaptchaResponse\x12\x9e\x01\n\x19GetNextPlantCaptchaStatus\x12?.carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest\<EMAIL>.plant_captcha.GetNextPlantCaptchaStatusResponse\x12\x9b\x01\n\x18GetNextPlantCaptchasList\x12>.carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest\x1a?.carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse\x12\x62\n\x12\x44\x65letePlantCaptcha\x12\x38.carbon.frontend.plant_captcha.DeletePlantCaptchaRequest\x1a\x12.carbon.util.Empty\x12\x80\x01\n\x0fGetPlantCaptcha\x12\x35.carbon.frontend.plant_captcha.GetPlantCaptchaRequest\x1a\x36.carbon.frontend.plant_captcha.GetPlantCaptchaResponse\x12<\n\x12\x43\x61ncelPlantCaptcha\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12l\n\x17StartPlantCaptchaUpload\x12=.carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest\x1a\x12.carbon.util.Empty\x12\xad\x01\n\x1eGetNextPlantCaptchaUploadState\x12\x44.carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest\x1a\x45.carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse\x12p\n\x19SubmitPlantCaptchaResults\x12?.carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest\x1a\x12.carbon.util.Empty\x12\xa1\x01\n\x1aGetPlantCaptchaItemResults\<EMAIL>.plant_captcha.GetPlantCaptchaItemResultsRequest\x1a\x41.carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse\x12\x92\x01\n\x15\x43\x61lculatePlantCaptcha\x12;.carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest\x1a<.carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse\x12\xa7\x01\n\x1cGetOriginalModelinatorConfig\x12\x42.carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.plant_captcha_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_PLANTCAPTCHAUPLOADSTATE']._serialized_start=3636
  _globals['_PLANTCAPTCHAUPLOADSTATE']._serialized_end=3698
  _globals['_PLANTLABELALGORITHMFAILUREREASON']._serialized_start=3700
  _globals['_PLANTLABELALGORITHMFAILUREREASON']._serialized_end=3793
  _globals['_PLANTCAPTCHA']._serialized_start=138
  _globals['_PLANTCAPTCHA']._serialized_end=262
  _globals['_STARTPLANTCAPTCHAREQUEST']._serialized_start=264
  _globals['_STARTPLANTCAPTCHAREQUEST']._serialized_end=358
  _globals['_STARTPLANTCAPTCHARESPONSE']._serialized_start=360
  _globals['_STARTPLANTCAPTCHARESPONSE']._serialized_end=387
  _globals['_GETNEXTPLANTCAPTCHASTATUSREQUEST']._serialized_start=389
  _globals['_GETNEXTPLANTCAPTCHASTATUSREQUEST']._serialized_end=459
  _globals['_GETNEXTPLANTCAPTCHASTATUSRESPONSE']._serialized_start=462
  _globals['_GETNEXTPLANTCAPTCHASTATUSRESPONSE']._serialized_end=652
  _globals['_GETNEXTPLANTCAPTCHASLISTREQUEST']._serialized_start=654
  _globals['_GETNEXTPLANTCAPTCHASLISTREQUEST']._serialized_end=723
  _globals['_PLANTCAPTCHALISTITEM']._serialized_start=726
  _globals['_PLANTCAPTCHALISTITEM']._serialized_end=864
  _globals['_GETNEXTPLANTCAPTCHASLISTRESPONSE']._serialized_start=867
  _globals['_GETNEXTPLANTCAPTCHASLISTRESPONSE']._serialized_end=1014
  _globals['_DELETEPLANTCAPTCHAREQUEST']._serialized_start=1016
  _globals['_DELETEPLANTCAPTCHAREQUEST']._serialized_end=1057
  _globals['_GETPLANTCAPTCHAREQUEST']._serialized_start=1059
  _globals['_GETPLANTCAPTCHAREQUEST']._serialized_end=1097
  _globals['_PLANTCAPTCHAITEM']._serialized_start=1100
  _globals['_PLANTCAPTCHAITEM']._serialized_end=1298
  _globals['_GETPLANTCAPTCHARESPONSE']._serialized_start=1301
  _globals['_GETPLANTCAPTCHARESPONSE']._serialized_end=1458
  _globals['_STARTPLANTCAPTCHAUPLOADREQUEST']._serialized_start=1460
  _globals['_STARTPLANTCAPTCHAUPLOADREQUEST']._serialized_end=1506
  _globals['_GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST']._serialized_start=1508
  _globals['_GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST']._serialized_end=1597
  _globals['_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE']._serialized_start=1600
  _globals['_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE']._serialized_end=1771
  _globals['_PLANTCAPTCHAITEMRESULT']._serialized_start=1773
  _globals['_PLANTCAPTCHAITEMRESULT']._serialized_end=1877
  _globals['_SUBMITPLANTCAPTCHARESULTSREQUEST']._serialized_start=1879
  _globals['_SUBMITPLANTCAPTCHARESULTSREQUEST']._serialized_end=1999
  _globals['_GETPLANTCAPTCHAITEMRESULTSREQUEST']._serialized_start=2001
  _globals['_GETPLANTCAPTCHAITEMRESULTSREQUEST']._serialized_end=2062
  _globals['_GETPLANTCAPTCHAITEMRESULTSRESPONSE']._serialized_start=2064
  _globals['_GETPLANTCAPTCHAITEMRESULTSRESPONSE']._serialized_end=2172
  _globals['_CALCULATEPLANTCAPTCHAREQUEST']._serialized_start=2174
  _globals['_CALCULATEPLANTCAPTCHAREQUEST']._serialized_end=2218
  _globals['_CALCULATEPLANTCAPTCHARESPONSE']._serialized_start=2221
  _globals['_CALCULATEPLANTCAPTCHARESPONSE']._serialized_end=2430
  _globals['_PLANTCAPTCHARESULT']._serialized_start=2433
  _globals['_PLANTCAPTCHARESULT']._serialized_end=2570
  _globals['_PLANTCAPTCHARESULTS']._serialized_start=2573
  _globals['_PLANTCAPTCHARESULTS']._serialized_end=3349
  _globals['_VESELKAPLANTCAPTCHARESPONSE']._serialized_start=3351
  _globals['_VESELKAPLANTCAPTCHARESPONSE']._serialized_end=3471
  _globals['_GETORIGINALMODELINATORCONFIGREQUEST']._serialized_start=3473
  _globals['_GETORIGINALMODELINATORCONFIGREQUEST']._serialized_end=3524
  _globals['_GETORIGINALMODELINATORCONFIGRESPONSE']._serialized_start=3526
  _globals['_GETORIGINALMODELINATORCONFIGRESPONSE']._serialized_end=3634
  _globals['_PLANTCAPTCHASERVICE']._serialized_start=3796
  _globals['_PLANTCAPTCHASERVICE']._serialized_end=5449
# @@protoc_insertion_point(module_scope)
