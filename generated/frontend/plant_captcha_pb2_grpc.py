# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import plant_captcha_pb2 as frontend_dot_plant__captcha__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/plant_captcha_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PlantCaptchaServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha',
                request_serializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaResponse.FromString,
                _registered_method=True)
        self.GetNextPlantCaptchaStatus = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus',
                request_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.FromString,
                _registered_method=True)
        self.GetNextPlantCaptchasList = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList',
                request_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.FromString,
                _registered_method=True)
        self.DeletePlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha',
                request_serializer=frontend_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha',
                request_serializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaResponse.FromString,
                _registered_method=True)
        self.CancelPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.StartPlantCaptchaUpload = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload',
                request_serializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextPlantCaptchaUploadState = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState',
                request_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.FromString,
                _registered_method=True)
        self.SubmitPlantCaptchaResults = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults',
                request_serializer=frontend_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetPlantCaptchaItemResults = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults',
                request_serializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.FromString,
                _registered_method=True)
        self.CalculatePlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha',
                request_serializer=frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.FromString,
                _registered_method=True)
        self.GetOriginalModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig',
                request_serializer=frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.FromString,
                _registered_method=True)


class PlantCaptchaServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchaStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchasList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartPlantCaptchaUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchaUploadState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubmitPlantCaptchaResults(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlantCaptchaItemResults(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CalculatePlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOriginalModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PlantCaptchaServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPlantCaptcha,
                    request_deserializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaResponse.SerializeToString,
            ),
            'GetNextPlantCaptchaStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchaStatus,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.SerializeToString,
            ),
            'GetNextPlantCaptchasList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchasList,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.SerializeToString,
            ),
            'DeletePlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePlantCaptcha,
                    request_deserializer=frontend_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlantCaptcha,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaResponse.SerializeToString,
            ),
            'CancelPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelPlantCaptcha,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartPlantCaptchaUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPlantCaptchaUpload,
                    request_deserializer=frontend_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextPlantCaptchaUploadState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchaUploadState,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.SerializeToString,
            ),
            'SubmitPlantCaptchaResults': grpc.unary_unary_rpc_method_handler(
                    servicer.SubmitPlantCaptchaResults,
                    request_deserializer=frontend_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPlantCaptchaItemResults': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlantCaptchaItemResults,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.SerializeToString,
            ),
            'CalculatePlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculatePlantCaptcha,
                    request_deserializer=frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.SerializeToString,
            ),
            'GetOriginalModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOriginalModelinatorConfig,
                    request_deserializer=frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.FromString,
                    response_serializer=frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.plant_captcha.PlantCaptchaService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.plant_captcha.PlantCaptchaService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PlantCaptchaService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha',
            frontend_dot_plant__captcha__pb2.StartPlantCaptchaRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.StartPlantCaptchaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextPlantCaptchaStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus',
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextPlantCaptchasList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList',
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha',
            frontend_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha',
            frontend_dot_plant__captcha__pb2.GetPlantCaptchaRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetPlantCaptchaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartPlantCaptchaUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload',
            frontend_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextPlantCaptchaUploadState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState',
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SubmitPlantCaptchaResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults',
            frontend_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPlantCaptchaItemResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults',
            frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CalculatePlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha',
            frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOriginalModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig',
            frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.SerializeToString,
            frontend_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
