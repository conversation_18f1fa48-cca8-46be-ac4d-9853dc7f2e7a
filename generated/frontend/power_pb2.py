# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/power.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/power.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rontend/power.proto\x12\x15\x63\x61rbon.frontend.power\x1a\x0futil/util.proto\"\xdd\x02\n\x0c\x44\x65viceStatus\x12-\n\x06\x64\x65vice\x18\x01 \x01(\x0e\x32\x1d.carbon.frontend.power.Device\x12\r\n\x05label\x18\x02 \x01(\t\x12\x45\n\nrelay_type\x18\x06 \x01(\x0b\x32/.carbon.frontend.power.DeviceStatus.RelayStatusH\x00\x12G\n\x0bsensor_type\x18\x07 \x01(\x0b\x32\x30.carbon.frontend.power.DeviceStatus.SensorStatusH\x00\x1a\x1f\n\x0bRelayStatus\x12\x10\n\x08\x64isabled\x18\x03 \x01(\x08\x1aV\n\x0cSensorStatus\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x36\n\x05\x63olor\x18\x05 \x01(\x0e\x32\'.carbon.frontend.power.DeviceValueColorB\x06\n\x04type\"8\n\x12PowerStatusRequest\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"o\n\x13PowerStatusResponse\x12\x34\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32#.carbon.frontend.power.DeviceStatus\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\".\n\x0eValueWithRange\x12\r\n\x05value\x18\x01 \x01(\x01\x12\r\n\x05is_ok\x18\x02 \x01(\x08\"\xd0\x01\n\x17\x45nvironmentalSensorData\x12<\n\rtemperature_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12:\n\x0bhumidity_rh\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0cpressure_hpa\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\"\x8e\x01\n\x11\x43oolantSensorData\x12<\n\rtemperature_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0cpressure_kpa\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\"\xad\x01\n\x10NetworkPortState\x12\x0f\n\x07link_up\x18\x01 \x01(\x08\x12\x42\n\x11\x61\x63tual_link_speed\x18\x02 \x01(\x0e\x32\'.carbon.frontend.power.NetworkLinkSpeed\x12\x44\n\x13\x65xpected_link_speed\x18\x03 \x01(\x0e\x32\'.carbon.frontend.power.NetworkLinkSpeed\"\x97\t\n\x12ReaperPcSensorData\x12\x45\n\x16temperature_cpu_core_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x43\n\x14temperature_system_c\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12G\n\x13temperature_gpu_1_c\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x00\x88\x01\x01\x12G\n\x13temperature_gpu_2_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x01\x88\x01\x01\x12\x36\n\x07psu_12v\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x35\n\x06psu_5v\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x36\n\x07psu_3v3\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x33\n\x04load\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x0e\n\x06uptime\x18\t \x01(\r\x12@\n\x11ram_usage_percent\x18\n \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x64isk_usage_percent\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12?\n\x0escanner_a_link\x18\x0c \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12?\n\x0escanner_b_link\x18\r \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x42\n\x11target_cam_a_link\x18\x0e \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x42\n\x11target_cam_b_link\x18\x0f \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x41\n\x10predict_cam_link\x18\x10 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12:\n\tipmi_link\x18\x11 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x39\n\x08\x65xt_link\x18\x12 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortStateB\x16\n\x14_temperature_gpu_1_cB\x16\n\x14_temperature_gpu_2_c\"\x92\x02\n\x18ReaperScannerLaserStatus\x12\r\n\x05model\x18\x01 \x01(\t\x12\n\n\x02sn\x18\x02 \x01(\t\x12\x13\n\x0brated_power\x18\x03 \x01(\r\x12<\n\rtemperature_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x37\n\x08humidity\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12?\n\x10laser_current_ma\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x0e\n\x06\x66\x61ults\x18\x07 \x03(\t\"\x8d\x02\n\x16ReaperScannerMotorData\x12\x15\n\rcontroller_sn\x18\x01 \x01(\t\x12\x43\n\x14temperature_output_c\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0emotor_supply_v\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12>\n\x0fmotor_current_a\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x18\n\x10\x65ncoder_position\x18\x05 \x01(\x03\"\xce\x06\n\x17ReaperScannerSensorData\x12\x12\n\nscanner_sn\x18\x01 \x01(\t\x12\x38\n\tcurrent_a\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x14\n\x0c\x66use_tripped\x18\x03 \x01(\x08\x12G\n\x18temperature_collimator_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13temperature_fiber_c\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12<\n\rlaser_power_w\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x17\n\x0flaser_connected\x18\x07 \x01(\x08\x12J\n\x0claser_status\x18\x08 \x01(\x0b\x32/.carbon.frontend.power.ReaperScannerLaserStatusH\x00\x88\x01\x01\x12\x18\n\x10target_connected\x18\t \x01(\x08\x12\x16\n\ttarget_sn\x18\n \x01(\tH\x01\x88\x01\x01\x12H\n\x14temperature_target_c\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x02\x88\x01\x01\x12\x45\n\tmotor_pan\x18\x0c \x01(\x0b\x32-.carbon.frontend.power.ReaperScannerMotorDataH\x03\x88\x01\x01\x12\x46\n\nmotor_tilt\x18\r \x01(\x0b\x32-.carbon.frontend.power.ReaperScannerMotorDataH\x04\x88\x01\x01\x12\x1d\n\x15scanner_power_enabled\x18\x0e \x01(\x08\x12 \n\x18target_cam_power_enabled\x18\x0f \x01(\x08\x42\x0f\n\r_laser_statusB\x0c\n\n_target_snB\x17\n\x15_temperature_target_cB\x0c\n\n_motor_panB\r\n\x0b_motor_tilt\"E\n\rReaperGpsData\x12\x0f\n\x07has_fix\x18\x01 \x01(\x08\x12\x10\n\x08latitude\x18\x02 \x01(\x02\x12\x11\n\tlongitude\x18\x03 \x01(\x02\"A\n\x16ReaperWheelEncoderData\x12\x12\n\nfront_left\x18\x01 \x01(\x03\x12\x13\n\x0b\x66ront_right\x18\x02 \x01(\x03\"\x84\x0e\n\x19ReaperCenterEnclosureData\x12\x1c\n\x14water_protect_status\x18\x01 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x02 \x01(\x08\x12:\n\x0cpower_status\x18\x03 \x01(\x0e\x32$.carbon.frontend.power.AcPowerStatus\x12\x15\n\rlifted_status\x18\x04 \x01(\x08\x12 \n\x14temp_humidity_status\x18\x05 \x01(\x08\x42\x02\x18\x01\x12\x15\n\rtractor_power\x18\x06 \x01(\x08\x12;\n\x0c\x61\x63_frequency\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_a_b\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_b_c\x18\t \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_a_c\x18\n \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_a\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_b\x18\x0c \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_c\x18\r \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x17\n\x0fphase_power_w_3\x18\x0e \x01(\x03\x12\x18\n\x10phase_power_va_3\x18\x0f \x01(\x03\x12;\n\x0cpower_factor\x18\x10 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13server_cabinet_temp\x18\x11 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x46\n\x17server_cabinet_humidity\x18\x12 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13\x62\x61ttery_voltage_12v\x18\x13 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x1e\n\x16wheel_encoder_disabled\x18\x14 \x01(\x08\x12\x1b\n\x0fstrobe_disabled\x18\x15 \x01(\x08\x42\x02\x18\x01\x12\x14\n\x0cgps_disabled\x18\x16 \x01(\x08\x12\x1f\n\x17main_contactor_disabled\x18\x17 \x01(\x08\x12 \n\x18\x61ir_conditioner_disabled\x18\x18 \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18\x19 \x01(\x08\x12\x16\n\x0e\x63hiller_alarms\x18\x1a \x03(\t\x12=\n\x0e\x63hiller_temp_c\x18\x1b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x63hiller_flow_l_min\x18\x1c \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x43\n\x14\x63hiller_pressure_psi\x18\x1d \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_conductivity_us_cm\x18\x1e \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x63hiller_set_temp_c\x18\x1f \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_heat_transfer_kbtu\x18  \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_fluid_delta_temp_c\x18! \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x31\n\x03gps\x18\" \x01(\x0b\x32$.carbon.frontend.power.ReaperGpsData\x12\x44\n\rwheel_encoder\x18# \x01(\x0b\x32-.carbon.frontend.power.ReaperWheelEncoderData\"\x95\x07\n\x16ReaperModuleSensorData\x12\x11\n\tmodule_id\x18\x01 \x01(\x05\x12\x11\n\tmodule_sn\x18\x02 \x01(\t\x12H\n\x10\x65nviro_enclosure\x18\x03 \x01(\x0b\x32..carbon.frontend.power.EnvironmentalSensorData\x12\x41\n\tenviro_pc\x18\x04 \x01(\x0b\x32..carbon.frontend.power.EnvironmentalSensorData\x12?\n\rcoolant_inlet\x18\x05 \x01(\x0b\x32(.carbon.frontend.power.CoolantSensorData\x12@\n\x0e\x63oolant_outlet\x18\x06 \x01(\x0b\x32(.carbon.frontend.power.CoolantSensorData\x12\x43\n\x14strobe_temperature_c\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12strobe_cap_voltage\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0estrobe_current\x18\t \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12:\n\x02pc\x18\n \x01(\x0b\x32).carbon.frontend.power.ReaperPcSensorDataH\x00\x88\x01\x01\x12\x46\n\tscanner_a\x18\x0b \x01(\x0b\x32..carbon.frontend.power.ReaperScannerSensorDataH\x01\x88\x01\x01\x12\x46\n\tscanner_b\x18\x0c \x01(\x0b\x32..carbon.frontend.power.ReaperScannerSensorDataH\x02\x88\x01\x01\x12\x18\n\x10pc_power_enabled\x18\r \x01(\x08\x12\x1c\n\x14lasers_power_enabled\x18\x0e \x01(\x08\x12!\n\x19predict_cam_power_enabled\x18\x0f \x01(\x08\x12\x1c\n\x14strobe_power_enabled\x18\x10 \x01(\x08\x12\x16\n\x0estrobe_enabled\x18\x11 \x01(\x08\x42\x05\n\x03_pcB\x0c\n\n_scanner_aB\x0c\n\n_scanner_b\"\x1e\n\x1c\x43\x65nterEnclosureStatusRequest\"0\n\x1bModuleHardwareStatusRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\x05\"\xf8\x01\n\"GetNextReaperHardwareStatusRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12V\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x33.carbon.frontend.power.CenterEnclosureStatusRequestH\x00\x12K\n\rmodule_status\x18\x03 \x01(\x0b\x32\x32.carbon.frontend.power.ModuleHardwareStatusRequestH\x00\x42\t\n\x07request\"\xf2\x01\n#GetNextReaperHardwareStatusResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12S\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x30.carbon.frontend.power.ReaperCenterEnclosureDataH\x00\x12\x46\n\rmodule_status\x18\x03 \x01(\x0b\x32-.carbon.frontend.power.ReaperModuleSensorDataH\x00\x42\n\n\x08response\"K\n%GetNextReaperAllHardwareStatusRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\xe5\x01\n&GetNextReaperAllHardwareStatusResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12Q\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x30.carbon.frontend.power.ReaperCenterEnclosureData\x12\x44\n\rmodule_status\x18\x03 \x03(\x0b\x32-.carbon.frontend.power.ReaperModuleSensorData\"=\n\x0cRelayRequest\x12-\n\x06\x64\x65vice\x18\x01 \x01(\x0e\x32\x1d.carbon.frontend.power.Device\" \n\rRelayResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x95\x01\n\x1cSetReaperScannerPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1c\n\x0fscanner_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0fscanner_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x12\n\x10_scanner_a_powerB\x12\n\x10_scanner_b_power\"0\n\x1dSetReaperScannerPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x90\x01\n\x1bSetReaperTargetPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1b\n\x0etarget_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1b\n\x0etarget_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x11\n\x0f_target_a_powerB\x11\n\x0f_target_b_power\"/\n\x1cSetReaperTargetPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"E\n\x1fSetReaperPredictCamPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"3\n SetReaperPredictCamPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"B\n\x1cSetReaperStrobeEnableRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"0\n\x1dSetReaperStrobeEnableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"3\n SetReaperAllStrobesEnableRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"4\n!SetReaperAllStrobesEnableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"C\n\x1dSetReaperModulePcPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"1\n\x1eSetReaperModulePcPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"F\n SetReaperModuleLaserPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"4\n!SetReaperModuleLaserPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08*\xd1\x07\n\x06\x44\x65vice\x12\x11\n\rSENSOR_LIFTED\x10\x00\x12\x1d\n\x19SENSOR_SERVER_TEMPERATURE\x10\x01\x12\x1a\n\x16SENSOR_SERVER_HUMIDITY\x10\x02\x12\x10\n\x0cSENSOR_WATER\x10\x03\x12\x16\n\x12SENSOR_12V_BATTERY\x10\x04\x12\x18\n\x14SENSOR_POWER_QUALITY\x10\x05\x12\x12\n\x0eSENSOR_TRACTOR\x10\x06\x12\x17\n\x13SENSOR_AC_FREQUENCY\x10\x07\x12\x15\n\x11SENSOR_AB_VOLTAGE\x10\x08\x12\x15\n\x11SENSOR_BC_VOLTAGE\x10\t\x12\x15\n\x11SENSOR_AC_VOLTAGE\x10\n\x12\x14\n\x10SENSOR_A_CURRENT\x10\x0b\x12\x14\n\x10SENSOR_B_CURRENT\x10\x0c\x12\x14\n\x10SENSOR_C_CURRENT\x10\r\x12\x11\n\rRELAY_SUICIDE\x10\x0e\x12\x10\n\x0cRELAY_REBOOT\x10\x0f\x12\x0e\n\nRELAY_MAIN\x10\x10\x12\x0f\n\x0bRELAY_ROW_1\x10\x11\x12\x0f\n\x0bRELAY_ROW_2\x10\x12\x12\x0f\n\x0bRELAY_ROW_3\x10\x13\x12\x12\n\x0eRELAY_LIGHTS_1\x10\x14\x12\x12\n\x0eRELAY_LIGHTS_2\x10\x15\x12\x12\n\x0eRELAY_LIGHTS_3\x10\x16\x12\x13\n\x0fRELAY_SCANNER_1\x10\x17\x12\x13\n\x0fRELAY_SCANNER_2\x10\x18\x12\x13\n\x0fRELAY_SCANNER_3\x10\x19\x12\x0c\n\x08RELAY_AC\x10\x1a\x12\x11\n\rRELAY_CHILLER\x10\x1b\x12\x10\n\x0cRELAY_STROBE\x10\x1c\x12\x1c\n\x18RELAY_ENCODER_FRONT_LEFT\x10\x1d\x12\x1d\n\x19RELAY_ENCODER_FRONT_RIGHT\x10\x1e\x12\x1b\n\x17RELAY_ENCODER_BACK_LEFT\x10\x1f\x12\x1c\n\x18RELAY_ENCODER_BACK_RIGHT\x10 \x12\x1d\n\x19SENSOR_ENCODER_FRONT_LEFT\x10!\x12\x1e\n\x1aSENSOR_ENCODER_FRONT_RIGHT\x10\"\x12\x1c\n\x18SENSOR_ENCODER_BACK_LEFT\x10#\x12\x1d\n\x19SENSOR_ENCODER_BACK_RIGHT\x10$\x12\r\n\tRELAY_GPS\x10%\x12\x13\n\x0fSENSOR_LATITUDE\x10&\x12\x14\n\x10SENSOR_LONGITUDE\x10\'\x12\x0e\n\nSENSOR_KEY\x10(\x12\x14\n\x10SENSOR_INTERLOCK\x10)\x12\x17\n\x13RELAY_ENCODER_BOARD\x10**T\n\x10\x44\x65viceValueColor\x12\x0e\n\nCOLOR_GRAY\x10\x00\x12\x0f\n\x0b\x43OLOR_GREEN\x10\x01\x12\x10\n\x0c\x43OLOR_ORANGE\x10\x02\x12\r\n\tCOLOR_RED\x10\x03*\xbf\x01\n\x10NetworkLinkSpeed\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x12\n\x0eSPEED_10M_HALF\x10\x01\x12\x12\n\x0eSPEED_10M_FULL\x10\x02\x12\x13\n\x0fSPEED_100M_HALF\x10\x03\x12\x13\n\x0fSPEED_100M_FULL\x10\x04\x12\x11\n\rSPEED_1G_FULL\x10\x05\x12\x12\n\x0eSPEED_2G5_FULL\x10\x06\x12\x11\n\rSPEED_5G_FULL\x10\x07\x12\x12\n\x0eSPEED_10G_FULL\x10\x08*U\n\rAcPowerStatus\x12\x11\n\rPOWER_UNKNOWN\x10\x00\x12\x0e\n\nPOWER_GOOD\x10\x01\x12\r\n\tPOWER_BAD\x10\x02\x12\x12\n\x0ePOWER_VERY_BAD\x10\x03\x32\xbb\x0c\n\x0cPowerService\x12k\n\x12GetNextPowerStatus\x12).carbon.frontend.power.PowerStatusRequest\x1a*.carbon.frontend.power.PowerStatusResponse\x12Z\n\rTurnOffDevice\x12#.carbon.frontend.power.RelayRequest\x1a$.carbon.frontend.power.RelayResponse\x12Y\n\x0cTurnOnDevice\x12#.carbon.frontend.power.RelayRequest\x1a$.carbon.frontend.power.RelayResponse\x12\x9d\x01\n\x1eGetNextReaperAllHardwareStatus\x12<.carbon.frontend.power.GetNextReaperAllHardwareStatusRequest\x1a=.carbon.frontend.power.GetNextReaperAllHardwareStatusResponse\x12\x94\x01\n\x1bGetNextReaperHardwareStatus\x12\x39.carbon.frontend.power.GetNextReaperHardwareStatusRequest\x1a:.carbon.frontend.power.GetNextReaperHardwareStatusResponse\x12\x84\x01\n\x15SetReaperScannerPower\x12\x33.carbon.frontend.power.SetReaperScannerPowerRequest\x1a\x34.carbon.frontend.power.SetReaperScannerPowerResponse\"\x00\x12\x81\x01\n\x14SetReaperTargetPower\x12\x32.carbon.frontend.power.SetReaperTargetPowerRequest\x1a\x33.carbon.frontend.power.SetReaperTargetPowerResponse\"\x00\x12\x8d\x01\n\x18SetReaperPredictCamPower\x12\x36.carbon.frontend.power.SetReaperPredictCamPowerRequest\x1a\x37.carbon.frontend.power.SetReaperPredictCamPowerResponse\"\x00\x12\x84\x01\n\x15SetReaperStrobeEnable\x12\x33.carbon.frontend.power.SetReaperStrobeEnableRequest\x1a\x34.carbon.frontend.power.SetReaperStrobeEnableResponse\"\x00\x12\x90\x01\n\x19SetReaperAllStrobesEnable\x12\x37.carbon.frontend.power.SetReaperAllStrobesEnableRequest\x1a\x38.carbon.frontend.power.SetReaperAllStrobesEnableResponse\"\x00\x12\x87\x01\n\x16SetReaperModulePcPower\x12\x34.carbon.frontend.power.SetReaperModulePcPowerRequest\x1a\x35.carbon.frontend.power.SetReaperModulePcPowerResponse\"\x00\x12\x90\x01\n\x19SetReaperModuleLaserPower\x12\x37.carbon.frontend.power.SetReaperModuleLaserPowerRequest\x1a\x38.carbon.frontend.power.SetReaperModuleLaserPowerResponse\"\x00\x42\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.power_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_REAPERCENTERENCLOSUREDATA'].fields_by_name['temp_humidity_status']._loaded_options = None
  _globals['_REAPERCENTERENCLOSUREDATA'].fields_by_name['temp_humidity_status']._serialized_options = b'\030\001'
  _globals['_REAPERCENTERENCLOSUREDATA'].fields_by_name['strobe_disabled']._loaded_options = None
  _globals['_REAPERCENTERENCLOSUREDATA'].fields_by_name['strobe_disabled']._serialized_options = b'\030\001'
  _globals['_DEVICE']._serialized_start=8578
  _globals['_DEVICE']._serialized_end=9555
  _globals['_DEVICEVALUECOLOR']._serialized_start=9557
  _globals['_DEVICEVALUECOLOR']._serialized_end=9641
  _globals['_NETWORKLINKSPEED']._serialized_start=9644
  _globals['_NETWORKLINKSPEED']._serialized_end=9835
  _globals['_ACPOWERSTATUS']._serialized_start=9837
  _globals['_ACPOWERSTATUS']._serialized_end=9922
  _globals['_DEVICESTATUS']._serialized_start=65
  _globals['_DEVICESTATUS']._serialized_end=414
  _globals['_DEVICESTATUS_RELAYSTATUS']._serialized_start=287
  _globals['_DEVICESTATUS_RELAYSTATUS']._serialized_end=318
  _globals['_DEVICESTATUS_SENSORSTATUS']._serialized_start=320
  _globals['_DEVICESTATUS_SENSORSTATUS']._serialized_end=406
  _globals['_POWERSTATUSREQUEST']._serialized_start=416
  _globals['_POWERSTATUSREQUEST']._serialized_end=472
  _globals['_POWERSTATUSRESPONSE']._serialized_start=474
  _globals['_POWERSTATUSRESPONSE']._serialized_end=585
  _globals['_VALUEWITHRANGE']._serialized_start=587
  _globals['_VALUEWITHRANGE']._serialized_end=633
  _globals['_ENVIRONMENTALSENSORDATA']._serialized_start=636
  _globals['_ENVIRONMENTALSENSORDATA']._serialized_end=844
  _globals['_COOLANTSENSORDATA']._serialized_start=847
  _globals['_COOLANTSENSORDATA']._serialized_end=989
  _globals['_NETWORKPORTSTATE']._serialized_start=992
  _globals['_NETWORKPORTSTATE']._serialized_end=1165
  _globals['_REAPERPCSENSORDATA']._serialized_start=1168
  _globals['_REAPERPCSENSORDATA']._serialized_end=2343
  _globals['_REAPERSCANNERLASERSTATUS']._serialized_start=2346
  _globals['_REAPERSCANNERLASERSTATUS']._serialized_end=2620
  _globals['_REAPERSCANNERMOTORDATA']._serialized_start=2623
  _globals['_REAPERSCANNERMOTORDATA']._serialized_end=2892
  _globals['_REAPERSCANNERSENSORDATA']._serialized_start=2895
  _globals['_REAPERSCANNERSENSORDATA']._serialized_end=3741
  _globals['_REAPERGPSDATA']._serialized_start=3743
  _globals['_REAPERGPSDATA']._serialized_end=3812
  _globals['_REAPERWHEELENCODERDATA']._serialized_start=3814
  _globals['_REAPERWHEELENCODERDATA']._serialized_end=3879
  _globals['_REAPERCENTERENCLOSUREDATA']._serialized_start=3882
  _globals['_REAPERCENTERENCLOSUREDATA']._serialized_end=5678
  _globals['_REAPERMODULESENSORDATA']._serialized_start=5681
  _globals['_REAPERMODULESENSORDATA']._serialized_end=6598
  _globals['_CENTERENCLOSURESTATUSREQUEST']._serialized_start=6600
  _globals['_CENTERENCLOSURESTATUSREQUEST']._serialized_end=6630
  _globals['_MODULEHARDWARESTATUSREQUEST']._serialized_start=6632
  _globals['_MODULEHARDWARESTATUSREQUEST']._serialized_end=6680
  _globals['_GETNEXTREAPERHARDWARESTATUSREQUEST']._serialized_start=6683
  _globals['_GETNEXTREAPERHARDWARESTATUSREQUEST']._serialized_end=6931
  _globals['_GETNEXTREAPERHARDWARESTATUSRESPONSE']._serialized_start=6934
  _globals['_GETNEXTREAPERHARDWARESTATUSRESPONSE']._serialized_end=7176
  _globals['_GETNEXTREAPERALLHARDWARESTATUSREQUEST']._serialized_start=7178
  _globals['_GETNEXTREAPERALLHARDWARESTATUSREQUEST']._serialized_end=7253
  _globals['_GETNEXTREAPERALLHARDWARESTATUSRESPONSE']._serialized_start=7256
  _globals['_GETNEXTREAPERALLHARDWARESTATUSRESPONSE']._serialized_end=7485
  _globals['_RELAYREQUEST']._serialized_start=7487
  _globals['_RELAYREQUEST']._serialized_end=7548
  _globals['_RELAYRESPONSE']._serialized_start=7550
  _globals['_RELAYRESPONSE']._serialized_end=7582
  _globals['_SETREAPERSCANNERPOWERREQUEST']._serialized_start=7585
  _globals['_SETREAPERSCANNERPOWERREQUEST']._serialized_end=7734
  _globals['_SETREAPERSCANNERPOWERRESPONSE']._serialized_start=7736
  _globals['_SETREAPERSCANNERPOWERRESPONSE']._serialized_end=7784
  _globals['_SETREAPERTARGETPOWERREQUEST']._serialized_start=7787
  _globals['_SETREAPERTARGETPOWERREQUEST']._serialized_end=7931
  _globals['_SETREAPERTARGETPOWERRESPONSE']._serialized_start=7933
  _globals['_SETREAPERTARGETPOWERRESPONSE']._serialized_end=7980
  _globals['_SETREAPERPREDICTCAMPOWERREQUEST']._serialized_start=7982
  _globals['_SETREAPERPREDICTCAMPOWERREQUEST']._serialized_end=8051
  _globals['_SETREAPERPREDICTCAMPOWERRESPONSE']._serialized_start=8053
  _globals['_SETREAPERPREDICTCAMPOWERRESPONSE']._serialized_end=8104
  _globals['_SETREAPERSTROBEENABLEREQUEST']._serialized_start=8106
  _globals['_SETREAPERSTROBEENABLEREQUEST']._serialized_end=8172
  _globals['_SETREAPERSTROBEENABLERESPONSE']._serialized_start=8174
  _globals['_SETREAPERSTROBEENABLERESPONSE']._serialized_end=8222
  _globals['_SETREAPERALLSTROBESENABLEREQUEST']._serialized_start=8224
  _globals['_SETREAPERALLSTROBESENABLEREQUEST']._serialized_end=8275
  _globals['_SETREAPERALLSTROBESENABLERESPONSE']._serialized_start=8277
  _globals['_SETREAPERALLSTROBESENABLERESPONSE']._serialized_end=8329
  _globals['_SETREAPERMODULEPCPOWERREQUEST']._serialized_start=8331
  _globals['_SETREAPERMODULEPCPOWERREQUEST']._serialized_end=8398
  _globals['_SETREAPERMODULEPCPOWERRESPONSE']._serialized_start=8400
  _globals['_SETREAPERMODULEPCPOWERRESPONSE']._serialized_end=8449
  _globals['_SETREAPERMODULELASERPOWERREQUEST']._serialized_start=8451
  _globals['_SETREAPERMODULELASERPOWERREQUEST']._serialized_end=8521
  _globals['_SETREAPERMODULELASERPOWERRESPONSE']._serialized_start=8523
  _globals['_SETREAPERMODULELASERPOWERRESPONSE']._serialized_end=8575
  _globals['_POWERSERVICE']._serialized_start=9925
  _globals['_POWERSERVICE']._serialized_end=11520
# @@protoc_insertion_point(module_scope)
