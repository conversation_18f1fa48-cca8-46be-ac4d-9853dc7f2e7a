# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import power_pb2 as frontend_dot_power__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/power_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PowerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextPowerStatus = channel.unary_unary(
                '/carbon.frontend.power.PowerService/GetNextPowerStatus',
                request_serializer=frontend_dot_power__pb2.PowerStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.PowerStatusResponse.FromString,
                _registered_method=True)
        self.TurnOffDevice = channel.unary_unary(
                '/carbon.frontend.power.PowerService/TurnOffDevice',
                request_serializer=frontend_dot_power__pb2.RelayRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.RelayResponse.FromString,
                _registered_method=True)
        self.TurnOnDevice = channel.unary_unary(
                '/carbon.frontend.power.PowerService/TurnOnDevice',
                request_serializer=frontend_dot_power__pb2.RelayRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.RelayResponse.FromString,
                _registered_method=True)
        self.GetNextReaperAllHardwareStatus = channel.unary_unary(
                '/carbon.frontend.power.PowerService/GetNextReaperAllHardwareStatus',
                request_serializer=frontend_dot_power__pb2.GetNextReaperAllHardwareStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.GetNextReaperAllHardwareStatusResponse.FromString,
                _registered_method=True)
        self.GetNextReaperHardwareStatus = channel.unary_unary(
                '/carbon.frontend.power.PowerService/GetNextReaperHardwareStatus',
                request_serializer=frontend_dot_power__pb2.GetNextReaperHardwareStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.GetNextReaperHardwareStatusResponse.FromString,
                _registered_method=True)
        self.SetReaperScannerPower = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperScannerPower',
                request_serializer=frontend_dot_power__pb2.SetReaperScannerPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperScannerPowerResponse.FromString,
                _registered_method=True)
        self.SetReaperTargetPower = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperTargetPower',
                request_serializer=frontend_dot_power__pb2.SetReaperTargetPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperTargetPowerResponse.FromString,
                _registered_method=True)
        self.SetReaperPredictCamPower = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperPredictCamPower',
                request_serializer=frontend_dot_power__pb2.SetReaperPredictCamPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperPredictCamPowerResponse.FromString,
                _registered_method=True)
        self.SetReaperStrobeEnable = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperStrobeEnable',
                request_serializer=frontend_dot_power__pb2.SetReaperStrobeEnableRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperStrobeEnableResponse.FromString,
                _registered_method=True)
        self.SetReaperAllStrobesEnable = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperAllStrobesEnable',
                request_serializer=frontend_dot_power__pb2.SetReaperAllStrobesEnableRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperAllStrobesEnableResponse.FromString,
                _registered_method=True)
        self.SetReaperModulePcPower = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperModulePcPower',
                request_serializer=frontend_dot_power__pb2.SetReaperModulePcPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperModulePcPowerResponse.FromString,
                _registered_method=True)
        self.SetReaperModuleLaserPower = channel.unary_unary(
                '/carbon.frontend.power.PowerService/SetReaperModuleLaserPower',
                request_serializer=frontend_dot_power__pb2.SetReaperModuleLaserPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_power__pb2.SetReaperModuleLaserPowerResponse.FromString,
                _registered_method=True)


class PowerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextPowerStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TurnOffDevice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TurnOnDevice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextReaperAllHardwareStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextReaperHardwareStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperScannerPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperTargetPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperPredictCamPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperStrobeEnable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperAllStrobesEnable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperModulePcPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperModuleLaserPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PowerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextPowerStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPowerStatus,
                    request_deserializer=frontend_dot_power__pb2.PowerStatusRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.PowerStatusResponse.SerializeToString,
            ),
            'TurnOffDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.TurnOffDevice,
                    request_deserializer=frontend_dot_power__pb2.RelayRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.RelayResponse.SerializeToString,
            ),
            'TurnOnDevice': grpc.unary_unary_rpc_method_handler(
                    servicer.TurnOnDevice,
                    request_deserializer=frontend_dot_power__pb2.RelayRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.RelayResponse.SerializeToString,
            ),
            'GetNextReaperAllHardwareStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextReaperAllHardwareStatus,
                    request_deserializer=frontend_dot_power__pb2.GetNextReaperAllHardwareStatusRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.GetNextReaperAllHardwareStatusResponse.SerializeToString,
            ),
            'GetNextReaperHardwareStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextReaperHardwareStatus,
                    request_deserializer=frontend_dot_power__pb2.GetNextReaperHardwareStatusRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.GetNextReaperHardwareStatusResponse.SerializeToString,
            ),
            'SetReaperScannerPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperScannerPower,
                    request_deserializer=frontend_dot_power__pb2.SetReaperScannerPowerRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperScannerPowerResponse.SerializeToString,
            ),
            'SetReaperTargetPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperTargetPower,
                    request_deserializer=frontend_dot_power__pb2.SetReaperTargetPowerRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperTargetPowerResponse.SerializeToString,
            ),
            'SetReaperPredictCamPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperPredictCamPower,
                    request_deserializer=frontend_dot_power__pb2.SetReaperPredictCamPowerRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperPredictCamPowerResponse.SerializeToString,
            ),
            'SetReaperStrobeEnable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperStrobeEnable,
                    request_deserializer=frontend_dot_power__pb2.SetReaperStrobeEnableRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperStrobeEnableResponse.SerializeToString,
            ),
            'SetReaperAllStrobesEnable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperAllStrobesEnable,
                    request_deserializer=frontend_dot_power__pb2.SetReaperAllStrobesEnableRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperAllStrobesEnableResponse.SerializeToString,
            ),
            'SetReaperModulePcPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperModulePcPower,
                    request_deserializer=frontend_dot_power__pb2.SetReaperModulePcPowerRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperModulePcPowerResponse.SerializeToString,
            ),
            'SetReaperModuleLaserPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperModuleLaserPower,
                    request_deserializer=frontend_dot_power__pb2.SetReaperModuleLaserPowerRequest.FromString,
                    response_serializer=frontend_dot_power__pb2.SetReaperModuleLaserPowerResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.power.PowerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.power.PowerService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PowerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextPowerStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/GetNextPowerStatus',
            frontend_dot_power__pb2.PowerStatusRequest.SerializeToString,
            frontend_dot_power__pb2.PowerStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TurnOffDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/TurnOffDevice',
            frontend_dot_power__pb2.RelayRequest.SerializeToString,
            frontend_dot_power__pb2.RelayResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TurnOnDevice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/TurnOnDevice',
            frontend_dot_power__pb2.RelayRequest.SerializeToString,
            frontend_dot_power__pb2.RelayResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextReaperAllHardwareStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/GetNextReaperAllHardwareStatus',
            frontend_dot_power__pb2.GetNextReaperAllHardwareStatusRequest.SerializeToString,
            frontend_dot_power__pb2.GetNextReaperAllHardwareStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextReaperHardwareStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/GetNextReaperHardwareStatus',
            frontend_dot_power__pb2.GetNextReaperHardwareStatusRequest.SerializeToString,
            frontend_dot_power__pb2.GetNextReaperHardwareStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperScannerPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperScannerPower',
            frontend_dot_power__pb2.SetReaperScannerPowerRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperScannerPowerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperTargetPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperTargetPower',
            frontend_dot_power__pb2.SetReaperTargetPowerRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperTargetPowerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperPredictCamPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperPredictCamPower',
            frontend_dot_power__pb2.SetReaperPredictCamPowerRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperPredictCamPowerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperStrobeEnable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperStrobeEnable',
            frontend_dot_power__pb2.SetReaperStrobeEnableRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperStrobeEnableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperAllStrobesEnable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperAllStrobesEnable',
            frontend_dot_power__pb2.SetReaperAllStrobesEnableRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperAllStrobesEnableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperModulePcPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperModulePcPower',
            frontend_dot_power__pb2.SetReaperModulePcPowerRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperModulePcPowerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetReaperModuleLaserPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.power.PowerService/SetReaperModuleLaserPower',
            frontend_dot_power__pb2.SetReaperModuleLaserPowerRequest.SerializeToString,
            frontend_dot_power__pb2.SetReaperModuleLaserPowerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
