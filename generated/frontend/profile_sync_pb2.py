# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/profile_sync.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/profile_sync.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x66rontend/profile_sync.proto\x12\x1c\x63\x61rbon.frontend.profile_sync\"\x92\x01\n\x0fProfileSyncData\x12?\n\x0cprofile_type\x18\x01 \x01(\x0e\x32).carbon.frontend.profile_sync.ProfileType\x12\x1a\n\x12last_updated_ts_ms\x18\x02 \x01(\x03\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x11\n\tprotected\x18\x04 \x01(\x08*\x9f\x01\n\x0bProfileType\x12\x0b\n\x07\x41LMANAC\x10\x00\x12\x11\n\rDISCRIMINATOR\x10\x01\x12\x0f\n\x0bMODELINATOR\x10\x03\x12\x0b\n\x07\x42\x41NDING\x10\x04\x12\x0c\n\x08THINNING\x10\x05\x12\x1d\n\x19TARGET_VELOCITY_ESTIMATOR\x10\x06\x12\x17\n\x13\x43\x41TEGORY_COLLECTION\x10\x07\x12\x0c\n\x08\x43\x41TEGORY\x10\x08\x42\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.profile_sync_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_PROFILETYPE']._serialized_start=211
  _globals['_PROFILETYPE']._serialized_end=370
  _globals['_PROFILESYNCDATA']._serialized_start=62
  _globals['_PROFILESYNCDATA']._serialized_end=208
# @@protoc_insertion_point(module_scope)
