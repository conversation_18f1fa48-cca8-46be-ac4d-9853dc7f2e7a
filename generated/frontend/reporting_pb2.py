# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/reporting.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/reporting.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x66rontend/reporting.proto\x12\x18\x63\x61rbon.frontend.features\x1a\x0futil/util.proto\"y\n\x08Location\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x10\n\x08latitude\x18\x02 \x01(\x02\x12\x11\n\tlongitude\x18\x03 \x01(\x02\x12\x10\n\x08\x61ltitude\x18\x04 \x01(\x02\x12\x12\n\nis_weeding\x18\x05 \x01(\x08\"j\n\x0fLocationHistory\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x33\n\x07history\x18\x02 \x03(\x0b\x32\".carbon.frontend.features.Location2o\n\x10ReportingService\x12[\n\x16GetNextLocationHistory\x12\x16.carbon.util.Timestamp\x1a).<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.reporting_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_LOCATION']._serialized_start=71
  _globals['_LOCATION']._serialized_end=192
  _globals['_LOCATIONHISTORY']._serialized_start=194
  _globals['_LOCATIONHISTORY']._serialized_end=300
  _globals['_REPORTINGSERVICE']._serialized_start=302
  _globals['_REPORTINGSERVICE']._serialized_end=413
# @@protoc_insertion_point(module_scope)
