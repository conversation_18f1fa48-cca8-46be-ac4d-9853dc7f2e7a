# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/software.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/software.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x66rontend/software.proto\x12\x18\x63\x61rbon.frontend.software\x1a\x0futil/util.proto\"@\n\x0fSoftwareVersion\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x11\n\tavailable\x18\x02 \x01(\x08\x12\r\n\x05ready\x18\x03 \x01(\x08\"\x94\x02\n\x18HostSoftwareVersionState\x12\x11\n\thost_name\x18\x01 \x01(\t\x12\x0f\n\x07host_id\x18\x02 \x01(\r\x12\x0e\n\x06\x61\x63tive\x18\x03 \x01(\x08\x12:\n\x07\x63urrent\x18\x04 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x39\n\x06target\x18\x05 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12;\n\x08previous\x18\x06 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x10\n\x08updating\x18\x07 \x01(\x08\"\x89\x03\n\x14SoftwareVersionState\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12:\n\x07\x63urrent\x18\x02 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x39\n\x06target\x18\x03 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12;\n\x08previous\x18\x04 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x10\n\x08updating\x18\x05 \x01(\x08\x12$\n\x1cshow_software_update_to_user\x18\x06 \x01(\x08\x12G\n\x0bhost_states\x18\x07 \x03(\x0b\x32\x32.carbon.frontend.software.HostSoftwareVersionState\x12\x18\n\x10version_mismatch\x18\x08 \x01(\x08\"Z\n\x1bSoftwareVersionStateRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x17\n\x0fget_host_states\x18\x02 \x01(\x08\"$\n\x11UpdateHostRequest\x12\x0f\n\x07host_id\x18\x01 \x01(\r2\x89\x03\n\x0fSoftwareService\x12\x84\x01\n\x1bGetNextSoftwareVersionState\x12\x35.carbon.frontend.software.SoftwareVersionStateRequest\x1a..carbon.frontend.software.SoftwareVersionState\x12M\n\nUpdateHost\x12+.carbon.frontend.software.UpdateHostRequest\x1a\x12.carbon.util.Empty\x12\x30\n\x06Update\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12\x30\n\x06Revert\x12\x12.carbon.util.Empty\x1a\x12.carbon.util.Empty\x12<\n\x12\x46ixVersionMismatch\x12\x12.carbon.util.Empty\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.software_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_SOFTWAREVERSION']._serialized_start=70
  _globals['_SOFTWAREVERSION']._serialized_end=134
  _globals['_HOSTSOFTWAREVERSIONSTATE']._serialized_start=137
  _globals['_HOSTSOFTWAREVERSIONSTATE']._serialized_end=413
  _globals['_SOFTWAREVERSIONSTATE']._serialized_start=416
  _globals['_SOFTWAREVERSIONSTATE']._serialized_end=809
  _globals['_SOFTWAREVERSIONSTATEREQUEST']._serialized_start=811
  _globals['_SOFTWAREVERSIONSTATEREQUEST']._serialized_end=901
  _globals['_UPDATEHOSTREQUEST']._serialized_start=903
  _globals['_UPDATEHOSTREQUEST']._serialized_end=939
  _globals['_SOFTWARESERVICE']._serialized_start=942
  _globals['_SOFTWARESERVICE']._serialized_end=1335
# @@protoc_insertion_point(module_scope)
