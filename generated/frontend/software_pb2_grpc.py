# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import software_pb2 as frontend_dot_software__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/software_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SoftwareServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextSoftwareVersionState = channel.unary_unary(
                '/carbon.frontend.software.SoftwareService/GetNextSoftwareVersionState',
                request_serializer=frontend_dot_software__pb2.SoftwareVersionStateRequest.SerializeToString,
                response_deserializer=frontend_dot_software__pb2.SoftwareVersionState.FromString,
                _registered_method=True)
        self.UpdateHost = channel.unary_unary(
                '/carbon.frontend.software.SoftwareService/UpdateHost',
                request_serializer=frontend_dot_software__pb2.UpdateHostRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.Update = channel.unary_unary(
                '/carbon.frontend.software.SoftwareService/Update',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.Revert = channel.unary_unary(
                '/carbon.frontend.software.SoftwareService/Revert',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.FixVersionMismatch = channel.unary_unary(
                '/carbon.frontend.software.SoftwareService/FixVersionMismatch',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class SoftwareServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextSoftwareVersionState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateHost(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Update(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Revert(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FixVersionMismatch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SoftwareServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextSoftwareVersionState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextSoftwareVersionState,
                    request_deserializer=frontend_dot_software__pb2.SoftwareVersionStateRequest.FromString,
                    response_serializer=frontend_dot_software__pb2.SoftwareVersionState.SerializeToString,
            ),
            'UpdateHost': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateHost,
                    request_deserializer=frontend_dot_software__pb2.UpdateHostRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'Update': grpc.unary_unary_rpc_method_handler(
                    servicer.Update,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'Revert': grpc.unary_unary_rpc_method_handler(
                    servicer.Revert,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'FixVersionMismatch': grpc.unary_unary_rpc_method_handler(
                    servicer.FixVersionMismatch,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.software.SoftwareService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.software.SoftwareService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SoftwareService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextSoftwareVersionState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.software.SoftwareService/GetNextSoftwareVersionState',
            frontend_dot_software__pb2.SoftwareVersionStateRequest.SerializeToString,
            frontend_dot_software__pb2.SoftwareVersionState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateHost(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.software.SoftwareService/UpdateHost',
            frontend_dot_software__pb2.UpdateHostRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Update(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.software.SoftwareService/Update',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Revert(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.software.SoftwareService/Revert',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FixVersionMismatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.software.SoftwareService/FixVersionMismatch',
            util_dot_util__pb2.Empty.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
