# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/startup_task.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/startup_task.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.startup_task import startup_task_pb2 as startup__task_dot_startup__task__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x66rontend/startup_task.proto\x12\x1c\x63\x61rbon.frontend.startup_task\x1a\x1fstartup_task/startup_task.proto\x1a\x0futil/util.proto\"d\n\x14GetNextTasksResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12(\n\x05tasks\x18\x02 \x03(\x0b\x32\x19.carbon.startup_task.Task\"*\n\x17MarkTaskCompleteRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\x1a\n\x18MarkTaskCompleteResponse2\xf4\x01\n\x12StartupTaskService\x12Z\n\x0cGetNextTasks\x12\x16.carbon.util.Timestamp\x1a\x32.carbon.frontend.startup_task.GetNextTasksResponse\x12\x81\x01\n\x10MarkTaskComplete\x12\x35.carbon.frontend.startup_task.MarkTaskCompleteRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.startup_task_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GETNEXTTASKSRESPONSE']._serialized_start=111
  _globals['_GETNEXTTASKSRESPONSE']._serialized_end=211
  _globals['_MARKTASKCOMPLETEREQUEST']._serialized_start=213
  _globals['_MARKTASKCOMPLETEREQUEST']._serialized_end=255
  _globals['_MARKTASKCOMPLETERESPONSE']._serialized_start=257
  _globals['_MARKTASKCOMPLETERESPONSE']._serialized_end=283
  _globals['_STARTUPTASKSERVICE']._serialized_start=286
  _globals['_STARTUPTASKSERVICE']._serialized_end=530
# @@protoc_insertion_point(module_scope)
