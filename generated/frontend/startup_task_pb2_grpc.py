# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import startup_task_pb2 as frontend_dot_startup__task__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/startup_task_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class StartupTaskServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextTasks = channel.unary_unary(
                '/carbon.frontend.startup_task.StartupTaskService/GetNextTasks',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_startup__task__pb2.GetNextTasksResponse.FromString,
                _registered_method=True)
        self.MarkTaskComplete = channel.unary_unary(
                '/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete',
                request_serializer=frontend_dot_startup__task__pb2.MarkTaskCompleteRequest.SerializeToString,
                response_deserializer=frontend_dot_startup__task__pb2.MarkTaskCompleteResponse.FromString,
                _registered_method=True)


class StartupTaskServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextTasks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MarkTaskComplete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StartupTaskServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextTasks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTasks,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_startup__task__pb2.GetNextTasksResponse.SerializeToString,
            ),
            'MarkTaskComplete': grpc.unary_unary_rpc_method_handler(
                    servicer.MarkTaskComplete,
                    request_deserializer=frontend_dot_startup__task__pb2.MarkTaskCompleteRequest.FromString,
                    response_serializer=frontend_dot_startup__task__pb2.MarkTaskCompleteResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.startup_task.StartupTaskService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.startup_task.StartupTaskService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class StartupTaskService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextTasks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.startup_task.StartupTaskService/GetNextTasks',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_startup__task__pb2.GetNextTasksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MarkTaskComplete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete',
            frontend_dot_startup__task__pb2.MarkTaskCompleteRequest.SerializeToString,
            frontend_dot_startup__task__pb2.MarkTaskCompleteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
