# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/status_bar.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/status_bar.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import translation_pb2 as frontend_dot_translation__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x66rontend/status_bar.proto\x12\x1a\x63\x61rbon.frontend.status_bar\x1a\x1a\x66rontend/translation.proto\x1a\x0futil/util.proto\"C\n\x0cGlobalStatus\x12\x0c\n\x04hint\x18\x01 \x01(\t\x12\x11\n\ticon_name\x18\x02 \x01(\t\x12\x12\n\nicon_color\x18\x03 \x01(\t\"\\\n\rServiceStatus\x12\x0c\n\x04name\x18\x01 \x01(\t\x12=\n\x0cstatus_level\x18\x02 \x01(\x0e\x32\'.carbon.frontend.status_bar.StatusLevel\"\x90\x01\n\x0cServerStatus\x12=\n\x0cstatus_level\x18\x01 \x01(\x0e\x32\'.carbon.frontend.status_bar.StatusLevel\x12\x41\n\x0eservice_status\x18\x02 \x03(\x0b\x32).carbon.frontend.status_bar.ServiceStatus\"\x86\x01\n\x1eTranslatedStatusMessageDetails\x12\x1c\n\x12\x64\x65tails_string_key\x18\x01 \x01(\tH\x00\x12;\n\x05timer\x18\x02 \x01(\x0b\x32*.carbon.frontend.translation.DurationValueH\x00\x42\t\n\x07\x64\x65tails\"v\n\x17TranslatedStatusMessage\x12\x0e\n\x06prefix\x18\x01 \x01(\t\x12K\n\x07\x64\x65tails\x18\x02 \x01(\x0b\x32:.carbon.frontend.status_bar.TranslatedStatusMessageDetails\"\xe9\x04\n\x10StatusBarMessage\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x14\n\x08\x65stopped\x18\x02 \x01(\x08\x42\x02\x18\x01\x12\x16\n\x0elasers_enabled\x18\x03 \x01(\x08\x12\x17\n\x0fweeding_enabled\x18\x04 \x01(\x08\x12\x38\n\x0cstatus_level\x18\x05 \x01(\x0e\x32\".carbon.frontend.status_bar.Status\x12\x16\n\x0estatus_message\x18\x06 \x01(\t\x12\x0e\n\x06serial\x18\x07 \x01(\t\x12O\n\nrow_status\x18\x08 \x03(\x0b\x32;.carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry\x12@\n\x0e\x63ommand_status\x18\t \x01(\x0b\x32(.carbon.frontend.status_bar.ServerStatus\x12\x41\n\x0fglobal_statuses\x18\n \x03(\x0b\x32(.carbon.frontend.status_bar.GlobalStatus\x12V\n\x19translated_status_message\x18\x0b \x01(\x0b\x32\x33.carbon.frontend.status_bar.TranslatedStatusMessage\x1aZ\n\x0eRowStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x37\n\x05value\x18\x02 \x01(\x0b\x32(.carbon.frontend.status_bar.ServerStatus:\x02\x38\x01\"?\n\x12ReportIssueRequest\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x14\n\x0cphone_number\x18\x02 \x01(\t\",\n\x13SupportPhoneMessage\x12\x15\n\rsupport_phone\x18\x01 \x01(\t*\xea\x03\n\x06Status\x12\x10\n\x0cSTATUS_ERROR\x10\x00\x12\x13\n\x0fSTATUS_ESTOPPED\x10\x01\x12\x14\n\x10STATUS_PRE_ARMED\x10\x02\x12\x17\n\x13STATUS_POWERED_DOWN\x10\x03\x12\x16\n\x12STATUS_POWERING_UP\x10\x04\x12\x1c\n\x18STATUS_UPDATE_INSTALLING\x10\x05\x12\x18\n\x14STATUS_MODEL_LOADING\x10\x06\x12\x1b\n\x17STATUS_MODEL_INSTALLING\x10\x07\x12\x12\n\x0eSTATUS_WEEDING\x10\x08\x12\x12\n\x0eSTATUS_STANDBY\x10\t\x12\x12\n\x0eSTATUS_UNKNOWN\x10\n\x12\x17\n\x13STATUS_DISCONNECTED\x10\x0b\x12\x11\n\rSTATUS_LIFTED\x10\x0c\x12\x12\n\x0eSTATUS_LOADING\x10\r\x12$\n STATUS_ALARM_AUTOFIX_IN_PROGRESS\x10\x0e\x12\x1d\n\x19STATUS_FAILED_TO_POWER_UP\x10\x0f\x12\"\n\x1eSTATUS_SERVER_CABINET_COOLDOWN\x10\x10\x12\x1b\n\x17STATUS_CHILLER_COOLDOWN\x10\x11\x12\x1b\n\x17STATUS_TRACTOR_NOT_SAFE\x10\x12*D\n\x0bStatusLevel\x12\x0b\n\x07INVALID\x10\x00\x12\t\n\x05READY\x10\x01\x12\x0b\n\x07LOADING\x10\x02\x12\x10\n\x08\x45STOPPED\x10\x03\x1a\x02\x08\x01\x32\x94\x02\n\x10StatusBarService\x12U\n\rGetNextStatus\x12\x16.carbon.util.Timestamp\x1a,.carbon.frontend.status_bar.StatusBarMessage\x12Q\n\x0bReportIssue\x12..carbon.frontend.status_bar.ReportIssueRequest\x1a\x12.carbon.util.Empty\x12V\n\x0fGetSupportPhone\x12\x12.carbon.util.Empty\x1a/.<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.status_bar_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_STATUSLEVEL'].values_by_name["ESTOPPED"]._loaded_options = None
  _globals['_STATUSLEVEL'].values_by_name["ESTOPPED"]._serialized_options = b'\010\001'
  _globals['_STATUSBARMESSAGE_ROWSTATUSENTRY']._loaded_options = None
  _globals['_STATUSBARMESSAGE_ROWSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_STATUSBARMESSAGE'].fields_by_name['estopped']._loaded_options = None
  _globals['_STATUSBARMESSAGE'].fields_by_name['estopped']._serialized_options = b'\030\001'
  _globals['_STATUS']._serialized_start=1401
  _globals['_STATUS']._serialized_end=1891
  _globals['_STATUSLEVEL']._serialized_start=1893
  _globals['_STATUSLEVEL']._serialized_end=1961
  _globals['_GLOBALSTATUS']._serialized_start=102
  _globals['_GLOBALSTATUS']._serialized_end=169
  _globals['_SERVICESTATUS']._serialized_start=171
  _globals['_SERVICESTATUS']._serialized_end=263
  _globals['_SERVERSTATUS']._serialized_start=266
  _globals['_SERVERSTATUS']._serialized_end=410
  _globals['_TRANSLATEDSTATUSMESSAGEDETAILS']._serialized_start=413
  _globals['_TRANSLATEDSTATUSMESSAGEDETAILS']._serialized_end=547
  _globals['_TRANSLATEDSTATUSMESSAGE']._serialized_start=549
  _globals['_TRANSLATEDSTATUSMESSAGE']._serialized_end=667
  _globals['_STATUSBARMESSAGE']._serialized_start=670
  _globals['_STATUSBARMESSAGE']._serialized_end=1287
  _globals['_STATUSBARMESSAGE_ROWSTATUSENTRY']._serialized_start=1197
  _globals['_STATUSBARMESSAGE_ROWSTATUSENTRY']._serialized_end=1287
  _globals['_REPORTISSUEREQUEST']._serialized_start=1289
  _globals['_REPORTISSUEREQUEST']._serialized_end=1352
  _globals['_SUPPORTPHONEMESSAGE']._serialized_start=1354
  _globals['_SUPPORTPHONEMESSAGE']._serialized_end=1398
  _globals['_STATUSBARSERVICE']._serialized_start=1964
  _globals['_STATUSBARSERVICE']._serialized_end=2240
# @@protoc_insertion_point(module_scope)
