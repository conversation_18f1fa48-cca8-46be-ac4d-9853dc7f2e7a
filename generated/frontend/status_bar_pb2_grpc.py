# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import status_bar_pb2 as frontend_dot_status__bar__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/status_bar_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class StatusBarServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextStatus = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/GetNextStatus',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_status__bar__pb2.StatusBarMessage.FromString,
                _registered_method=True)
        self.ReportIssue = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/ReportIssue',
                request_serializer=frontend_dot_status__bar__pb2.ReportIssueRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetSupportPhone = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/GetSupportPhone',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_status__bar__pb2.SupportPhoneMessage.FromString,
                _registered_method=True)


class StatusBarServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReportIssue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSupportPhone(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StatusBarServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextStatus,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_status__bar__pb2.StatusBarMessage.SerializeToString,
            ),
            'ReportIssue': grpc.unary_unary_rpc_method_handler(
                    servicer.ReportIssue,
                    request_deserializer=frontend_dot_status__bar__pb2.ReportIssueRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetSupportPhone': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSupportPhone,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_status__bar__pb2.SupportPhoneMessage.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.status_bar.StatusBarService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.status_bar.StatusBarService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class StatusBarService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.status_bar.StatusBarService/GetNextStatus',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_status__bar__pb2.StatusBarMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReportIssue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.status_bar.StatusBarService/ReportIssue',
            frontend_dot_status__bar__pb2.ReportIssueRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSupportPhone(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.status_bar.StatusBarService/GetSupportPhone',
            util_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_status__bar__pb2.SupportPhoneMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
