# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/target_velocity_estimator.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/target_velocity_estimator.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.target_velocity_estimator import target_velocity_estimator_pb2 as target__velocity__estimator_dot_target__velocity__estimator__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(frontend/target_velocity_estimator.proto\x12)carbon.frontend.target_velocity_estimator\x1a\x0futil/util.proto\x1a\x39target_velocity_estimator/target_velocity_estimator.proto\"\x94\x01\n#GetNextAvailableTVEProfilesResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12I\n\x08profiles\x18\x02 \x03(\x0b\x32\x37.carbon.aimbot.target_velocity_estimator.ProfileDetails\"\x8b\x01\n\x1fGetNextActiveTVEProfileResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x44\n\x07profile\x18\x02 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\"#\n\x15LoadTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x16LoadTVEProfileResponse\x12\x44\n\x07profile\x18\x01 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\"q\n\x15SaveTVEProfileRequest\x12\x44\n\x07profile\x18\x01 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\x12\x12\n\nset_active\x18\x02 \x01(\x08\"$\n\x16SaveTVEProfileResponse\x12\n\n\x02id\x18\x01 \x01(\t\"(\n\x1aSetActiveTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x1d\n\x1bSetActiveTVEProfileResponse\"<\n\x17\x44\x65leteTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\rnew_active_id\x18\x02 \x01(\t\"\x1a\n\x18\x44\x65leteTVEProfileResponse2\x83\x07\n\x1eTargetVelocityEstimatorService\x12\x82\x01\n\x18GetNextAvailableProfiles\x12\x16.carbon.util.Timestamp\x1aN.carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse\x12z\n\x14GetNextActiveProfile\x12\x16.carbon.util.Timestamp\x1aJ.carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse\x12\x92\x01\n\x0bLoadProfile\<EMAIL>.target_velocity_estimator.LoadTVEProfileRequest\x1a\x41.carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse\x12\x92\x01\n\x0bSaveProfile\<EMAIL>.target_velocity_estimator.SaveTVEProfileRequest\x1a\x41.carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse\x12\x9a\x01\n\tSetActive\x12\x45.carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest\x1a\x46.carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse\x12\x98\x01\n\rDeleteProfile\x12\x42.carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.target_velocity_estimator_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GETNEXTAVAILABLETVEPROFILESRESPONSE']._serialized_start=164
  _globals['_GETNEXTAVAILABLETVEPROFILESRESPONSE']._serialized_end=312
  _globals['_GETNEXTACTIVETVEPROFILERESPONSE']._serialized_start=315
  _globals['_GETNEXTACTIVETVEPROFILERESPONSE']._serialized_end=454
  _globals['_LOADTVEPROFILEREQUEST']._serialized_start=456
  _globals['_LOADTVEPROFILEREQUEST']._serialized_end=491
  _globals['_LOADTVEPROFILERESPONSE']._serialized_start=493
  _globals['_LOADTVEPROFILERESPONSE']._serialized_end=587
  _globals['_SAVETVEPROFILEREQUEST']._serialized_start=589
  _globals['_SAVETVEPROFILEREQUEST']._serialized_end=702
  _globals['_SAVETVEPROFILERESPONSE']._serialized_start=704
  _globals['_SAVETVEPROFILERESPONSE']._serialized_end=740
  _globals['_SETACTIVETVEPROFILEREQUEST']._serialized_start=742
  _globals['_SETACTIVETVEPROFILEREQUEST']._serialized_end=782
  _globals['_SETACTIVETVEPROFILERESPONSE']._serialized_start=784
  _globals['_SETACTIVETVEPROFILERESPONSE']._serialized_end=813
  _globals['_DELETETVEPROFILEREQUEST']._serialized_start=815
  _globals['_DELETETVEPROFILEREQUEST']._serialized_end=875
  _globals['_DELETETVEPROFILERESPONSE']._serialized_start=877
  _globals['_DELETETVEPROFILERESPONSE']._serialized_end=903
  _globals['_TARGETVELOCITYESTIMATORSERVICE']._serialized_start=906
  _globals['_TARGETVELOCITYESTIMATORSERVICE']._serialized_end=1805
# @@protoc_insertion_point(module_scope)
