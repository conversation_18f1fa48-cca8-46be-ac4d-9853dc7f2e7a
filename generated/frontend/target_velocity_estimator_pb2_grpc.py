# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import target_velocity_estimator_pb2 as frontend_dot_target__velocity__estimator__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/target_velocity_estimator_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TargetVelocityEstimatorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextAvailableProfiles = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.FromString,
                _registered_method=True)
        self.GetNextActiveProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.FromString,
                _registered_method=True)
        self.LoadProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile',
                request_serializer=frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.FromString,
                _registered_method=True)
        self.SaveProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile',
                request_serializer=frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.FromString,
                _registered_method=True)
        self.SetActive = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive',
                request_serializer=frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.FromString,
                _registered_method=True)
        self.DeleteProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile',
                request_serializer=frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.FromString,
                _registered_method=True)


class TargetVelocityEstimatorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextAvailableProfiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActive(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TargetVelocityEstimatorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextAvailableProfiles': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAvailableProfiles,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.SerializeToString,
            ),
            'GetNextActiveProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveProfile,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.SerializeToString,
            ),
            'LoadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadProfile,
                    request_deserializer=frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.SerializeToString,
            ),
            'SaveProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveProfile,
                    request_deserializer=frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.SerializeToString,
            ),
            'SetActive': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActive,
                    request_deserializer=frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TargetVelocityEstimatorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextAvailableProfiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextActiveProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile',
            frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile',
            frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetActive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive',
            frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile',
            frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.SerializeToString,
            frontend_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
