# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/thinning.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/thinning.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.thinning import thinning_pb2 as thinning_dot_thinning__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x66rontend/thinning.proto\x12\x18\x63\x61rbon.frontend.thinning\x1a\x17thinning/thinning.proto\x1a\x0futil/util.proto\"\x8e\x01\n\x1dGetNextConfigurationsResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x36\n\x0b\x64\x65\x66initions\x18\x02 \x03(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x11\n\tactive_id\x18\x03 \x01(\t\"]\n\x19GetNextActiveConfResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x10\n\x04name\x18\x02 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x03 \x01(\t\"\x9f\x01\n\x1a\x44\x65\x66ineConfigurationRequest\x12\x35\n\ndefinition\x18\x01 \x01(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x12\n\nset_active\x18\x02 \x01(\x08\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\")\n\x1b\x44\x65\x66ineConfigurationResponse\x12\n\n\x02id\x18\x01 \x01(\t\"n\n\x16SetActiveConfigRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x02 \x01(\t\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\"\x19\n\x17SetActiveConfigResponse\"\x82\x01\n\x13\x44\x65leteConfigRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x02 \x01(\t\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\x12\x15\n\rnew_active_id\x18\x04 \x01(\t\"\x16\n\x14\x44\x65leteConfigResponse*5\n\x0fThinningConfVer\x12\x10\n\x0cTHIN_CONF_V1\x10\x00\x12\x10\n\x0cTHIN_CONF_V2\x10\x01\x32\xc9\x04\n\x0fThinningService\x12h\n\x15GetNextConfigurations\x12\x16.carbon.util.Timestamp\x1a\x37.carbon.frontend.thinning.GetNextConfigurationsResponse\x12`\n\x11GetNextActiveConf\x12\x16.carbon.util.Timestamp\x1a\x33.carbon.frontend.thinning.GetNextActiveConfResponse\x12\x82\x01\n\x13\x44\x65\x66ineConfiguration\x12\x34.carbon.frontend.thinning.DefineConfigurationRequest\x1a\x35.carbon.frontend.thinning.DefineConfigurationResponse\x12v\n\x0fSetActiveConfig\x12\x30.carbon.frontend.thinning.SetActiveConfigRequest\x1a\x31.carbon.frontend.thinning.SetActiveConfigResponse\x12m\n\x0c\x44\x65leteConfig\x12-.carbon.frontend.thinning.DeleteConfigRequest\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.thinning_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GETNEXTACTIVECONFRESPONSE'].fields_by_name['name']._loaded_options = None
  _globals['_GETNEXTACTIVECONFRESPONSE'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_SETACTIVECONFIGREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_SETACTIVECONFIGREQUEST'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_DELETECONFIGREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_DELETECONFIGREQUEST'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_THINNINGCONFVER']._serialized_start=836
  _globals['_THINNINGCONFVER']._serialized_end=889
  _globals['_GETNEXTCONFIGURATIONSRESPONSE']._serialized_start=96
  _globals['_GETNEXTCONFIGURATIONSRESPONSE']._serialized_end=238
  _globals['_GETNEXTACTIVECONFRESPONSE']._serialized_start=240
  _globals['_GETNEXTACTIVECONFRESPONSE']._serialized_end=333
  _globals['_DEFINECONFIGURATIONREQUEST']._serialized_start=336
  _globals['_DEFINECONFIGURATIONREQUEST']._serialized_end=495
  _globals['_DEFINECONFIGURATIONRESPONSE']._serialized_start=497
  _globals['_DEFINECONFIGURATIONRESPONSE']._serialized_end=538
  _globals['_SETACTIVECONFIGREQUEST']._serialized_start=540
  _globals['_SETACTIVECONFIGREQUEST']._serialized_end=650
  _globals['_SETACTIVECONFIGRESPONSE']._serialized_start=652
  _globals['_SETACTIVECONFIGRESPONSE']._serialized_end=677
  _globals['_DELETECONFIGREQUEST']._serialized_start=680
  _globals['_DELETECONFIGREQUEST']._serialized_end=810
  _globals['_DELETECONFIGRESPONSE']._serialized_start=812
  _globals['_DELETECONFIGRESPONSE']._serialized_end=834
  _globals['_THINNINGSERVICE']._serialized_start=892
  _globals['_THINNINGSERVICE']._serialized_end=1477
# @@protoc_insertion_point(module_scope)
