# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import thinning_pb2 as frontend_dot_thinning__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/thinning_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ThinningServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextConfigurations = channel.unary_unary(
                '/carbon.frontend.thinning.ThinningService/GetNextConfigurations',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_thinning__pb2.GetNextConfigurationsResponse.FromString,
                _registered_method=True)
        self.GetNextActiveConf = channel.unary_unary(
                '/carbon.frontend.thinning.ThinningService/GetNextActiveConf',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_thinning__pb2.GetNextActiveConfResponse.FromString,
                _registered_method=True)
        self.DefineConfiguration = channel.unary_unary(
                '/carbon.frontend.thinning.ThinningService/DefineConfiguration',
                request_serializer=frontend_dot_thinning__pb2.DefineConfigurationRequest.SerializeToString,
                response_deserializer=frontend_dot_thinning__pb2.DefineConfigurationResponse.FromString,
                _registered_method=True)
        self.SetActiveConfig = channel.unary_unary(
                '/carbon.frontend.thinning.ThinningService/SetActiveConfig',
                request_serializer=frontend_dot_thinning__pb2.SetActiveConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_thinning__pb2.SetActiveConfigResponse.FromString,
                _registered_method=True)
        self.DeleteConfig = channel.unary_unary(
                '/carbon.frontend.thinning.ThinningService/DeleteConfig',
                request_serializer=frontend_dot_thinning__pb2.DeleteConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_thinning__pb2.DeleteConfigResponse.FromString,
                _registered_method=True)


class ThinningServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextConfigurations(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveConf(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DefineConfiguration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActiveConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ThinningServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextConfigurations': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextConfigurations,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_thinning__pb2.GetNextConfigurationsResponse.SerializeToString,
            ),
            'GetNextActiveConf': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveConf,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_thinning__pb2.GetNextActiveConfResponse.SerializeToString,
            ),
            'DefineConfiguration': grpc.unary_unary_rpc_method_handler(
                    servicer.DefineConfiguration,
                    request_deserializer=frontend_dot_thinning__pb2.DefineConfigurationRequest.FromString,
                    response_serializer=frontend_dot_thinning__pb2.DefineConfigurationResponse.SerializeToString,
            ),
            'SetActiveConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActiveConfig,
                    request_deserializer=frontend_dot_thinning__pb2.SetActiveConfigRequest.FromString,
                    response_serializer=frontend_dot_thinning__pb2.SetActiveConfigResponse.SerializeToString,
            ),
            'DeleteConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteConfig,
                    request_deserializer=frontend_dot_thinning__pb2.DeleteConfigRequest.FromString,
                    response_serializer=frontend_dot_thinning__pb2.DeleteConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.thinning.ThinningService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.thinning.ThinningService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ThinningService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextConfigurations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.thinning.ThinningService/GetNextConfigurations',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_thinning__pb2.GetNextConfigurationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextActiveConf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.thinning.ThinningService/GetNextActiveConf',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_thinning__pb2.GetNextActiveConfResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DefineConfiguration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.thinning.ThinningService/DefineConfiguration',
            frontend_dot_thinning__pb2.DefineConfigurationRequest.SerializeToString,
            frontend_dot_thinning__pb2.DefineConfigurationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetActiveConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.thinning.ThinningService/SetActiveConfig',
            frontend_dot_thinning__pb2.SetActiveConfigRequest.SerializeToString,
            frontend_dot_thinning__pb2.SetActiveConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.thinning.ThinningService/DeleteConfig',
            frontend_dot_thinning__pb2.DeleteConfigRequest.SerializeToString,
            frontend_dot_thinning__pb2.DeleteConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
