# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/tractor.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/tractor.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x66rontend/tractor.proto\x12\x17\x63\x61rbon.frontend.tractor\x1a\x0futil/util.proto\"5\n\x0eTractorIfState\x12\x10\n\x08\x65xpected\x18\x01 \x01(\x08\x12\x11\n\tconnected\x18\x02 \x01(\x08\"7\n\x12TractorSafetyState\x12\x0f\n\x07is_safe\x18\x01 \x01(\x08\x12\x10\n\x08\x65nforced\x18\x02 \x01(\x08\"{\n\x1dGetNextTractorIfStateResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x36\n\x05state\x18\x02 \x01(\x0b\x32\'.carbon.frontend.tractor.TractorIfState\"\x83\x01\n!GetNextTractorSafetyStateResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12:\n\x05state\x18\x02 \x01(\x0b\x32+.carbon.frontend.tractor.TractorSafetyState\"/\n\x1bSetEnforcementPolicyRequest\x12\x10\n\x08\x65nforced\x18\x01 \x01(\x08\"\x1e\n\x1cSetEnforcementPolicyResponse2\xf0\x02\n\x0eTractorService\x12g\n\x15GetNextTractorIfState\x12\x16.carbon.util.Timestamp\x1a\x36.carbon.frontend.tractor.GetNextTractorIfStateResponse\x12o\n\x19GetNextTractorSafetyState\x12\x16.carbon.util.Timestamp\x1a:.carbon.frontend.tractor.GetNextTractorSafetyStateResponse\x12\x83\x01\n\x14SetEnforcementPolicy\x12\x34.carbon.frontend.tractor.SetEnforcementPolicyRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.tractor_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_TRACTORIFSTATE']._serialized_start=68
  _globals['_TRACTORIFSTATE']._serialized_end=121
  _globals['_TRACTORSAFETYSTATE']._serialized_start=123
  _globals['_TRACTORSAFETYSTATE']._serialized_end=178
  _globals['_GETNEXTTRACTORIFSTATERESPONSE']._serialized_start=180
  _globals['_GETNEXTTRACTORIFSTATERESPONSE']._serialized_end=303
  _globals['_GETNEXTTRACTORSAFETYSTATERESPONSE']._serialized_start=306
  _globals['_GETNEXTTRACTORSAFETYSTATERESPONSE']._serialized_end=437
  _globals['_SETENFORCEMENTPOLICYREQUEST']._serialized_start=439
  _globals['_SETENFORCEMENTPOLICYREQUEST']._serialized_end=486
  _globals['_SETENFORCEMENTPOLICYRESPONSE']._serialized_start=488
  _globals['_SETENFORCEMENTPOLICYRESPONSE']._serialized_end=518
  _globals['_TRACTORSERVICE']._serialized_start=521
  _globals['_TRACTORSERVICE']._serialized_end=889
# @@protoc_insertion_point(module_scope)
