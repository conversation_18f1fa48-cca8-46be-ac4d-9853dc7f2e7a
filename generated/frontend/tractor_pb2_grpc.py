# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import tractor_pb2 as frontend_dot_tractor__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/tractor_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TractorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextTractorIfState = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/GetNextTractorIfState',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_tractor__pb2.GetNextTractorIfStateResponse.FromString,
                _registered_method=True)
        self.GetNextTractorSafetyState = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState',
                request_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_tractor__pb2.GetNextTractorSafetyStateResponse.FromString,
                _registered_method=True)
        self.SetEnforcementPolicy = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/SetEnforcementPolicy',
                request_serializer=frontend_dot_tractor__pb2.SetEnforcementPolicyRequest.SerializeToString,
                response_deserializer=frontend_dot_tractor__pb2.SetEnforcementPolicyResponse.FromString,
                _registered_method=True)


class TractorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextTractorIfState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextTractorSafetyState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetEnforcementPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TractorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextTractorIfState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTractorIfState,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_tractor__pb2.GetNextTractorIfStateResponse.SerializeToString,
            ),
            'GetNextTractorSafetyState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTractorSafetyState,
                    request_deserializer=util_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_tractor__pb2.GetNextTractorSafetyStateResponse.SerializeToString,
            ),
            'SetEnforcementPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.SetEnforcementPolicy,
                    request_deserializer=frontend_dot_tractor__pb2.SetEnforcementPolicyRequest.FromString,
                    response_serializer=frontend_dot_tractor__pb2.SetEnforcementPolicyResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.tractor.TractorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.tractor.TractorService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TractorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextTractorIfState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.tractor.TractorService/GetNextTractorIfState',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_tractor__pb2.GetNextTractorIfStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextTractorSafetyState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState',
            util_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_tractor__pb2.GetNextTractorSafetyStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetEnforcementPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.tractor.TractorService/SetEnforcementPolicy',
            frontend_dot_tractor__pb2.SetEnforcementPolicyRequest.SerializeToString,
            frontend_dot_tractor__pb2.SetEnforcementPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
