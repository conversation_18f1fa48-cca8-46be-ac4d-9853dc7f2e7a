# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/translation.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/translation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1a\x66rontend/translation.proto\x12\x1b\x63\x61rbon.frontend.translation\"\x1d\n\x0cIntegerValue\x12\r\n\x05value\x18\x01 \x01(\x03\"\x1c\n\x0b\x44oubleValue\x12\r\n\x05value\x18\x01 \x01(\x01\"\x1c\n\x0bStringValue\x12\r\n\x05value\x18\x01 \x01(\t\"D\n\x10TemperatureValue\x12\x11\n\x07\x63\x65lcius\x18\x01 \x01(\x01H\x00\x12\x14\n\nfahrenheit\x18\x02 \x01(\x01H\x00\x42\x07\n\x05value\"\x1f\n\x0cPercentValue\x12\x0f\n\x07percent\x18\x01 \x01(\r\"\x1d\n\x0cVoltageValue\x12\r\n\x05volts\x18\x01 \x01(\x01\"\x1f\n\x0e\x46requencyValue\x12\r\n\x05hertz\x18\x01 \x01(\x01\"i\n\tAreaValue\x12\x0f\n\x05\x61\x63res\x18\x01 \x01(\x01H\x00\x12\x12\n\x08hectares\x18\x02 \x01(\x01H\x00\x12\x15\n\x0bsquare_feet\x18\x03 \x01(\x01H\x00\x12\x17\n\rsquare_meters\x18\x04 \x01(\x01H\x00\x42\x07\n\x05value\"g\n\rDurationValue\x12\x16\n\x0cmilliseconds\x18\x01 \x01(\x04H\x00\x12\x11\n\x07seconds\x18\x02 \x01(\x04H\x00\x12\x11\n\x07minutes\x18\x03 \x01(\x04H\x00\x12\x0f\n\x05hours\x18\x04 \x01(\x04H\x00\x42\x07\n\x05value\"\xa1\x01\n\rDistanceValue\x12\x15\n\x0bmillimeters\x18\x01 \x01(\x01H\x00\x12\x10\n\x06meters\x18\x02 \x01(\x01H\x00\x12\x14\n\nkilometers\x18\x03 \x01(\x01H\x00\x12\x10\n\x06inches\x18\x04 \x01(\x01H\x00\x12\x0e\n\x04\x66\x65\x65t\x18\x05 \x01(\x01H\x00\x12\x0f\n\x05miles\x18\x06 \x01(\x01H\x00\x12\x15\n\x0b\x63\x65ntimeters\x18\x07 \x01(\x01H\x00\x42\x07\n\x05value\"N\n\nSpeedValue\x12\x1d\n\x13kilometers_per_hour\x18\x01 \x01(\x01H\x00\x12\x18\n\x0emiles_per_hour\x18\x02 \x01(\x01H\x00\x42\x07\n\x05value\"\x97\x06\n\x14TranslationParameter\x12\x0c\n\x04name\x18\x01 \x01(\t\x12>\n\tint_value\x18\x02 \x01(\x0b\x32).carbon.frontend.translation.IntegerValueH\x00\x12@\n\x0c\x64ouble_value\x18\x03 \x01(\x0b\x32(.carbon.frontend.translation.DoubleValueH\x00\x12@\n\x0cstring_value\x18\x04 \x01(\x0b\x32(.carbon.frontend.translation.StringValueH\x00\x12J\n\x11temperature_value\x18\x05 \x01(\x0b\x32-.carbon.frontend.translation.TemperatureValueH\x00\x12\x42\n\rpercent_value\x18\x06 \x01(\x0b\x32).carbon.frontend.translation.PercentValueH\x00\x12\x42\n\rvoltage_value\x18\x07 \x01(\x0b\x32).carbon.frontend.translation.VoltageValueH\x00\x12\x46\n\x0f\x66requency_value\x18\x08 \x01(\x0b\x32+.carbon.frontend.translation.FrequencyValueH\x00\x12<\n\narea_value\x18\t \x01(\x0b\x32&.carbon.frontend.translation.AreaValueH\x00\x12\x44\n\x0e\x64uration_value\x18\n \x01(\x0b\x32*.carbon.frontend.translation.DurationValueH\x00\x12\x44\n\x0e\x64istance_value\x18\x0b \x01(\x0b\x32*.carbon.frontend.translation.DistanceValueH\x00\x12>\n\x0bspeed_value\x18\x0c \x01(\x0b\x32\'.carbon.frontend.translation.SpeedValueH\x00\x42\x07\n\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.translation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_INTEGERVALUE']._serialized_start=59
  _globals['_INTEGERVALUE']._serialized_end=88
  _globals['_DOUBLEVALUE']._serialized_start=90
  _globals['_DOUBLEVALUE']._serialized_end=118
  _globals['_STRINGVALUE']._serialized_start=120
  _globals['_STRINGVALUE']._serialized_end=148
  _globals['_TEMPERATUREVALUE']._serialized_start=150
  _globals['_TEMPERATUREVALUE']._serialized_end=218
  _globals['_PERCENTVALUE']._serialized_start=220
  _globals['_PERCENTVALUE']._serialized_end=251
  _globals['_VOLTAGEVALUE']._serialized_start=253
  _globals['_VOLTAGEVALUE']._serialized_end=282
  _globals['_FREQUENCYVALUE']._serialized_start=284
  _globals['_FREQUENCYVALUE']._serialized_end=315
  _globals['_AREAVALUE']._serialized_start=317
  _globals['_AREAVALUE']._serialized_end=422
  _globals['_DURATIONVALUE']._serialized_start=424
  _globals['_DURATIONVALUE']._serialized_end=527
  _globals['_DISTANCEVALUE']._serialized_start=530
  _globals['_DISTANCEVALUE']._serialized_end=691
  _globals['_SPEEDVALUE']._serialized_start=693
  _globals['_SPEEDVALUE']._serialized_end=771
  _globals['_TRANSLATIONPARAMETER']._serialized_start=774
  _globals['_TRANSLATIONPARAMETER']._serialized_end=1565
# @@protoc_insertion_point(module_scope)
