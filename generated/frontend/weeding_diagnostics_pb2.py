# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/weeding_diagnostics.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/weeding_diagnostics.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.frontend import image_stream_pb2 as frontend_dot_image__stream__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2
from generated.core.controls.exterminator.controllers.aimbot.process import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_aimbot__pb2
from generated.cv.runtime import cv_runtime_pb2 as cv_dot_runtime_dot_cv__runtime__pb2
from generated.thinning import thinning_pb2 as thinning_dot_thinning__pb2
from generated.recorder import recorder_pb2 as recorder_dot_recorder__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"frontend/weeding_diagnostics.proto\x12#carbon.frontend.weeding_diagnostics\x1a\x0futil/util.proto\x1a\x1b\x66rontend/image_stream.proto\x1a!weed_tracking/weed_tracking.proto\x1a\x42\x63ore/controls/exterminator/controllers/aimbot/process/aimbot.proto\x1a\x1b\x63v/runtime/cv_runtime.proto\x1a\x17thinning/thinning.proto\x1a\x17recorder/recorder.proto\"z\n\x1fRecordWeedingDiagnosticsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07ttl_sec\x18\x02 \x01(\r\x12\x1b\n\x13\x63rop_images_per_sec\x18\x03 \x01(\x02\x12\x1b\n\x13weed_images_per_sec\x18\x04 \x01(\x02\"/\n\x1dGetCurrentTrajectoriesRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\"\x1a\n\x18GetRecordingsListRequest\")\n\x19GetRecordingsListResponse\x12\x0c\n\x04name\x18\x01 \x03(\t\"U\n\x12GetSnapshotRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x17\n\x0fsnapshot_number\x18\x03 \x01(\r\"K\n\x13GetSnapshotResponse\x12\x34\n\x08snapshot\x18\x01 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot\".\n\x14OpenRecordingRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"%\n\x16TrajectoriesWithImages\x12\x0b\n\x03ids\x18\x01 \x03(\r\"\x1e\n\rPredictImages\x12\r\n\x05names\x18\x01 \x03(\t\"\xce\x01\n\x13PredictImagesPerCam\x12T\n\x06images\x18\x01 \x03(\x0b\x32\x44.carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry\x1a\x61\n\x0bImagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x41\n\x05value\x18\x02 \x01(\x0b\x32\x32.carbon.frontend.weeding_diagnostics.PredictImages:\x02\x38\x01\"\xf9\x05\n\x15OpenRecordingResponse\x12q\n\x15num_snapshots_per_row\x18\x01 \x03(\x0b\x32R.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry\x12P\n\x0erecording_data\x18\x02 \x01(\x0b\x32\x38.carbon.frontend.weeding_diagnostics.StaticRecordingData\x12y\n\x19trajectory_images_per_row\x18\x03 \x03(\x0b\x32V.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry\x12s\n\x16predict_images_per_row\x18\x04 \x03(\x0b\x32S.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry\x1a\x39\n\x17NumSnapshotsPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r:\x02\x38\x01\x1az\n\x1bTrajectoryImagesPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12J\n\x05value\x18\x02 \x01(\x0b\x32;.carbon.frontend.weeding_diagnostics.TrajectoriesWithImages:\x02\x38\x01\x1at\n\x18PredictImagesPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12G\n\x05value\x18\x02 \x01(\x0b\x32\x38.carbon.frontend.weeding_diagnostics.PredictImagesPerCam:\x02\x38\x01\"0\n\x16\x44\x65leteRecordingRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"\xe2\x02\n\x12\x43onfigNodeSnapshot\x12S\n\x06values\x18\x01 \x03(\x0b\x32\x43.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry\x12\\\n\x0b\x63hild_nodes\x18\x02 \x03(\x0b\x32G.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry\x1a-\n\x0bValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1aj\n\x0f\x43hildNodesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot:\x02\x38\x01\"1\n\x10\x43\x61meraDimensions\x12\r\n\x05width\x18\x01 \x01(\r\x12\x0e\n\x06height\x18\x02 \x01(\r\"\xb9\x01\n\nRowCameras\x12G\n\x04\x63\x61ms\x18\x01 \x03(\x0b\x32\x39.carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry\x1a\x62\n\tCamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x44\n\x05value\x18\x02 \x01(\x0b\x32\x35.carbon.frontend.weeding_diagnostics.CameraDimensions:\x02\x38\x01\"\xff\x08\n\x13StaticRecordingData\x12\x63\n\x0elasers_enabled\x18\x01 \x03(\x0b\x32K.carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry\x12L\n\x0broot_config\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\x12\x15\n\rrows_recorded\x18\x03 \x03(\x05\x12\x63\n\x0erow_dimensions\x18\x04 \x03(\x0b\x32K.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry\x12}\n\x1d\x63rop_safety_radius_mm_per_row\x18\x05 \x03(\x0b\x32V.carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry\x12:\n\x0fthinning_config\x18\x06 \x01(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x1b\n\x13recording_timestamp\x18\x07 \x01(\x03\x12]\n\x0brow_cameras\x18\x08 \x03(\x0b\x32H.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry\x12\x1c\n\x14weed_point_threshold\x18\t \x01(\x02\x12\x1c\n\x14\x63rop_point_threshold\x18\n \x01(\x02\x12#\n\x1bwheel_diameter_back_left_in\x18\x0b \x01(\x02\x12$\n\x1cwheel_diameter_back_right_in\x18\x0c \x01(\x02\x12$\n\x1cwheel_diameter_front_left_in\x18\r \x01(\x02\x12%\n\x1dwheel_diameter_front_right_in\x18\x0e \x01(\x02\x1a\x34\n\x12LasersEnabledEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1aS\n\x12RowDimensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.aimbot.GetDimensionsResponse:\x02\x38\x01\x1a?\n\x1d\x43ropSafetyRadiusMmPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x62\n\x0fRowCamerasEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.carbon.frontend.weeding_diagnostics.RowCameras:\x02\x38\x01\"Y\n\x18GetTrajectoryDataRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"j\n\x0cLastSnapshot\x12\x35\n\ntrajectory\x18\x01 \x01(\x0b\x32!.weed_tracking.TrajectorySnapshot\x12#\n\x1b\x64iagnostics_snapshot_number\x18\x02 \x01(\r\"C\n\x14\x45xtraTrajectoryField\x12\r\n\x05label\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\r\n\x05\x63olor\x18\x03 \x01(\t\"\\\n\x0bTargetImage\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\x04\x12\x15\n\rp2p_predict_x\x18\x03 \x01(\r\x12\x15\n\rp2p_predict_y\x18\x04 \x01(\r\"\x91\x01\n\x0fP2PPredictImage\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x65nter_x_px\x18\x02 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x03 \x01(\x05\x12\x11\n\tradius_px\x18\x04 \x01(\x05\x12\"\n\x02ts\x18\x05 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07pcam_id\x18\x06 \x01(\t\"\x92\x01\n\x1eTrajectoryPredictImageMetadata\x12\x13\n\x0b\x63\x65nter_x_px\x18\x01 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x02 \x01(\x05\x12\x11\n\tradius_px\x18\x03 \x01(\x05\x12\"\n\x02ts\x18\x04 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0f\n\x07pcam_id\x18\x05 \x01(\t\"\xcc\x03\n\x0eTrajectoryData\x12Z\n\rpredict_image\x18\x01 \x01(\x0b\x32\x43.carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata\x12G\n\rtarget_images\x18\x02 \x03(\x0b\x32\x30.carbon.frontend.weeding_diagnostics.TargetImage\x12H\n\rlast_snapshot\x18\x03 \x01(\x0b\x32\x31.carbon.frontend.weeding_diagnostics.LastSnapshot\x12O\n\x0c\x65xtra_fields\x18\x04 \x03(\x0b\x32\x39.carbon.frontend.weeding_diagnostics.ExtraTrajectoryField\x12\x13\n\x0b\x63rosshair_x\x18\x05 \x01(\r\x12\x13\n\x0b\x63rosshair_y\x18\x06 \x01(\r\x12P\n\x12p2p_predict_images\x18\x07 \x03(\x0b\x32\x34.carbon.frontend.weeding_diagnostics.P2PPredictImage\"a\n GetTrajectoryPredictImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"t\n\x1fGetTrajectoryTargetImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\x12\x12\n\nimage_name\x18\x04 \x01(\t\"!\n\nImageChunk\x12\x13\n\x0bimage_chunk\x18\x01 \x01(\x0c\"\\\n\x1eGetPredictImageMetadataRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x12\n\nimage_name\x18\x03 \x01(\t\"\x8b\x02\n\x1fGetPredictImageMetadataResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12>\n\x0b\x61nnotations\x18\x02 \x01(\x0b\x32).carbon.frontend.image_stream.Annotations\x12\x39\n\x0f\x64\x65\x65pweed_output\x18\x03 \x01(\x0b\x32 .cv.runtime.proto.DeepweedOutput\x12I\n\x1f\x64\x65\x65pweed_output_below_threshold\x18\x04 \x01(\x0b\x32 .cv.runtime.proto.DeepweedOutput\"T\n\x16GetPredictImageRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x12\n\nimage_name\x18\x03 \x01(\t\",\n\x12StartUploadRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\"W\n\x19GetNextUploadStateRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\"\xa2\x01\n\x1aGetNextUploadStateResponse\x12\x46\n\x0cupload_state\x18\x01 \x01(\x0e\x32\x30.carbon.frontend.weeding_diagnostics.UploadState\x12\"\n\x02ts\x18\x02 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x18\n\x10percent_uploaded\x18\x03 \x01(\r\"Y\n\"GetDeepweedPredictionsCountRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\r\"4\n#GetDeepweedPredictionsCountResponse\x12\r\n\x05\x63ount\x18\x01 \x01(\r\"a\n\x1dGetDeepweedPredictionsRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x03 \x01(\r\x12\x0b\n\x03idx\x18\x04 \x01(\r\"Y\n\x1eGetDeepweedPredictionsResponse\x12\x37\n\x0bpredictions\x18\x01 \x01(\x0b\x32\".recorder.DeepweedPredictionRecord\"V\n\x15\x46indTrajectoryRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\x12\x15\n\rtrajectory_id\x18\x03 \x01(\r\"d\n\x16\x46indTrajectoryResponse\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\r\x12\x35\n\ntrajectory\x18\x02 \x01(\x0b\x32!.weed_tracking.TrajectorySnapshot\"?\n\x15GetRotaryTicksRequest\x12\x16\n\x0erecording_name\x18\x01 \x01(\t\x12\x0e\n\x06row_id\x18\x02 \x01(\r\"h\n\x16GetRotaryTicksResponse\x12,\n\x07records\x18\x01 \x03(\x0b\x32\x1b.recorder.RotaryTicksRecord\x12 \n\x18wheel_encoder_resolution\x18\x02 \x01(\r\".\n\x1cSnapshotPredictImagesRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\"5\n\x0cPcamSnapshot\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"e\n\x1dSnapshotPredictImagesResponse\x12\x44\n\tsnapshots\x18\x01 \x03(\x0b\x32\x31.carbon.frontend.weeding_diagnostics.PcamSnapshot\"\x80\x01\n\x1dGetChipForPredictImageRequest\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x65nter_x_px\x18\x03 \x01(\x05\x12\x13\n\x0b\x63\x65nter_y_px\x18\x04 \x01(\x05\x12\x0e\n\x06row_id\x18\x05 \x01(\r\"4\n\x1eGetChipForPredictImageResponse\x12\x12\n\nchip_image\x18\x01 \x01(\x0c*2\n\x0bUploadState\x12\x08\n\x04NONE\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x08\n\x04\x44ONE\x10\x02\x32\xa4\x15\n\x19WeedingDiagnosticsService\x12t\n\x18RecordWeedingDiagnostics\x12\x44.carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest\x1a\x12.carbon.util.Empty\x12\x80\x01\n\x16GetCurrentTrajectories\x12\x42.carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest\x1a\".weed_tracking.DiagnosticsSnapshot\x12\x92\x01\n\x11GetRecordingsList\x12=.carbon.frontend.weeding_diagnostics.GetRecordingsListRequest\x1a>.carbon.frontend.weeding_diagnostics.GetRecordingsListResponse\x12\x86\x01\n\rOpenRecording\x12\x39.carbon.frontend.weeding_diagnostics.OpenRecordingRequest\x1a:.carbon.frontend.weeding_diagnostics.OpenRecordingResponse\x12\x80\x01\n\x0bGetSnapshot\x12\x37.carbon.frontend.weeding_diagnostics.GetSnapshotRequest\x1a\x38.carbon.frontend.weeding_diagnostics.GetSnapshotResponse\x12\x62\n\x0f\x44\x65leteRecording\x12;.carbon.frontend.weeding_diagnostics.DeleteRecordingRequest\x1a\x12.carbon.util.Empty\x12\x87\x01\n\x11GetTrajectoryData\x12=.carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest\x1a\x33.carbon.frontend.weeding_diagnostics.TrajectoryData\x12\x95\x01\n\x19GetTrajectoryPredictImage\x12\x45.carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12\x93\x01\n\x18GetTrajectoryTargetImage\x12\x44.carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12\xa4\x01\n\x17GetPredictImageMetadata\x12\x43.carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest\x1a\x44.carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse\x12\x81\x01\n\x0fGetPredictImage\x12;.carbon.frontend.weeding_diagnostics.GetPredictImageRequest\x1a/.carbon.frontend.weeding_diagnostics.ImageChunk0\x01\x12Z\n\x0bStartUpload\x12\x37.carbon.frontend.weeding_diagnostics.StartUploadRequest\x1a\x12.carbon.util.Empty\x12\x95\x01\n\x12GetNextUploadState\x12>.carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest\x1a?.carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse\x12\xb0\x01\n\x1bGetDeepweedPredictionsCount\x12G.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest\x1aH.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse\x12\xa1\x01\n\x16GetDeepweedPredictions\x12\x42.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest\x1a\x43.carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse\x12\x89\x01\n\x0e\x46indTrajectory\x12:.carbon.frontend.weeding_diagnostics.FindTrajectoryRequest\x1a;.carbon.frontend.weeding_diagnostics.FindTrajectoryResponse\x12\x89\x01\n\x0eGetRotaryTicks\x12:.carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest\x1a;.carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse\x12\x9e\x01\n\x15SnapshotPredictImages\x12\x41.carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest\x1a\x42.carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse\x12\xa1\x01\n\x16GetChipForPredictImage\x12\x42.carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.weeding_diagnostics_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_PREDICTIMAGESPERCAM_IMAGESENTRY']._loaded_options = None
  _globals['_PREDICTIMAGESPERCAM_IMAGESENTRY']._serialized_options = b'8\001'
  _globals['_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY']._loaded_options = None
  _globals['_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY']._serialized_options = b'8\001'
  _globals['_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY']._loaded_options = None
  _globals['_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY']._serialized_options = b'8\001'
  _globals['_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY']._loaded_options = None
  _globals['_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY']._serialized_options = b'8\001'
  _globals['_CONFIGNODESNAPSHOT_VALUESENTRY']._loaded_options = None
  _globals['_CONFIGNODESNAPSHOT_VALUESENTRY']._serialized_options = b'8\001'
  _globals['_CONFIGNODESNAPSHOT_CHILDNODESENTRY']._loaded_options = None
  _globals['_CONFIGNODESNAPSHOT_CHILDNODESENTRY']._serialized_options = b'8\001'
  _globals['_ROWCAMERAS_CAMSENTRY']._loaded_options = None
  _globals['_ROWCAMERAS_CAMSENTRY']._serialized_options = b'8\001'
  _globals['_STATICRECORDINGDATA_LASERSENABLEDENTRY']._loaded_options = None
  _globals['_STATICRECORDINGDATA_LASERSENABLEDENTRY']._serialized_options = b'8\001'
  _globals['_STATICRECORDINGDATA_ROWDIMENSIONSENTRY']._loaded_options = None
  _globals['_STATICRECORDINGDATA_ROWDIMENSIONSENTRY']._serialized_options = b'8\001'
  _globals['_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY']._loaded_options = None
  _globals['_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_options = b'8\001'
  _globals['_STATICRECORDINGDATA_ROWCAMERASENTRY']._loaded_options = None
  _globals['_STATICRECORDINGDATA_ROWCAMERASENTRY']._serialized_options = b'8\001'
  _globals['_UPLOADSTATE']._serialized_start=6814
  _globals['_UPLOADSTATE']._serialized_end=6864
  _globals['_RECORDWEEDINGDIAGNOSTICSREQUEST']._serialized_start=303
  _globals['_RECORDWEEDINGDIAGNOSTICSREQUEST']._serialized_end=425
  _globals['_GETCURRENTTRAJECTORIESREQUEST']._serialized_start=427
  _globals['_GETCURRENTTRAJECTORIESREQUEST']._serialized_end=474
  _globals['_GETRECORDINGSLISTREQUEST']._serialized_start=476
  _globals['_GETRECORDINGSLISTREQUEST']._serialized_end=502
  _globals['_GETRECORDINGSLISTRESPONSE']._serialized_start=504
  _globals['_GETRECORDINGSLISTRESPONSE']._serialized_end=545
  _globals['_GETSNAPSHOTREQUEST']._serialized_start=547
  _globals['_GETSNAPSHOTREQUEST']._serialized_end=632
  _globals['_GETSNAPSHOTRESPONSE']._serialized_start=634
  _globals['_GETSNAPSHOTRESPONSE']._serialized_end=709
  _globals['_OPENRECORDINGREQUEST']._serialized_start=711
  _globals['_OPENRECORDINGREQUEST']._serialized_end=757
  _globals['_TRAJECTORIESWITHIMAGES']._serialized_start=759
  _globals['_TRAJECTORIESWITHIMAGES']._serialized_end=796
  _globals['_PREDICTIMAGES']._serialized_start=798
  _globals['_PREDICTIMAGES']._serialized_end=828
  _globals['_PREDICTIMAGESPERCAM']._serialized_start=831
  _globals['_PREDICTIMAGESPERCAM']._serialized_end=1037
  _globals['_PREDICTIMAGESPERCAM_IMAGESENTRY']._serialized_start=940
  _globals['_PREDICTIMAGESPERCAM_IMAGESENTRY']._serialized_end=1037
  _globals['_OPENRECORDINGRESPONSE']._serialized_start=1040
  _globals['_OPENRECORDINGRESPONSE']._serialized_end=1801
  _globals['_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY']._serialized_start=1502
  _globals['_OPENRECORDINGRESPONSE_NUMSNAPSHOTSPERROWENTRY']._serialized_end=1559
  _globals['_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY']._serialized_start=1561
  _globals['_OPENRECORDINGRESPONSE_TRAJECTORYIMAGESPERROWENTRY']._serialized_end=1683
  _globals['_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY']._serialized_start=1685
  _globals['_OPENRECORDINGRESPONSE_PREDICTIMAGESPERROWENTRY']._serialized_end=1801
  _globals['_DELETERECORDINGREQUEST']._serialized_start=1803
  _globals['_DELETERECORDINGREQUEST']._serialized_end=1851
  _globals['_CONFIGNODESNAPSHOT']._serialized_start=1854
  _globals['_CONFIGNODESNAPSHOT']._serialized_end=2208
  _globals['_CONFIGNODESNAPSHOT_VALUESENTRY']._serialized_start=2055
  _globals['_CONFIGNODESNAPSHOT_VALUESENTRY']._serialized_end=2100
  _globals['_CONFIGNODESNAPSHOT_CHILDNODESENTRY']._serialized_start=2102
  _globals['_CONFIGNODESNAPSHOT_CHILDNODESENTRY']._serialized_end=2208
  _globals['_CAMERADIMENSIONS']._serialized_start=2210
  _globals['_CAMERADIMENSIONS']._serialized_end=2259
  _globals['_ROWCAMERAS']._serialized_start=2262
  _globals['_ROWCAMERAS']._serialized_end=2447
  _globals['_ROWCAMERAS_CAMSENTRY']._serialized_start=2349
  _globals['_ROWCAMERAS_CAMSENTRY']._serialized_end=2447
  _globals['_STATICRECORDINGDATA']._serialized_start=2450
  _globals['_STATICRECORDINGDATA']._serialized_end=3601
  _globals['_STATICRECORDINGDATA_LASERSENABLEDENTRY']._serialized_start=3299
  _globals['_STATICRECORDINGDATA_LASERSENABLEDENTRY']._serialized_end=3351
  _globals['_STATICRECORDINGDATA_ROWDIMENSIONSENTRY']._serialized_start=3353
  _globals['_STATICRECORDINGDATA_ROWDIMENSIONSENTRY']._serialized_end=3436
  _globals['_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_start=3438
  _globals['_STATICRECORDINGDATA_CROPSAFETYRADIUSMMPERROWENTRY']._serialized_end=3501
  _globals['_STATICRECORDINGDATA_ROWCAMERASENTRY']._serialized_start=3503
  _globals['_STATICRECORDINGDATA_ROWCAMERASENTRY']._serialized_end=3601
  _globals['_GETTRAJECTORYDATAREQUEST']._serialized_start=3603
  _globals['_GETTRAJECTORYDATAREQUEST']._serialized_end=3692
  _globals['_LASTSNAPSHOT']._serialized_start=3694
  _globals['_LASTSNAPSHOT']._serialized_end=3800
  _globals['_EXTRATRAJECTORYFIELD']._serialized_start=3802
  _globals['_EXTRATRAJECTORYFIELD']._serialized_end=3869
  _globals['_TARGETIMAGE']._serialized_start=3871
  _globals['_TARGETIMAGE']._serialized_end=3963
  _globals['_P2PPREDICTIMAGE']._serialized_start=3966
  _globals['_P2PPREDICTIMAGE']._serialized_end=4111
  _globals['_TRAJECTORYPREDICTIMAGEMETADATA']._serialized_start=4114
  _globals['_TRAJECTORYPREDICTIMAGEMETADATA']._serialized_end=4260
  _globals['_TRAJECTORYDATA']._serialized_start=4263
  _globals['_TRAJECTORYDATA']._serialized_end=4723
  _globals['_GETTRAJECTORYPREDICTIMAGEREQUEST']._serialized_start=4725
  _globals['_GETTRAJECTORYPREDICTIMAGEREQUEST']._serialized_end=4822
  _globals['_GETTRAJECTORYTARGETIMAGEREQUEST']._serialized_start=4824
  _globals['_GETTRAJECTORYTARGETIMAGEREQUEST']._serialized_end=4940
  _globals['_IMAGECHUNK']._serialized_start=4942
  _globals['_IMAGECHUNK']._serialized_end=4975
  _globals['_GETPREDICTIMAGEMETADATAREQUEST']._serialized_start=4977
  _globals['_GETPREDICTIMAGEMETADATAREQUEST']._serialized_end=5069
  _globals['_GETPREDICTIMAGEMETADATARESPONSE']._serialized_start=5072
  _globals['_GETPREDICTIMAGEMETADATARESPONSE']._serialized_end=5339
  _globals['_GETPREDICTIMAGEREQUEST']._serialized_start=5341
  _globals['_GETPREDICTIMAGEREQUEST']._serialized_end=5425
  _globals['_STARTUPLOADREQUEST']._serialized_start=5427
  _globals['_STARTUPLOADREQUEST']._serialized_end=5471
  _globals['_GETNEXTUPLOADSTATEREQUEST']._serialized_start=5473
  _globals['_GETNEXTUPLOADSTATEREQUEST']._serialized_end=5560
  _globals['_GETNEXTUPLOADSTATERESPONSE']._serialized_start=5563
  _globals['_GETNEXTUPLOADSTATERESPONSE']._serialized_end=5725
  _globals['_GETDEEPWEEDPREDICTIONSCOUNTREQUEST']._serialized_start=5727
  _globals['_GETDEEPWEEDPREDICTIONSCOUNTREQUEST']._serialized_end=5816
  _globals['_GETDEEPWEEDPREDICTIONSCOUNTRESPONSE']._serialized_start=5818
  _globals['_GETDEEPWEEDPREDICTIONSCOUNTRESPONSE']._serialized_end=5870
  _globals['_GETDEEPWEEDPREDICTIONSREQUEST']._serialized_start=5872
  _globals['_GETDEEPWEEDPREDICTIONSREQUEST']._serialized_end=5969
  _globals['_GETDEEPWEEDPREDICTIONSRESPONSE']._serialized_start=5971
  _globals['_GETDEEPWEEDPREDICTIONSRESPONSE']._serialized_end=6060
  _globals['_FINDTRAJECTORYREQUEST']._serialized_start=6062
  _globals['_FINDTRAJECTORYREQUEST']._serialized_end=6148
  _globals['_FINDTRAJECTORYRESPONSE']._serialized_start=6150
  _globals['_FINDTRAJECTORYRESPONSE']._serialized_end=6250
  _globals['_GETROTARYTICKSREQUEST']._serialized_start=6252
  _globals['_GETROTARYTICKSREQUEST']._serialized_end=6315
  _globals['_GETROTARYTICKSRESPONSE']._serialized_start=6317
  _globals['_GETROTARYTICKSRESPONSE']._serialized_end=6421
  _globals['_SNAPSHOTPREDICTIMAGESREQUEST']._serialized_start=6423
  _globals['_SNAPSHOTPREDICTIMAGESREQUEST']._serialized_end=6469
  _globals['_PCAMSNAPSHOT']._serialized_start=6471
  _globals['_PCAMSNAPSHOT']._serialized_end=6524
  _globals['_SNAPSHOTPREDICTIMAGESRESPONSE']._serialized_start=6526
  _globals['_SNAPSHOTPREDICTIMAGESRESPONSE']._serialized_end=6627
  _globals['_GETCHIPFORPREDICTIMAGEREQUEST']._serialized_start=6630
  _globals['_GETCHIPFORPREDICTIMAGEREQUEST']._serialized_end=6758
  _globals['_GETCHIPFORPREDICTIMAGERESPONSE']._serialized_start=6760
  _globals['_GETCHIPFORPREDICTIMAGERESPONSE']._serialized_end=6812
  _globals['_WEEDINGDIAGNOSTICSSERVICE']._serialized_start=6867
  _globals['_WEEDINGDIAGNOSTICSSERVICE']._serialized_end=9591
# @@protoc_insertion_point(module_scope)
