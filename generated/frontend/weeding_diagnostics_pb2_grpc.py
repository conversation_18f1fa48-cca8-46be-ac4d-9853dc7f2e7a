# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.frontend import weeding_diagnostics_pb2 as frontend_dot_weeding__diagnostics__pb2
from generated.util import util_pb2 as util_dot_util__pb2
from generated.weed_tracking import weed_tracking_pb2 as weed__tracking_dot_weed__tracking__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in frontend/weeding_diagnostics_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WeedingDiagnosticsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RecordWeedingDiagnostics = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.RecordWeedingDiagnosticsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetCurrentTrajectories = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetCurrentTrajectoriesRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_weed__tracking__pb2.DiagnosticsSnapshot.FromString,
                _registered_method=True)
        self.GetRecordingsList = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetRecordingsListRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetRecordingsListResponse.FromString,
                _registered_method=True)
        self.OpenRecording = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.OpenRecordingRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.OpenRecordingResponse.FromString,
                _registered_method=True)
        self.GetSnapshot = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetSnapshotRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetSnapshotResponse.FromString,
                _registered_method=True)
        self.DeleteRecording = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.DeleteRecordingRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetTrajectoryData = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryDataRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.TrajectoryData.FromString,
                _registered_method=True)
        self.GetTrajectoryPredictImage = channel.unary_stream(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryPredictImage',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryPredictImageRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
                _registered_method=True)
        self.GetTrajectoryTargetImage = channel.unary_stream(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryTargetImage',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryTargetImageRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
                _registered_method=True)
        self.GetPredictImageMetadata = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataResponse.FromString,
                _registered_method=True)
        self.GetPredictImage = channel.unary_stream(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImage',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
                _registered_method=True)
        self.StartUpload = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.StartUploadRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextUploadState = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateResponse.FromString,
                _registered_method=True)
        self.GetDeepweedPredictionsCount = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountResponse.FromString,
                _registered_method=True)
        self.GetDeepweedPredictions = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsResponse.FromString,
                _registered_method=True)
        self.FindTrajectory = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.FindTrajectoryRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.FindTrajectoryResponse.FromString,
                _registered_method=True)
        self.GetRotaryTicks = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksResponse.FromString,
                _registered_method=True)
        self.SnapshotPredictImages = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesResponse.FromString,
                _registered_method=True)
        self.GetChipForPredictImage = channel.unary_unary(
                '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage',
                request_serializer=frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageRequest.SerializeToString,
                response_deserializer=frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageResponse.FromString,
                _registered_method=True)


class WeedingDiagnosticsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def RecordWeedingDiagnostics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCurrentTrajectories(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRecordingsList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenRecording(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSnapshot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRecording(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTrajectoryData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTrajectoryPredictImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTrajectoryTargetImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPredictImageMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPredictImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextUploadState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedPredictionsCount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeepweedPredictions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindTrajectory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRotaryTicks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SnapshotPredictImages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChipForPredictImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WeedingDiagnosticsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RecordWeedingDiagnostics': grpc.unary_unary_rpc_method_handler(
                    servicer.RecordWeedingDiagnostics,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.RecordWeedingDiagnosticsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetCurrentTrajectories': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCurrentTrajectories,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetCurrentTrajectoriesRequest.FromString,
                    response_serializer=weed__tracking_dot_weed__tracking__pb2.DiagnosticsSnapshot.SerializeToString,
            ),
            'GetRecordingsList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRecordingsList,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetRecordingsListRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetRecordingsListResponse.SerializeToString,
            ),
            'OpenRecording': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenRecording,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.OpenRecordingRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.OpenRecordingResponse.SerializeToString,
            ),
            'GetSnapshot': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSnapshot,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetSnapshotRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetSnapshotResponse.SerializeToString,
            ),
            'DeleteRecording': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRecording,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.DeleteRecordingRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetTrajectoryData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTrajectoryData,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryDataRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.TrajectoryData.SerializeToString,
            ),
            'GetTrajectoryPredictImage': grpc.unary_stream_rpc_method_handler(
                    servicer.GetTrajectoryPredictImage,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryPredictImageRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.SerializeToString,
            ),
            'GetTrajectoryTargetImage': grpc.unary_stream_rpc_method_handler(
                    servicer.GetTrajectoryTargetImage,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetTrajectoryTargetImageRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.SerializeToString,
            ),
            'GetPredictImageMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPredictImageMetadata,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataResponse.SerializeToString,
            ),
            'GetPredictImage': grpc.unary_stream_rpc_method_handler(
                    servicer.GetPredictImage,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetPredictImageRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.ImageChunk.SerializeToString,
            ),
            'StartUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartUpload,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.StartUploadRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextUploadState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextUploadState,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateResponse.SerializeToString,
            ),
            'GetDeepweedPredictionsCount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedPredictionsCount,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountResponse.SerializeToString,
            ),
            'GetDeepweedPredictions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeepweedPredictions,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsResponse.SerializeToString,
            ),
            'FindTrajectory': grpc.unary_unary_rpc_method_handler(
                    servicer.FindTrajectory,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.FindTrajectoryRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.FindTrajectoryResponse.SerializeToString,
            ),
            'GetRotaryTicks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryTicks,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksResponse.SerializeToString,
            ),
            'SnapshotPredictImages': grpc.unary_unary_rpc_method_handler(
                    servicer.SnapshotPredictImages,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesResponse.SerializeToString,
            ),
            'GetChipForPredictImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChipForPredictImage,
                    request_deserializer=frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageRequest.FromString,
                    response_serializer=frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WeedingDiagnosticsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def RecordWeedingDiagnostics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics',
            frontend_dot_weeding__diagnostics__pb2.RecordWeedingDiagnosticsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCurrentTrajectories(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories',
            frontend_dot_weeding__diagnostics__pb2.GetCurrentTrajectoriesRequest.SerializeToString,
            weed__tracking_dot_weed__tracking__pb2.DiagnosticsSnapshot.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRecordingsList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList',
            frontend_dot_weeding__diagnostics__pb2.GetRecordingsListRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetRecordingsListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenRecording(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording',
            frontend_dot_weeding__diagnostics__pb2.OpenRecordingRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.OpenRecordingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSnapshot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot',
            frontend_dot_weeding__diagnostics__pb2.GetSnapshotRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetSnapshotResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRecording(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording',
            frontend_dot_weeding__diagnostics__pb2.DeleteRecordingRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTrajectoryData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData',
            frontend_dot_weeding__diagnostics__pb2.GetTrajectoryDataRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.TrajectoryData.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTrajectoryPredictImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryPredictImage',
            frontend_dot_weeding__diagnostics__pb2.GetTrajectoryPredictImageRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTrajectoryTargetImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryTargetImage',
            frontend_dot_weeding__diagnostics__pb2.GetTrajectoryTargetImageRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPredictImageMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata',
            frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetPredictImageMetadataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPredictImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImage',
            frontend_dot_weeding__diagnostics__pb2.GetPredictImageRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.ImageChunk.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload',
            frontend_dot_weeding__diagnostics__pb2.StartUploadRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextUploadState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState',
            frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetNextUploadStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedPredictionsCount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount',
            frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsCountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeepweedPredictions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions',
            frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetDeepweedPredictionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindTrajectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory',
            frontend_dot_weeding__diagnostics__pb2.FindTrajectoryRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.FindTrajectoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRotaryTicks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks',
            frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetRotaryTicksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SnapshotPredictImages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages',
            frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.SnapshotPredictImagesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetChipForPredictImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage',
            frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageRequest.SerializeToString,
            frontend_dot_weeding__diagnostics__pb2.GetChipForPredictImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
