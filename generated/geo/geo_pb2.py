# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: geo/geo.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'geo/geo.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rgeo/geo.proto\x12\ncarbon.geo\x1a\x1fgoogle/protobuf/timestamp.proto\"f\n\x0b\x43\x61ptureInfo\x12%\n\x08\x66ix_type\x18\x01 \x01(\x0e\x32\x13.carbon.geo.FixType\x12\x30\n\x0c\x63\x61pture_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x10\n\x02Id\x12\n\n\x02id\x18\x01 \x01(\t\"\x87\x01\n\x05Point\x12\x0b\n\x03lng\x18\x01 \x01(\x01\x12\x0b\n\x03lat\x18\x02 \x01(\x01\x12\x0b\n\x03\x61lt\x18\x03 \x01(\x01\x12-\n\x0c\x63\x61pture_info\x18\x04 \x01(\x0b\x32\x17.carbon.geo.CaptureInfo\x12\x1a\n\x02id\x18\x05 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x0c\n\x04name\x18\x06 \x01(\t\"/\n\nLineString\x12!\n\x06points\x18\x01 \x03(\x0b\x32\x11.carbon.geo.Point\"D\n\x06\x41\x62Line\x12\x1c\n\x01\x61\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x1c\n\x01\x62\x18\x02 \x01(\x0b\x32\x11.carbon.geo.Point\"\\\n\x07Polygon\x12)\n\x08\x62oundary\x18\x01 \x01(\x0b\x32\x17.carbon.geo.PolygonRing\x12&\n\x05holes\x18\x02 \x03(\x0b\x32\x17.carbon.geo.PolygonRing\"0\n\x0bPolygonRing\x12!\n\x06points\x18\x01 \x03(\x0b\x32\x11.carbon.geo.Point\"5\n\x0cMultiPolygon\x12%\n\x08polygons\x18\x01 \x03(\x0b\x32\x13.carbon.geo.Polygon*\x82\x01\n\x07\x46ixType\x12\x18\n\x14\x46IX_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06NO_FIX\x10\x01\x12\x08\n\x04GNSS\x10\x02\x12\x15\n\x11\x44IFFERENTIAL_GNSS\x10\x03\x12\r\n\tRTK_FIXED\x10\x04\x12\r\n\tRTK_FLOAT\x10\x05\x12\x12\n\x0e\x44\x45\x41\x44_RECKONING\x10\x06\x42=Z;github.com/carbonrobotics/protos/golang/generated/proto/geob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'geo.geo_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/geo'
  _globals['_FIXTYPE']._serialized_start=641
  _globals['_FIXTYPE']._serialized_end=771
  _globals['_CAPTUREINFO']._serialized_start=62
  _globals['_CAPTUREINFO']._serialized_end=164
  _globals['_ID']._serialized_start=166
  _globals['_ID']._serialized_end=182
  _globals['_POINT']._serialized_start=185
  _globals['_POINT']._serialized_end=320
  _globals['_LINESTRING']._serialized_start=322
  _globals['_LINESTRING']._serialized_end=369
  _globals['_ABLINE']._serialized_start=371
  _globals['_ABLINE']._serialized_end=439
  _globals['_POLYGON']._serialized_start=441
  _globals['_POLYGON']._serialized_end=533
  _globals['_POLYGONRING']._serialized_start=535
  _globals['_POLYGONRING']._serialized_end=583
  _globals['_MULTIPOLYGON']._serialized_start=585
  _globals['_MULTIPOLYGON']._serialized_end=638
# @@protoc_insertion_point(module_scope)
