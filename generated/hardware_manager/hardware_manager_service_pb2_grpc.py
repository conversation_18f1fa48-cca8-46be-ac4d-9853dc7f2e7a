# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.hardware_manager import hardware_manager_service_pb2 as hardware__manager_dot_hardware__manager__service__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in hardware_manager/hardware_manager_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class HardwareManagerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/Ping',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.PingRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.PingResponse.FromString,
                _registered_method=True)
        self.GetReady = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetReady',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetReadyRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetReadyResponse.FromString,
                _registered_method=True)
        self.GetNextDistance = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextDistance',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceResponse.FromString,
                _registered_method=True)
        self.GetNextVelocity = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextVelocity',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityResponse.FromString,
                _registered_method=True)
        self.GetRotaryTicks = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetRotaryTicks',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.FromString,
                _registered_method=True)
        self.GetDeltaTravelMM = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetDeltaTravelMM',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.FromString,
                _registered_method=True)
        self.GetWheelEncoderResolution = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetWheelEncoderResolution',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.FromString,
                _registered_method=True)
        self.GetSafetyStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetSafetyStatus',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.FromString,
                _registered_method=True)
        self.GetModemConnectionState = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetModemConnectionState',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateResponse.FromString,
                _registered_method=True)
        self.GetGPSData = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetGPSData',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataResponse.FromString,
                _registered_method=True)
        self.SetStrobeSettings = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetStrobeSettings',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.FromString,
                _registered_method=True)
        self.GetStrobeSettings = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetStrobeSettings',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
                _registered_method=True)
        self.GetManagedBoardErrors = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetManagedBoardErrors',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.FromString,
                _registered_method=True)
        self.GetSupervisoryStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetSupervisoryStatus',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.FromString,
                _registered_method=True)
        self.SetServerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetServerDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableResponse.FromString,
                _registered_method=True)
        self.SetBTLDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetBTLDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableResponse.FromString,
                _registered_method=True)
        self.SetScannersDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetScannersDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableResponse.FromString,
                _registered_method=True)
        self.SetWheelEncoderBoardDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.FromString,
                _registered_method=True)
        self.SetWheelEncoderDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetWheelEncoderDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.FromString,
                _registered_method=True)
        self.SetGPSDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetGPSDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableResponse.FromString,
                _registered_method=True)
        self.SuicideSwitch = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SuicideSwitch',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchResponse.FromString,
                _registered_method=True)
        self.CommandComputerPowerCycle = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/CommandComputerPowerCycle',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.FromString,
                _registered_method=True)
        self.SetMainContactorDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetMainContactorDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.FromString,
                _registered_method=True)
        self.SetStrobeDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetStrobeDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.FromString,
                _registered_method=True)
        self.SetAirConditionerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetAirConditionerDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.FromString,
                _registered_method=True)
        self.SetChillerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetChillerDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableResponse.FromString,
                _registered_method=True)
        self.SetTempBypassDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetTempBypassDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.FromString,
                _registered_method=True)
        self.SetHumidityBypassDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetHumidityBypassDisable',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.FromString,
                _registered_method=True)
        self.Get240vUptime = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/Get240vUptime',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeResponse.FromString,
                _registered_method=True)
        self.GetRuntime = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetRuntime',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeResponse.FromString,
                _registered_method=True)
        self.GetAvailableUSBStorage = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetAvailableUSBStorage',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.FromString,
                _registered_method=True)
        self.SetJimboxSpeed = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetJimboxSpeed',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.FromString,
                _registered_method=True)
        self.SetCruiseEnabled = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetCruiseEnabled',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.FromString,
                _registered_method=True)
        self.GetCruiseStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetCruiseStatus',
                request_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.FromString,
                _registered_method=True)


class HardwareManagerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReady(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDistance(self, request, context):
        """Rotary
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRotaryTicks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeltaTravelMM(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWheelEncoderResolution(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSafetyStatus(self, request, context):
        """Safety
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModemConnectionState(self, request, context):
        """Modem
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGPSData(self, request, context):
        """GPS
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetStrobeSettings(self, request, context):
        """Strobe
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStrobeSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetManagedBoardErrors(self, request, context):
        """Managed Boards
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSupervisoryStatus(self, request, context):
        """Supervisory PLC
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetServerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetBTLDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetScannersDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWheelEncoderBoardDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWheelEncoderDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGPSDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SuicideSwitch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CommandComputerPowerCycle(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetMainContactorDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetStrobeDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAirConditionerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetChillerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTempBypassDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetHumidityBypassDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get240vUptime(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRuntime(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableUSBStorage(self, request, context):
        """USB
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetJimboxSpeed(self, request, context):
        """Jimbox
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCruiseEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCruiseStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HardwareManagerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.PingRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.PingResponse.SerializeToString,
            ),
            'GetReady': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReady,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetReadyRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetReadyResponse.SerializeToString,
            ),
            'GetNextDistance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDistance,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceResponse.SerializeToString,
            ),
            'GetNextVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextVelocity,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityResponse.SerializeToString,
            ),
            'GetRotaryTicks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryTicks,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.SerializeToString,
            ),
            'GetDeltaTravelMM': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeltaTravelMM,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.SerializeToString,
            ),
            'GetWheelEncoderResolution': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWheelEncoderResolution,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.SerializeToString,
            ),
            'GetSafetyStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSafetyStatus,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.SerializeToString,
            ),
            'GetModemConnectionState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModemConnectionState,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateResponse.SerializeToString,
            ),
            'GetGPSData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGPSData,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataResponse.SerializeToString,
            ),
            'SetStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStrobeSettings,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.SerializeToString,
            ),
            'GetStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStrobeSettings,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
            ),
            'GetManagedBoardErrors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetManagedBoardErrors,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.SerializeToString,
            ),
            'GetSupervisoryStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSupervisoryStatus,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.SerializeToString,
            ),
            'SetServerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetServerDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableResponse.SerializeToString,
            ),
            'SetBTLDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetBTLDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableResponse.SerializeToString,
            ),
            'SetScannersDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetScannersDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableResponse.SerializeToString,
            ),
            'SetWheelEncoderBoardDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWheelEncoderBoardDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.SerializeToString,
            ),
            'SetWheelEncoderDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWheelEncoderDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.SerializeToString,
            ),
            'SetGPSDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGPSDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableResponse.SerializeToString,
            ),
            'SuicideSwitch': grpc.unary_unary_rpc_method_handler(
                    servicer.SuicideSwitch,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchResponse.SerializeToString,
            ),
            'CommandComputerPowerCycle': grpc.unary_unary_rpc_method_handler(
                    servicer.CommandComputerPowerCycle,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.SerializeToString,
            ),
            'SetMainContactorDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetMainContactorDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.SerializeToString,
            ),
            'SetStrobeDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStrobeDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.SerializeToString,
            ),
            'SetAirConditionerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAirConditionerDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.SerializeToString,
            ),
            'SetChillerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetChillerDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableResponse.SerializeToString,
            ),
            'SetTempBypassDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTempBypassDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.SerializeToString,
            ),
            'SetHumidityBypassDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetHumidityBypassDisable,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.SerializeToString,
            ),
            'Get240vUptime': grpc.unary_unary_rpc_method_handler(
                    servicer.Get240vUptime,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeResponse.SerializeToString,
            ),
            'GetRuntime': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRuntime,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeResponse.SerializeToString,
            ),
            'GetAvailableUSBStorage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableUSBStorage,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.SerializeToString,
            ),
            'SetJimboxSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.SetJimboxSpeed,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.SerializeToString,
            ),
            'SetCruiseEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCruiseEnabled,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.SerializeToString,
            ),
            'GetCruiseStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCruiseStatus,
                    request_deserializer=hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'hardware_manager.HardwareManagerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('hardware_manager.HardwareManagerService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class HardwareManagerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/Ping',
            hardware__manager_dot_hardware__manager__service__pb2.PingRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.PingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetReady',
            hardware__manager_dot_hardware__manager__service__pb2.GetReadyRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetReadyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextDistance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetNextDistance',
            hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetNextDistanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetNextVelocity',
            hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetNextVelocityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRotaryTicks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetRotaryTicks',
            hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeltaTravelMM(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetDeltaTravelMM',
            hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetWheelEncoderResolution(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetWheelEncoderResolution',
            hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSafetyStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetSafetyStatus',
            hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetModemConnectionState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetModemConnectionState',
            hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetModemConnectionStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetGPSData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetGPSData',
            hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetGPSDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetStrobeSettings',
            hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetStrobeSettings',
            hardware__manager_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetManagedBoardErrors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetManagedBoardErrors',
            hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSupervisoryStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetSupervisoryStatus',
            hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetServerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetServerDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetServerDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetBTLDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetBTLDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetBTLDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetScannersDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetScannersDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetScannersDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetWheelEncoderBoardDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetWheelEncoderDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetWheelEncoderDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetGPSDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetGPSDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetGPSDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SuicideSwitch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SuicideSwitch',
            hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SuicideSwitchResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CommandComputerPowerCycle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/CommandComputerPowerCycle',
            hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetMainContactorDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetMainContactorDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetStrobeDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetStrobeDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetAirConditionerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetAirConditionerDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetChillerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetChillerDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetChillerDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetTempBypassDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetTempBypassDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetHumidityBypassDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetHumidityBypassDisable',
            hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Get240vUptime(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/Get240vUptime',
            hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.Get240vUptimeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRuntime(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetRuntime',
            hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetRuntimeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableUSBStorage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetAvailableUSBStorage',
            hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetJimboxSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetJimboxSpeed',
            hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetCruiseEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/SetCruiseEnabled',
            hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCruiseStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/hardware_manager.HardwareManagerService/GetCruiseStatus',
            hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.SerializeToString,
            hardware__manager_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
