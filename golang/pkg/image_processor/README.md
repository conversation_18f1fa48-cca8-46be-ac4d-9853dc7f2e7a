# Image Processing Library

This is a **common library for image processing**, designed to support multiple use-cases such as:

- A hosted microservice (`cloud-image`)
- AWS Lambda functions for event-driven processing

The core functionality is built in Go and structured to be modular, enabling easy reuse across services.

---
# Orchestration

The main entry point for this library is [`image-processor.go`](./image-processor.go), which orchestrates the entire image processing pipeline:

1. **Check destination:**  
   If the destination already contains an image for this orchestration, the process **skips further operations and directly returns the destination URL.**

2. **Read the input image from a source**  
   (e.g., S3, local file system)

3. **Run image operations**  
   such as:
   - Crop
   - Resize

4. **Write the modified image to a destination**  
   (e.g., S3, local output folder)

---

## Interfaces

This library is built around two key interfaces to allow easy extensibility and reuse across different environments.

### FileManager

The `FileManager` interface abstracts reading from and writing to different storage backends.

Currently, `FileManager` is implemented for **Amazon S3**.  
This interface can be extended to implement other storage types like **local filesystem**, or any other custom storage solutions.

### Operation

The `Operation` interface provides a standard way to define image processing tasks.  
Examples of operations include:

- **Crop**
- **Resize**

Each operation implements this interface, allowing the `image-processor` orchestrator to run any sequence of operations in a consistent way.

---

## Development

To run the tests defined in this package, use:

```bash
go test -v .
```
