package image_processor

import (
	"fmt"
	"image"

	"github.com/carbonrobotics/cloud/golang/pkg/image_processor/operations"
)

type LambdaResponse struct {
	S3Url        string `json:"s3Url,omitempty"`
	ErrorMessage string `json:"errorMessage,omitempty"`
	ErrorType    string `json:"errorType,omitempty"`
}

type ImageProcessInputParams struct {
	X               int    `form:"x"  json:"x"`
	Y               int    `form:"y"  json:"y"`
	Width           int    `form:"width" json:"width"`
	Height          int    `form:"height" json:"height"`
	FillColor       string `form:"fill" json:"fill"`
	Operation       string `form:"op" json:"op"`
	Bucket          string `form:"bucket,omitempty" json:"bucket"`
	Key             string `form:"key,omitempty" json:"key"`
	GetPresignedUrl bool   `form:"get_presigned_url" default:"false" json:"get_presigned_url"`
	TargetBucket    string `form:"target_bucket,omitempty" json:"target_bucket,omitempty"`
	TargetKeyPrefix string `form:"target_key_prefix,omitempty" json:"target_key_prefix,omitempty"`
}

func (params ImageProcessInputParams) GetOperation() (operations.CacheOperation, error) {
	switch params.Operation {
	case "resize":
		return operations.ImageResize, nil
	case "crop":
		return operations.ImageCrop, nil
	default:
		return "", fmt.Errorf("unsupported operation: %s", params.Operation)
	}
}

func (params ImageProcessInputParams) CreateOperation() (operations.Operation, error) {
	op, err := params.GetOperation()
	if err != nil {
		return nil, err
	}
	switch op {
	case operations.ImageCrop:
		cropRect := image.Rect(params.X, params.Y, params.X+params.Width, params.Y+params.Height)
		return operations.NewCropOperation(cropRect, params.FillColor)
	case operations.ImageResize:
		resizeRect := image.Rect(params.X, params.Y, params.X+params.Width, params.Y+params.Height)
		return operations.NewResizeOperation(resizeRect)
	default:
		return nil, fmt.Errorf("unsupported operation: %s", params.Operation)
	}
}
