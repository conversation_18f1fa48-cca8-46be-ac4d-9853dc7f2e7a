package image_processor

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/carbonrobotics/cloud/golang/pkg/image_processor/operations"
)

func TestImageProcessorParamsGetOperation(t *testing.T) {
	tests := []struct {
		name          string
		inputParams   ImageProcessInputParams
		expectedType  interface{}
		expectedError bool
	}{
		{
			name: "WHEN input params contains operation as crop THEN return crop operation",
			inputParams: ImageProcessInputParams{
				X:         10,
				Y:         20,
				Width:     30,
				Height:    40,
				FillColor: "000000",
				Operation: "crop",
			},
			expectedType:  operations.ImageCrop,
			expectedError: false,
		},
		{
			name: "WHEN input params contains operation as resize THEN return crop operation",
			inputParams: ImageProcessInputParams{
				X:         15,
				Y:         25,
				Width:     50,
				Height:    60,
				Operation: "resize",
			},
			expectedType:  operations.ImageResize,
			expectedError: false,
		},
		{
			name: "WHEN input params contains operation as invalid THEN return error",
			inputParams: ImageProcessInputParams{
				X:         15,
				Y:         25,
				Width:     50,
				Height:    60,
				Operation: "invalid",
			},
			expectedType:  operations.ImageResize,
			expectedError: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			operationType, err := test.inputParams.GetOperation()
			if test.expectedError {
				assert.Error(t, err)
				assert.Equal(t, operations.CacheOperation(""), operationType)
				return
			} else {
				assert.Equal(t, test.expectedType, operationType)
			}
		})
	}
}

func TestImageProcessorParamsCreateOperation(t *testing.T) {
	tests := []struct {
		name         string
		inputParams  ImageProcessInputParams
		expectError  bool
		expectedType interface{}
	}{
		{
			name: "WHEN input params contains operation as crop THEN return crop operation",
			inputParams: ImageProcessInputParams{
				X:         10,
				Y:         20,
				Width:     30,
				Height:    40,
				FillColor: "000000",
				Operation: "crop",
			},
			expectError:  false,
			expectedType: &operations.CropOperation{},
		},
		{
			name: "WHEN input params contains operation as resize THEN return crop operation",
			inputParams: ImageProcessInputParams{
				X:         15,
				Y:         25,
				Width:     50,
				Height:    60,
				Operation: "resize",
			},
			expectError:  false,
			expectedType: &operations.ResizeOperation{},
		},
		{
			name: "WHEN input param contains unsupported operation THEN return error",
			inputParams: ImageProcessInputParams{
				Operation: "unsupported_operation",
			},
			expectError:  true,
			expectedType: nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			operation, err := test.inputParams.CreateOperation()
			if test.expectError {
				assert.Error(t, err)
				assert.Nil(t, operation)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, operation)
				assert.IsType(t, test.expectedType, operation)
			}
		})
	}
}
