package slack

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"sync"

	gocache "github.com/patrickmn/go-cache"
	log "github.com/sirupsen/logrus"
	"github.com/slack-go/slack"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	"github.com/carbonrobotics/cloud/golang/pkg/environment"
	"github.com/carbonrobotics/cloud/golang/pkg/grpc_clients"
	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	crcache "github.com/carbonrobotics/cloud/golang/services/portal/cache"
)

type NotifyType string

const TrainingFailure NotifyType = "training-failure"

// if messages are supposed to go to Slack, make sure they end up *somewhere*
// or we might miss something important
func DefaultSupportChannel(instance environment.Instance) string {
	if instance == environment.InstanceProduction {
		return "#support"
	}
	return "#support-dev"
}

func SupportTagForChannel(channelName string) string {
	// Development channels don't get a support tag.
	if channelName == "" || channelName == "software" || strings.HasSuffix(channelName, "-dev") {
		return ""
	}
	if strings.HasPrefix(channelName, "hh-ops-") {
		return "@remote-drivers"
	}
	return "@oncall-technical-support"
}

func GetAllChannels(slackClient *slack.Client) ([]slack.Channel, error) {
	params := &slack.GetConversationsParameters{
		ExcludeArchived: true,
		Limit:           1000,
		Types:           []string{"public_channel"},
	}
	channels, nextCursor, err := slackClient.GetConversations(params)
	if err != nil {
		return nil, err
	}

	for nextCursor != "" {
		params.Cursor = nextCursor
		page, cursor, err := slackClient.GetConversations(params)
		if err != nil {
			return nil, err
		}

		channels = append(channels, page...)
		nextCursor = cursor
	}
	return channels, nil
}

func sanitizeName(name string) string {
	return strings.Replace(name, "#", "", 1)
}

func GetChannelByName(slackClient *slack.Client, name string) (*slack.Channel, error) {
	channels, err := GetAllChannels(slackClient)
	if err != nil {
		return nil, err
	}
	sanitizedName := sanitizeName(name)
	for _, channel := range channels {
		if channel.GroupConversation.Name == sanitizedName {
			return &channel, nil
		}
	}
	return nil, nil
}

func supportSlackChannelName(context context.Context, instance environment.Instance, configClient *grpc_clients.ConfigClient, robotSyncerClient *robot_syncer.Client, caches *crcache.Caches, serial *carbon.ValidSerial) (string, error) {
	robotID, err := caches.RobotIDBySerialCache.Get(context, crcache.GetCacheableSerial(serial))
	if err != nil {
		return "", fmt.Errorf("failed to get robot ID for %s: %s", serial, err.Error())
	}
	robot, err := caches.RobotCache.Get(context, *robotID)
	if err != nil {
		return "", fmt.Errorf("failed to get robot %d: %s", robotID, err.Error())
	}

	if robot.SupportSlack != "" {
		return robot.SupportSlack, nil
	}

	var tree *config_service.ConfigNode
	if slices.Contains(carbon.TemplateSerials(), serial.String()) {
		return DefaultSupportChannel(instance), nil
	}
	var root string
	if serial.Class() == carbon.ClassBuds {
		root = "bud"
	} else if slices.Contains([]carbon.Classification{
		carbon.ClassSlayers,
		carbon.ClassSimulators,
		carbon.ClassReapers,
	}, serial.Class()) {
		root = "command"
	} else {
		root = "unknown"
	}

	if carbon.MigratedToRoSyConfigs(robot.SoftwareVersion) {
		tree, err = robotSyncerClient.GetConfig(context, robot_syncer.GetConfigParameters{
			Serial: serial.String(),
		})
		if err != nil {
			return "", fmt.Errorf("failed to get config from robot syncer: %s", err.Error())
		}
	} else {
		tree, _, err = configClient.GetConfig(serial, false, false)
		if err != nil {
			return "", fmt.Errorf("failed to access config: %s", err.Error())
		}
	}

	configPath := fmt.Sprintf("%s/commander/support_slack", root)
	supportSlack, err := config.GetNodeFromPath(tree, configPath)
	if err != nil {
		return "", fmt.Errorf("failed to determine support channel name: %s", err.Error())
	}
	if supportSlack == nil {
		return "", fmt.Errorf("failed to read support channel name from %s", configPath)
	}
	if supportSlack.Value == nil {
		return "", fmt.Errorf("failed to read support channel metadata: %e", err)
	}
	return supportSlack.Value.GetStringVal(), nil
}

func SendMessage(ctx context.Context, build environment.Build, slackClient *slack.Client, caches crcache.Caches, channelName string, getMessageOptions func(channel *slack.Channel) []slack.MsgOption) error {
	if build != environment.BuildRelease {
		log.Debug("Skipping Slack message relay in dev mode")
		return nil
	}

	channel, err := caches.SlackChannelCache.Get(ctx, channelName)
	if err != nil {
		log.WithError(err).Warnf("Failed to send Slack message, unable to get channel %s from Slack", channelName)
		return err
	}

	_, _, err = slackClient.PostMessage(
		channel.GroupConversation.ID,
		getMessageOptions(channel)...,
	)
	if err != nil {
		log.WithError(err).Warnf("Failed to send Slack message")
		return err
	}

	return nil
}

func SendSlackToSupportChannel(ctx context.Context, build environment.Build, slackClient *slack.Client, caches crcache.Caches, serial *carbon.ValidSerial, getMessageOptions func(channel *slack.Channel) []slack.MsgOption) error {
	channelName, err := caches.SupportChannelBySerialCache.Get(ctx, crcache.GetCacheableSerial(serial))
	if err != nil {
		log.WithError(err).Warnf("Failed to send Slack message, unable to get support channel channel for %s", serial)
		return err
	}
	return SendMessage(ctx, build, slackClient, caches, *channelName, getMessageOptions)
}

func InitializeSlackChannelCache(slackClient *slack.Client, caches *crcache.Caches) crcache.Cache[string, *slack.Channel] {
	loader := func(ctx context.Context, inputName string) (*slack.Channel, error) {
		name := sanitizeName(inputName)

		// get all channels because Slack doesn't let us get by name
		channels, err := GetAllChannels(slackClient)
		if err != nil {
			log.WithError(err).Error("Failed to get all channels")
			return nil, err
		}

		var result *slack.Channel
		// cache all channels, even the ones we didn't ask for so we don't have to do this again
		for _, channel := range channels {
			caches.SlackChannelCache.Set(ctx, channel.GroupConversation.Name, &channel)
			if channel.GroupConversation.Name == name {
				result = &channel
			}
		}
		if result == nil {
			return nil, fmt.Errorf("failed to find channel: %s", name)
		} else {
			return result, nil
		}
	}
	return crcache.NewLoadable(crcache.LoadableOptions[string, *slack.Channel]{
		Name:    "SlackChannelCache",
		Expiry:  gocache.NoExpiration,
		Cleanup: gocache.NoExpiration,
		Load:    loader,
	})
}

func InitializeSupportChannelBySerialCache(instance environment.Instance, configClient *grpc_clients.ConfigClient, robotSyncerClient *robot_syncer.Client, caches *crcache.Caches) crcache.Cache[crcache.CacheableSerial, *string] {
	loader := func(ctx context.Context, cacheable crcache.CacheableSerial) (*string, error) {
		name, err := supportSlackChannelName(ctx, instance, configClient, robotSyncerClient, caches, cacheable.Serial)
		if err != nil {
			log.WithError(err).Errorf("Failed to get support slack for %s", cacheable.Serial)
			return nil, err
		}
		return &name, err
	}
	return crcache.NewLoadable(crcache.LoadableOptions[crcache.CacheableSerial, *string]{
		Name:    "SupportChannelBySerialCache",
		Expiry:  gocache.NoExpiration,
		Cleanup: gocache.NoExpiration,
		Load:    loader,
	})
}

type Message struct {
	Type      NotifyType
	ChannelID string
	Message   string
	data      map[string]interface{}

	mu sync.Mutex
}

func NewTrainingFailureMessage(message string) *Message {
	return NewMessage(TrainingFailure, "", message)
}

func NewMessage(typ NotifyType, channelID, message string) *Message {
	return &Message{
		Type:      typ,
		ChannelID: channelID,
		Message:   message,
	}
}

func MessageFromJSON(b []byte) (*Message, error) {
	tmp := struct {
		Type      string `json:"type"`
		ChannelID string
		Message   string                 `json:"message"`
		Data      map[string]interface{} `json:"data"`
	}{}
	if err := json.Unmarshal(b, &tmp); err != nil {
		return nil, err
	}
	return &Message{
		Type:      NotifyType(tmp.Type),
		ChannelID: tmp.ChannelID,
		Message:   tmp.Message,
		data:      tmp.Data,
	}, nil
}

func (m *Message) Data() map[string]interface{} {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.data
}

func (m *Message) AddData(key string, value interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.data == nil {
		m.data = map[string]interface{}{
			key: value,
		}
	} else {
		m.data[key] = value
	}
}

// Validate checks if the message is valid for 'slacking'
func (m *Message) Validate() error {
	// we allow empty channel for certain types
	if len(m.Type) < 1 {
		return fmt.Errorf("invalid message type")
	} else if m.Type == "" && len(m.ChannelID) < 1 {
		return fmt.Errorf("invalid channel id")
	}
	if len(m.Message) < 1 {
		return fmt.Errorf("invalid message")
	}
	return nil
}

func (m *Message) ToJSON() string {
	b, _ := json.Marshal(struct {
		Type      NotifyType             `json:"type,omitempty"`
		ChannelID string                 `json:"channel_id,omitempty"`
		Message   string                 `json:"message"`
		Data      map[string]interface{} `json:"data,omitempty"`
	}{
		Type:      m.Type,
		ChannelID: m.ChannelID,
		Message:   m.Message,
		Data:      m.Data(),
	})
	return string(b)
}
