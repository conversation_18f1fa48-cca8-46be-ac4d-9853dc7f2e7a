# Cloud Image Service

This is an **HTTP service for image processing**, built on top of the [common image processing library](../../pkg/image_processor/README.md).
It exposes a RESTful API that allows on-demand operations like crop, resize, and more on images stored in S3.

---

## Overview

The `cloud-image` service acts as a thin HTTP layer over the core image processing library (see: `pkg/image_processor`).
It provides APIs to perform image operations such as crop and resize, returning a URL pointing to the processed image stored in S3.

The HTTP layer includes a built-in rate limiter. When the rate limit is exceeded, the service automatically offloads image processing to an AWS Lambda function.  
The Lambda uses the **same underlying image processing library**, ensuring consistent processing logic across both the HTTP service and Lambda, regardless of where the request is handled.


## Supported APIs

### `/image`

- **Method:** Any (`GET`, `POST`, etc. — handled by `router.Any("/image", ...)`)
- **Auth:** Requires Auth0 authentication (if configured)
- **Description:**  
  Main entry point for image processing operations.  
  Accepts query parameters specifying the operation (e.g., crop, resize), S3 bucket/key, and other options. 
  If no query parameter operations provided, the default behavior is to create a presigned url and return that.
  Returns a URL to the processed image in S3 (optionally as a presigned URL).
- **ReturnType:**
  {'s3_uri': <img_url>}

#### Query Parameters

| Parameter            | Type    | Description                                                       |
|----------------------|---------|-------------------------------------------------------------------|
| `x`                  | int     | X coordinate (top-left) for crop/resize rectangle                 |
| `y`                  | int     | Y coordinate (top-left) for crop/resize rectangle                 |
| `width`              | int     | Width of the crop/resize rectangle                                |
| `height`             | int     | Height of the crop/resize rectangle                               |
| `fill`               | string  | Fill color (hex, e.g., `ffffff`), used when crop area exceeds image bounds |
| `op`                 | string  | Operation to perform. Supported: `crop`, `resize`                 |
| `bucket`             | string  | S3 bucket name of the source image                                |
| `key`                | string  | S3 key (path) of the source image                                 |
| `get_presigned_url`  | bool    | If `true`, returns a presigned S3 URL to the processed image      |
| `target_bucket`      | string  | (Optional) S3 bucket to store the processed image                 |
| `target_key_prefix`  | string  | (Optional) S3 key prefix for storing the processed image          |

> 📌 **Notes:**
> - For `crop`/`resize`, `x`, `y`, `width`, and `height` are required.  
> - `fill` is only relevant for `crop` operations.

---

### `/`

- **Method:** `GET`
- **Description:**
  Health check endpoint. Returns a simple `"I am alive!"` string.

---

### `/metrics`

- **Method:** `GET`
- **Description:**  
  Exposes Prometheus metrics for the service.

---

### `/health`

- **Method:** `GET`
- **Description:**  
  Returns a JSON status object for health checks.

---

### Fallback (NoRoute)

- **Method:** Any
- **Description:**  
  Any other route will also be handled by the image processing logic, but **without authentication**.

---


## Development

To run the tests defined in this package, use:

```bash
go test -v .
```

### Using test env for validation

To test your changes in the test environment:
1. **Push your changes to Git as a PR.**
2. **Use ArgoCD (in test - https://argocd.cloud.carbonrobotics.com/applications/argocd/testing?view=network&resource=) to update the `cloud-image` deployment to use your PR branch instead of `master`.**
   - This allows the test environment to pull and run your changes for validation.

Once deployed, you can verify the updated functionality by calling the API endpoints in the test environment via curl.
The api calls are authenticated and therefore we need a valid JWT token to call cloud-image.

**Getting JWT token**
On the browser open the developer console and the networks tab. Go to `https://customer-test.cloud.carbonrobotics.com/` - look for the api call called `token` -> inspect the response -> copy the value of `access_token`.

Add the access_token as a variable in the terminal.

`TOKEN="<access token goes here>"`

#### Sample curl commands for test

**Command to run crop operation**

`curl 'https://cloud-image-test.cloud.carbonrobotics.com/image?op=crop&bucket=carbon-images&key=chip_image%2Fslayer1%2F2025-05-23%2Fchip_slayer1_row1_predict3_2025-05-23T21-01-40-000000Z.png&x=200&y=200&width=201&height=201&fill=%23000000' \
  -H "authorization: Bearer $TOKEN" \
  -H 'x-auth0-audience: https://customer-test.cloud.carbonrobotics.com'`


**Command to run resize operation**

`curl 'https://cloud-image-test.cloud.carbonrobotics.com/image?op=resize&bucket=carbon-images&key=chip_image%2Fslayer1%2F2025-05-23%2Fchip_slayer1_row1_predict3_2025-05-23T21-01-40-000000Z.png&x=200&y=200&width=201&height=201&fill=%23000000' \
  -H "authorization: Bearer $TOKEN" \
  -H 'x-auth0-audience: https://customer-test.cloud.carbonrobotics.com'`


**Command to generate pre-signed url without running operation**

`curl 'https://cloud-image-test.cloud.carbonrobotics.com/image?bucket=carbon-images&key=chip_image%2Fslayer1%2F2025-05-23%2Fchip_slayer1_row1_predict3_2025-05-23T21-01-40-000000Z.png' \
  -H "authorization: Bearer $TOKEN" \
  -H 'x-auth0-audience: https://customer-test.cloud.carbonrobotics.com'`