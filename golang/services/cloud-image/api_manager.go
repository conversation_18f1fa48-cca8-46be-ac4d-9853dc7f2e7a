package main

import (
	"fmt"
	"net/http"
	"path"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/lambda"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	"github.com/kelseyhightower/envconfig"

	log "github.com/sirupsen/logrus"

	craws "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/image_processor"
	"github.com/carbonrobotics/cloud/golang/pkg/image_processor/file_manager"
	"github.com/carbonrobotics/cloud/golang/pkg/image_processor/operations"
	"github.com/carbonrobotics/cloud/golang/pkg/util"
	"github.com/carbonrobotics/cloud/golang/services/cloud-image/helper"
)

type ImageProcessAWSConfig struct {
	TransformStorageBucket      string `envconfig:"TRANSFORM_STORAGE_BUCKET" default:"carbon-automation-testing"`
	TransformStorageKeyPrefix   string `envconfig:"TRANSFORM_STORAGE_KEY_PREFIX" default:"cache_test/"`
	AWSRegion                   string `envconfig:"AWS_REGION"                default:"us-west-2"`
	AWSAccessKeyID              string `envconfig:"AWS_ACCESS_KEY_ID"`
	AWSSecretAccessKey          string `envconfig:"AWS_SECRET_ACCESS_KEY"`
	AWSCredentialsFile          string `envconfig:"AWS_CREDENTIALS_FILE"`
	SpillOverLambdaFunctionName string `envconfig:"SPILLOVER_LAMBDA_FUNCTION_NAME" default:"image-service-testing"`
}

type APIManager struct {
	s3Facade     craws.AWSS3Facade
	lambdaFacade craws.LambdaFacade
	awsConfig    ImageProcessAWSConfig
	rqLimiter    chan struct{}
}

func NewAPIManager(rateLimit int) (*APIManager, error) {
	var awsConfig ImageProcessAWSConfig
	if err := envconfig.Process("", &awsConfig); err != nil {
		return nil, fmt.Errorf("failed to process env vars: %w", err)
	}

	var creds *credentials.Credentials
	if awsConfig.AWSCredentialsFile != "" {
		creds = credentials.NewSharedCredentials(awsConfig.AWSCredentialsFile, "")
	}
	if awsConfig.AWSAccessKeyID != "" && awsConfig.AWSSecretAccessKey != "" {
		creds = credentials.NewEnvCredentials()
	}
	sess := session.Must(session.NewSession(&aws.Config{
		Credentials: creds,
		Region:      aws.String(awsConfig.AWSRegion),
	}))

	s3Facade := craws.NewAWSS3Facade(s3.New(sess))
	lambdaClient := craws.NewLambdaFacade(lambda.New(sess))
	return &APIManager{
		s3Facade:     *s3Facade,
		lambdaFacade: *lambdaClient,
		awsConfig:    awsConfig,
		rqLimiter:    make(chan struct{}, rateLimit),
	}, nil
}

func (m *APIManager) Run(c *gin.Context, forcePresignedUrl bool) {
	var apiRequest image_processor.ImageProcessInputParams
	if err := c.ShouldBindQuery(&apiRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}

	if apiRequest.Bucket == "" || apiRequest.Key == "" {
		apiRequest.Bucket, apiRequest.Key = craws.SplitS3URL(c.Request.URL.Path)
	}

	getPresignedUrl := forcePresignedUrl || apiRequest.GetPresignedUrl

	// Empty operation means we are just getting a presigned URL
	if apiRequest.Operation == "" {
		destinationPath := util.BuildS3Path(apiRequest.Bucket, apiRequest.Key)
		m.constructAPIResponse(c, destinationPath, getPresignedUrl)
	} else {
		m.runOperation(c, apiRequest, getPresignedUrl)
	}
}

func (m *APIManager) runOperation(c *gin.Context, apiRequest image_processor.ImageProcessInputParams, getPresignedUrl bool) {
	operation, err := apiRequest.GetOperation()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid operation: %s", err.Error())})
		return
	}

	if m.rqLimiter != nil {
		select {
		case m.rqLimiter <- struct{}{}:
			defer func() { <-m.rqLimiter }()
		default:
			apiRequest.TargetBucket = m.awsConfig.TransformStorageBucket
			apiRequest.TargetKeyPrefix = m.awsConfig.TransformStorageKeyPrefix
			var destinationPath string
			destinationPath, err := helper.InvokeLambda(m.lambdaFacade, m.awsConfig.SpillOverLambdaFunctionName, apiRequest)
			if err != nil {
				if aerr, ok := err.(awserr.Error); ok {
					if aerr.Code() == lambda.ErrCodeTooManyRequestsException {
						c.Status(http.StatusTooManyRequests)
						return
					} else {
						c.JSON(http.StatusInternalServerError, gin.H{"error": aerr.Message()})
						return
					}
				}
			}
			m.constructAPIResponse(c, destinationPath, getPresignedUrl)
			return
		}
	}

	keyPrefix, inputFileName := path.Split(apiRequest.Key)
	srcFileManager, err := file_manager.NewS3FileManager(&m.s3Facade, apiRequest.Bucket, keyPrefix)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	destFileManager, err := file_manager.NewS3FileManager(&m.s3Facade, m.awsConfig.TransformStorageBucket, path.Join(m.awsConfig.TransformStorageKeyPrefix, keyPrefix))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	operations, err := apiRequest.CreateOperation()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	destinationPath, isCacheHit, err := image_processor.ProcessImage(srcFileManager, destFileManager, operations, inputFileName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	m.updateMetrics(isCacheHit, operation)
	m.constructAPIResponse(c, destinationPath, getPresignedUrl)
}

func (m *APIManager) constructAPIResponse(c *gin.Context, destinationPath string, getPresignedUrl bool) {
	if getPresignedUrl {
		destBucket, destKey := craws.SplitS3URL(destinationPath)
		presignedURL, err := m.s3Facade.PreSignedGetObjectURL(destBucket, destKey, time.Hour)
		if err != nil {
			log.WithError(err).Errorf("failed to get presigned bucket: %s, key: %s", destBucket, destKey)
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"s3_uri": presignedURL})
		return
	}
	c.JSON(http.StatusOK, gin.H{"s3_uri": destinationPath})
}

func (m *APIManager) updateMetrics(isCacheHit bool, operation operations.CacheOperation) {
	if isCacheHit {
		helper.CacheHitCounter.WithLabelValues("hosted", string(operation)).Inc()
	} else {
		helper.CacheMissCounter.WithLabelValues("hosted", string(operation)).Inc()
	}
}
