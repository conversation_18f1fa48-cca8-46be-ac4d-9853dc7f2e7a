package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	craws "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
)

func TestAPIManagerConstructAPIResponse(t *testing.T) {
	gin.SetMode(gin.TestMode)

	var destinationPath = "s3://testBucket/testPrefix/testImage.png"
	var presignedUrl, _ = url.Parse("https://testBucket.s3.amazonaws.com/testPrefix/testImage.png?AWSAccessKeyId=accessKey&Expires=1234567890&Signature=signature")

	s3Mock := new(mocks.S3Requester)
	lambdaMock := new(mocks.LambdaRequester)

	s3Mock.On("GetObjectRequest", mock.MatchedBy(func(input *s3.GetObjectInput) bool {
		return input.Bucket != nil && *input.Bucket == "testBucket"
	})).Return(&request.Request{
		Operation: &request.Operation{
			Name: "GetObject",
		},
		HTTPRequest: &http.Request{
			Method: "GET",
			URL:    presignedUrl,
		},
	}, nil)

	apiManager := &APIManager{
		s3Facade:     *craws.NewAWSS3Facade(s3Mock),
		lambdaFacade: *craws.NewLambdaFacade(lambdaMock),
		awsConfig: ImageProcessAWSConfig{
			TransformStorageBucket:    "testBucket",
			TransformStorageKeyPrefix: "testPrefix/",
		},
		rqLimiter: make(chan struct{}, 1),
	}

	tests := []struct {
		name            string
		destinationPath string
		getPresignedURL bool
		expectResponse  string
		expectedStatus  int
		expectedBody    map[string]string
	}{
		{
			name:            "WHEN constructAPIResponse called with pre-signed url false THEN return non-presigned url",
			destinationPath: destinationPath,
			getPresignedURL: false,
			expectedStatus:  200,
			expectedBody:    map[string]string{"s3_uri": destinationPath},
		},
		{
			name:            "WHEN constructAPIResponse called with pre-signed url true THEN return presigned url",
			destinationPath: destinationPath,
			getPresignedURL: true,
			expectedStatus:  200,
			expectedBody:    map[string]string{"s3_uri": presignedUrl.String()},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			apiManager.constructAPIResponse(c, test.destinationPath, test.getPresignedURL)
			assert.Equal(t, test.expectedStatus, w.Code, "status code")
			if test.expectedStatus == 200 {
				var body map[string]string
				_ = json.Unmarshal(w.Body.Bytes(), &body)
				assert.Equal(t, test.expectedBody, body, "response body")
			}
		})
	}
}

func TestAPIManagerRun(t *testing.T) {
	gin.SetMode(gin.TestMode)

	var presignedUrl, _ = url.Parse("https://testBucket.s3.amazonaws.com/testPrefix/testImage.png?AWSAccessKeyId=accessKey&Expires=1234567890&Signature=signature")

	s3Mock := new(mocks.S3Requester)
	lambdaMock := new(mocks.LambdaRequester)

	s3Mock.On("HeadObject", mock.Anything).Return(&s3.HeadObjectOutput{}, nil)

	s3Mock.On("GetObjectRequest", mock.Anything).Return(&request.Request{
		Operation: &request.Operation{
			Name: "GetObject",
		},
		HTTPRequest: &http.Request{
			Method: "GET",
			URL:    presignedUrl,
		},
	}, nil)

	apiManager := &APIManager{
		s3Facade:     *craws.NewAWSS3Facade(s3Mock),
		lambdaFacade: *craws.NewLambdaFacade(lambdaMock),
		awsConfig: ImageProcessAWSConfig{
			TransformStorageBucket:    "testBucket",
			TransformStorageKeyPrefix: "testPrefix/",
		},
		rqLimiter: make(chan struct{}, 1),
	}

	tests := []struct {
		name                 string
		req                  string
		forcePresignedUrl    bool
		expectedStatus       int
		expectedUrlIfSuccess string
	}{
		{
			name:                 "WHEN input url is invalid THEN return error response",
			req:                  "/?x=not-an-int",
			forcePresignedUrl:    false,
			expectedStatus:       http.StatusBadRequest,
			expectedUrlIfSuccess: "",
		},
		{
			name:                 "WHEN operation is invalid THEN return error response",
			req:                  "/testBucket/testKey/testFilename.png?op=invalid&x=0&y=0&width=100&height=100&op=invalid",
			forcePresignedUrl:    false,
			expectedStatus:       http.StatusBadRequest,
			expectedUrlIfSuccess: "",
		},
		{
			name:                 "WHEN operation is valid presigned url set THEN return success response with presigned url",
			req:                  "/testBucket/testKey/testFilename.png?x=0&y=0&width=100&height=100&op=crop&get_presigned_url=true",
			forcePresignedUrl:    false,
			expectedStatus:       http.StatusOK,
			expectedUrlIfSuccess: presignedUrl.String(),
		},
		{
			name:                 "WHEN operation is valid force presigned url set THEN return success response with presigned url",
			req:                  "/testBucket/testKey/testFilename.png?x=0&y=0&width=100&height=100&op=crop&get_presigned_url=false",
			forcePresignedUrl:    true,
			expectedStatus:       http.StatusOK,
			expectedUrlIfSuccess: presignedUrl.String(),
		},
		{
			name:                 "WHEN operation is empty and force presigned url set THEN return success response with presigned url",
			req:                  "/testBucket/testKey/testFilename.png",
			forcePresignedUrl:    true,
			expectedStatus:       http.StatusOK,
			expectedUrlIfSuccess: presignedUrl.String(),
		},
		{
			name:                 "WHEN operation is empty and force presigned url not set THEN return success response without presigned url",
			req:                  "/testBucket/testKey/testFilename.png",
			forcePresignedUrl:    false,
			expectedStatus:       http.StatusOK,
			expectedUrlIfSuccess: "s3://testBucket/testKey/testFilename.png",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(http.MethodGet, test.req, nil)

			apiManager.Run(c, test.forcePresignedUrl)
			assert.Equal(t, test.expectedStatus, w.Code, "status code")
			if test.expectedStatus == http.StatusOK {
				var body map[string]string
				_ = json.Unmarshal(w.Body.Bytes(), &body)
				assert.Equal(t, test.expectedUrlIfSuccess, body["s3_uri"], "response body")
			}
		})
	}
}
