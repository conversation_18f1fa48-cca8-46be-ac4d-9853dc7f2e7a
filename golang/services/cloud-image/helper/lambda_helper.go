package helper

import (
	"encoding/json"
	"fmt"

	craws "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/image_processor"
)

func InvokeLambda(lambdaFacade craws.LambdaFacade, lambdaFuncName string, apiRequest image_processor.ImageProcessInputParams) (string, error) {
	payload, err := json.Marshal(apiRequest)
	response, err := lambdaFacade.Invoke(lambdaFuncName, "RequestResponse", payload)
	if err != nil {
		return "", err
	}

	var lambdaResp image_processor.LambdaResponse
	operation, err := apiRequest.GetOperation()
	if err := json.Unmarshal(response.Payload, &lambdaResp); err == nil {
		if lambdaResp.ErrorMessage != "" {
			LambdaErrorInvocationCounter.WithLabelValues(string(operation)).Inc()
			return "", fmt.Errorf("lambda error: %s - %s", lambdaResp.ErrorType, lambdaResp.ErrorMessage)
		}
		LambdaSuccessInvocationCounter.WithLabelValues(string(operation)).Inc()
		return lambdaResp.S3Url, nil
	}

	LambdaErrorInvocationCounter.WithLabelValues(string(operation)).Inc()
	return "", fmt.Errorf("failed to parse lambda response: %s", string(response.Payload))
}
