package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/kelseyhightower/envconfig"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/pkg/auth/middleware"
	"github.com/carbonrobotics/cloud/golang/pkg/carbon"
	"github.com/carbonrobotics/crgo/metrics"
)

type ImageProcessAPIConfig struct {
	Auth0Domain        string   `envconfig:"AUTH0_DOMAIN"`
	Auth0Audiences     []string `envconfig:"AUTH0_AUDIENCES" default:"https://veselka.cloud.carbonrobotics.com"`
	CorsAllowedOrigins []string `envconfig:"CORS_ALLOWED_ORIGINS"`
	Port               int      `envconfig:"PORT" default:"8080"`
	RateLimit          int      `envconfig:"RATE_LIMIT" default:"10"`
}

func main() {
	var apiConfig ImageProcessAPIConfig
	if err := envconfig.Process("", &apiConfig); err != nil {
		log.Fatalln("failed to process env vars: %w", err)
	}

	apiManager, err := NewAPIManager(apiConfig.RateLimit)
	if err != nil {
		log.Fatalln("failed to create API manager: %w", err)
	}

	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	router := gin.Default()
	router.Use(metrics.PrometheusMiddlewareGin())
	router.GET("/", func(c *gin.Context) { c.String(http.StatusOK, "I am alive!") })
	router.GET("/metrics", metrics.HandlerGin())
	router.GET("/health", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"status": "ok"}) })
	router.NoRoute(func(c *gin.Context) { apiManager.Run(c, false) })

	// Create authenticated endpoint if auth0 domain is provided
	if len(apiConfig.Auth0Domain) > 0 && len(apiConfig.Auth0Audiences) > 0 {
		auth0Middleware := middleware.Auth0JWTDynamicValidatorGin(apiConfig.Auth0Domain, apiConfig.Auth0Audiences[0], apiConfig.Auth0Audiences)
		corsCfg := buildCorsConfig(apiConfig.CorsAllowedOrigins)
		router.Any("/image", corsCfg, auth0Middleware, func(c *gin.Context) {
			apiManager.Run(c, true)
		})
	}

	if err := startServer(ctx, router, apiConfig.Port); err != nil {
		log.Fatalf("failed to start server: %v", err)
	}
}

func buildCorsConfig(corsAllowedOrigins []string) gin.HandlerFunc {
	corsCfg := cors.DefaultConfig()
	corsCfg.AllowOrigins = corsAllowedOrigins
	corsCfg.AllowCredentials = true
	corsCfg.AddExposeHeaders(http.CanonicalHeaderKey("content-length"))
	corsCfg.AddAllowHeaders(http.CanonicalHeaderKey("authorization"), carbon.Auth0AudienceHttpHeader)

	return cors.New(corsCfg)
}

func startServer(ctx context.Context, router *gin.Engine, port int) error {
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      router,
		ReadTimeout:  10 * time.Minute,
		WriteTimeout: 10 * time.Minute,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	<-ctx.Done()
	log.Println("shutting down...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		return fmt.Errorf("server forced to shutdown: %w", err)
	}
	return nil
}
