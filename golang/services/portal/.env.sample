###
# ENVIRONMENT
###
# INSTANCE=PRODUCTION
# INSTANCE=STAGING
# INSTANCE=TESTING
INSTANCE=DEVELOPMENT
# BUILD=RELEASE
BUILD=DEBUG


###
# PORTS ON HOST
###
SERVER_PORT=8085
GRPC_PORT=9090
VESELKA_PORT=8083
CLIENT_PORT=3000
ROBOT_SYNCER_PORT=8080

###
# VESELKA
###
VESELKA_URL=http://veselka:8080
## STAGING ##
# VESELKA_ENV=staging
# REACT_APP_VESELKA_URL=https://veselka-stg.cloud.carbonrobotics.com
## PRODUCTION ##
VESELKA_ENV=production
REACT_APP_VESELKA_URL=https://veselka.cloud.carbonrobotics.com


###
# ROBOT SYNCER
###
ROBOT_SYNCER_URL=robot-syncer:8080
SHARED_CONFIG_SCHEMAS_PATH=/data/shared_config_schemas
## PRODUCTION ##
# ROBOT_SYNCER_URL=robot-syncer.cloud.carbonrobotics.com
# ROBOT_SYNCER_ENV=production
## STAGING ##
# ROBOT_SYNCER_URL=robot-syncer-stg.cloud.carbonrobotics.com
ROBOT_SYNCER_ENV=staging

###
# RTC SERVICES (REMOTE TRACTOR CONTROL)
###
RTC_JOBS_TARGET=rtc-jobs-grpc-stg.cloud.carbonrobotics.com
REACT_APP_RTC_LOCATOR_URL=https://rtc-locator-stg.cloud.carbonrobotics.com
REACT_APP_RTC_JOBS_URL=https://rtc-jobs-stg.cloud.carbonrobotics.com

###
# CONFIG
###
## STAGING ##
# CONFIG_ENV=staging
# CONFIG_URL=config-stg.cloud.carbonrobotics.com:443
##
## PRODUCTION ##
CONFIG_ENV=production
CONFIG_URL=config.cloud.carbonrobotics.com:443
##

###
# DATABASE
###
PSQL_USER=test
PSQL_PASSWORD=testing
PSQL_HOST=db
PSQL_PORT=5432
PSQL_DB=test
PSQL_CONNECTION=postgres://${PSQL_USER}:${PSQL_PASSWORD}@${PSQL_HOST}:${PSQL_PORT}/${PSQL_DB}?sslmode=disable
## STAGING ##

# PROD_CONNECTION=<GET FROM BITWARDEN>
##
## PRODUCTION ##
PROD_CONNECTION=<GET FROM BITWARDEN>
##

LOG_LEVEL=debug

###
# Server
###
DEV_EMAILS=<YOUR EMAIL>
DIST_PATH=ui/dist
# DIST_PATH=../ui/dist
PERF_MODE=true
ROOT_URL=http://localhost:${CLIENT_PORT}

###
# CLIENT
###
SERVER_URL=http://localhost:${SERVER_PORT}

###
# AUTH0
###
# Applications:
#   - "GRPC client" is called "Ops Center (Robots)" in Auth0
#   - "REST client" is called "Ops Center" in Auth0
#   - "Server" is called "Ops Center (Management)" in Auth0

## STAGING ##
AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
AUTH0_GRPC_CLIENT_ID=zzF8okD4fuot74zXgsYdpr90SfPRUKx2
AUTH0_GRPC_CLIENT_SECRET=<GET FROM ANSEL>
AUTH0_REST_AUDIENCE=https://customer-staging.cloud.carbonrobotics.com
AUTH0_REST_CLIENT_ID=0mtA1j7fbSjDqTuxifEkFn6J9H9g6IHm
AUTH0_REST_CLIENT_SECRET=<GET FROM ANSEL>
AUTH0_SERVER_ID=MYHcBFSW6i48V4m2rJjiRXRvk1OsN3eE
AUTH0_SERVER_SECRET=<GET FROM ANSEL>
AUTH0_TENANT_DOMAIN=dev-jx2b6o0d.us.auth0.com

## PRODUCTION ##
# AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
# AUTH0_GRPC_CLIENT_ID=zkuRcsVeAZDnBVOcvPmjKT69iALemQkj
# AUTH0_GRPC_CLIENT_SECRET=<GET FROM ANSEL>
# AUTH0_REST_AUDIENCE=https://customer.cloud.carbonrobotics.com
# AUTH0_REST_CLIENT_ID=HsMRYnWO4zzckHSy5oKn8vNueRjM7AOT
# AUTH0_REST_CLIENT_SECRET=<GET FROM ANSEL>
# AUTH0_SERVER_ID=2xCVmxAmWtG7vdheT8aKhlw0Tf94gRpu
# AUTH0_SERVER_SECRET=<GET FROM ANSEL>
# AUTH0_TENANT_DOMAIN=carbonrobotics.us.auth0.com

## TEST ##
# AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
# AUTH0_GRPC_CLIENT_ID=p27gPTHGb2dfqZaJEb0D7K51i4Kl1S62
# AUTH0_GRPC_CLIENT_SECRET=<GET FROM ANSEL>
# AUTH0_REST_AUDIENCE=https://customer-test.cloud.carbonrobotics.com
# AUTH0_REST_CLIENT_ID=PBz7DgkZ51ALcVMv23KFS9MP0zsW2h5Z
# AUTH0_REST_CLIENT_SECRET=<GET FROM ANSEL>
# AUTH0_SERVER_ID=CdeX4sl7myyLWuaB0Wowcx3s4MrSf903
# AUTH0_SERVER_SECRET=<GET FROM ANSEL>
# AUTH0_TENANT_DOMAIN=carbonrobotics-dev.us.auth0.com

## Common substitutions across environments ##
REACT_APP_AUTH0_AUDIENCE=${AUTH0_REST_AUDIENCE}
REACT_APP_AUTH0_AUTH_DOMAIN=${AUTH0_TENANT_DOMAIN}
REACT_APP_AUTH0_REST_CLIENT_ID=${AUTH0_REST_CLIENT_ID}

###
# AWS
###
AWS_BUCKET=carbon-portal-testing
AWS_CREDENTIALS_FILE=/home/<USER>/.aws/credentials
VPC_JUMPHOST=<GET FROM SETH>@<GET FROM SETH>

###
# EMAIL
###
EMAIL_FROM_DOMAIN=carbonrobotics.com
SENDGRID_API_KEY=<GET FROM ANSEL>

###
# GITHUB
###
GITHUB_TOKEN=<USE YOURS>

###
# GOLANG
###
GO111MODULE=on
GOPRIVATE=github.com/carbonrobotics

###
# GOOGLE ANALYTICS
###
REACT_APP_GOOGLE_MEASUREMENT_ID=G-XGTZTFRDCF

###
# MAPBOX
###
REACT_APP_MAPBOX_ACCESS_TOKEN=<GET FROM ANSEL>

###
# MUI
###
REACT_APP_MUI_LICENSE_KEY=<GET FROM ANSEL>

###
# PAGERDUTY
###
# PAGERDUTY_API_KEY=<GET FROM ANSEL>
# PAGERDUTY_INTEGRATION_KEY=<GET FROM ANSEL>

###
# REMOTE.IT
###
R3_ACCESS_KEY_ID=RQ2HGFNXT4DJJB3RVWAC
R3_SECRET_ACCESS_KEY=<GET FROM ANSEL>
R3_ACCOUNT_ID=E8B8B878-B754-4112-A239-86D24178C2A9

###
# SALESFORCE
###
# SALESFORCE_WEBHOOK_TOKEN=9e84a4a4-236d-4332-8a87-28269c56c8af
## STAGING ##
# SALESFORCE_URL=https://carbonrobotics--partial.my.salesforce.com/
# SALESFORCE_USER=<EMAIL>
# SALESFORCE_PASSWORD=<GET FROM ANSEL>
# SALESFORCE_TOKEN=<GET FROM ANSEL>
# SALESFORCE_CONSUMER_KEY=<GET FROM ANSEL>
# SALESFORCE_CONSUMER_SECRET=<GET FROM ANSEL>
##
## PRODUCTION ##
# SALESFORCE_URL=https://carbonrobotics.my.salesforce.com/
# SALESFORCE_USER=<EMAIL>
# SALESFORCE_PASSWORD=<GET FROM ANSEL>
# SALESFORCE_TOKEN=<GET FROM ANSEL>
# SALESFORCE_CONSUMER_KEY=<GET FROM ANSEL>
# SALESFORCE_CONSUMER_SECRET=<GET FROM ANSEL>

###
# SENTRY
###
SENTRY_DSN=https://<EMAIL>/****************
REACT_APP_SENTRY_DSN=https://<EMAIL>/6177154
REACT_APP_SENTRY_ENVIRONMENT=development
SENTRY_AUTH_TOKEN=<GET FROM ANSEL>

###
# SLACK
###
SLACK_OAUTH_TOKEN=<GET FROM ANSEL>

###
# TABLEAU
###
TABLEAU_WEBHOOK_TOKEN=<GET FROM ANSEL>

###
# TWILIO
###
# TWILIO_API_KEY=<GET FROM ANSEL>

###
# STREAM
###
REACT_APP_STREAM_API_KEY=5zpznx355bth
STREAM_API_SECRET=<GET FROM ANSEL>

###
# IDM
###
REACT_APP_IDM_URL=https://idm-stg.cloud.carbonrobotics.com

###
# S3 CACHE PROXY
###
REACT_APP_IMAGE_SERVICE_URL=https://cloud-image-test.cloud.carbonrobotics.com

###
# JIRA
###
JIRA_WEBHOOK_TOKEN=<GET FROM ANSEL>


###
# DEVELOPMENT FEATURE FLAG
###
# if enabled, you can see the light/dark mode theme switcher
REACT_APP_FEATURE_LIGHT_DARK_THEME=false