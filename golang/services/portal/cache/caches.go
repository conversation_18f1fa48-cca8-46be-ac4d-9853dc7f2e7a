package cache

import (
	"context"
	"fmt"
	"time"

	chat "github.com/GetStream/stream-chat-go/v6"
	"github.com/auth0/go-auth0/management"
	ekocache "github.com/eko/gocache/lib/v4/cache"
	"github.com/eko/gocache/lib/v4/store"
	ekoadapter "github.com/eko/gocache/store/go_cache/v4"
	gocache "github.com/patrickmn/go-cache"
	"github.com/slack-go/slack"

	modelslib "github.com/carbonrobotics/cloud/golang/pkg/carbon/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/cacheview"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/crgo/carbon"
)

// Cache[K, V] is a variant of ekocache.CacheInterface[V] that is also
// type-safe on the key type. You can use the Restrict function to convert a
// CacheInterface[V] to a Cache[K, V] for any key type K.
type Cache[K comparable, V any] interface {
	Get(ctx context.Context, key K) (V, error)
	Set(ctx context.Context, key K, value V, options ...store.Option) error
	Delete(ctx context.Context, key K) error
	Invalidate(ctx context.Context, options ...store.InvalidateOption) error
	Clear(ctx context.Context) error
	GetType() string
}

type Caches struct {
	CropByIDCache               Cache[string, *modelslib.Crop]
	CustomerCache               Cache[uint, *models.Customer]
	CustomerIDByRobotIDCache    Cache[uint, *uint]
	HasNewAlarmsCache           Cache[uint, bool]
	JobNameByUUIDCache          Cache[string, *string]
	ProfileNameByUUIDCache      Cache[string, *string]
	RobotCache                  Cache[uint, *models.Robot]
	RobotIDBySerialCache        Cache[CacheableSerial, *uint]
	RobotSerialByIDCache        Cache[uint, *carbon.ValidSerial]
	SlackChannelCache           Cache[string, *slack.Channel]
	StreamChannelCache          ekocache.CacheInterface[*chat.CreateChannelResponse] // crgo requires this for now
	StreamMessageProcessedCache MessageProcessedCache
	SupportChannelBySerialCache Cache[CacheableSerial, *string]
	UserCache                   Cache[string, *CachedUser]
	UserCacheNonLoading         ekocache.CacheInterface[*CachedUser]
	UserIDByEmailCache          Cache[string, *string]
}

// clear all caches
func (caches Caches) Clear(ctx context.Context) {
	caches.CropByIDCache.Clear(ctx)
	caches.CustomerCache.Clear(ctx)
	caches.CustomerIDByRobotIDCache.Clear(ctx)
	caches.HasNewAlarmsCache.Clear(ctx)
	caches.JobNameByUUIDCache.Clear(ctx)
	caches.ProfileNameByUUIDCache.Clear(ctx)
	caches.RobotCache.Clear(ctx)
	caches.RobotIDBySerialCache.Clear(ctx)
	caches.RobotSerialByIDCache.Clear(ctx)
	caches.SlackChannelCache.Clear(ctx)
	caches.StreamChannelCache.Clear(ctx)
	caches.SupportChannelBySerialCache.Clear(ctx)
	caches.UserCache.Clear(ctx)
	caches.UserIDByEmailCache.Clear(ctx)
}

// GetAuth0User is a convenience helper to read the Auth0 user from UserCache
// without the extra cached data.
func (caches Caches) GetAuth0User(ctx context.Context, userID string) (*management.User, error) {
	user, err := caches.UserCache.Get(ctx, userID)
	if err != nil || user == nil {
		return nil, err
	}
	return user.Auth0User, nil
}

type keyTypeError[K comparable] struct {
	got any
}

func (err *keyTypeError[K]) Error() string {
	var wantType K
	return fmt.Sprintf("invalid cache key: got %T (%v), want %T", err.got, err.got, wantType)
}

type LoadableOptions[K comparable, V any] struct {
	// Name sets a globally unique name for this cache. If provided, this cache
	// will be given to cacheview.Register so that its usage statistics can be
	// introspected later.
	Name string
	// Expiry sets the time after which a cache entry is no longer valid.
	// See patrickmn/go-cache.New.
	Expiry time.Duration
	// Cleanup sets the interval on which stale cache entries will be actually
	// cleaned up and evicted. See patrickmn/go-cache.New.
	Cleanup time.Duration
	// Load is called on a cache miss, and should compute the value to be
	// filled into the cache for a given key.
	Load func(ctx context.Context, key K) (V, error)
}

func NewLoadable[K comparable, V any](opts LoadableOptions[K, V]) Cache[K, V] {
	cache, _ := NewLoadableAndDirectClient(opts)
	return cache
}

// NewLoadableAndDirectClient creates a new loadable cache and returns it,
// along with a client that can be used to access the underlying cache without
// going through the loader.
func NewLoadableAndDirectClient[K comparable, V any](opts LoadableOptions[K, V]) (Cache[K, V], ekocache.CacheInterface[V]) {
	store := gocache.New(opts.Expiry, opts.Cleanup)
	if opts.Name != "" {
		cacheview.Register(opts.Name, store)
	}
	adapter := ekoadapter.NewGoCache(store)
	loader := func(ctx context.Context, rawKey any) (V, error) {
		key, ok := rawKey.(K)
		if !ok {
			// We don't expect to hit this, since we only expose this loader
			// through a restrictedCache with the right type bound.
			var zero V
			return zero, &keyTypeError[K]{got: rawKey}
		}
		return opts.Load(ctx, key)
	}
	client := ekocache.New[V](adapter)
	return Restrict[K, V](ekocache.NewLoadable[V](loader, client)), client
}

// restrictedCache coaxes Go's type system to accept contravariance.
type restrictedCache[K comparable, V any] struct {
	inner ekocache.CacheInterface[V]
}

func (c restrictedCache[K, V]) Get(ctx context.Context, key K) (V, error) {
	return c.inner.Get(ctx, key)
}

func (c restrictedCache[K, V]) Set(ctx context.Context, key K, value V, options ...store.Option) error {
	return c.inner.Set(ctx, key, value, options...)
}

func (c restrictedCache[K, V]) Delete(ctx context.Context, key K) error {
	return c.inner.Delete(ctx, key)
}

func (c restrictedCache[K, V]) Invalidate(ctx context.Context, options ...store.InvalidateOption) error {
	return c.inner.Invalidate(ctx, options...)
}

func (c restrictedCache[K, V]) Clear(ctx context.Context) error {
	return c.inner.Clear(ctx)
}

func (c restrictedCache[K, V]) GetType() string {
	return c.inner.GetType()
}

func Restrict[K comparable, V any](cache ekocache.CacheInterface[V]) Cache[K, V] {
	return restrictedCache[K, V]{inner: cache}
}

// CacheableSerial is a carbon.ValidSerial wrapper
// that implements CacheKeyGenerator from eko/gocache
type CacheableSerial struct {
	Serial *carbon.ValidSerial
}

func (cacheableSerial CacheableSerial) GetCacheKey() string {
	return cacheableSerial.Serial.String()
}

func GetCacheableSerial(serial *carbon.ValidSerial) CacheableSerial {
	return CacheableSerial{
		Serial: serial,
	}
}

// make sure we implemented this right
var _ ekocache.CacheKeyGenerator = CacheableSerial{}
