package cachewarm

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

var (
	reportAuthorsWarmedTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "report_authors_warmed_total",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Cumulative number of user cache entries that have been pre-filled",
	})
	reportAuthorsSkippedTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "report_authors_skipped_total",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Cumulative number of user cache entries that have been skipped because they were already warm",
	})
	reportAuthorsFillSecondsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "report_authors_fill_seconds_total",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Cumulative time spent in the cache fill step of pre-warming user caches",
	})

	reportAuthorsQueriesTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "report_authors_queries_total",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Cumulative number of times we've queried the set of authors whose cache entries should be warmed",
	})
	reportAuthorsQuerySecondsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "report_authors_query_seconds_total",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Cumulative time spent getting the set of authors whose cache entries should be warmed",
	})
	reportAuthorsUserCount = promauto.NewGauge(prometheus.GaugeOpts{
		Name:      "report_authors_user_count",
		Subsystem: "cachewarm",
		Namespace: utils.PortalBackgroundNamespace,
		Help:      "Number of report authors for whom we're currently warming cache entries",
	})
)
