package cachewarm

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/reports"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

// ReportAuthors keeps caches warm for users who have created reports.
func ReportAuthors(ctx context.Context, db *gorm.DB, caches *cache.Caches, delay time.Duration) {
	logger := utils.Logger(ctx)

	for {
		start := time.Now()
		authors, err := reports.GetAuthors(ctx, db)
		elapsed := time.Since(start)
		reportAuthorsQuerySecondsTotal.Add(elapsed.Seconds())
		reportAuthorsQueriesTotal.Inc()
		reportAuthorsUserCount.Set(float64(len(authors)))

		if err != nil {
			logger.WithError(err).Warn("Failed to get report authors")
			select {
			case <-ctx.Done():
				return
			case <-time.After(delay):
				continue
			}
		}

		warmed := 0
		skipped := 0
		for author := range authors {
			entry := logger.WithField("author", author)

			// If this user is already cached, skip it and try the next one.
			if _, err := caches.UserCacheNonLoading.Get(ctx, author); err == nil {
				skipped++
				reportAuthorsSkippedTotal.Inc()
				continue
			}

			start := time.Now()
			user, err := caches.GetAuth0User(ctx, author)
			elapsed := time.Since(start)
			reportAuthorsFillSecondsTotal.Add(elapsed.Seconds())

			if err == nil {
				warmed++
				reportAuthorsWarmedTotal.Inc()
				entry.WithField("userExists", user != nil).Debug("Warmed user cache successfully")
			} else {
				entry.WithError(err).Warn("Failed to warm user cache")
			}

			select {
			case <-ctx.Done():
				return
			case <-time.After(delay):
			}
		}

		logger.WithField("warmed", warmed).WithField("skipped", skipped).Info("Finished warming loop")
		// Make sure we sleep at least once per cycle so we don't just loop out
		// of control when everything's already warm.
		if warmed == 0 {
			select {
			case <-ctx.Done():
				return
			case <-time.After(delay):
				continue
			}
		}
	}
}
