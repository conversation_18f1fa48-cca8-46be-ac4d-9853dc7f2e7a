package main

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os/exec"
	"strings"
	"time"

	chat "github.com/GetStream/stream-chat-go/v6"
	"github.com/auth0/go-auth0/management"
	"github.com/aws/aws-sdk-go/aws"
	awscredentials "github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cloudwatchlogs"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/getsentry/sentry-go"
	"github.com/go-co-op/gocron"
	"github.com/kelseyhightower/envconfig"
	"github.com/ringsaturn/tzf"
	"github.com/simpleforce/simpleforce"
	log "github.com/sirupsen/logrus"
	"github.com/slack-go/slack"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
	"google.golang.org/grpc"
	grpccredentials "google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"gorm.io/gorm"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/environment"
	"github.com/carbonrobotics/cloud/golang/pkg/grpc_clients"
	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	slackutil "github.com/carbonrobotics/cloud/golang/pkg/slack"
	veselkalib "github.com/carbonrobotics/cloud/golang/pkg/veselka"
	"github.com/carbonrobotics/cloud/golang/services/portal/alarms"
	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/cachewarm"
	"github.com/carbonrobotics/cloud/golang/services/portal/customers"
	"github.com/carbonrobotics/cloud/golang/services/portal/data_migrations"
	database "github.com/carbonrobotics/cloud/golang/services/portal/database"
	"github.com/carbonrobotics/cloud/golang/services/portal/jobs"
	"github.com/carbonrobotics/cloud/golang/services/portal/profiles"
	"github.com/carbonrobotics/cloud/golang/services/portal/prommetrics"
	"github.com/carbonrobotics/cloud/golang/services/portal/reports"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/stream"
	"github.com/carbonrobotics/cloud/golang/services/portal/users"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/cloud/golang/services/portal/veselka"
	buildlib "github.com/carbonrobotics/crgo/build"
	httplib "github.com/carbonrobotics/crgo/http"
	"github.com/carbonrobotics/crgo/remoteit"
	"github.com/carbonrobotics/protos/golang/generated/proto/rtc"
)

func logPair(label string, value string) {
	log.Infof("%15s : %s", label, value)
}

type PortalService struct {
	// Environment variables
	AWSBucket                     string        `envconfig:"AWS_BUCKET" default:"carbon-portal-stg"`
	AWSCredentialsFile            string        `envconfig:"AWS_CREDENTIALS_FILE"`
	AWSRegion                     string        `envconfig:"AWS_REGION" default:"us-west-2"`
	Auth0Domain                   string        `envconfig:"AUTH0_TENANT_DOMAIN" default:"dev-jx2b6o0d.us.auth0.com"`
	Auth0GRPCAudience             string        `envconfig:"AUTH0_GRPC_AUDIENCE" default:"https://robot.carbonrobotics.com"`
	Auth0RESTAudience             string        `envconfig:"AUTH0_REST_AUDIENCE" default:"https://customer.cloud.carbonrobotics.com"`
	Auth0RESTClientId             string        `envconfig:"AUTH0_REST_CLIENT_ID"`
	Auth0RESTClientSecret         string        `envconfig:"AUTH0_REST_CLIENT_SECRET"`
	Auth0ServerId                 string        `envconfig:"AUTH0_SERVER_ID"`
	Auth0ServerSecret             string        `envconfig:"AUTH0_SERVER_SECRET"`
	BlockStartupForever           bool          `envconfig:"BLOCK_STARTUP_FOREVER"`
	Instance                      string        `envconfig:"INSTANCE" default:""`
	Build                         string        `envconfig:"BUILD" default:""`
	CachewarmAuthorInterval       time.Duration `envconfig:"CACHEWARM_AUTHOR_INTERVAL" default:"10s"`
	ConfigFile                    string        `envconfig:"CONFIG_FILE" default:"config.json"`
	ConfigURL                     string        `envconfig:"CONFIG_URL" default:"config-stg.cloud.carbonrobotics.com:443"`
	DevEmails                     string        `envconfig:"DEV_EMAILS" default:""`
	DistPath                      string        `envconfig:"DIST_PATH" default:"/dist"`
	DBDiscourageTransactions      bool          `envconfig:"DB_DISCOURAGE_TRANSACTIONS"`
	DBGormPrepareStmt             bool          `envconfig:"DB_GORM_PREPARE_STMT"`
	DBMaxConns                    int32         `envconfig:"DB_MAX_CONNS" default:"0"`
	DBReadOnly                    bool          `envconfig:"DB_READ_ONLY"`
	DBTrace                       bool          `envconfig:"DB_TRACE"`
	Environment                   string        `envconfig:"ENVIRONMENT" default:"PRODUCTION"`
	FeatureFlagOverrides          string        `envconfig:"FEATURE_FLAG_OVERRIDES" default:""`
	JiraWebhookToken              string        `envconfig:"JIRA_WEBHOOK_TOKEN"`
	LogLevel                      string        `envconfig:"LOG_LEVEL" default:"debug"`
	PSQLConnection                string        `envconfig:"PSQL_CONNECTION" default:"postgresql://test:testing@localhost:5432/test?sslmode=disable"`
	PagerDutyAPIKey               string        `envconfig:"PAGERDUTY_API_KEY"`
	PagerDutyIntegrationKey       string        `envconfig:"PAGERDUTY_INTEGRATION_KEY"`
	PerfMode                      bool          `envconfig:"PERF_MODE"`
	Port                          int           `envconfig:"SERVER_PORT" default:"8080"`
	GRPCPort                      int           `envconfig:"GRPC_PORT" default:"9090"`
	RITAccountID                  string        `envconfig:"R3_ACCOUNT_ID"`
	RobotSyncerURL                string        `envconfig:"ROBOT_SYNCER_URL" default:"https://robot-syncer-stg.cloud.carbonrobotics.com"`
	RobotPrometheusExportInterval time.Duration `envconfig:"ROBOT_PROMETHEUS_EXPORT_INTERVAL" default:"14s"`
	RTCJobsTarget                 string        `envconfig:"RTC_JOBS_TARGET" default:"rtc-jobs-grpc-stg.cloud.carbonrobotics.com"`
	RootURL                       string        `envconfig:"ROOT_URL" default:"https://localhost:3000"`
	SalesforceConsumerKey         string        `envconfig:"SALESFORCE_CONSUMER_KEY"`
	SalesforceConsumerSecret      string        `envconfig:"SALESFORCE_CONSUMER_SECRET"`
	SalesforcePassword            string        `envconfig:"SALESFORCE_PASSWORD"`
	SalesforceToken               string        `envconfig:"SALESFORCE_TOKEN"`
	SalesforceURL                 string        `envconfig:"SALESFORCE_URL" default:"https://carbonrobotics.my.salesforce.com"`
	SalesforceUser                string        `envconfig:"SALESFORCE_USER" default:"<EMAIL>"`
	SalesforceWebhookToken        string        `envconfig:"SALESFORCE_WEBHOOK_TOKEN"`
	SendgridAPIKey                string        `envconfig:"SENDGRID_API_KEY"`
	SentryDSN                     string        `envconfig:"SENTRY_DSN"`
	SharedConfigSchemasPath       string        `envconfig:"SHARED_CONFIG_SCHEMAS_PATH" default:"/data/shared_config_schemas"`
	SlackOauthToken               string        `envconfig:"SLACK_OAUTH_TOKEN"`
	SpatialExportConfigFile       string        `envconfig:"SPATIAL_EXPORT_CONFIG_FILE" default:"/config/spatial-config.json"`
	StartupDescription            string        `envconfig:"STARTUP_DESCRIPTION" default:"Please stand by…"`
	StartupTitle                  string        `envconfig:"STARTUP_TITLE" default:"Ops Center is starting up."`
	StreamAPIKey                  string        `envconfig:"REACT_APP_STREAM_API_KEY"`
	StreamAPISecret               string        `envconfig:"STREAM_API_SECRET"`
	TableauWebhookToken           string        `envconfig:"TABLEAU_WEBHOOK_TOKEN"`
	VeselkaURL                    string        `envconfig:"VESELKA_URL" default:"https://veselka-test.cloud.carbonrobotics.com"`

	caches   cache.Caches
	database *gorm.DB
	remoteit.Credentials
	s3             AwsS3
	timezoneFinder tzf.F
}

type AwsS3 interface {
	HeadObject(input *s3.HeadObjectInput) (*s3.HeadObjectOutput, error)
}

func (portal *PortalService) Run(serviceContext context.Context) (err error) {
	logPair("Version", buildlib.Version)
	logPair("Commit", buildlib.Commit)
	logPair("Built On", buildlib.BuiltOn)

	if err := envconfig.Process("", portal); err != nil {
		return err
	}

	env := utils.NewEnvironment(
		utils.GetInstance(portal.Instance, portal.RootURL),
		utils.GetBuild(portal.Build, portal.Environment),
		portal.DBReadOnly,
	)
	logPair("Instance", env.Instance().String())
	logPair("Build", env.Build().String())

	httplib.ConfigureGin(env.Build() == environment.BuildRelease)

	/**
	 * Sentry
	 **/
	sentryEnvironment := "staging"
	if env.Instance() == environment.InstanceProduction {
		sentryEnvironment = "production"
	}
	err = sentry.Init(sentry.ClientOptions{
		Dsn:              portal.SentryDSN,
		Debug:            env.Build() == environment.BuildDebug,
		Environment:      sentryEnvironment,
		EnableTracing:    true,
		TracesSampleRate: 0.25,
		Release:          buildlib.Version,
	})
	if err != nil {
		log.WithError(err).Error("Failed to start Sentry")
		return err
	} else {
		logPair("Sentry", "Initialized")
	}
	defer sentry.Flush(2 * time.Second)

	/**
	 * Loading page
	 */
	startupHandler, err := GetStartupModeREST(env, &PageArgs{
		Title:       portal.StartupTitle,
		Description: portal.StartupDescription,
	})
	if err != nil {
		return err
	}
	hotswap := httplib.NewHotswapHandler(startupHandler)

	/**
	 * Start HTTP servers
	 */
	serverAddr := fmt.Sprintf(":%d", portal.Port)
	http2Server := &http2.Server{}
	server := &http.Server{
		Addr:    serverAddr,
		Handler: h2c.NewHandler(hotswap, http2Server),
	}
	go func() {
		logPair("Server", fmt.Sprintf("Listening (%v)", serverAddr))
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.WithError(err).Fatal("Failed to start server")
		}
	}()
	defer func() {
		serviceContext, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		log.Println("Shutting down server...")
		if shutdownErr := server.Shutdown(serviceContext); shutdownErr != nil {
			// overwrite return value of outer function Run
			err = fmt.Errorf("server forced to shutdown: %w", shutdownErr)
		}
	}()

	logPair("StartupPage", "Serving")

	if portal.BlockStartupForever {
		log.Info("Entering maintenance mode: serving startup page forever")
		// graceful shutdown
		<-serviceContext.Done()
		return nil
	}

	timezoneFinder, err := tzf.NewDefaultFinder()
	if err != nil {
		log.WithError(err).Error("Failed to start timezone finder")
		return err
	}
	portal.timezoneFinder = timezoneFinder

	/**
	 * AWS
	 **/
	var creds *awscredentials.Credentials
	if portal.AWSCredentialsFile != "" {
		creds = awscredentials.NewSharedCredentials(portal.AWSCredentialsFile, "")
	}
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(portal.AWSRegion),
		Credentials: creds,
	})
	if err != nil {
		log.WithError(err).Error("Failed to start AWS session")
		return err
	}
	portal.s3 = awslib.NewS3Facade(sess)
	cwl := awslib.NewCWLFacade(cloudwatchlogs.New(sess))
	logPair("AWS", "Initialized")

	/**
	 * Salesforce
	 **/

	var salesforce *simpleforce.Client
	if portal.SalesforceToken == "" {
		logPair("Salesforce", "DISABLED")
	} else {
		logPair("Salesforce", "ENABLED")
		salesforce := simpleforce.NewClient(portal.SalesforceURL, portal.SalesforceConsumerKey, "53.0")
		if salesforce == nil {
			err := "failed to initialize Salesforce"
			log.Error(err)
			return errors.New(err)
		}
		err = salesforce.LoginPassword(portal.SalesforceUser, portal.SalesforcePassword+portal.SalesforceToken, "")
		if err != nil {
			err := "failed to log in to Salesforce"
			log.Error(err)
			return errors.New(err)
		}
	}

	/**
	 * Slack
	 **/
	var slackClient *slack.Client
	if portal.SlackOauthToken == "" {
		logPair("Slack", "DISABLED")
	} else {
		logPair("Slack", "ENABLED")
		slackClient = slack.New(portal.SlackOauthToken)
	}

	/**
	 * Auth0
	 **/
	auth0Client, err := management.New(portal.Auth0Domain, management.WithClientCredentials(serviceContext, portal.Auth0ServerId, portal.Auth0ServerSecret))
	if err != nil {
		log.WithError(err).Error("Failed to initialize Auth0")
		return err
	}
	logPair("Auth0", "Initialized")

	/**
	 * Stream
	 **/
	streamClient, err := chat.NewClient(portal.StreamAPIKey, portal.StreamAPISecret)
	if err != nil {
		log.WithError(err).Error("Failed to initialize Stream")
		return err
	}
	logPair("Stream", "Initialized")

	/**
	 * Veselka
	 **/
	var veselkaClient *veselkalib.Client
	if env.Build() == environment.BuildRelease {
		veselkaClient = veselkalib.New(portal.VeselkaURL, veselkalib.OptionInternal())
		logPair("Veselka", "Initialized")
	} else {
		veselkaClient = veselkalib.New(portal.VeselkaURL, veselkalib.OptionInternal(), veselkalib.OptionDebug())
		logPair("Veselka", "Initialized (DEBUG)")
	}

	/**
	 * Robot Syncer
	 **/
	var robotSyncerClient *robot_syncer.Client
	if env.Build() == environment.BuildRelease {
		robotSyncerClient = robot_syncer.NewClient(
			portal.RobotSyncerURL,
			portal.SharedConfigSchemasPath,
			robot_syncer.WithSecure(false),
		)
		logPair("Robot Syncer Client", "Initialized")
	} else {
		robotSyncerClient = robot_syncer.NewClient(
			portal.RobotSyncerURL,
			portal.SharedConfigSchemasPath,
			robot_syncer.WithDebug(true),
			robot_syncer.WithSecure(false),
		)
		logPair("Robot Syncer Client", "Initialized (DEBUG)")
	}

	/**
	 * RTC Jobs Service
	 */
	rtcJobsClient, err := newRTCJobsClient(portal.RTCJobsTarget)
	if err != nil {
		log.WithError(err).Error("Failed to open connection to RTC jobs service")
		return err
	}
	logPair("RTC Jobs", "Initialized")

	/**
	 * Database
	 **/
	var traceDumper *database.TraceDumper
	dbConfig := &database.Config{
		Trace:                  portal.DBTrace,
		PoolMaxConns:           portal.DBMaxConns,
		GormPrepareStmt:        portal.DBGormPrepareStmt,
		DiscourageTransactions: portal.DBDiscourageTransactions,
	}
	portal.database, traceDumper, err = database.OpenPostgresDB(serviceContext, portal.PSQLConnection, true, dbConfig)
	if err != nil {
		log.WithError(err).Error("Failed to initialize Postgres database")
		return err
	}
	logPair("Postgres", "Initialized")

	/**
	 * Logging
	 * Saw no need to add trace, error, fatal, or panic levels
	 **/
	logLevel := log.DebugLevel
	if portal.LogLevel == "info" {
		logLevel = log.InfoLevel
	} else if portal.LogLevel == "warn" {
		logLevel = log.WarnLevel
	}
	log.SetLevel(logLevel)
	logPair("Log Level", logLevel.String())

	/**
	 * Config
	 **/
	configClient := grpc_clients.NewConfigClient(portal.ConfigURL, robotSyncerClient)
	logPair("Config", "Initialized")

	/**
	 * Caches
	 **/
	portal.caches = cache.Caches{
		CropByIDCache:               veselka.InitializeCropByIDCache(veselkaClient),
		CustomerCache:               customers.InitializeCustomerCache(portal.database),
		CustomerIDByRobotIDCache:    customers.InitializeCustomerIDByRobotIDCache(portal.database),
		HasNewAlarmsCache:           alarms.InitializeHasNewAlarmsCache(portal.database),
		JobNameByUUIDCache:          jobs.InitializeJobNameByUUIDCache(portal.database),
		ProfileNameByUUIDCache:      profiles.InitializeProfileNameByUUIDCache(portal.database),
		RobotCache:                  robots.InitializeRobotCache(portal.database),
		RobotIDBySerialCache:        robots.InitializeRobotIDBySerialCache(portal.database),
		RobotSerialByIDCache:        robots.InitializeRobotSerialByIDCache(portal.database),
		StreamChannelCache:          stream.InitializeStreamChannelCache(streamClient),
		StreamMessageProcessedCache: stream.InitializeStreamMessageProcessedCache(),
	}
	portal.caches.SlackChannelCache = slackutil.InitializeSlackChannelCache(slackClient, &portal.caches)
	portal.caches.SupportChannelBySerialCache = slackutil.InitializeSupportChannelBySerialCache(env.Instance(), configClient, robotSyncerClient, &portal.caches)
	portal.caches.UserCache, portal.caches.UserCacheNonLoading = users.InitializeUserCache(&portal.caches, auth0Client, portal.Auth0Domain)
	portal.caches.UserIDByEmailCache = users.InitializeUserIDByEmailCache(&portal.caches, auth0Client, portal.Auth0Domain)
	logPair("Caches", "Initialized")

	/**
	 * Migrations
	 **/
	data_migrations.RunDataMigration(serviceContext, data_migrations.MigrationContext{DB: portal.database, Caches: portal.caches})
	logPair("Migrations", "Complete")

	/**
	 * Warm user cache for report authors, so reports list loads faster
	 */
	func() {
		delay := portal.CachewarmAuthorInterval
		if delay <= 0 {
			log.WithField("delay", delay).Warn("CachewarmAuthorInterval is not positive; will not warm caches")
			return
		}
		ctx := utils.WithRequestID(serviceContext, "BackgroundTask=cachewarm/reportauthors")
		go cachewarm.ReportAuthors(ctx, portal.database, &portal.caches, delay)
	}()

	/**
	 * Keep robot Prometheus metrics up to date
	 */
	func() {
		delay := portal.RobotPrometheusExportInterval
		if delay <= 0 {
			log.WithField("delay", delay).Warn("RobotPrometheusExportInterval is not positive; will not export robot metrics")
			return
		}
		ctx := utils.WithRequestID(serviceContext, "BackgroundTask=prommetrics/robots")
		go prommetrics.ExportRobotMetrics(ctx, portal.database, delay)
	}()

	/**
	 * PagerDuty
	 **/
	if portal.PagerDutyAPIKey != "" && portal.PagerDutyIntegrationKey != "" {
		logPair("PagerDuty", "Initialized")
	} else {
		logPair("PagerDuty", "Disabled")
	}

	// initialize index.html with runtime environment variables
	if env.Build() == environment.BuildRelease {
		if _, err := exec.Command("runtime-js-env", "-i", fmt.Sprintf("%s/index.html", portal.DistPath)).CombinedOutput(); err != nil {
			log.WithError(err).Fatal("Failed to inject client runtime environment")
			return err
		}
	} else {
		log.Infof("Skipping index.html injection in development environment")
	}

	devEmails := strings.Split(portal.DevEmails, ",")

	// initialize REST and gRPC servers
	GRPCServer := GetGRPC(
		portal.database,
		portal.caches,
		auth0Client,
		portal.Auth0Domain,
		portal.Auth0GRPCAudience,
		portal.PagerDutyIntegrationKey,
		portal.RootURL,
		portal.RITAccountID,
		portal.Credentials,
		salesforce,
		slackClient,
		env,
	)

	overrides, unknownOverrideFlags, err := utils.ParseFeatureFlagOverrides(portal.FeatureFlagOverrides)
	if err != nil {
		return fmt.Errorf("parsing feature flag overrides %q: %w", portal.FeatureFlagOverrides, err)
	}
	if len(unknownOverrideFlags) > 0 {
		log.WithField("unknownFlags", unknownOverrideFlags).Warn("Unknown flag names in feature flag overrides list")
	}
	logPair("FeatureFlagOverrides", fmt.Sprint(overrides))

	var mainHandler http.Handler
	mainHandler = &httplib.RESTGRPCHandler{
		REST: GetREST(
			serviceContext,
			portal.database,
			traceDumper,
			portal.caches,
			auth0Client,
			portal.Auth0Domain,
			portal.Auth0GRPCAudience, // Not a typo, default for backwards compatibility
			[]string{portal.Auth0RESTAudience, portal.Auth0GRPCAudience},
			configClient,
			portal.SendgridAPIKey,
			portal.RootURL,
			veselkaClient,
			cwl,
			s3.New(sess),
			portal.SalesforceWebhookToken,
			slackClient,
			salesforce,
			portal.AWSBucket,
			portal.RITAccountID,
			env,
			portal.PerfMode,
			portal.TableauWebhookToken,
			portal.timezoneFinder,
			devEmails,
			portal.SpatialExportConfigFile,
			streamClient,
			portal.JiraWebhookToken,
			robotSyncerClient,
			rtcJobsClient,
		),
		GRPC: GRPCServer,
	}
	mainHandler = utils.NewFeatureFlagOverrideHandler(mainHandler, overrides)
	mainHandler = utils.NewLogLevelHandler(mainHandler)
	mainHandler = utils.NewRequestIDHandler(mainHandler)

	scheduler := gocron.NewScheduler(time.UTC)
	// run at the top of every hour
	scheduler.Cron("0 * * * *").Do(func() {
		reports.RunScheduledReports(serviceContext, portal.database, portal.caches, env, auth0Client, portal.RootURL, portal.SendgridAPIKey, devEmails)
	})
	scheduler.StartAsync()

	// uncomment to send reports for the current hour on startup
	// NOTE: Disable reports for all customers except the one you want to test before doing this
	//       There is a safeguard to prevent sending emails to non-carbon addresses outside of production
	// portal.database.RunScheduledReports(serviceContext, portal.caches, portal.Environment, auth0Client, portal.RootURL, portal.SendgridAPIKey)

	// switch from startup server to main server
	hotswap.Store(mainHandler)

	/*********************************************************************/
	// DUPLICATE GRPC SERVER
	// listen on port 9090 for GRPC traffic
	// this will replace the main GRPC server after all robots have moved to it
	grpcServerAddr := fmt.Sprintf(":%d", portal.GRPCPort)
	go func() {
		listener, err := net.Listen("tcp", grpcServerAddr)
		if err != nil {
			log.WithError(err).Fatal("Failed to listen")
		}
		log.Infof("GRPC server: Listening on (%v)", grpcServerAddr)
		if err := GRPCServer.Serve(listener); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.WithError(err).Fatal("Failed to start server")
		}
	}()
	defer func() {
		log.Info("Shutting down grpc server...")
		GRPCServer.GracefulStop()
	}()
	/*********************************************************************/

	logPair("API", "Serving")

	// graceful shutdown
	<-serviceContext.Done()
	log.Println("Shutting down...")

	log.Println("  Stopping long running processes...")
	log.Print("    Cron scheduler...")
	scheduler.Stop()

	return nil
}

func newRTCJobsClient(address string) (rtc.JobServiceClient, error) {
	var creds grpccredentials.TransportCredentials

	// Use TLS for cloud connections, or insecure HTTP locally.
	if strings.Contains(address, "cloud.carbonrobotics.com") {
		creds = grpccredentials.NewTLS(&tls.Config{})
	} else {
		creds = insecure.NewCredentials()
	}

	connection, err := grpc.NewClient(address, grpc.WithTransportCredentials(creds))
	if err != nil {
		return nil, err
	}
	return rtc.NewJobServiceClient(connection), nil
}
