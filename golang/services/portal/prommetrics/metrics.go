package prommetrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	areaWeededTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{Name: "area_weeded_total"},
		[]string{"robot"},
	)
	timeWeededTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{Name: "time_weeded_total"},
		[]string{"robot"},
	)
	weedKillCountTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{Name: "weed_kill_count_total"},
		[]string{"robot"},
	)
	cropKillCountTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{Name: "crop_kill_count_total"},
		[]string{"robot"},
	)
)
