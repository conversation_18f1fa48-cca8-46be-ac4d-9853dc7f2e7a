package prommetrics

import (
	"context"
	"encoding/json"
	"regexp"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

var robotMetricsByKey = []struct {
	pattern *regexp.Regexp
	metric  *prometheus.GaugeVec
}{
	{
		pattern: regexp.MustCompile("^commander/area_weeded_total$"),
		metric:  areaWeededTotal,
	},
	{
		pattern: regexp.MustCompile("^commander/time_weeded_total$"),
		metric:  timeWeededTotal,
	},
	{
		pattern: regexp.MustCompile("^weed_tracking/row[0-9]+/weed_kill_count_total$"),
		metric:  weedKillCountTotal,
	},
	{
		pattern: regexp.MustCompile("^weed_tracking/row[0-9]+/crop_kill_count_total$"),
		metric:  cropKillCountTotal,
	},
}

// ExportRobotMetrics continually exports select metrics from the "robots"
// database table to Prometheus, until the context is canceled.
func ExportRobotMetrics(ctx context.Context, db *gorm.DB, delay time.Duration) {
	log := utils.Logger(ctx)

	for {
		robotList, err := robots.GetRobots(ctx, db, nil, true, true)
		if err != nil {
			log.WithError(err).Error("Failed to retrieve robots")
		} else {
			for _, robot := range robotList {
				updateRobotMetrics(log.WithField("serial", robot.Serial), robot)
			}
		}

		select {
		case <-ctx.Done():
			return
		case <-time.After(delay):
		}
	}
}

func updateRobotMetrics(log *logrus.Entry, robot *models.Robot) {
	if robot.MetricTotals == nil || len(robot.MetricTotals) == 0 {
		// no metrics to report
		return
	}

	metrics := map[string]uint64{}
	if err := json.Unmarshal(robot.MetricTotals, &metrics); err != nil {
		log.WithError(err).WithField("metricTotals", robot.MetricTotals).Error("Failed to parse metric totals")
		return
	}

	totals := map[*prometheus.GaugeVec]uint64{}
	for key, value := range metrics {
		for _, candidate := range robotMetricsByKey {
			if !candidate.pattern.MatchString(key) {
				continue
			}
			totals[candidate.metric] += value
		}
	}

	// Only set those metrics for which we have values.
	for metric, value := range totals {
		metric.WithLabelValues(robot.Serial).Set(float64(value))
	}
}
