package prommetrics

import (
	"regexp"
	"testing"

	"github.com/prometheus/client_golang/prometheus"
)

func TestRobotMetricsByKeyRegexp(t *testing.T) {
	// Pull one of the trickier paterns to test it.
	var weedKillCountPattern *regexp.Regexp
	for _, datum := range robotMetricsByKey {
		if datum.metric == weedKillCountTotal {
			weedKillCountPattern = datum.pattern
		}
	}
	if weedKillCountPattern == nil {
		t.Fatal("couldn't find pattern for weedKillCountTotal")
	}

	for _, test := range []struct {
		key         string
		shouldMatch bool
	}{
		// unrelated keys
		{key: "commander/time_weeded_total", shouldMatch: false},
		{key: "weed_tracking/row1/crop_kill_count_total", shouldMatch: false},

		// true matches
		{key: "weed_tracking/row1/weed_kill_count_total", shouldMatch: true},
		{key: "weed_tracking/row6/weed_kill_count_total", shouldMatch: true},

		// similar keys, but class-specific; don't want to double-count
		{key: "weed_tracking/row6/class/GRASS/weed_kill_count_total", shouldMatch: false},
	} {
		t.Run(test.key, func(t *testing.T) {
			matched := weedKillCountPattern.MatchString(test.key)
			if matched != test.shouldMatch {
				t.Errorf("weedKillCountPattern.MatchString(%q): got %v, want %v", test.key, matched, test.shouldMatch)
			}
		})
	}
}

func TestRobotMetricsByKeyDistinct(t *testing.T) {
	metrics := make(map[*prometheus.GaugeVec]struct{})
	for _, datum := range robotMetricsByKey {
		_, dupe := metrics[datum.metric]
		if dupe {
			t.Errorf("duplicate metric for key pattern %s", datum.pattern)
		}
		metrics[datum.metric] = struct{}{}
	}
}
