package reports

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/dchest/uniuri"
	"github.com/lib/pq"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/carbonrobotics/crgo/carbon"
	timelib "github.com/carbonrobotics/crgo/time"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/email"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

func GetReports(ctx context.Context, db *gorm.DB, customerID *uint, userID *string) ([]*models.Report, error) {
	query := db.Model(&models.Report{})
	if customerID != nil && userID != nil {
		query = query.Where("customer_id = ? OR author_id = ?", customerID, userID)
	} else if customerID != nil {
		query = query.Where("customer_id = ?", customerID)
	} else if userID != nil {
		query = query.Where("author_id = ?", userID)
	}
	var reports []*models.Report
	err := query.WithContext(ctx).Find(&reports).Error
	if err != nil {
		log.WithError(err).Error("Error getting reports")
		return nil, err
	}
	return reports, nil
}

// GetAuthors finds all distinct authors of reports and report instances.
func GetAuthors(ctx context.Context, db *gorm.DB) (map[string]struct{}, error) {
	db = db.WithContext(ctx)

	var reports []*models.Report
	var instances []*models.ReportInstance
	if err := db.Select("author_id").Distinct().Find(&reports).Error; err != nil {
		return nil, fmt.Errorf("fetching reports: %w", err)
	}
	if err := db.Select("author_id").Distinct().Find(&instances).Error; err != nil {
		return nil, fmt.Errorf("fetching report instances: %w", err)
	}

	result := make(map[string]struct{})
	for _, report := range reports {
		result[report.AuthorID] = struct{}{}
	}
	for _, instance := range instances {
		result[instance.AuthorID] = struct{}{}
	}
	return result, nil
}

func GetReportBySlug(db *gorm.DB, slug string) (*models.Report, error) {
	report := &models.Report{}
	err := db.Where("slug = ?", slug).First(report).Error
	if err != nil {
		log.WithError(err).Errorf("No report with slug %s", slug)
		return nil, err
	}
	return report, nil
}

func GetInstancesByReport(ctx context.Context, db *gorm.DB, report *models.Report) ([]*models.ReportInstance, error) {
	instances := []*models.ReportInstance{}
	err := db.WithContext(ctx).Where("report_id = ?", report.Base.ID).Find(&instances).Error
	if err != nil {
		log.WithError(err).Errorf("Failed to find instances for report %s", strconv.FormatUint(uint64(report.Base.ID), 10))
		return nil, err
	}
	return instances, nil
}

func GetReportInstanceBySlug(ctx context.Context, db *gorm.DB, slug string) (*models.ReportInstance, error) {
	instance := &models.ReportInstance{}
	err := db.WithContext(ctx).Where("slug = ?", slug).First(instance).Error
	if err != nil {
		log.WithError(err).Errorf("No report instance with slug %s", slug)
		return nil, err
	}
	return instance, nil
}

func SaveReport(db *gorm.DB, report *models.Report) error {
	return db.Save(report).Error
}

func SaveReportInstance(db *gorm.DB, reportInstance *models.ReportInstance) error {
	return db.Save(reportInstance).Error
}

func DeleteReport(db *gorm.DB, reportID uint) error {
	return db.Delete(&models.Report{}, "id = ?", reportID).Error
}

func DeleteReportInstance(db *gorm.DB, reportInstanceID uint) error {
	return db.Delete(&models.ReportInstance{}, "id = ?", reportInstanceID).Error
}

func SendReportEmails(context context.Context, env *utils.Environment, db *gorm.DB, auth0Client *management.Management, rootURL string, sendgridAPIKey string, report *models.Report, reportInstance *models.ReportInstance, devEmails []string) error {
	if len(reportInstance.PublishEmails) == 0 {
		return nil
	}
	var err error
	log.Infof("Sending report emails to %s", strings.Join(reportInstance.PublishEmails, ", "))
	personalization := mail.NewPersonalization()
	emails := []*mail.Email{}
	for _, email := range reportInstance.PublishEmails {
		if email == "" {
			continue
		}
		users, _ := auth0Client.User.ListByEmail(context, email)
		if len(users) >= 1 {
			emails = append(emails, mail.NewEmail(users[0].GetName(), email))
		} else {
			emails = append(emails, mail.NewEmail(email, email))
		}
	}
	if len(emails) == 0 {
		log.Error("Must include at least 1 email")
		return nil
	}
	personalization.AddTos(emails...)
	personalization.SetDynamicTemplateData("name", reportInstance.Name)
	var robotSerials []string
	for _, robotID := range reportInstance.RobotIDs {
		robot, err := robots.GetRobot(db, uint(robotID), nil)
		if err != nil {
			robotSerials = append(robotSerials, "Unknown Robot")
		} else {
			serial, err := carbon.ParseSerial(robot.Serial)
			if err != nil {
				return status.Errorf(codes.InvalidArgument, "Invalid serial: %s", robot.Serial)
			}
			robotSerials = append(robotSerials, carbon.GetCustomerSerial(serial))
		}
	}
	joinedSerials := strings.Join(robotSerials, ", ")
	lastCommaIndex := strings.LastIndex(joinedSerials, ",")
	var oxfordSerials string
	if lastCommaIndex > 0 {
		oxfordSerials = joinedSerials[:lastCommaIndex] + ", & " + joinedSerials[lastCommaIndex+2:]
	} else {
		oxfordSerials = joinedSerials
	}
	personalization.SetDynamicTemplateData("robot_list", oxfordSerials)
	startDate := time.Unix(reportInstance.StartDate, 0)
	endDate := time.Unix(reportInstance.EndDate, 0)
	personalization.SetDynamicTemplateData("date_start", startDate.Format("01/02/2006"))
	personalization.SetDynamicTemplateData("date_end", endDate.Format("01/02/2006"))
	personalization.SetDynamicTemplateData("link", fmt.Sprintf("%s/reports/scheduled/%s/runs/%s", rootURL, report.Slug, reportInstance.Slug))
	response, err := email.SendEmailTemplate(env, sendgridAPIKey, "d-380170f3a42a4f59a899e132d735b5bb", personalization, email.ASM_GROUP_REPORTS, devEmails)
	if err != nil {
		log.WithError(err).Errorf("Failed to send report notification email")
	}
	if response != nil && response.StatusCode >= http.StatusBadRequest {
		log.Error("Report notification email error", response.Body)
	}
	return err
}

func RunScheduledReports(context context.Context, db *gorm.DB, caches cache.Caches, env *utils.Environment, auth0Client *management.Management, rootURL string, sendgridAPIKey string, devEmails []string) error {
	log.Info("Running automated reports...")
	errorPrefix := "Failed to run automated reports"

	// get all reports
	reports, err := GetReports(context, db, nil, nil)
	if err != nil {
		log.WithError(err).Errorf("%s: failed to get reports", errorPrefix)
		return nil
	}

	for _, report := range reports {
		// filter automated only
		if !report.AutomateWeekly {
			continue
		}

		// filter unassigned reports
		if report.CustomerID == 0 {
			continue
		}

		// filter customers with reports disabled
		customer, err := caches.CustomerCache.Get(context, uint(report.CustomerID))
		if err != nil {
			log.WithError(err).Errorf("%s: failed to get customer %d for report %d", errorPrefix, report.CustomerID, report.Base.ID)
			continue
		}
		if !customer.WeeklyReportEnabled {
			continue
		}

		if len(customer.Emails) == 0 {
			log.WithError(err).Errorf("%s: no emails assigned for customer %d for report %d", errorPrefix, report.CustomerID, report.Base.ID)
			continue
		}

		// filter out non matching day/times
		location, err := time.LoadLocation(customer.WeeklyReportTimezone)
		if err != nil {
			log.WithError(err).Errorf("%s: failed to load timezone %s for report %d", errorPrefix, customer.WeeklyReportTimezone, report.Base.ID)
			continue
		}
		now := time.Now().In(location)
		if now.Weekday() != time.Weekday(customer.WeeklyReportDay) {
			continue
		}
		if now.Hour() != int(customer.WeeklyReportHour) {
			continue
		}

		// run the report!

		// get all robots
		robotList, err := robots.GetRobots(context, db, &customer.Base.ID, true, true)
		if err != nil {
			log.WithError(err).Errorf("%s: failed to get robots for customer %d for report %d", errorPrefix, customer.Base.ID, report.Base.ID)
			continue
		}

		// filter only active robots
		activeRobotIDs := pq.Int64Array{}
		for _, robot := range robotList {
			if robot.ImplementationStatus == string(models.Active) {
				activeRobotIDs = append(activeRobotIDs, int64(robot.Base.ID))
			}
		}
		if len(activeRobotIDs) == 0 {
			continue
		}

		// calculate date range
		startDate := now.AddDate(0, 0, int(-1*customer.WeeklyReportLookbackDays))
		endDate := now.AddDate(0, 0, -1)

		// run report with active robots
		reportInstance := &models.ReportInstance{
			AuthorID:       "",
			Automated:      true,
			EndDate:        endDate.Unix(),
			ManagerID:      "",
			Name:           report.Name,
			OrderAsc:       report.OrderAsc,
			OrderBy:        report.OrderBy,
			PublishEmails:  customer.Emails,
			ReportID:       uint64(report.Base.ID),
			RobotIDs:       activeRobotIDs,
			ShowAverage:    report.ShowAverage,
			ShowTotal:      report.ShowTotal,
			Slug:           uniuri.NewLen(8),
			StartDate:      startDate.Unix(),
			VisibleColumns: report.VisibleColumns,
		}
		log.Infof("Running automated report %s for %s (%s - %s)", report.Name, customer.Name, timelib.ToDateSlug(startDate), timelib.ToDateSlug(endDate))
		err = SaveReportInstance(db, reportInstance)
		if err != nil {
			log.WithError(err).Errorf("%s: failed to save report instance for report %d", errorPrefix, report.Base.ID)
			continue
		}
		err = SendReportEmails(context, env, db, auth0Client, rootURL, sendgridAPIKey, report, reportInstance, devEmails)
		if err != nil {
			log.WithError(err).Errorf("%s: failed to send emails for report instance %d", errorPrefix, reportInstance.Base.ID)
			continue
		}
	}
	log.Debug("Automated reports done.")
	return err
}
