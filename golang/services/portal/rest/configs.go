package rest

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"path"
	"slices"
	"strings"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"github.com/slack-go/slack"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
	rosypb "github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	"github.com/carbonrobotics/cloud/golang/pkg/grpc_clients"
	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/portal/admin"
	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/configs"
	"github.com/carbonrobotics/cloud/golang/services/portal/middleware"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

func getConfig(context *gin.Context, configClient *grpc_clients.ConfigClient, robotSyncerClient *robot_syncer.Client, caches cache.Caches, serial *carbon.ValidSerial, errorPrefix *string, fetchFirst bool, noCache bool) (*config_service.ConfigNode, *config_service.ConfigNode, error) {
	canRead := configs.CanReadConfig(context, caches, errorPrefix, serial)
	if !canRead {
		ReturnError(context, http.StatusForbidden, nil, errorPrefix, "User not authorized")
		return nil, nil, errors.New("user not authorized")
	}

	// a template was requested directly
	if slices.Contains(carbon.TemplateSerials(), serial.String()) {
		// RoSy templates enabled but using the old API, throw an error
		ReturnError(context, http.StatusBadRequest, nil, errorPrefix, "Using legacy API with RoSy templates enabled")
		return nil, nil, errors.New("RoSy templates enabled but a request for a template was made with the legacy API")
	}

	softwareVersion, err := robots.GetSoftwareVersionBySerial(context, caches, serial)
	if err != nil {
		ReturnError(context, http.StatusInternalServerError, err, errorPrefix, "Unable to fetch software version")
		return nil, nil, err
	}

	var tree *config_service.ConfigNode
	migratedToRoSy := carbon.MigratedToRoSyConfigs(softwareVersion)
	if !migratedToRoSy {
		tree, _, err = configClient.GetConfig(serial, fetchFirst, noCache)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, errorPrefix, "Config not found")
			return nil, nil, err
		}

		// overwrite the config in RoSy with the one defined in config service
		if err := robotSyncerClient.OverwriteConfig(context, serial, tree); err != nil {
			log.WithError(err).Warnf("Failed to put config to robot_syncer %s", serial)
		}
	}

	// override the config with the one defined in robot syncer (depending on software version)
	if migratedToRoSy {
		tree, err = robotSyncerClient.GetConfig(context, robot_syncer.GetConfigParameters{Serial: serial.String()})
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, errorPrefix, "Failed to fetch config from robot syncer")
			return nil, nil, err
		}
	}

	// override the template with the one defined in robot syncer
	template, err := robotSyncerClient.GetTemplate(context, robot_syncer.GetTemplateParameters{Class: serial.Class().String()})
	if err != nil {
		ReturnError(context, http.StatusInternalServerError, err, errorPrefix, "Failed to get template from robot syncer")
		return nil, nil, err
	}

	return tree, template, nil
}

func GetTreeHandler(configClient *grpc_clients.ConfigClient, caches cache.Caches, robotSyncerClient *robot_syncer.Client) gin.HandlerFunc {
	return func(context *gin.Context) {
		errorPrefix := "Failed to get config"

		rawFetchFirst, _ := context.GetQuery("fetchFirst")
		fetchFirst := rawFetchFirst == "true"

		rawNoCache, _ := context.GetQuery("noCache")
		noCache := rawNoCache == "true"

		serial, err := carbon.ParseSerial(context.Param("serial"))
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
			return
		}
		errorPrefix = fmt.Sprintf("Failed to get config for %s", serial)

		config, template, err := getConfig(context, configClient, robotSyncerClient, caches, serial, &errorPrefix, fetchFirst, noCache)
		if err != nil {
			return
		}
		if config == nil {
			ReturnError(context, http.StatusNotFound, nil, &errorPrefix, "User not authorized")
			return
		}

		rosyUnsyncedKeys, err := robotSyncerClient.GetUnsyncedConfigChanges(context, robot_syncer.GetUnsyncedConfigChangesParameters{Serial: serial.String()})
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Failed to get unsynced config changes")
			return
		}

		portalUnsyncedKeys := make([]*portal.UnsyncedKey, len(rosyUnsyncedKeys))
		for index, rosyKey := range rosyUnsyncedKeys {
			portalUnsyncedKeys[index] = &portal.UnsyncedKey{
				Key:      rosyKey.Key,
				Ts:       rosyKey.Ts,
				NewValue: rosyKey.NewValue,
				OldValue: rosyKey.OldValue,
			}
		}

		response := &portal.ConfigResponse{
			Config:       config,
			Template:     template,
			UnsyncedKeys: portalUnsyncedKeys,
		}
		ReturnProtoJSON(context, response, &errorPrefix)
	}
}

func SetValueHandler(configClient *grpc_clients.ConfigClient, caches cache.Caches, slackClient *slack.Client, robotSyncerClient *robot_syncer.Client, env *utils.Environment) gin.HandlerFunc {
	return func(context *gin.Context) {
		errorPrefix := "Failed to set config value"

		if !configs.CanUpdateConfig(context, caches, &errorPrefix, nil) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		input := struct {
			Path  string
			Value interface{}
		}{}
		err := context.Bind(&input)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
			return
		}

		var node *config_service.ConfigNode
		var key string
		var addToList bool
		serial, err := carbon.ParseSerial(context.Param("serial"))
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
			return
		}
		errorPrefix = fmt.Sprintf("Failed to get config for %s", serial)

		response, _, err := getConfig(context, configClient, robotSyncerClient, caches, serial, &errorPrefix, true, false)
		if err != nil {
			return
		}
		key = path.Join(serial.Class().String(), serial.String(), input.Path)
		errorPrefix = fmt.Sprintf("Failed to set %s config value", key)
		node, err = config.GetNodeFromPath(response, input.Path)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Node not found")
			return
		}

		if node == nil {
			ReturnError(context, http.StatusNotFound, nil, &errorPrefix, "Node not found")
			return
		}

		softwareVersion, err := robots.GetSoftwareVersionBySerial(context, caches, serial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Unable to fetch software version")
			return
		}

		if carbon.MigratedToRoSyConfigs(softwareVersion) {
			rawCurrentUser, _ := context.Get(middleware.UserContextKey)
			currentUser, ok := rawCurrentUser.(*management.User)
			if !ok {
				ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Failed to gather requesting user information")
				return
			}

			robotSyncerClient.SetConfigValue(context, robot_syncer.SetConfigValueParameters{
				Serial:    serial.String(),
				Path:      input.Path,
				Value:     input.Value,
				Timestamp: time.Now().UnixMilli(), // Eventually add ts to input for this handler
				UserID:    *currentUser.ID,
			})

			if node.Def.Type == config_service.ConfigType_LIST {
				// ensure we send the correct slack message
				addToList = true
			}
		} else {
			switch node.Def.Type {
			case config_service.ConfigType_LIST:
				err = configClient.AddToList(key, input.Value.(string))
				addToList = true
			case config_service.ConfigType_INT:
				var safeValue int64
				switch input.Value.(type) {
				case float64:
					safeValue = int64(input.Value.(float64))
				default:
					safeValue = input.Value.(int64)
				}
				err = configClient.SetIntValue(key, safeValue)
			case config_service.ConfigType_UINT:
				var safeValue uint64
				switch input.Value.(type) {
				case float64:
					safeValue = uint64(input.Value.(float64))
				default:
					safeValue = input.Value.(uint64)
				}
				err = configClient.SetUIntValue(key, safeValue)
			case config_service.ConfigType_FLOAT:
				err = configClient.SetDoubleValue(key, input.Value.(float64))
			case config_service.ConfigType_BOOL:
				err = configClient.SetBoolValue(key, input.Value.(bool))
			case config_service.ConfigType_STRING:
				err = configClient.SetStringValue(key, input.Value.(string))
			default:
				err = fmt.Errorf("unknown node type")
			}
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Update failed")
				return
			}
		}

		oldValue := cloudconfig.ConfigValueToString(node.Value)
		if slackClient != nil {
			message := fmt.Sprintf("`%s`\n:diff-minus: ~%v~ :diff-plus: %v", key, oldValue, input.Value)
			if addToList {
				message = fmt.Sprintf("`%s`\n:diff-plus: Added config list key %s", key, input.Value.(string))
			}

			postSlackMessageToSupportChannel(context, env, caches, message, serial, slackClient)
		}

		// if the support slack got changed, purge that key from the support channel cache
		if input.Path == "command/commander/support_slack" {
			caches.SupportChannelBySerialCache.Delete(context, cache.GetCacheableSerial(serial))
		}

		context.JSON(http.StatusOK, gin.H{})
	}
}

func DeleteValueHandler(configClient *grpc_clients.ConfigClient, caches cache.Caches, slackClient *slack.Client, robotSyncerClient *robot_syncer.Client, env *utils.Environment) gin.HandlerFunc {
	return func(context *gin.Context) {
		errorPrefix := "Failed to delete config key"

		if !configs.CanUpdateConfig(context, caches, &errorPrefix, nil) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		requestPath := context.Param("path")[1:]
		segments := strings.Split(requestPath, "/")
		parentPath := strings.Join(segments[:len(segments)-1], "/")
		name := segments[len(segments)-1]

		serial, err := carbon.ParseSerial(context.Param("serial"))
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
			return
		}
		errorPrefix = fmt.Sprintf("Failed to delete config key for %s", serial)

		key := path.Join(serial.Class().String(), serial.String(), parentPath)
		errorPrefix = fmt.Sprintf("Failed to delete %s config key", key)

		if slackClient != nil {
			message := fmt.Sprintf("`%s`\n:diff-minus: Removed config list key %s", key, name)
			postSlackMessageToSupportChannel(context, env, caches, message, serial, slackClient)
		}

		softwareVersion, err := robots.GetSoftwareVersionBySerial(context, caches, serial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Unable to fetch software version")
			return
		}

		if carbon.MigratedToRoSyConfigs(softwareVersion) {
			rawCurrentUser, _ := context.Get(middleware.UserContextKey)
			currentUser, ok := rawCurrentUser.(*management.User)
			if !ok {
				ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Failed to gather requesting user information")
				return
			}

			robotSyncerClient.DeleteConfigValue(context, robot_syncer.DeleteConfigValueParameters{
				Serial:    serial.String(),
				Path:      requestPath,
				Timestamp: time.Now().UnixMilli(), // Eventually add ts to input for this handler
				UserID:    *currentUser.ID,
			})
		} else {
			err = configClient.RemoveFromList(key, name)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}
		}

		context.JSON(http.StatusOK, gin.H{})
	}
}

func postSlackMessageToSupportChannel(context *gin.Context, env *utils.Environment, caches cache.Caches, message string, serial *carbon.ValidSerial, slackClient *slack.Client) {
	rawCurrentUser, _ := context.Get(middleware.UserContextKey)
	currentUser, ok := rawCurrentUser.(*management.User)
	if !ok {
		log.Warn("Unable to notify support slack channel of config change: unable to get current user")
		return
	}
	err := configs.NotifyEdit(context, env, caches, slackClient, currentUser, serial, message)
	if err != nil {
		log.WithError(err).Warn("Unable to notify support slack channel of config change")
	}
}

func BulkEditConfigHandler(serviceCtx context.Context, caches cache.Caches, slackClient *slack.Client, robotSyncerClient *robot_syncer.Client, env *utils.Environment) gin.HandlerFunc {
	return func(context *gin.Context) {
		errorPrefix := "Failed to bulk update configs"

		if !configs.CanUpdateConfig(context, caches, &errorPrefix, nil) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		rawCurrentUser, _ := context.Get(middleware.UserContextKey)
		currentUser, ok := rawCurrentUser.(*management.User)
		if !ok {
			ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Failed to gather requesting user information")
			return
		}

		bulkEditRequest := &rosypb.BulkEditRequest{}
		if err := ReadProtoJSON(context, bulkEditRequest); err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
			return
		}

		if len(bulkEditRequest.Serials) == 0 {
			ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Must provide at least one serial")
			return
		}

		if len(bulkEditRequest.Operations) == 0 {
			ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Must provide at least one operation")
			return
		}

		if bulkEditRequest.Timestamp == nil {
			ReturnError(context, http.StatusBadRequest, nil, &errorPrefix, "Must provide a timestamp")
			return
		}

		bulkEditRequest.UserId = *currentUser.ID
		response, err := robotSyncerClient.BulkUpdateConfigs(context, bulkEditRequest)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Failed to bulk update configs")
			return
		}

		{
			background := serviceCtx
			if id, ok := utils.RequestID(context); ok {
				background = utils.WithRequestID(background, id)
			}
			go configs.NotifyBulkEditsSummary(background, env, caches, slackClient, currentUser, bulkEditRequest, response)
		}

		ReturnProtoJSON(context, response, &errorPrefix)
	}
}

func RegisterConfigs(serviceCtx context.Context, router *gin.RouterGroup, env *utils.Environment, auth0Middleware gin.HandlerFunc, domain string, auth0Client *management.Management, configClient *grpc_clients.ConfigClient, robotSyncerClient *robot_syncer.Client, caches cache.Caches, slackClient *slack.Client) {
	routes := router.Group("/configs")
	routes.Use(auth0Middleware)
	{
		routes.DELETE("/caches/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
			errorPrefix := "Failed to refresh config cache"

			serial, err := carbon.ParseSerial(ctx.Param("serial"))
			if err != nil {
				ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", ctx.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to refresh config cache for %s", serial)

			if !admin.CanUpdateAdmin(ctx, &errorPrefix) {
				ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			rawCurrentUser, _ := ctx.Get(middleware.UserContextKey)
			currentUser, ok := rawCurrentUser.(*management.User)
			if !ok {
				ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Failed to gather requesting user information")
				return
			}

			err = robotSyncerClient.RefreshConfigCache(ctx, serial, *currentUser.ID)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Robot Syncer call failed")
				return
			}

			ctx.JSON(http.StatusOK, gin.H{})
		})
		routes.GET("/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), GetTreeHandler(configClient, caches, robotSyncerClient))
		routes.POST("/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), SetValueHandler(configClient, caches, slackClient, robotSyncerClient, env))
		routes.POST("/bulk-edit", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), BulkEditConfigHandler(serviceCtx, caches, slackClient, robotSyncerClient, env))
		routes.DELETE("/:serial/*path", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), DeleteValueHandler(configClient, caches, slackClient, robotSyncerClient, env))
	}

	// used by frontend to manage config templates
	templates := routes.Group("/templates")
	{
		templates.GET("/:class", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), GetTemplateHandler(robotSyncerClient))
		templates.POST("/:class", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), SetTemplateValueHandler(robotSyncerClient))
		templates.DELETE("/:class", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), DeleteTemplateValueHandler(robotSyncerClient))
		templates.DELETE("/:class/cache", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), RefreshTemplateCache(robotSyncerClient))
	}

	schemas := routes.Group("/schemas")
	{
		schemas.GET("/:class", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), GetSchemaHandler(robotSyncerClient))
	}
}
