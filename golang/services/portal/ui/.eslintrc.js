/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable unicorn/prefer-module */
const path = require("node:path");
module.exports = {
  root: true,
  env: {
    browser: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:jest/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:react-hooks/recommended",
    "plugin:react/recommended",
    "plugin:sonarjs/recommended",
    "plugin:unicorn/recommended",
    "plugin:i18n-json/recommended",
    "prettier",
  ],
  globals: {
    _jsenv: "readonly",
    process: "readonly",
    Promise: "readonly",
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 6,
    sourceType: "module",
    ecmaFeatures: {
      modules: true,
      jsx: true,
    },
    project: path.join(__dirname, "tsconfig.json"),
  },
  plugins: [
    "@typescript-eslint",
    "jest",
    "jsx-a11y",
    "prettier",
    "react",
    "react-hooks",
    "sonarjs",
    "sort-imports-es6-autofix",
    "unicorn",
    "unused-imports",
    "i18next",
  ],
  settings: {
    jest: {
      version: 26,
    },
    react: {
      version: "detect",
    },
  },
  rules: {
    "no-undef": "off",
    "react/jsx-filename-extension": [
      "error",
      {
        extensions: ["tsx"],
      },
    ],
    "react/display-name": "off",
    "react/no-unescaped-entities": "off",
    "react/prop-types": "off",
    "jsx-a11y/click-events-have-key-events": "off",
    "@typescript-eslint/no-inferrable-types": "off",
    "@typescript-eslint/no-empty-function": "warn",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/explicit-function-return-type": [
      "error",
      { allowExpressions: true },
    ],
    camelcase: ["error", { properties: "never", ignoreDestructuring: true }],
    "prettier/prettier": "error",
    "sort-imports-es6-autofix/sort-imports-es6": [
      "error",
      {
        ignoreCase: true,
        ignoreMemberSort: false,
        memberSyntaxSortOrder: ["none", "all", "multiple", "single"],
      },
    ],
    "no-template-curly-in-string": "error",
    "require-atomic-updates": "error",
    curly: ["error", "all"],
    "dot-notation": "error",
    eqeqeq: "error",
    "no-alert": "warn",
    "no-empty": ["error", { allowEmptyCatch: true }],
    "no-empty-function": "off",
    "no-empty-pattern": "warn",
    "no-eq-null": "error",
    "no-eval": "error",
    "no-extend-native": "error",
    "no-extra-bind": "error",
    "no-floating-decimal": "error",
    "no-implicit-coercion": "error",
    "no-implicit-globals": "error",
    "no-implied-eval": "error",
    "no-iterator": "error",
    "no-lone-blocks": "warn",
    "no-loop-func": "error",
    "no-multi-str": "error",
    "no-new-wrappers": "error",
    "no-proto": "error",
    "no-restricted-properties": "warn",
    "no-return-assign": "error",
    "no-return-await": "off",
    "no-script-url": "error",
    "no-self-compare": "warn",
    "no-sequences": "error",
    "no-throw-literal": "error",
    "no-unused-expressions": "warn",
    "no-unused-vars": "off",
    "no-useless-call": "error",
    "no-useless-catch": "error",
    "no-useless-concat": "error",
    "no-useless-return": "error",
    "no-void": "error",
    "no-warning-comments": ["off", { location: "anywhere" }],
    "no-with": "error",
    radix: ["error", "as-needed"],
    "require-await": "off",
    yoda: "error",
    "consistent-this": "error",
    "func-name-matching": "error",
    "new-cap": "error",
    "no-lonely-if": "error",
    "no-multi-assign": "error",
    "no-negated-condition": "error",
    "no-nested-ternary": "error",
    "no-unneeded-ternary": "error",
    "one-var": ["error", "never"],
    "operator-assignment": "error",
    "no-duplicate-imports": "error",
    "no-useless-computed-key": "error",
    "no-useless-constructor": "off",
    "@typescript-eslint/no-useless-constructor": "error",
    "no-useless-rename": "error",
    "no-var": "error",
    "object-shorthand": "error",
    "prefer-arrow-callback": ["error", { allowNamedFunctions: true }],
    "prefer-const": "error",
    "prefer-numeric-literals": "error",
    "prefer-rest-params": "error",
    "prefer-spread": "error",
    "prefer-template": "error",
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        vars: "all",
        varsIgnorePattern: "^_",
        args: "after-used",
        argsIgnorePattern: "^_",
        ignoreRestSiblings: true,
      },
    ],
    "@typescript-eslint/prefer-for-of": "error",
    "unicorn/better-regex": "off",
    "unicorn/consistent-function-scoping": "off",
    "unicorn/filename-case": [
      "error",
      {
        cases: {
          camelCase: true,
          pascalCase: true,
        },
      },
    ],
    "unicorn/prefer-number-properties": ["error", { checkInfinity: false }],
    "unicorn/prefer-ternary": "off",
    "unicorn/prevent-abbreviations": "off",
    "unicorn/no-for-loop": "off",
    "unicorn/no-useless-undefined": [
      "error",
      {
        checkArguments: false,
      },
    ],
    "sonarjs/cognitive-complexity": ["error", 999],
    "sonarjs/no-duplicate-string": "off",
    "sonarjs/no-small-switch": "off",
    "sonarjs/prefer-single-boolean-return": "off",
    "i18next/no-literal-string": "error",
    "jest/expect-expect": ["warn", { assertFunctionNames: ["expect*"] }],
    "unicorn/consistent-destructuring": "off",
    "@typescript-eslint/no-unnecessary-condition": "error",
    "react-hooks/exhaustive-deps": [
      "error",
      {
        additionalHooks: "(useMemoAsync)",
      },
    ],

    // Prettier is the only arbiter of lexical style
    "lines-between-class-members": "off",
    "no-trailing-spaces": "off",
    "no-whitespace-before-property": "off",
    "arrow-spacing": "off",
  },
  overrides: [
    {
      files: ["*.ts", "*.tsx"],
      rules: {
        // https://github.com/typescript-eslint/typescript-eslint/issues/46#issuecomment-470486034
        "@typescript-eslint/no-unused-vars": "warn",
      },
    },
  ],
};
