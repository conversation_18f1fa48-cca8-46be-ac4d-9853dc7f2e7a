{"name": "portal", "description": "Carbon Robotics Portal", "version": "1.0.0", "author": "@anstosa", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@mui/x-license-pro": "catalog:"}, "devDependencies": {"@auth0/auth0-react": "^2.1.0", "@auth0/auth0-spa-js": "^2.1.3", "@babel/core": "^7.21.8", "@bufbuild/protobuf": "^2.0.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "catalog:", "@emotion/styled": "^11.11.0", "@fontsource/poppins": "^5.0.8", "@fontsource/russo-one": "^4.5.10", "@grpc/grpc-js": "^1.8.14", "@jest/globals": "catalog:", "@mapbox/mapbox-gl-draw": "^1.4.1", "@mui/icons-material": "catalog:", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "catalog:", "@mui/system": "catalog:", "@mui/x-data-grid-premium": "^7.12.0", "@mui/x-date-pickers-pro": "^7.12.0", "@mui/x-tree-view": "^7.12.0", "@reduxjs/toolkit": "catalog:", "@sentry/browser": "^7.68.0", "@sentry/react": "^7.68.0", "@sentry/tracing": "^7.51.2", "@sentry/vite-plugin": "^3.5.0", "@sentry/webpack-plugin": "^2.16.0", "@sinonjs/fake-timers": "^11.2.2", "@svgr/webpack": "^8.0.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.9", "@testing-library/dom": "^9.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@turf/area": "^6.5.0", "@turf/bearing": "catalog:", "@turf/buffer": "catalog:", "@turf/centroid": "^6.5.0", "@turf/destination": "catalog:", "@turf/distance": "catalog:", "@turf/helpers": "catalog:", "@turf/transform-translate": "^6.5.0", "@turf/turf": "^6.5.0", "@types/circular-dependency-plugin": "^5.0.5", "@types/color": "catalog:", "@types/csp-html-webpack-plugin": "^3.0.2", "@types/geojson": "catalog:", "@types/google-protobuf": "^3.15.6", "@types/humanize-duration": "^3.27.1", "@types/jest": "^29.5.1", "@types/luxon": "catalog:", "@types/mapbox-gl": "catalog:", "@types/mapbox__mapbox-gl-draw": "^1.4.0", "@types/mini-css-extract-plugin": "^2.5.1", "@types/ngeohash": "^0.6.8", "@types/node": "^20.1.3", "@types/numeral": "^2.0.2", "@types/path-browserify": "^1.0.0", "@types/postcss-preset-env": "^7.7.0", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/react-helmet": "^6.1.6", "@types/react-test-renderer": "^18.0.0", "@types/react-virtualized": "^9.22.2", "@types/semver": "^7.5.0", "@types/sinonjs__fake-timers": "^8.1.5", "@types/supertest": "^2.0.12", "@types/tailwindcss": "^3.1.0", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.14", "browserslist": "^4.21.5", "chart.js": "^4.4.1", "chartjs-adapter-luxon": "^1.3.1", "chartjs-plugin-zoom": "^2.0.1", "cheap-ruler": "^3.0.2", "circular-dependency-plugin": "^5.2.2", "clsx": "catalog:", "color": "catalog:", "convert-units": "^3.0.0-beta.6", "copy-webpack-plugin": "^12.0.2", "csp-html-webpack-plugin": "^5.1.0", "css-loader": "^6.7.3", "date-fns": "^2.30.0", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-i18n-json": "catalog:", "eslint-plugin-i18next": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jest": "catalog:", "eslint-plugin-jsx-a11y": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-sonarjs": "catalog:", "eslint-plugin-sort-imports-es6-autofix": "catalog:", "eslint-plugin-unicorn": "catalog:", "eslint-plugin-unused-imports": "catalog:", "eslint-webpack-plugin": "^4.0.1", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^8.0.0", "formik": "^2.2.9", "formik-mui": "^5.0.0-alpha.0", "fuse.js": "^6.6.2", "git-revision-webpack-plugin": "^5.0.0", "google-protobuf": "^3.21.2", "helvatica-neue-lt": "^1.0.1", "history": "^5.3.0", "html-webpack-plugin": "^5.5.1", "humanize-duration": "^3.28.0", "husky": "^8.0.0", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.2.2", "i18next-locales-sync": "^2.0.1", "jest": "29.5.0", "jest-environment-jsdom": "^29.6.4", "jest-fetch-mock": "^3.0.3", "jest-ts-webcompat-resolver": "^1.0.0", "luxon": "catalog:", "mapbox-gl": "catalog:", "mini-css-extract-plugin": "^2.7.5", "ngeohash": "^0.6.3", "nodemon": "^3.0.1", "notistack": "^3.0.1", "postcss": "^8.4.23", "postcss-load-config": "^4.0.1", "postcss-loader": "^7.3.0", "postcss-preset-env": "8.3.2", "prettier": "catalog:", "react": "catalog:", "react-chartjs-2": "^5.2.0", "react-dom": "catalog:", "react-dropzone": "^14.2.3", "react-ga": "^3.3.1", "react-helmet": "^6.1.0", "react-hooks-testing-library": "^0.6.0", "react-hotkeys": "^2.0.0", "react-i18next": "^13.5.0", "react-map-gl": "catalog:", "react-markdown": "^8.0.7", "react-redux": "catalog:", "react-router": "catalog:", "react-router-dom": "catalog:", "react-test-renderer": "^18.2.0", "react-virtualized": "^9.22.6", "recharts": "^2.10.3", "reconnecting-websocket": "catalog:", "redux": "^5.0.1", "redux-persist": "^6.0.0", "remark-gfm": "^3.0.1", "resolve-url-loader": "^5.0.0", "sass": "^1.69.7", "sass-loader": "^13.2.2", "semver": "^7.5.1", "stream-chat": "^8.16.0", "stream-chat-react": "^11.8.0", "style-loader": "^3.3.2", "stylelint": "^15.6.1", "stylelint-config-sass-guidelines": "^10.0.0", "stylelint-order": "^6.0.3", "supertest": "^6.3.3", "syncpack": "^9.8.6", "tailwind-merge": "catalog:", "tailwindcss": "catalog:", "timezone-mock": "^1.3.6", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tss-react": "^4.8.5", "typescript": "^5.5.4", "url-loader": "^4.1.1", "uuid": "^9.0.0", "vite": "^6.2.5", "vite-plugin-checker": "^0.9.1", "vite-plugin-svgr": "^4.3.0", "webpack": "^5.82.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0", "yup": "catalog:"}, "engines": {"node": "20.13.*", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "^10.12.2"}, "license": "UNLICENSED", "private": true, "repository": "https://github.com/carbonrobotics/cloud/tree/master/golang/services/portal/ui", "scripts": {"build": "NODE_ENV=production vite build", "ci": "pnpm lint && pnpm type-check && pnpm test:ci", "clean": "rm -rf dist", "lint": "pnpm lint:js && pnpm lint:css", "lint:css": "stylelint \"**/*.scss\"", "lint:css:fix": "stylelint --fix \"**/*.scss\"", "lint:fix": "pnpm lint:js:fix && pnpm lint:css:fix", "lint:js": "eslint --ext .js,.jsx,.ts,.tsx,.json src/ ../../../../ui/common/src/", "lint:js:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx,.json src/ ../../../../ui/common/src/", "prepare": "cd ../../../../ && husky install golang/services/portal/ui/.husky", "start": "NODE_ENV=development vite", "start:test": "jest --colors --watch", "test": "jest --colors --passWithNoTests", "test:ci": "jest --ci --no-cache --passWithNoTests", "test:coverage": "jest --colors --passWithNoTests --coverage --coverageDirectory=/tmp/coverage", "test:snapshotupdate": "jest -u", "type-check": "tsc --noEmit"}}