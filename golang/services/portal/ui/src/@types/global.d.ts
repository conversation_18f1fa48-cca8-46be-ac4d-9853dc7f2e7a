import "jest-fetch-mock";

declare global {
  interface Window {
    _jsenv: JSEnvironment;
    carbonThemeStore?: ThemeStore;
  }
}
interface JSEnvironment {
  REACT_APP_AUTH0_AUDIENCE: string;
  REACT_APP_AUTH0_AUTH_DOMAIN: string;
  REACT_APP_AUTH0_REST_CLIENT_ID: string;
  REACT_APP_RELEASE: string;
  REACT_APP_COMMITHASH: string;
  REACT_APP_GOOGLE_MEASUREMENT_ID: string;
  REACT_APP_IDM_URL: string;
  REACT_APP_MAPBOX_ACCESS_TOKEN: string;
  REACT_APP_MUI_LICENSE_KEY: string;
  REACT_APP_RTC_LOCATOR_URL: string;
  REACT_APP_RTC_JOBS_URL: string;
  REACT_APP_SENTRY_DSN: string;
  REACT_APP_SENTRY_ENVIRONMENT: string;
  REACT_APP_STREAM_API_KEY: string;
  REACT_APP_IMAGE_SERVICE_URL: string;
  REACT_APP_VESELKA_URL: string;
  REACT_APP_FEATURE_LIGHT_DARK_THEME: string;
}
