import { classes } from "portal/theme/theme";
import {
  DateRange,
  DateTimeRangePicker,
  type DateTimeRangePickerProps,
} from "@mui/x-date-pickers-pro";
import { DateTime } from "luxon";
import { isSameDay, snapToDayEnd } from "portal/utils/dates";
import { useShadowValue } from "portal/utils/hooks/useShadowValue";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import React, { FunctionComponent } from "react";

const _CarbonDateTimeRangePicker: FunctionComponent<
  Omit<DateTimeRangePickerProps<DateTime>, "onChange"> & {
    openEnded?: boolean;
    onChange?: (val: DateRange<DateTime> | undefined) => void;
  }
> = ({
  slots,
  slotProps,
  value: externalValue,
  readOnly,
  onChange,
  openEnded = false,
  ...props
}) => {
  const { t } = useTranslation();

  const [value, setValue] = useShadowValue<DateRange<DateTime> | undefined>(
    externalValue
  );

  return (
    <DateTimeRangePicker<DateTime>
      readOnly={readOnly}
      localeText={{
        start: t("components.DateRangePicker.startDate"),
        end: t("components.DateRangePicker.endDate"),
        clearButtonLabel: t("components.DateRangePicker.clear"),
      }}
      slots={{
        ...slots,
      }}
      value={value ?? externalValue}
      onChange={([start, end]) => {
        let safeEnd = end;
        if (value && !value[1] && end) {
          const endOfDay = snapToDayEnd(end);
          const isToday = isSameDay(end);
          safeEnd = isToday ? DateTime.now() : endOfDay;
        }
        const newRange: DateRange<DateTime> = [start, safeEnd];
        setValue(newRange);

        if (!onChange) {
          return;
        }
        if (!start && !safeEnd) {
          onChange(undefined);
          return;
        }
        if (
          (!openEnded && (!start || !safeEnd)) ||
          (start && !start.isValid) ||
          (safeEnd && !safeEnd.isValid)
        ) {
          return;
        }
        onChange(newRange);
      }}
      slotProps={{
        fieldRoot: {
          className: classes(props.className, {
            "Mui-readOnly": readOnly,
          }),
        },
        fieldSeparator: {
          className: "hidden",
        },
        previousIconButton: {
          className: "text-white",
        },
        nextIconButton: {
          className: "text-white",
        },
        ...slotProps,
      }}
      {...props}
    />
  );
};

export const CarbonDateTimeRangePicker = withErrorBoundary(
  {
    i18nKey: "components.DateRangePicker.error",
    small: true,
  },
  _CarbonDateTimeRangePicker
);
