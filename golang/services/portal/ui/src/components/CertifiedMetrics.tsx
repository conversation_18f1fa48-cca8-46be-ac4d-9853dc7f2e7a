import { <PERSON><PERSON>, Card, Skeleton, Tooltip, Typography } from "@mui/material";
import { buildPermission } from "portal/utils/auth";
import { classes } from "portal/theme/theme";
import { DailyMetricResponse } from "protos/portal/metrics";
import { DATE_PATH_FORMAT } from "portal/utils/dates";
import { DateTime } from "luxon";
import { FeatureFlag, useFeatureFlag } from "portal/utils/hooks/useFeatureFlag";
import { filterWhere, range as rangeArray } from "portal/utils/arrays";
import {
  getMetricHelp,
  getMetricName,
  METRIC_ACRES_WEEDED,
  METRIC_AVERAGE_CROP_SIZE,
  METRIC_AVERAGE_SPEED,
  METRIC_AVERAGE_TARGETABLE_REQ_LASER_TIME,
  METRIC_AVERAGE_WEED_SIZE,
  METRIC_BANDING_CONFIG,
  METRIC_COUNT_BROADLEAF,
  METRIC_COUNT_GRA<PERSON>,
  METRIC_COUNT_OFFSHOOT,
  METRIC_COUNT_PURSLANE,
  METRIC_COVERAGE_SPEED,
  METRIC_CROP,
  METRIC_CROP_DENSITY,
  METRIC_GROUPS,
  METRIC_JOB_NAME,
  METRIC_KEPT_CROPS,
  METRIC_KILLED_WEEDS,
  METRIC_MISSED_CROPS,
  METRIC_MISSED_WEEDS,
  METRIC_OPERATOR_EFFECTIVENESS,
  METRIC_OVERALL_EFFICIENCY,
  METRIC_SKIPPED_CROPS,
  METRIC_SKIPPED_WEEDS,
  METRIC_THINNED_CROPS,
  METRIC_THINNING_EFFICIENCY,
  METRIC_TIME_EFFICIENCY,
  METRIC_TOTAL_WEEDS,
  METRIC_TOTAL_WEEDS_IN_BAND,
  METRIC_UPTIME,
  METRIC_VALID_CROPS,
  METRIC_WEED_DENSITY,
  METRIC_WEEDING_EFFICIENCY,
  METRIC_WEEDING_TIME,
  MetricGroup,
} from "portal/utils/certifiedMetrics";
import { isEmpty } from "portal/utils/objects";
import { Measurement } from "./measurement/Measurement";
import { Metric, MetricValidation } from "portal/utils/metrics";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { titleCase } from "portal/utils/strings";
import { useAuthorizationRequired } from "./auth/WithAuthorizationRequired";
import { useLazyGetRobotMetricsQuery } from "portal/state/portalApi";
import { useLazyPopups } from "portal/utils/hooks/useApiPopups";
import { useMemoAsync } from "portal/utils/hooks/useMemoAsync";
import { useTranslation } from "react-i18next";
import { ViewPlaceholder } from "./ViewPlaceholder";
import Grid from "@mui/material/Unstable_Grid2";
import InfoIcon from "@mui/icons-material/InfoOutlined";
import JobStatusIcon from "@mui/icons-material/Circle";
import React, { FunctionComponent, PropsWithChildren } from "react";

interface CertifiedMetricsBaseProps {
  // If set (for job metrics), show a "Job Status: {Open/Complete}"
  // pseudometric. Otherwise (for daily metrics), show the job name.
  job?: JobData | undefined;
  className?: string;
}

interface JobData {
  open: boolean;
}

interface CertifiedMetricsParameterProps extends CertifiedMetricsBaseProps {
  serial: string;
  date: DateTime;
}

interface CertifiedMetricsObjectProps extends CertifiedMetricsBaseProps {
  metrics: DailyMetricResponse;
}

type CertifiedMetricsProps =
  | CertifiedMetricsParameterProps
  | CertifiedMetricsObjectProps;

export const CertifiedMetrics: FunctionComponent<CertifiedMetricsProps> = (
  props
) => {
  const { job } = props;

  const { t } = useTranslation();
  const isObjectProps = "metrics" in props;

  const hasMetricsRedesign =
    useFeatureFlag(FeatureFlag.METRICS_REDESIGN).isEnabled ?? false;

  let inputMetrics: DailyMetricResponse | undefined;
  let serial: string;
  let date: string;

  const today = DateTime.local().toFormat(DATE_PATH_FORMAT);

  if (isObjectProps) {
    inputMetrics = props.metrics;
    serial = inputMetrics.serial;
    date = inputMetrics.date;
  } else {
    date = today;
    ({ serial } = props);
  }

  const [getMetrics] = useLazyPopups(useLazyGetRobotMetricsQuery());
  // fetch metrics if they aren't set
  const [metrics, { isLoading }] = useMemoAsync<
    DailyMetricResponse | undefined
  >(
    async () => {
      if (!serial) {
        return;
      }
      if (isObjectProps) {
        return inputMetrics;
      }
      const { data } = await getMetrics({ serial, date }, true);
      return data;
    },
    [serial, isObjectProps, getMetrics, date, inputMetrics],
    undefined
  );

  if (isLoading) {
    return (
      <>
        {rangeArray(3, true).map((index) => (
          <Skeleton variant="rectangular" className="w-full" key={index} />
        ))}
      </>
    );
  }
  if (!metrics || isEmpty(metrics)) {
    return (
      <ViewPlaceholder
        text={t("views.fleet.robots.history.errors.noMetrics")}
      />
    );
  }

  const ongoingWarning = date === today && (
    <Alert className="mb-8 self-stretch" severity="warning">
      {t("views.fleet.robots.history.warnings.ongoing")}
    </Alert>
  );

  // Show old view to users who don't have the redesign feature flag.
  if (!hasMetricsRedesign) {
    return (
      <div className={props.className}>
        {ongoingWarning}
        <Grid container className="w-full items-stretch" spacing={2}>
          {METRIC_GROUPS.map((group) => (
            <OldMetricBlock group={group} data={metrics} key={group.i18nKey} />
          ))}
        </Grid>
      </div>
    );
  }

  return (
    <div className={props.className}>
      {ongoingWarning}

      <MetricSection className="mb-5">
        <Typography
          variant="h3"
          className="font-poppins normal-case text-[24px] font-bold mb-5"
        >
          {t("views.metrics.sections.performanceAndMachineStats")}
        </Typography>

        <Grid container className="w-full items-stretch" spacing={2}>
          <HeadlineMetric metric={METRIC_OVERALL_EFFICIENCY} data={metrics} />
          <HeadlineMetric metric={METRIC_COVERAGE_SPEED} data={metrics} />
          <HeadlineMetric metric={METRIC_TIME_EFFICIENCY} data={metrics} />
          <HeadlineMetric metric={METRIC_WEEDING_TIME} data={metrics} />
        </Grid>

        <Grid container spacing={2} className="w-full mt-2">
          <Grid xs={12} lg={6} className="flex flex-col gap-3 lg:pr-12">
            {job !== undefined && <JobStatus isJobOpen={job.open} />}
            <InlineMetric metric={METRIC_CROP} data={metrics} />
            {job === undefined && (
              <InlineMetric metric={METRIC_JOB_NAME} data={metrics} />
            )}
            <InlineMetric metric={METRIC_ACRES_WEEDED} data={metrics} />
            <InlineMetric metric={METRIC_WEEDING_EFFICIENCY} data={metrics} />
            <InlineMetric metric={METRIC_THINNING_EFFICIENCY} data={metrics} />
          </Grid>
          <Grid xs={12} lg={6} className="flex flex-col gap-3 lg:pr-12">
            <InlineMetric metric={METRIC_UPTIME} data={metrics} />
            <InlineMetric metric={METRIC_AVERAGE_SPEED} data={metrics} />
            <InlineMetric
              metric={METRIC_AVERAGE_TARGETABLE_REQ_LASER_TIME}
              data={metrics}
            />
            <InlineMetric
              metric={METRIC_OPERATOR_EFFECTIVENESS}
              data={metrics}
            />
            <InlineMetric metric={METRIC_BANDING_CONFIG} data={metrics} />
          </Grid>
        </Grid>
      </MetricSection>

      <MetricSection>
        <div className="mb-5 flex flex-col gap-1.5">
          <Typography
            variant="h3"
            className="font-poppins normal-case text-[24px] font-bold"
          >
            {t("views.metrics.sections.estimatedFieldMetrics")}
          </Typography>
          <Typography className="text-xs opacity-50 italic">
            {t("views.metrics.sections.estimatedFieldMetricsDisclaimer")}
          </Typography>
        </div>

        <Grid container spacing={2}>
          <Grid xs={12} lg={6} className="flex flex-col gap-3 lg:pr-12">
            <InlineMetric metric={METRIC_WEED_DENSITY} data={metrics} />
            <InlineMetric metric={METRIC_AVERAGE_WEED_SIZE} data={metrics} />
            <MetricSubsection>
              <InlineMetric metric={METRIC_COUNT_BROADLEAF} data={metrics} />
              <InlineMetric metric={METRIC_COUNT_PURSLANE} data={metrics} />
              <InlineMetric metric={METRIC_COUNT_OFFSHOOT} data={metrics} />
              <InlineMetric metric={METRIC_COUNT_GRASS} data={metrics} />
            </MetricSubsection>
            <MetricSubsection>
              <InlineMetric metric={METRIC_TOTAL_WEEDS} data={metrics} />
              <InlineMetric
                metric={METRIC_TOTAL_WEEDS_IN_BAND}
                data={metrics}
              />
              <InlineMetric metric={METRIC_KILLED_WEEDS} data={metrics} />
              <InlineMetric metric={METRIC_MISSED_WEEDS} data={metrics} />
              <InlineMetric metric={METRIC_SKIPPED_WEEDS} data={metrics} />
            </MetricSubsection>
          </Grid>
          <Grid xs={12} lg={6} className="flex flex-col gap-3 lg:pr-12">
            <InlineMetric metric={METRIC_CROP_DENSITY} data={metrics} />
            <InlineMetric metric={METRIC_AVERAGE_CROP_SIZE} data={metrics} />
            <MetricSubsection>
              <InlineMetric metric={METRIC_VALID_CROPS} data={metrics} />
              <InlineMetric metric={METRIC_THINNED_CROPS} data={metrics} />
              <InlineMetric metric={METRIC_SKIPPED_CROPS} data={metrics} />
              <InlineMetric metric={METRIC_MISSED_CROPS} data={metrics} />
              <InlineMetric metric={METRIC_KEPT_CROPS} data={metrics} />
            </MetricSubsection>
          </Grid>
        </Grid>
      </MetricSection>
    </div>
  );
};

interface MetricSectionProps {
  className?: string;
}

const MetricSection: FunctionComponent<
  PropsWithChildren<MetricSectionProps>
> = ({ className, children }) => {
  return (
    <Card
      variant="outlined"
      className={classes(
        "w-full px-8 py-7 bg-carbon-gray-dark border-1 border-carbon-gray-semidark",
        className
      )}
    >
      {children}
    </Card>
  );
};

const MetricSubsection: FunctionComponent<PropsWithChildren> = ({
  children,
}) => {
  return (
    <Card className="p-4 pe-3 bg-carbon-gray-semidark flex flex-col gap-1">
      {children}
    </Card>
  );
};

interface HeadlineMetricProps {
  metric: Metric;
  data: DailyMetricResponse;
}

const HeadlineMetric: FunctionComponent<HeadlineMetricProps> = ({
  metric,
  data,
}) => {
  const { t } = useTranslation();
  const value = metric.getValue(
    data[metric.id as keyof DailyMetricResponse],
    data,
    true
  );

  return (
    <Grid xs={12} lg={3}>
      <Card
        variant="outlined"
        className="h-full pt-6 pb-4 px-5 bg-carbon-gray-semidark border-1 border-carbon-gray-medium flex flex-col gap-2"
      >
        <Typography className="font-bold text-[20px]">
          <Measurement
            metric={metric}
            value={value}
            className="justify-start"
          />
        </Typography>
        <Typography className="text-sm" style={{ overflowWrap: "anywhere" }}>
          {getMetricName(t, metric, { hasMetricsRedesign: true })}
          <MetricHelp
            metric={metric}
            className="ms-1 align-middle text-gray-400"
          />
        </Typography>
      </Card>
    </Grid>
  );
};

interface JobStatusProps {
  isJobOpen: boolean;
}

const JobStatus: FunctionComponent<JobStatusProps> = ({ isJobOpen }) => {
  const { t } = useTranslation();

  return (
    <div className="flex justify-between items-center">
      <strong>{t("views.metrics.jobStatus.description")}</strong>
      <span>
        <JobStatusIcon
          className={classes(
            "me-1 align-baseline",
            isJobOpen ? "text-green-500" : "text-gray-400"
          )}
          style={{ fontSize: "1cap" }}
        />
        {isJobOpen
          ? t("views.metrics.jobStatus.open")
          : t("views.metrics.jobStatus.closed")}
      </span>
    </div>
  );
};

interface InlineMetricProps {
  metric: Metric;
  data: DailyMetricResponse;
}

const InlineMetric: FunctionComponent<InlineMetricProps> = ({
  metric,
  data,
}) => {
  const { t } = useTranslation();
  const value = metric.getValue(
    data[metric.id as keyof DailyMetricResponse],
    data,
    true
  );

  return (
    <div className="flex flex-wrap justify-between items-center">
      <strong>
        {getMetricName(t, metric, { hasMetricsRedesign: true })}
        <MetricHelp
          metric={metric}
          className="ms-1 align-middle text-gray-400"
        />
      </strong>
      <Measurement metric={metric} value={value} className="w-auto h-auto" />
    </div>
  );
};

interface MetricHelpProps {
  metric: Metric;
  className?: string;
}

const MetricHelp: FunctionComponent<MetricHelpProps> = ({
  metric,
  className,
}) => {
  const { t } = useTranslation();
  const tooltip = getMetricHelp(t, metric);
  if (!tooltip) {
    return;
  }
  return (
    <Tooltip arrow title={tooltip}>
      <InfoIcon fontSize="inherit" className={className} tabIndex={0} />
    </Tooltip>
  );
};

interface OldMetricBlockProps {
  group: MetricGroup;
  data: DailyMetricResponse;
}

const OldMetricBlock: FunctionComponent<OldMetricBlockProps> = ({
  group: { i18nKey: titleKey, metrics },
  data,
}) => {
  const { t } = useTranslation();

  const { isEnabled: hasUnvalidatedMetrics } = useFeatureFlag(
    FeatureFlag.UNVALIDATED_METRICS
  );
  const isInternalMetricsAuthorized = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.metrics_internal,
      PermissionDomain.all
    ),
  ]);
  const isAllPending =
    filterWhere(metrics, { validation: MetricValidation.VALIDATED })?.length ===
    0;

  if (isAllPending && !isInternalMetricsAuthorized && !hasUnvalidatedMetrics) {
    return;
  }
  return (
    <Grid xs={12} lg={6}>
      <Card className="p-4 h-full">
        <Typography
          variant="h4"
          className={classes(
            "text-md opacity-70 mb-0 leading-1 w-full text-lg",
            {
              "italic text-blue-400": isAllPending,
            }
          )}
        >
          {titleCase(t(titleKey))}
        </Typography>
        <Grid container columnSpacing={1} className="w-full">
          {metrics.map((metric) => (
            <OldMetricView key={metric.id} data={data} metric={metric} />
          ))}
        </Grid>
      </Card>
    </Grid>
  );
};

interface OldMetricProps {
  data: DailyMetricResponse;
  metric: Metric;
}
const OldMetricView: FunctionComponent<OldMetricProps> = ({ data, metric }) => {
  const { isEnabled: hasUnvalidatedMetrics } = useFeatureFlag(
    FeatureFlag.UNVALIDATED_METRICS
  );
  const isInternalMetricsAuthorized = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.metrics_internal,
      PermissionDomain.all
    ),
  ]);
  const value = metric.getValue(
    data[metric.id as keyof DailyMetricResponse],
    data,
    isInternalMetricsAuthorized
  );

  const { t } = useTranslation();
  const wrapperClasses = classes({
    "italic text-blue-600": metric.validation === MetricValidation.PENDING,
    "italic text-orange-200": metric.validation === MetricValidation.INTERNAL,
  });

  if (
    metric.validation === MetricValidation.PENDING &&
    !isInternalMetricsAuthorized &&
    !hasUnvalidatedMetrics
  ) {
    return;
  }

  if (
    metric.validation === MetricValidation.INTERNAL &&
    !isInternalMetricsAuthorized
  ) {
    return;
  }

  return (
    <Grid container className="items-center" xs={12}>
      <Grid
        className={classes(wrapperClasses, "whitespace-nowrap text-sm")}
        xs="auto"
      >
        {getMetricName(t, metric, { hasMetricsRedesign: false })}
      </Grid>
      <Grid
        className={classes(
          wrapperClasses,
          "font-mono font-bold whitespace-nowrap"
        )}
        xs="auto"
        xsOffset="auto"
      >
        <Measurement metric={metric} value={value} />
      </Grid>
    </Grid>
  );
};
