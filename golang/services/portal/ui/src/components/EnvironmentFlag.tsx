import { classes } from "portal/theme/theme";
import { CornerFlag } from "./CornerFlag";
import {
  isDevelopment,
  isProduction,
  isStaging,
} from "portal/utils/environment";
import { useTranslation } from "react-i18next";
import React, { FunctionComponent } from "react";

/**
 * Visual flag for upper left corner showing what the current environment is
 */
export const EnvironmentFlag: FunctionComponent = () => {
  const { t } = useTranslation();
  return (
    <CornerFlag
      label={(() => {
        if (isStaging) {
          return t("components.EnvironmentFlag.beta");
        }
        if (isDevelopment) {
          return t("components.EnvironmentFlag.dev");
        }
        return "";
      })()}
      className={classes("pointer-events-none fixed z-[99999] print:hidden", {
        hidden: isProduction,
        "bg-red-800": isStaging,
        "bg-purple-800": isDevelopment,
      })}
    />
  );
};
