import { isNil } from "portal/utils/identity";
import { number, object } from "yup";
import { SMALL_TEXT_FIELD_DARK } from "portal/theme/theme";
import { TextField, TextFieldProps } from "@mui/material";
import { yupToFormErrors } from "formik";
import React, { FC, useState } from "react";

// don't let undefined value go in because it messes with the MUI controlled / uncontrolled
const convertToControlSafeValue = (val: number | undefined): string => {
  return val === undefined ? "" : String(val);
};

interface NumberInputProps {
  label: string;
  value: number | undefined;
  onChange: (val: number | undefined) => void;
  min?: number;
  max?: number;
  error?: string;
  textFieldProps?: Partial<TextFieldProps>;
}
export const NumberInput: FC<NumberInputProps> = ({
  label,
  value,
  onChange,
  min,
  max,
  error,
  textFieldProps = {},
}) => {
  const [editedValue, setEditedValue] = useState<string>(
    convertToControlSafeValue(value)
  );
  const [internalError, setInternalError] = useState<string | undefined>();

  const validationSchema = object({
    value: number()
      .nullable()
      .min(min ?? -Infinity)
      .max(max ?? Infinity),
  });

  const validate = (val: number | undefined): boolean => {
    try {
      validationSchema.validateSync({ value: val }, { abortEarly: false });
      setInternalError(undefined);
      return true;
    } catch (yupError) {
      const validationErrors = yupToFormErrors<{ value: number }>(yupError);
      setInternalError(validationErrors.value);
      return false;
    }
  };

  const onBlur = ({
    target: { value },
  }: React.FocusEvent<HTMLInputElement>): void => {
    const val = value ? Number(value) : undefined;
    if (
      isNil(val) ||
      (!isNil(min) && val < min) ||
      (!isNil(max) && val > max)
    ) {
      onChange(undefined);
    }
    validate(val);
    onChange(val);
  };

  return (
    <TextField
      inputMode="numeric"
      inputProps={{ pattern: "[0-9]*" }}
      label={label}
      {...SMALL_TEXT_FIELD_DARK}
      {...textFieldProps}
      onChange={(e) => setEditedValue(e.target.value)}
      value={editedValue}
      onBlur={onBlur}
      error={Boolean(isNil(error) ? internalError : error)}
      helperText={isNil(error) ? internalError : error}
    />
  );
};
