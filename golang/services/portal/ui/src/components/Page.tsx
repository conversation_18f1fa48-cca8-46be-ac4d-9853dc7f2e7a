import { classes } from "portal/theme/theme";
import { Container, ContainerProps } from "@mui/material";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import React, {
  createContext,
  FunctionComponent,
  PropsWithChildren,
  RefObject,
  useContext,
  useRef,
} from "react";

type PageRefContext = RefObject<HTMLDivElement> | null;
// eslint-disable-next-line unicorn/no-null
export const PageRefContext = createContext<PageRefContext>(null);

export const usePageRef = (): PageRefContext => {
  const ctx = useContext(PageRefContext);
  if (!ctx) {
    throw new Error("usePageRef must be used within PageRefContext.Provider");
  }
  return ctx;
};

interface Props extends PropsWithChildren {
  maxWidth?: ContainerProps["maxWidth"];
}

/**
 * Basic page wrapper for portal.
 * Handles breakpoints, scrolling, default layout, etc
 */
const _Page: FunctionComponent<Props> = ({ maxWidth = "xl", children }) => {
  const pageRef = useRef<HTMLDivElement>(null);
  return (
    <PageRefContext.Provider value={pageRef}>
      <div
        ref={pageRef}
        className="overflow-auto w-full mt-16 print:mt-0 flex-grow flex flex-col"
      >
        <Container
          maxWidth={maxWidth}
          className={classes(
            "relative",
            "py-10 print:py-0 w-full print:px-0",
            "flex-grow flex flex-col"
          )}
        >
          {children}
        </Container>
      </div>
    </PageRefContext.Provider>
  );
};

export const Page = withErrorBoundary({}, _Page);

const _FullViewportPage: FunctionComponent<Props> = ({ children }) => {
  return (
    <div id="page" className="flex w-full h-screen">
      <Container
        maxWidth={false}
        className={classes("flex h-full flex-col w-full")}
      >
        {children}
      </Container>
    </div>
  );
};

export const FullViewportPage = withErrorBoundary({}, _FullViewportPage);

/**
 * Wrapper for an element inside a Page that removes the Page's
 * scroll behavior
 *
 * Can be dynamically disabled to allow scrolling for specific breakpoints
 */
interface NoScrollProps extends PropsWithChildren {
  className?: string;
  disable?: boolean;
}
export const NoScroll: FunctionComponent<NoScrollProps> = ({
  className,
  children,
  disable,
}) => {
  return disable ? (
    children
  ) : (
    <div
      className={classes(
        "flex flex-col overflow-hidden absolute inset-0 md:inset-10",
        className
      )}
    >
      {children}
    </div>
  );
};
