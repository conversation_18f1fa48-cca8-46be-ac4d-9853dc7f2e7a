import { classes } from "portal/theme/theme";
import { RobotClass } from "portal/utils/robots";
import { useSelf } from "portal/state/store";
import Bud<PERSON>ogo from "portal/images/bud_icon_border.svg?react";
import CarbonLogo from "portal/images/logo_icon_border.svg?react";
import React, { FunctionComponent } from "react";
import Rtc<PERSON>ogo from "portal/images/rtc_icon_border.svg?react";
import <PERSON><PERSON>ogo from "portal/images/slayer_icon_border.svg?react";

interface Props {
  className?: string;
  robotType: RobotClass;
}
export const RobotTypeIcon: FunctionComponent<Props> = ({
  className,
  robotType,
}) => {
  const { isInternal } = useSelf();
  const baseClass = "w-4 h-4";
  if (!isInternal) {
    return (
      <CarbonLogo
        className={classes(className, baseClass, "text-orange-500")}
      />
    );
  }
  switch (robotType) {
    case RobotClass.Simulators:
    case RobotClass.Slayers: {
      return (
        <SlayerLogo
          className={classes(className, baseClass, "text-orange-500")}
        />
      );
    }
    case RobotClass.Buds: {
      return (
        <BudLogo className={classes(className, baseClass, "text-blue-500")} />
      );
    }
    case RobotClass.Rtcs: {
      return (
        <RtcLogo className={classes(className, baseClass, "text-orange-500")} />
      );
    }
    default: {
      return (
        <CarbonLogo
          className={classes(className, baseClass, "text-orange-500")}
        />
      );
    }
  }
};
