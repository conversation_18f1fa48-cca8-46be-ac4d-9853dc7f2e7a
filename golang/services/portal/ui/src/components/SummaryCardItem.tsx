import { classes } from "portal/theme/theme";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import React, { FunctionComponent, ReactElement } from "react";

export interface SummaryCardItemProps {
  icon: ReactElement;
  className?: string;
  text: ReactElement | string;
  actions?: ReactElement[];
  isEmpty?: boolean;
  isNormal?: boolean;
  hideEmpty?: boolean;
}

const _SummaryCardItem: FunctionComponent<SummaryCardItemProps> = ({
  icon,
  className,
  text,
  isEmpty,
  hideEmpty = false,
  actions,
}) => {
  if (hideEmpty && isEmpty) {
    return;
  }
  return (
    <li className={classes("flex items-start gap-2", className)}>
      {icon}
      <span className="flex-1 whitespace-nowrap overflow-ellipsis overflow-hidden">
        {text}
      </span>
      {actions && <div className="flex-shrink-0">{actions}</div>}
    </li>
  );
};

export const SummaryCardItem = withErrorBoundary(
  { i18nKey: "components.robots.RobotSummary.failedShort", small: true },
  _SummaryCardItem
);
