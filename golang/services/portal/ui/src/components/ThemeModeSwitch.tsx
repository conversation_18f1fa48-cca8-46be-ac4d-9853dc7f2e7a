import { useThemeMode } from "portal/theme/ThemeProvider";
import DarkModeIcon from "@mui/icons-material/DarkMode";
import LightModeIcon from "@mui/icons-material/LightMode";
import React, { FC } from "react";
import Switch from "@mui/material/Switch";

export const ThemeModeSwitch: FC = () => {
  const { mode, toggleMode } = useThemeMode();
  return (
    <Switch
      onChange={() => {
        toggleMode();
      }}
      checked={mode === "dark"}
      icon={<LightModeIcon />}
      checkedIcon={<DarkModeIcon />}
    />
  );
};
