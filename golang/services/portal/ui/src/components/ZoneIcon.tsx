import { classes } from "portal/theme/theme";
import { Zone } from "protos/portal/farm";
import BoundaryIcon from "portal/images/icons/fence.svg?react";
import FieldIcon from "portal/images/icons/field.svg?react";
import HeadlandIcon from "portal/images/icons/headland.svg?react";
import ObstacleIcon from "portal/images/icons/obstacle.svg?react";
import React, { FC } from "react";
import RoadIcon from "portal/images/icons/road.svg?react";

export const ZoneIcon: FC<{ zone: Zone; className?: string }> = ({
  zone,
  className,
}) => {
  const contents = zone.contents;
  if (contents?.farmBoundary) {
    return (
      <BoundaryIcon className={classes(className ?? "h-5 w-5 fill-white")} />
    );
  }
  if (contents?.headland) {
    return (
      <HeadlandIcon className={classes(className ?? "h-5 w-5 fill-white")} />
    );
  }
  if (contents?.field) {
    return <FieldIcon className={classes(className ?? "h-5 w-5 fill-white")} />;
  }
  if (contents?.obstacle) {
    return (
      <ObstacleIcon className={classes(className ?? "h-5 w-5 fill-white")} />
    );
  }
  if (contents?.privateRoad) {
    return <RoadIcon className={classes(className ?? "h-5 w-5 fill-white")} />;
  }
};
