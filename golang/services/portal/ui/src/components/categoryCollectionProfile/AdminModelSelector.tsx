import { buildPermission } from "portal/utils/auth";
import {
  Button,
  capitalize,
  Card,
  DialogActions,
  Skeleton,
} from "@mui/material";
import {
  classes,
  RED_LOADING_BUTTON,
  TEXT_FIELD_DARK,
} from "portal/theme/theme";
import { Field, Form, Formik } from "formik";
import { LoadingButton } from "@mui/lab";
import { object, string } from "yup";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { t } from "i18next";
import { TextField } from "formik-mui";
import { titleCase } from "portal/utils/strings";
import {
  useAuthorizationRequired,
  withAuthorizationRequired,
} from "../auth/WithAuthorizationRequired";
import {
  useGetGlobalsQuery,
  useSetGlobalsMutation,
} from "portal/state/portalApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import React, { FC } from "react";

interface AdminModelSelector {
  className?: string;
}
export const _AdminModelSelector: FC<AdminModelSelector> = ({ className }) => {
  const editable = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.portal_globals,
      PermissionDomain.all
    ),
  ]);

  const { data: globalValues, isLoading } = useQueryPopups(
    useGetGlobalsQuery()
  );
  const [saveGlobals] = useMutationPopups(useSetGlobalsMutation(), {
    success: capitalize(
      t("utils.actions.savedLong", {
        subject: titleCase(t("models.globals.global_one")),
      })
    ),
  });

  const initialValues = {
    plantProfileModelId: globalValues?.plantProfileModelId ?? "",
  };
  return (
    <Card className={classes(className, "p-4 pt-6")}>
      {isLoading || !globalValues ? (
        <Skeleton variant="rectangular" height="3rem" />
      ) : (
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validationSchema={object({
            plantProfileModelId: string().required(t("utils.form.required")),
          })}
          onSubmit={async (values) => {
            if (editable) {
              await saveGlobals({
                globals: values,
                updateMask: ["plantProfileModelId"],
              });
            }
          }}
        >
          {({ submitForm, isSubmitting, dirty, resetForm }) => {
            return (
              <Form className="flex justify-between flex-wrap gap-4">
                <Field
                  {...TEXT_FIELD_DARK}
                  component={TextField}
                  name="plantProfileModelId"
                  label={t("models.globals.values.plantProfileModelId.label")}
                  helperText={t(
                    "models.globals.values.plantProfileModelId.description"
                  )}
                  className="w-1/2 min-w-64"
                  InputProps={{
                    className: "font-mono text-xs w-full",
                  }}
                  disabled={!editable}
                />
                {editable && (
                  <DialogActions>
                    <Button
                      variant="text"
                      className="text-white"
                      disabled={isSubmitting}
                      onClick={() => {
                        resetForm();
                      }}
                    >
                      {t("utils.actions.cancel")}
                    </Button>
                    <LoadingButton
                      {...RED_LOADING_BUTTON}
                      disabled={!dirty}
                      loading={isSubmitting}
                      onClick={() => {
                        submitForm();
                      }}
                    >
                      {t("utils.actions.save")}
                    </LoadingButton>
                  </DialogActions>
                )}
              </Form>
            );
          }}
        </Formik>
      )}
    </Card>
  );
};

export const AdminModelSelector = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.portal_globals,
      PermissionDomain.all
    ),
  ],
  _AdminModelSelector
);
