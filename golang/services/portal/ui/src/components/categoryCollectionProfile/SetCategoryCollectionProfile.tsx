import { array, object, string } from "yup";
import { Autocomplete, TextField } from "formik-mui";
import {
  type AutocompleteRenderGroupParams,
  type AutocompleteRenderOptionState,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField as TextFieldMui,
  TextFieldProps,
} from "@mui/material";
import {
  BLUE_LOADING_BUTTON,
  classes,
  TEXT_FIELD_DARK,
} from "portal/theme/theme";
import { buildPermission, getCustomerId } from "portal/utils/auth";
import {
  Category,
  CategoryCollection,
} from "protos/category_profile/category_profile";
import {
  type CategoryOption,
  getCategoryOptionLabel,
  getDefaultCategoryNames,
  hasStrictCategories,
  isCategoryNameOnly,
} from "portal/utils/categoryCollectionProfile";
import { ExpandedCategoryCollectionRequest } from "protos/portal/category_profile";
import { Field, Form, Formik } from "formik";
import { getAutocompleteText } from "portal/utils/forms";
import { GlobalHotKeys } from "react-hotkeys";
import { LoadingButton } from "@mui/lab";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { titleCase } from "portal/utils/strings";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import { v4 as uuid } from "uuid";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import CarbonLogo from "portal/images/logo_icon.svg?react";
import CopyIcon from "@mui/icons-material/ContentCopy";
import DeleteIcon from "@mui/icons-material/CloseOutlined";
import React, { FunctionComponent, useState } from "react";

interface Props {
  triggerButton: (onClick: () => void) => React.ReactNode;
  hotkey?: {
    sequence: string;
    key: string;
  };
  title: string;
  okText: string;
  okIcon?: JSX.Element;
  initialValue: {
    profile: CategoryCollection;
    categories: (Category & { profile?: string })[];
  };
  onSubmit: (value: ExpandedCategoryCollectionRequest) => void;
  areOptionsLoading?: boolean;
  categoryOptions: CategoryOption[];
}
const _SetCategoryCollectionProfile: FunctionComponent<Props> = ({
  triggerButton,
  hotkey,
  title,
  okText,
  okIcon,
  initialValue,
  onSubmit,
  areOptionsLoading = false,
  categoryOptions,
}) => {
  const { user } = useSelf();
  const customerId = getCustomerId(user);
  const customerIdString = customerId ? String(customerId) : undefined;

  const dialogTitle = titleCase(title);
  const { t } = useTranslation();
  const [isOpen, setOpen] = useState<boolean>(false);

  // For M1 we specifically need these 6 specific categories and them to be named in English
  const SHOULD_INTERNATIONALIZE = false;
  const requiredCategories = getDefaultCategoryNames(
    t,
    SHOULD_INTERNATIONALIZE
  );

  return (
    <>
      {hotkey && (
        <GlobalHotKeys
          keyMap={{
            [hotkey.key]: {
              name: dialogTitle,
              action: "keyup",
              sequence: hotkey.sequence,
            },
          }}
          handlers={{
            [hotkey.key]: () => setOpen(true),
          }}
        />
      )}
      {triggerButton(() => {
        setOpen(true);
      })}
      <Dialog
        open={isOpen}
        onClose={() => setOpen(false)}
        PaperProps={{
          className: "min-w-1/3",
        }}
      >
        <DialogTitle>{dialogTitle}</DialogTitle>
        <Formik
          enableReinitialize
          initialValues={{
            name: initialValue.profile.name,
            categories: initialValue.categories,
          }}
          validationSchema={object({
            name: string().required(),
            categories: array()
              .of(
                object().shape({
                  name: string().required(),
                  profile: string().optional(),
                })
              )
              .min(requiredCategories.length)
              .max(requiredCategories.length)
              .required()
              .test(
                "required-base-categories",
                `${t(
                  "models.categoryCollectionProfiles.fields.categories.requiredBaseCategories"
                )}"${requiredCategories.join(", ")}"`,
                (values) => hasStrictCategories(values, requiredCategories)
              ),
          })}
          onSubmit={({ name, categories }) => {
            // overwrite ids passed in to make sure that we enforce COPYING the categories as opposed to directly LINKING them
            const newCategories = categories.map((category) => ({
              ...category,
              id: uuid(),
            }));

            const newProfileData = {
              name,
              categoryIds: newCategories.map(({ id }) => id),
            };
            const newProfile = {
              ...initialValue.profile,
              ...newProfileData,
            };
            return onSubmit({
              profile: newProfile,
              categories: newCategories.map(({ profile, ...category }) =>
                Category.fromPartial(category)
              ),
            });
          }}
        >
          {({
            submitForm,
            isSubmitting,
            dirty,
            values,
            setFieldValue,
            setFieldTouched,
            touched,
            errors,
          }) => (
            <Form>
              <DialogContent className="flex flex-col gap-4 pt-2">
                <Field
                  {...TEXT_FIELD_DARK}
                  component={TextField}
                  name="name"
                  label={t("models.categoryCollectionProfiles.fields.name")}
                />
                <Field
                  name="categories"
                  component={Autocomplete}
                  {...getAutocompleteText(t)}
                  multiple
                  freeSolo
                  loading={areOptionsLoading}
                  options={categoryOptions.sort(
                    (a: CategoryOption, b: CategoryOption) =>
                      a.profile.localeCompare(b.profile)
                  )}
                  getOptionLabel={(option: CategoryOption) =>
                    getCategoryOptionLabel(t, option)
                  }
                  groupBy={(option: CategoryOption) => option.profile}
                  renderGroup={({
                    key,
                    group,
                    children,
                  }: AutocompleteRenderGroupParams) => {
                    const isProtected = categoryOptions.some(
                      (category) =>
                        category.profile === group && category.protected
                    );

                    return (
                      <li key={key}>
                        <div
                          className={classes(
                            "text-white text-sm p-1 pl-2 uppercase flex gap-2 items-center",
                            {
                              "bg-gray-600": !isProtected,
                              "bg-carbon-orange": isProtected,
                            }
                          )}
                        >
                          <div>{group}</div>
                          {isProtected && <CarbonLogo className="h-3" />}
                        </div>
                        <ul className="p-0 list-none text-base">{children}</ul>
                      </li>
                    );
                  }}
                  renderOption={(
                    props: AutocompleteRenderOptionState,
                    option: CategoryOption
                  ) => {
                    const isSelected = values.categories.some(
                      (cat) =>
                        cat.name === option.name &&
                        cat.profile === option.profile
                    );

                    return (
                      <li
                        {...props}
                        key={option.id}
                        className={classes(
                          "cursor-pointer p-2 pl-4 hover:bg-gray-600",
                          {
                            "text-gray-400 hover:text-gray-200": isSelected,
                          }
                        )}
                      >
                        {getCategoryOptionLabel(t, option)}
                      </li>
                    );
                  }}
                  value={values.categories}
                  isOptionEqualToValue={(
                    option: CategoryOption,
                    value: CategoryOption
                  ) => option.id === value.id}
                  renderTags={(
                    tagValue: CategoryOption[],
                    getTagProps: any
                  ) => {
                    return tagValue.map((option, index) => (
                      <Chip
                        {...getTagProps({ index })}
                        key={option.id}
                        label={option.name}
                        size="small"
                        icon={
                          option.profile ? (
                            <CopyIcon className="text-sm text-white" />
                          ) : undefined
                        }
                        onDelete={() => {
                          setFieldValue(
                            "categories",
                            values.categories.filter(
                              (value) => value.id !== option.id
                            )
                          );
                        }}
                      />
                    ));
                  }}
                  onChange={(
                    _: React.SyntheticEvent<Element, Event>,
                    value: Array<CategoryOption | string>
                  ) => {
                    const newCategories = value.map((category) =>
                      isCategoryNameOnly(category)
                        ? Category.fromPartial({
                            id: uuid(),
                            name: category,
                            customerId: customerIdString,
                            protected: initialValue.profile.protected,
                          })
                        : category
                    );
                    setFieldValue("categories", newCategories);
                    setFieldTouched("categories", true);
                  }}
                  renderInput={(parameters: TextFieldProps) => (
                    <TextFieldMui
                      {...TEXT_FIELD_DARK}
                      {...parameters}
                      error={touched.categories && Boolean(errors.categories)}
                      helperText={
                        errors.categories
                          ? JSON.stringify(errors.categories)
                          : undefined
                      }
                      label={t(
                        "models.categoryCollectionProfiles.fields.categories.name"
                      )}
                    />
                  )}
                />
                <div className="flex flex-col">
                  {values.categories.map((category) => {
                    const chipText =
                      category.chipIds.length > 0
                        ? ` (${category.chipIds.length} ${titleCase(
                            t("models.images.image", {
                              count: category.chipIds.length,
                            })
                          )})`
                        : "";

                    return (
                      <div key={category.id} className="flex items-center">
                        <p className="text-sm m-0">
                          <span>{category.name}</span>
                          {!category.profile && (
                            <span className="text-xs">{chipText}</span>
                          )}
                          {category.profile && (
                            <>
                              <span className="pl-2 text-gray-300">
                                <CopyIcon className="text-sm" />{" "}
                                {category.profile}
                              </span>
                              <span className="text-xs text-gray-300">
                                {chipText}
                              </span>
                            </>
                          )}
                        </p>
                        <IconButton
                          className="text-white"
                          size="small"
                          onClick={() => {
                            setFieldValue(
                              "categories",
                              values.categories.filter(
                                (value) => value.id !== category.id
                              )
                            );
                          }}
                        >
                          <DeleteIcon className="text-xs" />
                        </IconButton>
                      </div>
                    );
                  })}
                </div>
              </DialogContent>
              <DialogActions>
                <Button
                  variant="text"
                  className="text-white"
                  disabled={isSubmitting}
                  onClick={() => setOpen(false)}
                >
                  {t("utils.actions.cancel")}
                </Button>
                <LoadingButton
                  {...BLUE_LOADING_BUTTON}
                  disabled={!dirty}
                  loading={isSubmitting}
                  onClick={submitForm}
                  startIcon={okIcon}
                >
                  {okText}
                </LoadingButton>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
    </>
  );
};

export const SetCategoryCollectionProfile = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.update,
      PermissionResource.plant_category_profiles,
      PermissionDomain.self
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.plant_category_profiles,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.plant_category_profiles,
      PermissionDomain.all
    ),
  ],
  _SetCategoryCollectionProfile
);
