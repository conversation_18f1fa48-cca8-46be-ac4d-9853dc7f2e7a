import {
  BLUE_LOADING_BUTTON,
  classes,
  YELLOW_LOADING_BUTTON,
} from "portal/theme/theme";
import { Button, ButtonGroup, SvgIcon, Tooltip } from "@mui/material";
import { LoadingButton } from "@mui/lab";
import { titleCase } from "portal/utils/strings";
import { useTranslation } from "react-i18next";
import CancelIcon from "@mui/icons-material/Close";
import ModelIcon from "portal/images/icons/model.svg?react";
import React, { FC } from "react";

interface PreviewResultsButtonProps {
  onClick: () => void;
  canCancel?: boolean;
  onCancel: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  error?: boolean;
}
export const PreviewResultsButton: FC<PreviewResultsButtonProps> = ({
  onClick,
  canCancel,
  onCancel,
  isLoading,
  disabled,
  error,
}) => {
  const { t } = useTranslation();

  const buttonStyles = {
    ...(error ? YELLOW_LOADING_BUTTON : BLUE_LOADING_BUTTON),
  };
  buttonStyles.className = classes(buttonStyles.className ?? "", "h-8");
  return (
    <div className="flex flex-col">
      <ButtonGroup>
        <div>
          <LoadingButton
            {...buttonStyles}
            onClick={onClick}
            loading={isLoading}
            disabled={disabled}
            loadingPosition="start"
            loadingIndicator={<ModelSvg className="animate-spin" />}
            startIcon={<ModelSvg />}
          >
            {titleCase(
              t("components.categoryCollectionProfile.actions.testResults")
            )}
          </LoadingButton>
        </div>
        {canCancel && (
          <Tooltip
            title={t(
              isLoading ? "utils.actions.cancel" : "utils.actions.clear"
            )}
          >
            <div>
              <Button size="small" {...buttonStyles} onClick={onCancel}>
                <CancelIcon fontSize="small" />
              </Button>
            </div>
          </Tooltip>
        )}
      </ButtonGroup>
    </div>
  );
};

const ModelSvg: FC<{ className?: string }> = ({ className }) => (
  <SvgIcon className={classes("w-4 h-4", className)}>
    <ModelIcon />
  </SvgIcon>
);
