import {
  Autocomplete,
  AutocompleteProps,
  Chip,
  InputAdornment,
  TextField,
  TextFieldProps,
} from "@mui/material";
import { Entity } from "portal/components/filters/filter.types";
import { getAutocompleteText } from "portal/utils/forms";
import {
  SMALL_AUTOCOMPLETE_DARK,
  SMALL_TEXT_FIELD_DARK,
} from "portal/theme/theme";
import { useTranslation } from "react-i18next";
import React, { FunctionComponent } from "react";
import WarningIcon from "@mui/icons-material/WarningAmber";

type FilterProps = Pick<
  AutocompleteProps<Entity, true, true, false>,
  "loading" | "options" | "value"
> & {
  onChange: (value: Entity[]) => void;
  loadingError?: boolean;
  label: TextFieldProps["label"];
};

export const AutocompleteFilter: FunctionComponent<FilterProps> = ({
  loading,
  options,
  value,
  label,
  onChange,
  loadingError = false,
}) => {
  const { t } = useTranslation();
  return (
    <Autocomplete<Entity, true, true, false>
      {...getAutocompleteText(t)}
      {...SMALL_AUTOCOMPLETE_DARK}
      multiple
      loading={loading}
      options={options}
      value={value}
      getOptionLabel={(option) => option.name}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      limitTags={3}
      renderTags={(tagValue, getTagProps) => {
        return tagValue.map((option, index) => (
          <Chip
            {...getTagProps({ index })}
            key={option.id}
            label={option.name}
            size="small"
            classes={{
              root: "bg-white bg-opacity-80 text-gray-800",
              deleteIcon: "text-gray-800",
            }}
          />
        ));
      }}
      onChange={(_: React.SyntheticEvent<Element, Event>, value) => {
        onChange(value);
      }}
      renderInput={(parameters: TextFieldProps) => (
        <TextField
          {...parameters}
          size="small"
          className="min-w-32 md:min-w-48"
          label={label}
          InputProps={{
            ...parameters.InputProps,
            ...SMALL_TEXT_FIELD_DARK.InputProps,
            endAdornment: loadingError ? (
              <InputAdornment
                position="end"
                className="text-yellow-500 text-sm"
              >
                <WarningIcon />
              </InputAdornment>
            ) : undefined,
          }}
        />
      )}
    />
  );
};
