import { AutocompleteFilter } from "./AutocompleteFilter";
import { Entity } from "../../filters/filter.types";
import { PredictionPointFilter } from "./PredictionPointFilters";
import { titleCase } from "portal/utils/strings";
import { useTranslation } from "react-i18next";
import React, { FunctionComponent } from "react";

export type SessionFilters = Pick<PredictionPointFilter, "categoryIds">;

interface SessionFilterProps {
  categoryOptions: Entity[];
  filters: SessionFilters;
  onChange: (filters: SessionFilters) => void;
}

export const SessionFilters: FunctionComponent<SessionFilterProps> = ({
  categoryOptions,
  filters,
  onChange,
}) => {
  const { t } = useTranslation();
  return (
    <AutocompleteFilter
      label={titleCase(
        t("models.categoryCollectionProfiles.fields.categories.name")
      )}
      options={categoryOptions}
      value={filters.categoryIds}
      onChange={(value) => onChange({ categoryIds: value })}
    />
  );
};
