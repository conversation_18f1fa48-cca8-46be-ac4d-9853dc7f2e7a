import { Chip, Tooltip } from "@mui/material";
import { classes, theme } from "portal/theme/theme";
import { ReactComponent } from "react-hotkeys";
import React, { FunctionComponent } from "react";

interface ChipOverlayProps {
  icon?: ReactComponent;
  name: string;
  color?: {
    bg: string;
    text?: string;
  };
}
export const ChipOverlay: FunctionComponent<ChipOverlayProps> = ({
  name,
  color = {
    bg: theme.colors.carbon.orange,
    text: theme.colors.white,
  },
  icon,
}) => {
  return (
    <div
      className={classes(
        "absolute inset-0 bg-transparent pointer-events-none overflow-hidden"
      )}
    >
      <div>
        <Tooltip title={name}>
          <Chip
            icon={icon}
            label={name}
            className="font-normal p-1 m-1"
            style={{ color: color.text, backgroundColor: color.bg }}
            size="small"
          />
        </Tooltip>
      </div>
    </div>
  );
};
