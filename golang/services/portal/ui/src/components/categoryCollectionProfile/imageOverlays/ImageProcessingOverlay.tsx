import {
  assembleImageRequestParameters,
  ThumbnailImageItem,
} from "portal/components/images/ThumbnailImage";
import { useGetCropThumbnailQuery } from "portal/state/imageServiceApi";
import React, { FC } from "react";

interface ImageProcessingOverlayProps {
  image: ThumbnailImageItem;
}
export const ImageProcessingOverlay: FC<ImageProcessingOverlayProps> = ({
  image,
}) => {
  const { data: src } = useGetCropThumbnailQuery(
    assembleImageRequestParameters(image)
  );
  return <InvertedAnimatedImage src={src} />;
};

const InvertedAnimatedImage: FC<{ src?: string }> = ({ src }) => (
  <img
    alt="processing"
    src={src}
    className="absolute inset-0 w-full h-full object-cover invert animate-mask-sweep"
    style={{
      maskImage:
        "linear-gradient(to right, transparent 0%, black 70%, transparent 75%, transparent 100%)",
      maskSize: "200% 100%",
      willChange: "mask-position", // helps with performance
    }}
  />
);
