import { But<PERSON>, IconButton } from "@mui/material";
import { classes } from "portal/theme/theme";
import { CopyToClipboardButton } from "portal/components/CopyToClipboardButton";
import { PredictionPoint } from "protos/veselka/prediction_point";
import { userFacingDate } from "portal/utils/categoryCollectionProfile";
import { useTranslation } from "react-i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import InfoIcon from "@mui/icons-material/Info";
import React, { FC, useState } from "react";

interface MetadataOverlayProps {
  dimensions: {
    width: number;
    height: number;
  };
  data: PredictionPoint;
}

const TINY_CUTOFF = 75;

export const MetadataOverlay: FC<MetadataOverlayProps> = ({
  dimensions,
  data,
}) => {
  const { t, i18n } = useTranslation();
  const [showInfo, setShowInfo] = useState(false);

  const { id, x, y, radius, image, categoryId } = data;
  const capturedAt = image?.capturedAt
    ? userFacingDate(i18n, image.capturedAt)
    : undefined;
  const info = [
    {
      label: t("models.categoryCollectionProfiles.metadata.capturedAt"),
      value: capturedAt,
    },
    {
      label: t("models.categoryCollectionProfiles.metadata.pointId"),
      value: id,
    },
    {
      label: t("models.categoryCollectionProfiles.metadata.imageId"),
      value: image?.id,
    },
    { label: t("models.categoryCollectionProfiles.metadata.x"), value: x },
    { label: t("models.categoryCollectionProfiles.metadata.y"), value: y },
    {
      label: t("models.categoryCollectionProfiles.metadata.radius"),
      value: radius,
    },
    {
      label: t("models.categoryCollectionProfiles.metadata.ppcm"),
      value: image?.ppcm,
    },
  ];
  if (categoryId) {
    info.push({
      label: t("models.categoryCollectionProfiles.metadata.categoryId"),
      value: categoryId,
    });
  }
  return (
    <div
      className="absolute bottom-0 right-0 bg-transparent pointer-events-none"
      style={{ ...dimensions }}
    >
      {showInfo ? (
        <div className="bg-slate-900 text-white flex-col transition duration-200 ease-in overflow-auto h-full pointer-events-auto">
          <div className="flex justify-between">
            <IconButton
              className={classes("text-xs", {
                "p-0": dimensions.width < TINY_CUTOFF,
              })}
              size="small"
              onClick={() => {
                setShowInfo(false);
              }}
            >
              <ArrowBackIcon className="text-white" />
            </IconButton>
            <CopyToClipboardButton text={JSON.stringify(data)} />
          </div>
          <div className="text-[8px] md:text-[10px] lg:text-xs px-1">
            {info.map(({ label, value }) => (
              <Metadata key={label} label={label} value={value} />
            ))}
          </div>
        </div>
      ) : (
        <Button
          component="div"
          className="absolute p-1 min-w-6 pointer-events-auto"
          onClick={() => {
            setShowInfo(true);
          }}
        >
          <InfoIcon
            className={classes("hover:text-white", {
              "text-transparent": !showInfo,
              "text-white": showInfo,
            })}
          />
        </Button>
      )}
    </div>
  );
};

interface MetadataProps {
  label: string;
  value?: string | number;
}
const Metadata: FC<MetadataProps> = ({ label, value }) => (
  <p className="m-0 p-0">
    <span className="pr-2">{label}</span>
    <span className="font-mono text-carbon-yellow">{value}</span>
  </p>
);
