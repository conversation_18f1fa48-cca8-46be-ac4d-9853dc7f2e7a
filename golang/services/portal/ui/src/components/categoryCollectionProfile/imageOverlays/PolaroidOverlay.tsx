import { classes, theme } from "portal/theme/theme";
import { formatRadiusToDiameter } from "portal/utils/categoryCollectionProfile";
import { titleCase } from "portal/utils/strings";
import { Tooltip } from "@mui/material";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import React, { FunctionComponent, ReactNode } from "react";

interface PolaroidOverlayProps {
  name: string;
  dimensions: {
    width: number;
    height: number;
  };
  color?: {
    bg: string;
    text?: string;
  };
  radiusCm?: number;
  position?: "top" | "bottom";
  icon?: ReactNode;
}
export const PolaroidOverlay: FunctionComponent<PolaroidOverlayProps> = ({
  name,
  dimensions,
  color = {
    bg: theme.colors.carbon.orange,
    text: theme.colors.white,
  },
  radiusCm,
  position = "bottom",
  icon,
}) => {
  const { t, i18n } = useTranslation();
  const { measurementSystem } = useSelf();

  const formattedDiameter = radiusCm
    ? formatRadiusToDiameter(t, i18n, measurementSystem, radiusCm)
    : undefined;
  const textClasses = "text-xs m-0 pointer-events-auto";
  return (
    <div
      className={classes(
        "absolute top-0 left-0 bg-transparent border-solid border-[6px] pointer-events-none",
        {
          "border-b-0": position === "bottom",
          "border-t-0": position === "top",
        }
      )}
      style={{ ...dimensions, borderColor: color.bg }}
    >
      <div
        className={classes("flex flex-col h-full truncate", {
          "justify-end": position === "bottom",
          "justify-start": position === "top",
        })}
      >
        <Tooltip
          title={`${titleCase(name)}${
            formattedDiameter ? ` - ${formattedDiameter.toString()}` : ""
          }`}
        >
          <div
            className="flex justify-between w-full gap-1"
            style={{ backgroundColor: color.bg, color: color.text }}
          >
            <div className="flex gap-1 items-center">
              {icon}
              <p className={textClasses}>{name.toLocaleUpperCase()}</p>
            </div>
            {formattedDiameter && (
              <p
                className={classes(
                  textClasses,
                  dimensions.width < 100 ? "invisible" : "visible"
                )}
              >
                {formattedDiameter.toString()}
              </p>
            )}
          </div>
        </Tooltip>
      </div>
    </div>
  );
};
