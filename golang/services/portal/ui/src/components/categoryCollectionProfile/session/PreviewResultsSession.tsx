import { Breakpoint, useBreakpoint } from "portal/utils/hooks/useBreakpoint";
import { classes } from "portal/theme/theme";
import { Entity } from "portal/components/filters/filter.types";
import { isNil } from "common/utils/identity";
import { LinearProgress, Typography } from "@mui/material";
import { PreviewResultsButton } from "../TestResultsButton";
import { SessionFilters } from "../filters/SessionFilters";
import { useTranslation } from "react-i18next";
import React, { FC } from "react";

interface PreviewResultsSessionProps {
  onStart: () => void;
  onCancel: () => void;
  sessionId?: string;
  loading: boolean;
  disabled: boolean;
  progress?: {
    error: boolean;
    processed: number;
    total: number;
  };
  sessionFilters: SessionFilters;
  onSessionFiltersChange: (filters: SessionFilters) => void;
  categoryOptions: Entity[];
}
export const PreviewResultsSession: FC<PreviewResultsSessionProps> = ({
  onStart,
  onCancel,
  sessionId,
  loading,
  disabled,
  progress,
  sessionFilters,
  onSessionFiltersChange,
  categoryOptions,
}) => {
  const { t } = useTranslation();
  const breakpoint = useBreakpoint();
  const isSmall = breakpoint <= Breakpoint.sm;

  const total = progress?.total ?? 0;
  const progressPercent = progress ? progress.processed / total : 0;
  const isReady = Boolean(progress && progress.processed >= total);
  const showProgress = !isNil(progress) && progress.processed < total;
  let progressMessage = showProgress
    ? t(
        isSmall
          ? "components.categoryCollectionProfile.session.status"
          : "components.categoryCollectionProfile.session.statusLong",
        {
          processed: progress.processed,
          total,
        }
      )
    : undefined;
  if (progress?.error) {
    progressMessage = t("components.categoryCollectionProfile.session.error");
  }
  return (
    <div className="border-solid border-carbon-blue w-full">
      <div className="w-full grid grid-cols-2 items-center p-2">
        <div className="justify-self-start">
          {sessionId && (
            <SessionFilters
              filters={sessionFilters}
              onChange={onSessionFiltersChange}
              categoryOptions={categoryOptions}
            />
          )}
        </div>
        <div className="justify-self-end flex items-center gap-4">
          {showProgress && (
            <Typography className="text-gray-300 text-xs">
              {progressMessage}
            </Typography>
          )}
          <PreviewResultsButton
            onClick={onStart}
            onCancel={onCancel}
            isLoading={loading}
            disabled={disabled}
            error={progress?.error}
            canCancel={loading || isReady}
          />
        </div>
      </div>
      {showProgress && (
        <LinearProgress
          variant="determinate"
          className={classes("bg-transparent text-carbon-blue w-full", {
            "text-carbon-yellow": progress.error,
          })}
          color="inherit"
          value={progressPercent * 100}
        />
      )}
    </div>
  );
};
