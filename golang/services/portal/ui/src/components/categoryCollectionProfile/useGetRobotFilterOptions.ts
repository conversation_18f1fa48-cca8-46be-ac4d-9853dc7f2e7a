import { Entity } from "../filters/filter.types";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import { getCustomerSerial } from "portal/utils/robots";
import { isNil } from "portal/utils/identity";
import { SerializedError } from "@reduxjs/toolkit";
import { useListRobotsQuery } from "portal/state/portalApi";
import { useMemo } from "react";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";

export const useGetRobotFilterOptions = (
  customerId?: number
): {
  robots?: Entity[];
  isLoading: boolean;
  error: FetchBaseQueryError | SerializedError | undefined;
} => {
  const { t } = useTranslation();
  const { isInternal } = useSelf();
  const filterByCustomerId = useMemo(
    () => (isNil(customerId) ? undefined : customerId),
    [customerId]
  );

  // robots
  const {
    data: robotSummaries,
    isLoading: isRobotsLoading,
    error: robotsError,
  } = useQueryPopups(useListRobotsQuery({ latestMetrics: false }), {
    errorVariant: "warning",
  });

  const robots = useMemo<Entity[] | undefined>(() => {
    if (!robotSummaries) {
      return;
    }
    const allRobots = [];
    for (const robotSummary of robotSummaries) {
      const { robot } = robotSummary;
      if (!robot) {
        continue;
      }
      const { serial } = robot;

      if (
        filterByCustomerId &&
        (!robotSummary.customer?.db ||
          filterByCustomerId !== robotSummary.customer.db.id)
      ) {
        continue;
      }

      allRobots.push({
        id: serial,
        name: isInternal ? serial : getCustomerSerial(t, serial),
      });
    }
    return allRobots;
  }, [robotSummaries, filterByCustomerId, isInternal, t]);

  return {
    robots,
    isLoading: isRobotsLoading,
    error: robotsError,
  };
};
