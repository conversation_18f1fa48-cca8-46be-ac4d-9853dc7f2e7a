import { addNotification } from "portal/state/notifications";
import { capitalize } from "@mui/material";
import { ExpandedCategoryCollectionRequest } from "protos/portal/category_profile";
import {
  GetPredictionPointSessionResponse,
  SessionStatus,
} from "protos/veselka/prediction_point";
import { isEqual } from "portal/utils/equality";
import { isNil } from "portal/utils/identity";
import { skipToken } from "@reduxjs/toolkit/query";
import { t } from "i18next";
import { useCallback, useEffect, useState } from "react";
import {
  useCreateCategoryCollectionSessionMutation,
  useGetCategoryCollectionSessionQuery,
  useGetGlobalsQuery,
  useGetRobotQuery,
} from "portal/state/portalApi";
import { useDispatch } from "react-redux";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";

interface Session {
  id?: string;
  status?: SessionStatus;
  error: boolean;
  stale: boolean;
}
const INITIAL_STATE = {
  id: undefined,
  status: undefined,
  error: false,
  stale: false,
};

const SESSION_POLL_INTERVAL_MS = 1000;

export const usePreviewResults = (
  serial?: string,
  categoryCollectionProfile?: ExpandedCategoryCollectionRequest,
  onStatusChange?: (sessionId: string, status?: SessionStatus) => void
): {
  testResults: () => void;
  reset: () => void;
  canStartSession: boolean;
  isLoading: boolean;
  session?: Session;
} => {
  const dispatch = useDispatch();
  const [session, setSession] = useState<Session>(INITIAL_STATE);
  const [lastRunProfile, setLastRunProfile] =
    useState<ExpandedCategoryCollectionRequest>();
  const [prevSessionStatus, setPrevSessionStatus] = useState<
    | {
        response: GetPredictionPointSessionResponse | undefined;
        error: boolean;
      }
    | undefined
  >(undefined);
  const [prevSessionDone, setPrevSessionDone] = useState(false);

  const resetSession = useCallback(() => {
    setPrevSessionStatus(undefined);
    setPrevSessionDone(false);
    setLastRunProfile(undefined);
    setSession(INITIAL_STATE);
  }, []);

  // get the necessary parameters to initialize a session
  const { data: summary } = useQueryPopups(
    useGetRobotQuery(serial ? { serial } : skipToken),
    { errorVariant: "error" }
  );
  const customerId = summary?.customer?.db?.id;
  const { data: globalValues } = useQueryPopups(useGetGlobalsQuery());
  const modelId = globalValues?.plantProfileModelId;
  const canStartSession =
    (!serial || (serial && !isNil(customerId))) &&
    !isNil(modelId) &&
    categoryCollectionProfile?.categories.some((c) => c.chipIds.length);

  const [createSession, { isLoading: isCreateSessionLoading }] =
    useMutationPopups(useCreateCategoryCollectionSessionMutation(), {
      success: capitalize(
        t("utils.actions.createdLong", {
          subject: t(
            "components.categoryCollectionProfile.session.session_one"
          ),
        })
      ),
    });

  const testResults = useCallback(async () => {
    resetSession();
    if (!canStartSession) {
      return;
    }
    const result = await createSession({
      modelId,
      customerId,
      categoryCollectionProfile,
    });
    setSession({ id: result.data?.sessionId, error: false, stale: false });
    setLastRunProfile(categoryCollectionProfile);
  }, [
    resetSession,
    canStartSession,
    createSession,
    modelId,
    customerId,
    categoryCollectionProfile,
  ]);

  const isSessionDone = Boolean(
    session.id &&
      session.status &&
      session.status.countOfResults >= session.status.expectedCount
  );

  if (prevSessionDone !== isSessionDone) {
    setPrevSessionDone(isSessionDone);
    if (isSessionDone && session.id) {
      dispatch(
        addNotification({
          message: t("components.categoryCollectionProfile.session.ready"),
          variant: "success",
        })
      );
    }
  }

  const shouldPoll = session.id && (!session.status || !isSessionDone);
  const {
    data: sessionStatus,
    isLoading: isSessionStatusLoading,
    error: sessionPollingError,
  } = useGetCategoryCollectionSessionQuery(
    shouldPoll && session.id ? { sessionId: session.id } : skipToken,
    { pollingInterval: SESSION_POLL_INTERVAL_MS }
  );

  const currentSessionState = {
    response: sessionStatus,
    error: Boolean(sessionPollingError),
  };
  if (shouldPoll && !isEqual(prevSessionStatus, currentSessionState)) {
    setPrevSessionStatus(currentSessionState);
    const status = sessionStatus?.sessionStatus;
    if (session.id) {
      onStatusChange?.(session.id, status);
    }
    setSession((prev) => ({
      ...prev,
      status,
      error: Boolean(sessionPollingError),
    }));
  }

  useEffect(() => {
    setSession((prev) => ({
      ...prev,
      stale: !isEqual(categoryCollectionProfile, lastRunProfile),
    }));
  }, [categoryCollectionProfile, lastRunProfile, resetSession]);

  return {
    testResults,
    reset: resetSession,
    canStartSession: Boolean(canStartSession),
    isLoading: Boolean(
      isCreateSessionLoading ||
        isSessionStatusLoading ||
        (session.id && !isSessionDone)
    ),
    session,
  };
};
