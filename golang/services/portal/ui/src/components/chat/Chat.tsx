import { APIErrorResponse, StreamChat } from "stream-chat";
import { buildPermission, CarbonUser } from "portal/utils/auth";
import { CarbonMessage } from "./CarbonMessage";
import {
  Channel,
  MessageInput,
  ReactionOptions,
  Chat as StreamWrapper,
  Thread,
  UnreadMessagesNotification,
  UnreadMessagesSeparator,
  VirtualizedMessageList,
  Window,
} from "stream-chat-react";
import { classes } from "portal/theme/theme";
import { getCustomerSerial } from "portal/utils/robots";
import { IDMToken } from "portal/state/idmApi";
import { isObject } from "portal/utils/objects";
import { Loading } from "../Loading";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { portalLanguageOrDefault } from "portal/i18nConstants";
import { sleepMs } from "portal/utils/sleepMs";
import { toStreamLanguage } from "portal/utils/chats";
import { useTranslation } from "react-i18next";
import { WaitGroup } from "portal/utils/waitGroup";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import { withErrorBoundary } from "../ErrorBoundary";
import React, {
  FunctionComponent,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

const CARBON_REACTIONS: ReactionOptions = [
  {
    Component: () => <>✅</>,
    name: "Green Check Mark",
    type: "white_check_mark",
  },
  {
    Component: () => <>❌</>,
    name: "Cross",
    type: "x",
  },
  {
    Component: () => <>👀</>,
    name: "Eyeballs",
    type: "eyes",
  },
  {
    Component: () => <>🤔</>,
    name: "Thinker",
    type: "thinking_face",
  },
  {
    Component: () => <>❤️</>,
    name: "Love Heart",
    type: "heart",
  },
];

interface Props {
  serial: string;
  className?: string;
  user: CarbonUser;
  token: IDMToken;
}

const _Chat: FunctionComponent<Props> = ({
  serial,
  className,
  user,
  token,
}) => {
  const { t, i18n } = useTranslation();
  const language = portalLanguageOrDefault(i18n.language);

  const waitGroupRef = useRef<WaitGroup | undefined>(undefined);
  if (!waitGroupRef.current) {
    waitGroupRef.current = new WaitGroup();
  }
  const waitGroup: WaitGroup = waitGroupRef.current;

  const [streamClient, setStreamClient] = useState<StreamChat | undefined>();
  const [streamError, setStreamError] = useState<string | undefined>();

  useEffect(() => {
    let streamClient: StreamChat;
    const ac = new AbortController();
    (async () => {
      setStreamError(undefined);
      try {
        streamClient = new StreamChat(window._jsenv.REACT_APP_STREAM_API_KEY);
        await streamClient.connectUser(
          {
            id: token.userId,
            name: user.name,
            language: toStreamLanguage(language),
          },
          token.token
        );
      } catch (error: unknown) {
        let message: string;
        const streamError = asStreamApiError(error);
        if (streamError) {
          // Quote this, because sometimes it's an empty string >_>
          const status = JSON.stringify(streamError.StatusCode);
          message = `${streamError.message} [status ${status}]`;
        } else {
          message = String(error);
        }
        console.error("Chat: error creating Stream client:", message);
        setStreamError(t("components.Chat.errors.failed", { message }));
        return;
      }
      if (ac.signal.aborted) {
        console.debug("Chat: aborted; not setting client");
        return;
      }
      console.debug("Chat: setting client", streamClient);
      setStreamClient(streamClient);
      setStreamError(undefined);
    })();

    // disconnect websocket on unmount
    return () => {
      ac.abort();
      (async () => {
        // We try to clean up our own tasks, but `stream-chat-react`'s
        // `Channel` misses a few. Wait a while before disconnecting to help
        // them succeed.
        await sleepMs(5 * 1000);
        // Now, wait for our own tasks; this is a background cleanup, so we
        // can afford to wait a while.
        await waitGroup.wait(10 * 1000);
        console.debug("Chat: disconnecting from client", streamClient);
        streamClient.disconnectUser();
      })();
    };
  }, [language, t, token, user, waitGroup /* constant */]);

  const channel = useMemo(() => {
    if (!streamClient?.user) {
      return;
    }
    return streamClient.channel("team", serial, {
      name: getCustomerSerial(t, serial),
    });
  }, [serial, t, streamClient]);

  useEffect(() => {
    if (channel) {
      waitGroup.add(channel.watch());
      return () => {
        if (!channel.disconnected) {
          waitGroup.add(channel.stopWatching());
        }
      };
    }
  }, [channel, waitGroup /* constant */]);

  if (!streamClient || streamClient.wsConnection?.isConnecting) {
    return <Loading failed={Boolean(streamError)} error={streamError} />;
  }

  return (
    <div
      className={classes(
        "flex basis-0 flex-grow flex-shrink flex-col w-full overflow-y-auto",
        className
      )}
    >
      <StreamWrapper client={streamClient}>
        <Channel
          channel={channel}
          reactionOptions={CARBON_REACTIONS}
          UnreadMessagesNotification={UnreadMessagesNotification}
          UnreadMessagesSeparator={UnreadMessagesSeparator}
        >
          <Window>
            <VirtualizedMessageList Message={CarbonMessage} shouldGroupByUser />
            <MessageInput grow disableMentions />
          </Window>
          <div className="mt-10">
            <Thread Message={CarbonMessage} />
          </div>
        </Channel>
      </StreamWrapper>
    </div>
  );
};

const asStreamApiError = (error: unknown): APIErrorResponse | undefined => {
  if (isObject(error) && "StatusCode" in error) {
    return error as APIErrorResponse;
  }
  if (error instanceof Error) {
    // Sometimes we get `new Error('{"StatusCode":"...",...}')`.
    let parsed: unknown;
    try {
      parsed = JSON.parse(error.message);
    } catch {
      return;
    }
    // Recursion should be bounded because we can't hit this case again, since
    // JSON-parsing will never return an `Error`.
    return asStreamApiError(parsed);
  }
};

export const Chat = withErrorBoundary(
  {},
  withAuthorizationRequired(
    [
      buildPermission(
        PermissionAction.read,
        PermissionResource.chat,
        PermissionDomain.customer
      ),
      buildPermission(
        PermissionAction.read,
        PermissionResource.chat,
        PermissionDomain.all
      ),
    ],
    _Chat
  )
);
