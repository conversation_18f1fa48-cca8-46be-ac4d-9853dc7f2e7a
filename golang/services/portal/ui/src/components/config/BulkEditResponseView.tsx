import * as Cmp from "portal/utils/comparators";
import {
  BulkEditResponse,
  EditRecord,
  Outcome,
  RobotEditRecord,
} from "protos/robot_syncer/bulk";
import {
  ButtonBase,
  IconButton,
  SvgIcon,
  SvgIconProps,
  Typography,
} from "@mui/material";
import { classes } from "portal/theme/theme";
import { ConfigValueView } from "./ConfigValueView";
import { robotSort } from "portal/utils/robots";
import { useTranslation } from "react-i18next";
import ChangeIcon from "@mui/icons-material/ArrowForward";
import CloseIcon from "@mui/icons-material/Close";
import CollapsedIcon from "@mui/icons-material/KeyboardArrowRight";
import ExpandedIcon from "@mui/icons-material/KeyboardArrowDown";
import FailureIcon from "@mui/icons-material/CancelOutlined";
import MixedOutcomeSvg from "portal/images/mixed_outcome.svg?react";
import React, {
  ComponentType,
  FunctionComponent,
  ReactNode,
  useMemo,
  useState,
} from "react";
import SuccessIcon from "@mui/icons-material/CheckCircleOutline";

interface Props {
  response: BulkEditResponse;
  onClose?: () => void;
}

enum AggregateOutcome {
  SUCCESS = "success",
  FAILURE = "failure",
  PARTIAL = "partial",
}

export const BulkEditResponseView: FunctionComponent<Props> = ({
  response,
  onClose,
}) => {
  const { t } = useTranslation();

  const robotsByOutcome = useMemo<Record<AggregateOutcome, RobotEditRecord[]>>(
    () => aggregateRobots(response.robots),
    [response]
  );

  const header = (() => {
    const hasSuccess = robotsByOutcome[AggregateOutcome.SUCCESS].length > 0;
    const hasFailure = robotsByOutcome[AggregateOutcome.FAILURE].length > 0;
    const hasPartial = robotsByOutcome[AggregateOutcome.PARTIAL].length > 0;

    let headerContents;
    if (hasPartial || (hasSuccess && hasFailure)) {
      headerContents = (
        <>
          <OutcomeIcon outcome={AggregateOutcome.PARTIAL} fontSize="inherit" />
          <span>{t("views.admin.config.bulk.outcomes.partial")}</span>
        </>
      );
    } else if (hasFailure) {
      headerContents = (
        <>
          <OutcomeIcon outcome={AggregateOutcome.FAILURE} fontSize="inherit" />
          <span>{t("views.admin.config.bulk.outcomes.failure")}</span>
        </>
      );
    } else if (hasSuccess) {
      headerContents = (
        <>
          <OutcomeIcon outcome={AggregateOutcome.SUCCESS} fontSize="inherit" />
          <span>{t("views.admin.config.bulk.outcomes.success")}</span>
        </>
      );
    }
    return (
      <div className="flex items-start">
        <h2 className="flex-1 flex items-center gap-2 mt-0">
          {headerContents}
        </h2>
        {onClose && (
          <IconButton onClick={onClose}>
            <CloseIcon className="text-gray-400" />
          </IconButton>
        )}
      </div>
    );
  })();

  const section = (outcome: AggregateOutcome): ReactNode => {
    const robots = robotsByOutcome[outcome];
    if (robots.length === 0) {
      return;
    }
    return robots.map((robot) => (
      <RobotEditRecordView key={robot.serial} outcome={outcome} robot={robot} />
    ));
  };

  return (
    <div>
      {header}
      <div className="flex flex-col gap-1">
        {section(AggregateOutcome.SUCCESS)}
        {section(AggregateOutcome.PARTIAL)}
        {section(AggregateOutcome.FAILURE)}
      </div>
    </div>
  );
};

interface RobotEditRecordProps {
  outcome: AggregateOutcome;
  robot: RobotEditRecord;
}

const RobotEditRecordView: FunctionComponent<RobotEditRecordProps> = ({
  outcome,
  robot,
}) => {
  const { t } = useTranslation();
  const { serial, records, message } = robot;

  const { successCount, failureCount } = useMemo(() => {
    let successCount = 0;
    let failureCount = 0;
    for (const record of records) {
      if (record.outcome === Outcome.SUCCESS) {
        successCount++;
      } else {
        failureCount++;
      }
    }
    return { successCount, failureCount };
  }, [records]);

  const description = useMemo(() => {
    const parts = [];
    if (successCount > 0) {
      parts.push(
        t("views.admin.config.bulk.outcomeDescriptions.updatedKeys", {
          count: successCount,
        })
      );
    }
    if (failureCount > 0) {
      parts.push(
        t("views.admin.config.bulk.outcomeDescriptions.encounteredErrors", {
          count: failureCount,
        })
      );
    }
    if (parts.length === 0) {
      return t("views.admin.config.bulk.outcomeDescriptions.noChanges");
    }
    return parts.join(", ");
  }, [t, successCount, failureCount]);

  const [expanded, setExpanded] = useState(false);
  const ExpansionStateIcon = expanded ? ExpandedIcon : CollapsedIcon;
  const mayExpand = records.length > 0;

  return (
    <div>
      <ButtonBase
        onClick={() => setExpanded(!expanded)}
        className="w-full justify-start"
        disabled={!mayExpand}
      >
        <OutcomeIcon
          outcome={outcome}
          fontSize="small"
          className="self-start me-1 my-0.5"
        />
        <ExpansionStateIcon
          fontSize="small"
          className={classes(!mayExpand && "invisible")}
        />
        <Typography className="text-start" style={{ overflowWrap: "anywhere" }}>
          {`${serial}: ${description}`}
          {message.length > 0 && (
            <>
              {": "}
              <span className="opacity-75">{message}</span>
            </>
          )}
        </Typography>
      </ButtonBase>
      {expanded && (
        <ul className="my-0 pl-8 text-sm list-none">
          {records.map((r, i) => (
            <li key={i}>
              <EditRecordView record={r} />
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

interface EditRecordProps {
  record: EditRecord;
}

const EditRecordView: FunctionComponent<EditRecordProps> = ({ record }) => {
  const { key, action, outcome, message } = record;
  return (
    <span>
      <OutcomeIcon
        outcome={
          outcome === Outcome.SUCCESS
            ? AggregateOutcome.SUCCESS
            : AggregateOutcome.FAILURE
        }
        fontSize="inherit"
        className="align-middle me-1"
      />
      <span className="font-mono">{key}</span>
      {action?.set && (
        <>
          <ChangeIcon
            fontSize="inherit"
            className="mx-1 align-middle"
            aria-label="→"
          />
          <ConfigValueView value={action.set.value} />
        </>
      )}
      {message.length > 0 && (
        <>
          {": "}
          <span className="opacity-75">{message}</span>
        </>
      )}
    </span>
  );
};

const aggregateRobots = (
  robots: RobotEditRecord[]
): Record<AggregateOutcome, RobotEditRecord[]> => {
  const result: Record<AggregateOutcome, RobotEditRecord[]> = {
    [AggregateOutcome.SUCCESS]: [],
    [AggregateOutcome.FAILURE]: [],
    [AggregateOutcome.PARTIAL]: [],
  };

  const sortedRobots = robots.toSorted(
    Cmp.comparing((r) => r.serial, robotSort)
  );

  for (const robot of sortedRobots) {
    let hasSuccess = false;
    let hasFailure = false;
    for (const edit of robot.records) {
      switch (edit.outcome) {
        case Outcome.SUCCESS: {
          hasSuccess = true;
          break;
        }
        case Outcome.FAILURE: {
          hasFailure = true;
          break;
        }
      }
    }

    let outcome: AggregateOutcome;
    if (hasSuccess && hasFailure) {
      outcome = AggregateOutcome.PARTIAL;
    } else if (hasSuccess) {
      outcome = AggregateOutcome.SUCCESS;
    } else if (hasFailure) {
      outcome = AggregateOutcome.FAILURE;
    } else {
      // If it has no edit records and has a message, assume that's an
      // error message like "robot not found".
      outcome =
        robot.message.length > 0
          ? AggregateOutcome.FAILURE
          : AggregateOutcome.SUCCESS;
    }
    result[outcome].push(robot);
  }

  return result;
};

type OutcomeIconProps = SvgIconProps & {
  outcome: AggregateOutcome;
};
const OutcomeIcon: FunctionComponent<OutcomeIconProps> = ({
  outcome,
  className,
  ...props
}) => {
  const { t } = useTranslation();

  let Icon: ComponentType<SvgIconProps>;
  let color: string;
  let label: string;
  switch (outcome) {
    case AggregateOutcome.SUCCESS: {
      Icon = SuccessIcon;
      color = "text-green-500";
      label = t("views.admin.config.bulk.outcomes.success");
      break;
    }
    case AggregateOutcome.FAILURE: {
      Icon = FailureIcon;
      color = "text-red-500";
      label = t("views.admin.config.bulk.outcomes.failure");
      break;
    }
    case AggregateOutcome.PARTIAL: {
      Icon = MixedOutcomeIcon;
      color = "text-yellow-500";
      label = t("views.admin.config.bulk.outcomes.partial");
      break;
    }
  }
  return (
    <Icon
      {...props}
      className={classes(className, color)}
      titleAccess={label}
    />
  );
};

const MixedOutcomeIcon: FunctionComponent<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <MixedOutcomeSvg />
    </SvgIcon>
  );
};
