import {
  Alert,
  <PERSON>alog,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  BLUE_LOADING_BUTTON,
  classes,
  INPUT_DARK,
  LOADING_BUTTON,
  SELECT_DARK,
  TEXT_FIELD_DARK,
} from "portal/theme/theme";
import {
  boolean,
  number,
  object,
  ObjectSchema,
  string,
  StringSchema,
} from "yup";
import { buildPermission } from "portal/utils/auth";
import {
  BulkEditRequest,
  ComponentBranch,
  Operation,
} from "protos/robot_syncer/bulk";
import { BulkEditResponseView } from "./BulkEditResponseView";
import { capitalize, titleCase } from "portal/utils/strings";
import {
  ConfigType,
  ConfigValue,
  SchemaNode,
} from "protos/config/api/config_service";
import { ConfigValueView } from "./ConfigValueView";
import { Field, Form, Formik } from "formik";
import { TextField as FormikTextField, Select } from "formik-mui";
import {
  getClass,
  RobotClass,
  toLocalizedRobotClass,
} from "portal/utils/robots";
import { getSchemaNodeFromPath } from "portal/utils/configs";
import { LoadingButton } from "@mui/lab";
import { nanoid } from "@reduxjs/toolkit";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { RobotMultiSelector } from "portal/components/robots/RobotMultiSelector";
import { SchemaTree } from "./SchemaTree";
import { SwitchWithLabel } from "portal/components/SwitchWithLabel";
import { TFunction } from "i18next";
import {
  useBulkEditConfigMutation,
  useGetConfigSchemaQuery,
  useListRobotsQuery,
} from "portal/state/portalApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import { values } from "portal/utils/objects";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import AddIcon from "@mui/icons-material/AddOutlined";
import ChangeIcon from "@mui/icons-material/ArrowForward";
import DeleteIcon from "@mui/icons-material/Delete";
import React, {
  Fragment,
  FunctionComponent,
  ReactNode,
  useRef,
  useState,
} from "react";
import SaveIcon from "@mui/icons-material/SaveOutlined";
import SelectAllIcon from "@mui/icons-material/DoneAll";
import SelectNoneIcon from "@mui/icons-material/RemoveDone";

const SUPPORTED_ROBOT_CLASSES = Object.freeze([
  RobotClass.Slayers,
  RobotClass.Reapers,
  RobotClass.Rtcs,
  RobotClass.Simulators,
] as const);

type SupportedRobotClass = (typeof SUPPORTED_ROBOT_CLASSES)[number];

const _BulkEditor: FunctionComponent = () => {
  const { t } = useTranslation();

  const { user } = useSelf();
  const [submitBulkEdit, { data: response, isLoading: isBulkEditing }] =
    useMutationPopups(useBulkEditConfigMutation(), {
      success: capitalize(
        t("utils.actions.updatedLong", {
          subject: t("models.configs.config_other"),
        })
      ),
    });
  const [viewingResponse, setViewingResponse] = useState(false);

  const { data: allRobots } = useQueryPopups(useListRobotsQuery({}));

  const [robotClass, setRobotClass] = useState(RobotClass.Slayers);
  const { currentData: schema } = useQueryPopups(
    useGetConfigSchemaQuery({ robotClass })
  );

  interface PendingOperation {
    id: string;
    operation: Operation;
  }
  const [operations, setOperations] = useState<PendingOperation[]>([]);

  const [selectedPath, setSelectedPath] = useState<string>("");
  const selectedNode = schema && getSchemaNodeFromPath(schema, selectedPath);
  const formSpec = selectedNode && makeFormSpec(t, selectedNode);

  const [selectedRobotsByClass, setSelectedRobotsByClass] = useState<
    Record<SupportedRobotClass, number[]>
  >(() => {
    const result: Partial<Record<SupportedRobotClass, number[]>> = {};
    for (const c of SUPPORTED_ROBOT_CLASSES) {
      result[c] = [];
    }
    return result as Required<typeof result>;
  });

  const setSelectedRobots = (
    robotClass: SupportedRobotClass,
    selected: number[]
  ): void => {
    setSelectedRobotsByClass((old) => ({
      ...old,
      [robotClass]: selected,
    }));
    // When only one class of robots is selected, show the schema
    // for that robot class, for user convenience.
    if (selected.length > 0) {
      const anythingElseSelected = SUPPORTED_ROBOT_CLASSES.some(
        (otherClass) =>
          otherClass !== robotClass &&
          selectedRobotsByClass[otherClass].length > 0
      );
      if (!anythingElseSelected) {
        setRobotClass(robotClass);
      }
    }
  };

  const selectAllRobots =
    allRobots &&
    ((robotClass: SupportedRobotClass): void => {
      const allTheseRobots = allRobots
        .map(({ robot }) => {
          const id = robot?.db?.id;
          if (id === undefined || getClass(robot) !== robotClass) {
            return;
          }
          return id;
        })
        .filter((id) => id !== undefined);
      setSelectedRobots(robotClass, allTheseRobots);
    });

  const operationsListClasses = "h-40 my-2 p-0";
  const operationsListRef = useRef<HTMLOListElement | null>(null);

  return (
    <div className="flex flex-col h-full gap-5">
      <Dialog
        open={viewingResponse && Boolean(response)}
        onClose={() => setViewingResponse(false)}
        maxWidth="sm"
        fullWidth
        classes={{ paper: "p-8 h-full" }}
      >
        {response && (
          <BulkEditResponseView
            response={response}
            onClose={() => setViewingResponse(false)}
          />
        )}
      </Dialog>
      {/* action buttons */}
      <div className="flex justify-end">
        <LoadingButton
          {...LOADING_BUTTON}
          loading={isBulkEditing}
          startIcon={<SaveIcon />}
          onClick={() => {
            const serialById: Map<number, string> = new Map(
              (allRobots || []).flatMap(({ robot }) =>
                robot?.db?.id === undefined ? [] : [[robot.db.id, robot.serial]]
              )
            );
            const serials: string[] = values(selectedRobotsByClass)
              .flatMap((ids) => ids.map((id) => serialById.get(id)))
              .filter((serial) => serial !== undefined);
            submitBulkEdit(
              BulkEditRequest.fromPartial({
                userId: user?.userId ?? "",
                timestamp: new Date().toISOString(),
                serials,
                operations: operations.map(({ operation }) => operation),
              })
            ).then(() => setViewingResponse(true));
          }}
        >
          {t("utils.actions.submit")}
        </LoadingButton>
      </div>

      <div className="flex flex-col md:flex-row gap-5">
        {/* robots list */}
        <Paper className="p-4 bg-gray-700 flex-1 flex flex-col gap-1">
          <Typography variant="h6">
            {titleCase(t("models.robots.robot_other"))}
          </Typography>
          <div className="grid" />
          {SUPPORTED_ROBOT_CLASSES.map((robotClass) => (
            <div key={robotClass} className="flex">
              <RobotMultiSelector
                key={robotClass}
                label={toLocalizedRobotClass(t, true, robotClass)}
                selectedRobots={selectedRobotsByClass[robotClass]}
                onChange={(selected) => setSelectedRobots(robotClass, selected)}
                onlyShowRobotClass={robotClass}
                className="flex-1 me-2"
                themeProps={{
                  select: {
                    ...SELECT_DARK,
                    classes: {
                      ...SELECT_DARK.classes,
                      root: classes(SELECT_DARK.classes?.root, "w-full"),
                    },
                  },
                }}
              />
              <Tooltip arrow title={t("utils.actions.selectNone")}>
                <IconButton
                  onClick={() => setSelectedRobots(robotClass, [])}
                  className="text-inherit"
                  aria-label={t("utils.actions.selectNone")}
                >
                  <SelectNoneIcon />
                </IconButton>
              </Tooltip>
              <Tooltip arrow title={t("utils.actions.selectAll")}>
                <span>
                  <IconButton
                    onClick={
                      selectAllRobots && (() => selectAllRobots(robotClass))
                    }
                    disabled={!selectAllRobots}
                    className="text-inherit"
                    aria-label={t("utils.actions.selectAll")}
                  >
                    <SelectAllIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          ))}
        </Paper>

        {/* operations list */}
        <Paper className="p-4 bg-gray-700 flex-1">
          <Typography variant="h6">
            {titleCase(
              operations.length > 0
                ? t("views.admin.config.bulk.operationsCount", {
                    count: operations.length,
                  })
                : t("views.admin.config.bulk.operation_other")
            )}
          </Typography>
          {operations.length === 0 && (
            <div
              className={classes(
                operationsListClasses,
                "flex justify-center items-center text-center italic"
              )}
            >
              {t("views.admin.config.bulk.operationsHint")}
            </div>
          )}
          {operations.length > 0 && (
            <ol
              ref={operationsListRef}
              className={classes(
                operationsListClasses,
                "overflow-y-auto list-none"
              )}
            >
              {operations.map(({ id, operation }) => (
                <li key={id} className="my-1">
                  <OperationView
                    operation={operation}
                    onDelete={() =>
                      setOperations((ops) => ops.filter((op) => op.id !== id))
                    }
                  />
                </li>
              ))}
            </ol>
          )}
        </Paper>
      </div>

      {/* robot class selector */}
      <Tabs
        color="primary"
        value={robotClass}
        onChange={(event, value) => setRobotClass(value)}
      >
        {SUPPORTED_ROBOT_CLASSES.map((robotClass) => (
          <Tab
            key={robotClass}
            value={robotClass}
            label={toLocalizedRobotClass(t, true, robotClass)}
          />
        ))}
      </Tabs>

      {/* schema tree and operation editor */}
      <div className="flex flex-col-reverse md:flex-row gap-8 h-full">
        <SchemaTree
          className="w-full md:w-auto"
          schema={schema}
          onSelect={(path) => setSelectedPath(path || "")}
          selectedPath={selectedPath}
        />
        {formSpec && (
          <Paper className={classes("md:flex-grow", "p-8", "bg-gray-700")}>
            <Formik
              enableReinitialize
              initialValues={{ value: formSpec.defaultValue }}
              validationSchema={formSpec.schema}
              onSubmit={async (values) => {
                const value = formSpec.toConfigValue(
                  formSpec.schema.cast(values).value
                );
                if (!value) {
                  throw new Error(
                    "failed to convert form input to config value"
                  );
                }
                const operation = Operation.fromPartial({
                  action: { set: { value } },
                  keySpec: {
                    components: selectedPath.split("/").map((component) => {
                      if (component === "*") {
                        return { branches: [{ wildcard: {} }] };
                      } else {
                        return { branches: [{ literal: component }] };
                      }
                    }),
                  },
                });
                setOperations((operations) => [
                  ...operations,
                  { id: nanoid(), operation },
                ]);
                const listEl = operationsListRef.current;
                if (listEl) {
                  setTimeout(() => {
                    listEl.scrollTo({ top: listEl.scrollHeight });
                  }, 0);
                }
              }}
            >
              {({ submitForm, isSubmitting }) => (
                <Form className="flex flex-col items-start gap-4">
                  {formSpec.field}
                  <div className="flex items-center gap-4">
                    <LoadingButton
                      {...BLUE_LOADING_BUTTON}
                      loading={isSubmitting}
                      onClick={submitForm}
                      startIcon={<AddIcon />}
                    >
                      {t("utils.actions.addLong", {
                        subject: t("views.admin.config.bulk.operation_one"),
                      })}
                    </LoadingButton>
                  </div>
                </Form>
              )}
            </Formik>
          </Paper>
        )}
      </div>
    </div>
  );
};

// Analogous to `arrayOfStrings.join(sep)`, but works with React nodes. Each
// element in `nodes` should probably already have a `key`. The `sep` doesn't
// need to have a key.
const joinNodes = (nodes: ReactNode[], sep: ReactNode): ReactNode[] => {
  const result = [];
  for (const [i, node] of nodes.entries()) {
    if (i > 0) {
      result.push(<Fragment key={`sep-${i}`}>{sep}</Fragment>);
    }
    result.push(node);
  }
  return result;
};

interface OperationProps {
  operation: Operation;
  onDelete: () => void;
}
const OperationView: FunctionComponent<OperationProps> = ({
  operation,
  onDelete,
}) => {
  const { t } = useTranslation();
  const { keySpec, action } = operation;
  if (!action?.set) {
    // only "set value" actions supported in this UI for now
    return;
  }

  const keySpecText = ((): ReactNode => {
    const components = keySpec?.components ?? [];
    const formattedComponents = components.map(({ branches }, i) => {
      const formatBranch = (branch: ComponentBranch): ReactNode => {
        if (branch.literal) {
          return branch.literal;
        } else if (branch.wildcard) {
          const { prefix, suffix } = branch.wildcard;
          return (
            <span className="italic px-1.5 py-0.5 mx-0.5 rounded-sm bg-blue-500/50 border-solid border-0 border-b-2 border-b-blue-500">
              {prefix}*{suffix}
            </span>
          );
        } else {
          return "";
        }
      };
      const key = `part-${i}`;
      if (branches[0] && branches.length === 1) {
        return <span key={key}>{formatBranch(branches[0])}</span>;
      } else {
        const formattedBranches = branches.map((branch, j) => (
          <Fragment key={j}>{formatBranch(branch)}</Fragment>
        ));
        return <span key={key}>({joinNodes(formattedBranches, "|")})</span>;
      }
    });
    return joinNodes(formattedComponents, "/");
  })();

  return (
    <div className="flex">
      <IconButton
        className={classes(
          "align-self-start text-white w-7 h-7 rounded-sm me-2"
        )}
        aria-label={t("utils.actions.delete")}
        onClick={onDelete}
      >
        <DeleteIcon fontSize="inherit" />
      </IconButton>
      <div className="grow flex items-center">
        {/*
          inner wrapper so that:
            - when this fits on one line, it's centered vertically with respect
              to the "delete" button (which is taller than the text),
            - but when it wraps to multiple lines, the button still aligns to
              the top of the container.
          */}
        <div>
          <span className="inline-block px-2 me-2 rounded-full bg-blue-700 uppercase font-bold select-none">
            {t("views.admin.config.bulk.actions.set")}
          </span>
          <span className="font-mono">{keySpecText}</span>
          <ChangeIcon
            fontSize="inherit"
            className="mx-1 align-middle"
            aria-label="→"
          />
          <ConfigValueView value={action.set.value} />
        </div>
      </div>
    </div>
  );
};

interface FormSpec {
  field: ReactNode;
  schema: ObjectSchema<{ value: unknown }>;
  defaultValue: unknown;
  toConfigValue: (value: unknown) => ConfigValue | undefined;
}

// Given a schema node, `makeFormSpec` creates bindings to edit that
// node's value in a Formik form. The form has a single field, named
// "value". If a value in that field passes validation under `schema`,
// you can pass it to `toConfigValue` to get a `ConfigValue`.
const makeFormSpec = (t: TFunction, node: SchemaNode): FormSpec | undefined => {
  const { name, def } = node;
  if (!def) {
    return;
  }

  let hint = def.hint;
  const isDeprecated = hint && hint.toLowerCase().includes("deprecated");
  const deprecationWarning = isDeprecated ? hint : undefined;
  if (isDeprecated) {
    hint = "";
  }
  const deprecationAlert = deprecationWarning && (
    <Alert severity="error">{deprecationWarning}</Alert>
  );
  const units = def.units;
  const unitsAdornment = units && (
    <InputAdornment position="end">{units}</InputAdornment>
  );

  // Most of the implementation for uint64, int64, and float is shared.
  const makeNumberSpec = (codec: {
    integer: boolean;
    fromConfigValue: (value: ConfigValue) => number | undefined;
    toConfigValue: (value: number) => ConfigValue;
  }): FormSpec => {
    const baseNumberSchema = number()
      .required(t("utils.form.required"))
      .typeError(t("utils.form.numberType"));
    return {
      field: (
        <>
          <Field
            {...TEXT_FIELD_DARK}
            component={FormikTextField}
            name="value"
            label={name}
            InputProps={{ endAdornment: unitsAdornment }}
            inputProps={{
              inputMode: "numeric",
              pattern: codec.integer ? "[0-9]+" : "[0-9]+\\.?[0-9]*",
            }}
            helperText={hint}
          />
          {deprecationAlert}
        </>
      ),
      schema: object({
        value: codec.integer
          ? baseNumberSchema.integer(t("utils.form.integerType"))
          : baseNumberSchema,
      }),
      defaultValue: def.defaultValue
        ? codec.fromConfigValue(def.defaultValue)
        : 0,
      toConfigValue: (value) => {
        return typeof value === "number"
          ? codec.toConfigValue(value)
          : undefined;
      },
    };
  };

  switch (def.type) {
    case ConfigType.INT: {
      return makeNumberSpec({
        integer: true,
        fromConfigValue: (v: ConfigValue) => v.int64Val,
        toConfigValue: (n: number) => ConfigValue.fromPartial({ int64Val: n }),
      });
    }

    case ConfigType.UINT: {
      return makeNumberSpec({
        integer: true,
        fromConfigValue: (v: ConfigValue) => v.uint64Val,
        toConfigValue: (n: number) => ConfigValue.fromPartial({ uint64Val: n }),
      });
    }

    case ConfigType.FLOAT: {
      return makeNumberSpec({
        integer: false,
        fromConfigValue: (v: ConfigValue) => v.floatVal,
        toConfigValue: (n: number) => ConfigValue.fromPartial({ floatVal: n }),
      });
    }

    case ConfigType.BOOL: {
      return {
        field: (
          <FormControl>
            <div className="flex gap-2">
              <Field
                component={SwitchWithLabel}
                type="checkbox"
                name="value"
                label={name}
              />
            </div>
            {hint && <FormHelperText>{hint}</FormHelperText>}
            {deprecationAlert}
          </FormControl>
        ),
        schema: object({
          value: boolean().required(t("utils.form.required")),
        }),
        defaultValue: def.defaultValue?.boolVal ?? false,
        toConfigValue: (value) => {
          if (typeof value !== "boolean") {
            return;
          }
          return ConfigValue.fromPartial({ boolVal: value });
        },
      };
    }

    case ConfigType.STRING: {
      const { stringDef } = def;
      const sizeLimit = stringDef?.sizeLimit || Infinity; // zero means unlimited
      const choices = stringDef?.choices ?? [];

      let valueSchema: StringSchema;
      let formikField: ReactNode;
      if (choices.length > 0) {
        // drop-down menu when there's a fixed set of choices
        formikField = (
          <Field
            {...SELECT_DARK}
            labelId={`field-${name}`}
            component={Select}
            className={classes(SELECT_DARK.className, "min-w-52")}
            autoWidth
            name="value"
            label={name}
            input={<OutlinedInput {...INPUT_DARK} label={name} />}
            formHelperText={{ children: hint }}
          >
            {choices.map((choice: string) => (
              <MenuItem key={choice} value={choice}>
                {choice}
                {units ? ` ${units}` : ""}
              </MenuItem>
            ))}
          </Field>
        );
        valueSchema = string();
      } else {
        // free-form text field when there are no choices listed
        formikField = (
          <Field
            {...TEXT_FIELD_DARK}
            component={FormikTextField}
            InputProps={{ endAdornment: unitsAdornment }}
            name="value"
            label={name}
            helperText={hint}
          />
        );
        valueSchema = string()
          .test(
            "size_limit",
            t("utils.form.maxSize", { limit: sizeLimit }),
            (value) => (value ? value.length <= sizeLimit : true)
          )
          .typeError(t("utils.form.stringType"));
      }

      return {
        field: (
          <>
            {formikField}
            {deprecationWarning}
          </>
        ),
        schema: object({ value: valueSchema }),
        defaultValue: def.defaultValue?.stringVal ?? "",
        toConfigValue: (value) => {
          if (typeof value !== "string") {
            return;
          }
          return ConfigValue.fromPartial({ stringVal: value });
        },
      };
    }
  }
};

export const BulkEditor = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.update,
      PermissionResource.configs,
      PermissionDomain.all
    ),
  ],
  _BulkEditor
);
