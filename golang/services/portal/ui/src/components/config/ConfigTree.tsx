import { capitalize } from "portal/utils/strings";
import { classes, SMALL_TEXT_FIELD_DARK } from "portal/theme/theme";
import { ConfigNode } from "protos/config/api/config_service";
import { debounce } from "portal/utils/timing";
import {
  filterConfigTree,
  getParentPath,
  includeAncestors,
  indexConfigTree,
  SearchIndex,
  searchTree,
} from "portal/utils/configs";
import {
  FormControlLabel,
  IconButton,
  InputAdornment,
  Skeleton,
  Switch,
  TextField,
} from "@mui/material";
import { GlobalHotKeys } from "react-hotkeys";
import { SimpleTreeView, TreeItem } from "@mui/x-tree-view";
import { useLatest } from "common/hooks/useLatest";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import { without } from "portal/utils/arrays";
import ChangedIcon from "@mui/icons-material/ChangeHistoryOutlined";
import ClearIcon from "@mui/icons-material/ClearOutlined";
import CollapsedIcon from "@mui/icons-material/ExpandMoreOutlined";
import ExpandedIcon from "@mui/icons-material/ChevronRightOutlined";
import React, {
  FunctionComponent,
  SyntheticEvent,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";

const MAX_SEARCH_EXPANSIONS = 1000;

interface NodeProps {
  node: ConfigNode;
  path: string;
  onSelect: (path: string) => void;
  changedPathsAncestors: string[];
}

export const TreeNode: FunctionComponent<NodeProps> = ({
  node,
  onSelect,
  path,
  changedPathsAncestors,
}) => {
  return (
    <TreeItem
      classes={{
        label: "font-mono",
        selected: "font-bold bg-blue-600",
        content: "p-0",
      }}
      itemId={path}
      label={
        <span className="flex items-center">
          {changedPathsAncestors.includes(path) && (
            <ChangedIcon className="mr-2 text-yellow-400 text-xs" />
          )}
          {node.name}
        </span>
      }
      onClick={() => {
        onSelect(path);
      }}
    >
      {node.children.map((child) => {
        const childPath = `${path}/${child.name}`;
        return (
          <TreeNode
            key={childPath}
            node={child}
            onSelect={onSelect}
            path={childPath}
            changedPathsAncestors={changedPathsAncestors}
          />
        );
      })}
    </TreeItem>
  );
};

const TREE_SKELETON = (
  <>
    <Skeleton variant="text" className="w-10" />
    <Skeleton variant="text" className="w-16 ml-4" />
    <Skeleton variant="text" className="w-10 ml-4" />
    <Skeleton variant="text" className="w-12 ml-4" />
    <Skeleton variant="text" className="w-10" />
  </>
);

interface TreeProps {
  className?: string;
  config?: ConfigNode;
  initialShowChanged: boolean;
  onSelect: (path: string | null) => void;
  selectedPath: string;
  addedPaths: string[] | undefined;
  removedPaths: string[] | undefined;
  changedPathsAncestors: string[] | undefined;
  changedPathsTree?: ConfigNode;
  isTemplate?: boolean;
}

const _Tree: FunctionComponent<TreeProps> = ({
  className,
  config,
  initialShowChanged,
  onSelect,
  selectedPath,
  changedPathsAncestors,
  changedPathsTree,
  isTemplate = false,
}) => {
  const { t } = useTranslation();

  const { state } = useLocation();

  const [expanded, _setExpanded] = useState<string[]>(() =>
    includeAncestors([selectedPath])
  );

  const setExpanded = useCallback(
    (paths: string[], keepParent: boolean = true): void => {
      if (keepParent) {
        // keep parent of selected node expanded
        _setExpanded([...paths, getParentPath(selectedPath)]);
      } else {
        _setExpanded(paths);
      }
    },
    [selectedPath]
  );
  const setExpandedRef = useLatest(setExpanded);

  const searchIndex: SearchIndex = useMemo(
    () => indexConfigTree(config),
    [config]
  );
  const changedSearchIndex: SearchIndex = useMemo(
    () => indexConfigTree(changedPathsTree),
    [changedPathsTree]
  );
  const [showChanged, setShowChanged] = useState<boolean>(initialShowChanged);
  const activeConfig = showChanged ? changedPathsTree : config;
  const activeIndex = showChanged ? changedSearchIndex : searchIndex;
  const activeIndexRef = useLatest(activeIndex);
  const [isSearching, setSearching] = useState<boolean>(false);
  const [savedExpanded, saveExpanded] = useState<string[]>([]);
  // searchInput is live text box; searchQuery is debounced
  const [searchInput, setSearchInput] = useState<string>(state?.search ?? "");
  const [searchQuery, setSearchQuery] = useState<string>(state?.search ?? "");
  const debounceSearchState = useRef(0); // increment to cancel outstanding callback
  const finalizeSearchQuery = useMemo(
    () =>
      debounce((query, expectedState) => {
        if (debounceSearchState.current !== expectedState) {
          return;
        }
        setSearchQuery(query);
        setSearching(false);

        // Search changed tree or full tree depending on showChanged toggle
        const resultPaths = searchTree(activeIndexRef.current, query);

        // Reset set of expanded nodes when search query changes. Access
        // search index and `setExpanded` through refs instead of memo
        // dependencies, since changes to the config and selection
        // should not cause this to re-trigger.
        const setExpanded = setExpandedRef.current;
        setExpanded(
          includeAncestors(
            // slice off the root node because the tree doesn't know about it,
            // and limit the number of expansions to avoid performance issues
            resultPaths
              .slice(0, MAX_SEARCH_EXPANSIONS)
              .map((path) => path.split("/").slice(1).join("/"))
          )
        );
      }),
    [activeIndexRef, setExpandedRef]
  );

  const handleSearchChange = (value: string): void => {
    if (!value) {
      clearSearch();
      return;
    }
    // save expanded nodes so we can restore later
    if (value && !searchInput) {
      saveExpanded(expanded);
    }
    // update value
    setSearchInput(value);
    setSearching(true);
    finalizeSearchQuery(value, ++debounceSearchState.current);
  };
  const clearSearch = (): void => {
    ++debounceSearchState.current;
    setSearchInput("");
    setSearchQuery("");
    setSearching(false);
    setExpanded([selectedPath, ...savedExpanded], false);
  };

  const filteredConfig = useMemo(() => {
    if (!searchQuery) {
      return activeConfig;
    }

    // Search changed tree or full tree depending on showChanged toggle
    const resultPaths = searchTree(activeIndex, searchQuery);
    return filterConfigTree(activeConfig, resultPaths);
  }, [activeConfig, activeIndex, searchQuery]);

  // controlled tree
  const handleToggle = (event: SyntheticEvent, nodeId: string): void => {
    if (expanded.includes(nodeId)) {
      setExpanded(without(expanded, nodeId), false);
    } else {
      setExpanded([...expanded, nodeId], false);
    }
  };
  const handleSelect = (event: SyntheticEvent, nodeId: string | null): void => {
    onSelect(nodeId);
  };
  const inputRef = useRef<HTMLInputElement>();

  if (!config) {
    return (
      <div className={classes(className, "flex")}>
        <div className="min-w-32">{TREE_SKELETON}</div>
        <div className="h-full flex-grow flex pl-8">
          <Skeleton variant="rectangular" className="h-8 w-40" />
          <Skeleton variant="rectangular" className="h-8 w-28 ml-2" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <GlobalHotKeys
        keyMap={{ FOCUS: ["/", "ctrl+f"] }}
        handlers={{
          FOCUS: (event) => {
            inputRef.current?.focus();
            event?.preventDefault();
          },
        }}
      />

      <TextField
        {...SMALL_TEXT_FIELD_DARK}
        inputRef={inputRef}
        value={searchInput}
        onChange={(event) => handleSearchChange(event.target.value)}
        className="w-full mr-5"
        label={t("utils.actions.search", {
          subject: capitalize(t("models.configs.config_other")),
        })}
        inputProps={{ className: "font-mono" }}
        InputProps={{
          ...SMALL_TEXT_FIELD_DARK.InputProps,
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={clearSearch} edge="end">
                <ClearIcon
                  className={classes("text-white", {
                    hidden: !searchInput,
                  })}
                />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      {!isTemplate && (
        <FormControlLabel
          className="ml-1 text-sm"
          control={
            <Switch
              size="small"
              classes={{
                thumb: showChanged ? "bg-yellow-500" : "bg-white",
                track: showChanged ? "bg-yellow-100" : "bg-gray-200",
              }}
              checked={showChanged}
              onChange={(event, checked) => {
                setShowChanged(checked);
              }}
              name="changed"
            />
          }
          label={t("views.fleet.robots.config.onlyChanged")}
        />
      )}
      <div className="overflow-y-visible md:overflow-y-auto overflow-x-hidden basis-full md:basis-0 flex-grow-0 md:flex-grow">
        <SimpleTreeView
          className={classes(className)}
          selectedItems={selectedPath}
          expandedItems={expanded}
          onSelectedItemsChange={handleSelect}
          onItemExpansionToggle={handleToggle}
          slots={{
            collapseIcon: CollapsedIcon,
            expandIcon: ExpandedIcon,
          }}
        >
          {isSearching && TREE_SKELETON}
          {!isSearching &&
            filteredConfig?.children.map((child) => (
              <TreeNode
                key={child.name}
                path={child.name}
                node={child}
                onSelect={onSelect}
                changedPathsAncestors={changedPathsAncestors ?? []}
              />
            ))}
        </SimpleTreeView>
      </div>
    </div>
  );
};

export const Tree = withErrorBoundary(
  { i18nKey: "views.fleet.robots.config.errors.failed" },
  _Tree
);
