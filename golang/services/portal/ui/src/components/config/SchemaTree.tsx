import { capitalize } from "portal/utils/strings";
import { classes, SMALL_TEXT_FIELD_DARK } from "portal/theme/theme";
import { debounce } from "portal/utils/timing";
import {
  filterSchemaTree,
  getParentPath,
  includeAncestors,
  indexSchemaTree,
  SearchIndex,
  searchTree,
} from "portal/utils/configs";
import { GlobalHotKeys } from "react-hotkeys";
import { IconButton, InputAdornment, Skeleton, TextField } from "@mui/material";
import { SchemaNode } from "protos/config/api/config_service";
import { SimpleTreeView, TreeItem } from "@mui/x-tree-view";
import { useLatest } from "common/hooks/useLatest";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import { without } from "portal/utils/arrays";
import ClearIcon from "@mui/icons-material/ClearOutlined";
import CollapsedIcon from "@mui/icons-material/ExpandMoreOutlined";
import ExpandedIcon from "@mui/icons-material/ChevronRightOutlined";
import React, {
  FunctionComponent,
  ReactNode,
  SyntheticEvent,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";

const MAX_SEARCH_EXPANSIONS = 1000;

interface NodeProps {
  node: SchemaNode;
  isListItem: boolean;
  path: string;
  onSelect: (path: string) => void;
}

const TreeNode: FunctionComponent<NodeProps> = ({
  node,
  isListItem,
  onSelect,
  path,
}) => {
  const { t } = useTranslation();
  const childNode = (child: SchemaNode, isListItem: boolean): ReactNode => {
    const childPath = `${path}/${isListItem ? "*" : child.name}`;
    return (
      <TreeNode
        key={childPath}
        node={child}
        isListItem={isListItem}
        onSelect={onSelect}
        path={childPath}
      />
    );
  };

  return (
    <TreeItem
      classes={{
        selected: "font-bold bg-blue-600",
        content: "p-0",
      }}
      itemId={path}
      label={
        isListItem ? (
          <em>{t("views.admin.config.bulk.listItems")}</em>
        ) : (
          <span className="font-mono">{node.name}</span>
        )
      }
      onClick={() => {
        onSelect(path);
      }}
    >
      {node.listItem && childNode(node.listItem, true)}
      {node.nodeChildren.map((child) => childNode(child, false))}
    </TreeItem>
  );
};

const TREE_SKELETON = (
  <>
    <Skeleton variant="text" className="w-10" />
    <Skeleton variant="text" className="w-16 ml-4" />
    <Skeleton variant="text" className="w-10 ml-4" />
    <Skeleton variant="text" className="w-12 ml-4" />
    <Skeleton variant="text" className="w-10" />
  </>
);

interface TreeProps {
  className?: string;
  schema?: SchemaNode;
  onSelect: (path: string | null) => void;
  selectedPath: string;
}

const _Tree: FunctionComponent<TreeProps> = ({
  className,
  schema,
  onSelect,
  selectedPath,
}) => {
  const { t } = useTranslation();

  const { state } = useLocation();

  const [expanded, _setExpanded] = useState<string[]>(() =>
    includeAncestors([selectedPath])
  );

  const setExpanded = useCallback(
    (paths: string[]): void => {
      // keep parent of selected node expanded
      _setExpanded([...paths, getParentPath(selectedPath)]);
    },
    [selectedPath]
  );
  const setExpandedRef = useLatest(setExpanded);

  const searchIndex: SearchIndex = useMemo(
    () => indexSchemaTree(schema),
    [schema]
  );
  const searchIndexRef = useLatest(searchIndex);

  const [isSearching, setSearching] = useState<boolean>(false);
  const [savedExpanded, saveExpanded] = useState<string[]>([]);
  // searchInput is live text box; searchQuery is debounced
  const [searchInput, setSearchInput] = useState<string>(state?.search ?? "");
  const [searchQuery, setSearchQuery] = useState<string>(state?.search ?? "");
  const debounceSearchState = useRef(0); // increment to cancel outstanding callback
  const finalizeSearchQuery = useMemo(
    () =>
      debounce((query, expectedState) => {
        if (debounceSearchState.current !== expectedState) {
          return;
        }
        setSearchQuery(query);
        setSearching(false);

        // Reset set of expanded nodes when search query changes. Access
        // search index and `setExpanded` through refs instead of memo
        // dependencies, since changes to the config and selection
        // should not cause this to re-trigger.
        const resultPaths = searchTree(searchIndexRef.current, query);
        const setExpanded = setExpandedRef.current;
        setExpanded(
          includeAncestors(
            // slice off the root node because the tree doesn't know about it,
            // and limit the number of expansions to avoid performance issues
            resultPaths
              .slice(0, MAX_SEARCH_EXPANSIONS)
              .map((path) => path.split("/").slice(1).join("/"))
          )
        );
      }),
    [searchIndexRef, setExpandedRef]
  );

  const handleSearchChange = (value: string): void => {
    if (!value) {
      clearSearch();
      return;
    }
    // save expanded nodes so we can restore later
    if (value && !searchInput) {
      saveExpanded(expanded);
    }
    // update value
    setSearchInput(value);
    setSearching(true);
    finalizeSearchQuery(value, ++debounceSearchState.current);
  };
  const clearSearch = (): void => {
    ++debounceSearchState.current;
    setSearchInput("");
    setSearchQuery("");
    setSearching(false);
    setExpanded([selectedPath, ...savedExpanded]);
  };

  const filteredSchema: SchemaNode | undefined = useMemo(() => {
    if (!schema) {
      return schema;
    }
    const resultPaths = searchTree(searchIndex, searchQuery);
    return filterSchemaTree(schema, resultPaths);
  }, [schema, searchIndex, searchQuery]);

  // controlled tree
  const handleToggle = (event: SyntheticEvent, nodeId: string): void => {
    if (expanded.includes(nodeId)) {
      setExpanded(without(expanded, nodeId));
    } else {
      setExpanded([...expanded, nodeId]);
    }
  };
  const handleSelect = (event: SyntheticEvent, nodeId: string | null): void => {
    onSelect(nodeId);
  };
  const inputRef = useRef<HTMLInputElement>();

  if (!schema) {
    return (
      <div className={classes(className, "flex")}>
        <div className="min-w-32">{TREE_SKELETON}</div>
        <div className="h-full flex-grow flex pl-8">
          <Skeleton variant="rectangular" className="h-8 w-40" />
          <Skeleton variant="rectangular" className="h-8 w-28 ml-2" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <GlobalHotKeys
        keyMap={{ FOCUS: ["/", "ctrl+f"] }}
        handlers={{
          FOCUS: (event) => {
            inputRef.current?.focus();
            event?.preventDefault();
          },
        }}
      />

      <TextField
        {...SMALL_TEXT_FIELD_DARK}
        inputRef={inputRef}
        value={searchInput}
        onChange={(event) => handleSearchChange(event.target.value)}
        className="w-full mr-5"
        label={t("utils.actions.search", {
          subject: capitalize(t("models.configs.config_other")),
        })}
        inputProps={{ className: "font-mono" }}
        InputProps={{
          ...SMALL_TEXT_FIELD_DARK.InputProps,
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={clearSearch} edge="end">
                <ClearIcon
                  className={classes("text-white", {
                    hidden: !searchInput,
                  })}
                />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      <div className="overflow-y-visible md:overflow-y-auto overflow-x-hidden basis-full md:basis-0 flex-grow-0 md:flex-grow">
        <SimpleTreeView
          className={classes(className)}
          selectedItems={selectedPath}
          expandedItems={expanded}
          onSelectedItemsChange={handleSelect}
          onItemExpansionToggle={handleToggle}
          slots={{
            collapseIcon: CollapsedIcon,
            expandIcon: ExpandedIcon,
          }}
        >
          {isSearching && TREE_SKELETON}
          {!isSearching &&
            filteredSchema?.nodeChildren.map((child) => (
              <TreeNode
                key={child.name}
                node={child}
                isListItem={false}
                path={child.name}
                onSelect={onSelect}
              />
            ))}
        </SimpleTreeView>
      </div>
    </div>
  );
};

export const SchemaTree = withErrorBoundary(
  { i18nKey: "views.fleet.robots.config.errors.failed" },
  _Tree
);
