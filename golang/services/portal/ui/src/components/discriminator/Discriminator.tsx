import { AdvancedDiscriminator } from "./AdvancedDiscriminator";
import { <PERSON><PERSON>, Button, IconButton, Typography } from "@mui/material";
import { BasicDiscriminator } from "./BasicDiscriminator";
import { buildPermission } from "portal/utils/auth";
import { capitalize } from "portal/utils/strings";
import { ConfirmationDialog } from "../ConfirmationDialog";
import { DiscriminatorConfig } from "protos/almanac/almanac";
import { EditableTypography } from "../EditableTypography";
import { isUndefined } from "portal/utils/identity";
import { Link, useNavigate } from "react-router-dom";
import { Loading } from "portal/components/Loading";
import { LOCALSTORAGE_DISCRIMINATOR_MODE } from "portal/utils/localStorage";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { RED_BUTTON, WHITE_BUTTON } from "portal/theme/theme";
import { useAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import { useCrops } from "portal/utils/hooks/useCrops";
import {
  useDeleteDiscriminatorMutation,
  useGetDiscriminatorQuery,
  useGetRobotQuery,
  useSetDiscriminatorMutation,
} from "portal/state/portalApi";
import { useLocalStorage } from "@uidotdev/usehooks";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import DeleteIcon from "@mui/icons-material/DeleteOutlined";
import ParentIcon from "@mui/icons-material/ArrowBackOutlined";
import React, { FunctionComponent, useMemo, useState } from "react";

interface Props {
  parentLink?: string;
  serial: string;
  uuid: string;
}

export const Discriminator: FunctionComponent<Props> = ({
  uuid,
  parentLink,
  serial,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data: summary } = useQueryPopups(useGetRobotQuery({ serial }), {
    errorVariant: "warning",
  });

  const canEnableCrops = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.crops_advanced,
      PermissionDomain.all
    ),
  ]);

  // crops
  const {
    sortedCrops,
    isError: isCropsError,
    error: cropsError,
  } = useCrops({ serial });
  const crops = canEnableCrops ? sortedCrops?.all : sortedCrops?.enabled;

  // view state
  const [isAdvanced, setAdvanced] = useLocalStorage<boolean>(
    LOCALSTORAGE_DISCRIMINATOR_MODE,
    false
  );

  // local changes
  const [updatedDiscriminator, setUpdatedDiscriminator] = useState<
    DiscriminatorConfig | undefined
  >();

  // remote data
  const {
    data: freezedDiscriminator,
    isError: isDiscriminatorError,
    error: discriminatorError,
  } = useQueryPopups(useGetDiscriminatorQuery({ uuid }));

  // mutations
  const [deleteDiscriminator] = useMutationPopups(
    useDeleteDiscriminatorMutation(),
    {
      success: capitalize(
        t("utils.actions.deletedLong", {
          subject: t("models.discriminators.discriminator_one"),
        })
      ),
    }
  );
  const [updateDiscriminator] = useMutationPopups(
    useSetDiscriminatorMutation(),
    {
      success: capitalize(
        t("utils.actions.savedLong", {
          subject: t("models.discriminators.discriminator_one"),
        })
      ),
    }
  );

  // confirmation dialog
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);

  // unfreezed state
  const unfreezedDiscriminator = useMemo<DiscriminatorConfig | undefined>(
    () =>
      freezedDiscriminator
        ? DiscriminatorConfig.fromPartial(structuredClone(freezedDiscriminator))
        : undefined,
    [freezedDiscriminator]
  );

  // visible state
  const discriminator = updatedDiscriminator ?? unfreezedDiscriminator;
  const isActive =
    !isUndefined(discriminator?.id) &&
    discriminator.id ===
      summary?.robot?.health?.fieldConfig?.activeDiscriminatorId;

  const canRead = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.almanacs,
      PermissionDomain.all
    ),
    buildPermission(
      PermissionAction.read,
      PermissionResource.almanacs,
      PermissionDomain.customer
    ),
  ]);
  const canUpdate =
    useAuthorizationRequired([
      buildPermission(
        PermissionAction.update,
        PermissionResource.almanacs,
        PermissionDomain.all
      ),
      buildPermission(
        PermissionAction.update,
        PermissionResource.almanacs,
        PermissionDomain.customer
      ),
    ]) && !discriminator?.protected;

  if (!canRead) {
    return;
  }

  if (!discriminator || !crops) {
    return (
      <Loading
        failed={isCropsError || isDiscriminatorError}
        error={cropsError ?? discriminatorError}
      />
    );
  }

  return (
    <div className="flex flex-col min-h-full">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8 w-full gap-4">
        {/* Left Controls */}
        <div className="flex items-center">
          {/* Breadcrumb */}
          {parentLink && (
            <IconButton component={Link} to={parentLink} className="text-white">
              <ParentIcon />
            </IconButton>
          )}

          {/* Mode switcher */}
          <Button {...WHITE_BUTTON} onClick={() => setAdvanced(!isAdvanced)}>
            {isAdvanced
              ? t("components.almanac.switchModeBasic")
              : t("components.almanac.switchModeAdvanced")}
          </Button>
        </div>

        {/* Title */}
        {canUpdate ? (
          <EditableTypography
            variant="h4"
            value={discriminator.name}
            onEdit={async (name) => {
              const newDiscriminator = {
                ...discriminator,
                name,
              };
              setUpdatedDiscriminator(newDiscriminator);
              await updateDiscriminator({
                serial,
                discriminator: newDiscriminator,
              });
              setUpdatedDiscriminator(undefined);
            }}
          />
        ) : (
          <Typography variant="h4">{discriminator.name}</Typography>
        )}

        {/* Right Controls */}
        <div className="flex items-center gap-4">
          {canUpdate && (
            <Button
              {...RED_BUTTON}
              onClick={() => setConfirmDelete(true)}
              startIcon={<DeleteIcon />}
            >
              {t("utils.actions.delete")}
            </Button>
          )}
          {confirmDelete && (
            <ConfirmationDialog
              title={t("utils.actions.deleteLong", {
                subject: t("models.discriminators.discriminator_one"),
              })}
              description={
                isActive ? (
                  <Alert severity="warning" className="mb-8">
                    {t(
                      "components.ConfirmationDialog.delete.descriptionActive",
                      {
                        subject: t("models.discriminators.discriminator_one"),
                      }
                    )}
                  </Alert>
                ) : (
                  t("components.ConfirmationDialog.delete.description", {
                    subject: t("models.discriminators.discriminator_one"),
                  })
                )
              }
              destructive
              yesText={t("utils.actions.deleteLong", {
                subject: discriminator.name,
              })}
              yesDisabled={isActive}
              onClose={() => setConfirmDelete(false)}
              onYes={async () => {
                await deleteDiscriminator({ uuid: discriminator.id });
                if (parentLink) {
                  navigate(parentLink);
                }
              }}
            />
          )}
        </div>
      </div>

      {isActive && (
        <Alert severity="warning" className="mb-8">
          {t("components.discriminator.warnings.production")}
        </Alert>
      )}

      {/* Main View */}
      {isAdvanced ? (
        <AdvancedDiscriminator
          discriminator={discriminator}
          crops={crops}
          onChange={async (newDiscriminator) => {
            setUpdatedDiscriminator(newDiscriminator);
            await updateDiscriminator({
              serial,
              discriminator: newDiscriminator,
            });

            setUpdatedDiscriminator(undefined);
          }}
          readOnly={!canUpdate}
        />
      ) : (
        <BasicDiscriminator
          discriminator={discriminator}
          crops={crops}
          onChange={async (newDiscriminator) => {
            setUpdatedDiscriminator(newDiscriminator);
            await updateDiscriminator({
              serial,
              discriminator: newDiscriminator,
            });
            setUpdatedDiscriminator(undefined);
          }}
          readOnly={!canUpdate}
        />
      )}
    </div>
  );
};
