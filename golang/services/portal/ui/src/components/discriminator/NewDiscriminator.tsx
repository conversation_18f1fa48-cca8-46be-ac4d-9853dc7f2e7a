import { BLUE_LOADING_BUTTON, TEXT_FIELD_DARK } from "portal/theme/theme";
import { buildPermission } from "portal/utils/auth";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { capitalize, titleCase } from "portal/utils/strings";
import { DEFAULT_DISCRIMINATOR } from "portal/utils/discriminator";
import { DiscriminatorConfig } from "protos/almanac/almanac";
import { Field, Form, Formik } from "formik";
import { getRobotPath, RobotSubpath } from "portal/utils/routing";
import { GlobalHotKeys } from "react-hotkeys";
import { LoadingButton } from "@mui/lab";
import { object, string } from "yup";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { TextField } from "formik-mui";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useNavigate } from "react-router-dom";
import { useSetDiscriminatorMutation } from "portal/state/portalApi";
import { useTranslation } from "react-i18next";
import { v4 as uuid } from "uuid";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import AddIcon from "@mui/icons-material/AddOutlined";
import React, { FunctionComponent, useState } from "react";

interface Props {
  hotkey?: string;
  serial: string;
}

const _NewDiscriminator: FunctionComponent<Props> = ({ hotkey, serial }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [isOpen, setOpen] = useState<boolean>(false);

  const [setDiscriminator] = useMutationPopups(useSetDiscriminatorMutation(), {
    success: capitalize(
      t("utils.actions.createdLong", {
        subject: t("models.discriminators.discriminator_one"),
      })
    ),
  });

  return (
    <>
      {hotkey && (
        <GlobalHotKeys
          keyMap={{
            NEW_DISCRIMINATOR: {
              name: titleCase(
                t("utils.actions.createdLong", {
                  subject: t("models.discriminators.discriminator_one"),
                })
              ),
              action: "keyup",
              sequence: hotkey,
            },
          }}
          handlers={{
            NEW_DISCRIMINATOR: () => setOpen(true),
          }}
        />
      )}
      <Button
        className="text-white"
        startIcon={<AddIcon />}
        onClick={() => setOpen(true)}
      >
        <span className="hidden sm:inline">
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.discriminators.discriminator_one"),
            })
          )}
        </span>
        <span className="sm:hidden">{t("utils.actions.new")}</span>
      </Button>
      <Dialog open={isOpen} onClose={() => setOpen(false)}>
        <DialogTitle>
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.discriminators.discriminator_one"),
            })
          )}
        </DialogTitle>
        <Formik
          enableReinitialize
          initialValues={{
            name: "",
          }}
          validationSchema={object({
            name: string().required(),
          })}
          onSubmit={async ({ name }) => {
            const id = uuid();
            await setDiscriminator({
              serial,
              discriminator: DiscriminatorConfig.fromPartial({
                ...DEFAULT_DISCRIMINATOR,
                id,
                name,
              }),
            });
            navigate(
              `${getRobotPath(serial, RobotSubpath.DISCRIMINATOR)}/${id}`
            );
          }}
        >
          {({ submitForm, isSubmitting, dirty }) => (
            <Form>
              <DialogContent className="flex flex-col gap-4 pt-2">
                <Field
                  {...TEXT_FIELD_DARK}
                  component={TextField}
                  name="name"
                  label={t("models.almanacs.fields.name")}
                />
              </DialogContent>
              <DialogActions>
                <Button
                  variant="text"
                  className="text-white"
                  disabled={isSubmitting}
                  onClick={() => setOpen(false)}
                >
                  {t("utils.actions.cancel")}
                </Button>
                <LoadingButton
                  {...BLUE_LOADING_BUTTON}
                  disabled={!dirty}
                  loading={isSubmitting}
                  onClick={submitForm}
                  startIcon={<AddIcon />}
                >
                  {t("utils.actions.create")}
                </LoadingButton>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
    </>
  );
};

export const NewDiscriminator = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.update,
      PermissionResource.discriminators,
      PermissionDomain.self
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.discriminators,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.discriminators,
      PermissionDomain.all
    ),
  ],
  _NewDiscriminator
);
