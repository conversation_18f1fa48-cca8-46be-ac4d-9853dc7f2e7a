import { classes } from "portal/theme/theme";
import { Collapse, IconButton, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import React, { FC, PropsWithChildren, useState } from "react";

interface FilterBarProps extends PropsWithChildren {
  collapsedSummary?: React.ReactNode;
}

export const FilterBar: FC<FilterBarProps> = ({
  children,
  collapsedSummary,
}) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(true);

  const iconClasses = "text-xl w-[20px] h-[20px] text-white";
  return (
    <div className="bg-gray-600 rounded w-full p-4">
      <div className="flex justify-between items-center w-full gap-4">
        <Typography variant="caption" className="uppercase">
          {t("components.filters.filters")}
        </Typography>
        {!expanded && collapsedSummary ? (
          <div className="flex flex-wrap gap-1 mt-1 grow">
            {collapsedSummary}
          </div>
        ) : undefined}
        <IconButton onClick={() => setExpanded(!expanded)} size="small">
          {expanded ? (
            <KeyboardArrowUpIcon className={classes(iconClasses)} />
          ) : (
            <KeyboardArrowDownIcon className={classes(iconClasses)} />
          )}
        </IconButton>
      </div>
      <Collapse in={expanded}>{children}</Collapse>
    </div>
  );
};
