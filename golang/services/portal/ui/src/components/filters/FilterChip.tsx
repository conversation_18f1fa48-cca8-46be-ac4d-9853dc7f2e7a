import { Chip } from "@mui/material";
import React, { FC } from "react";

export interface FilterChipProps {
  name: string;
  value: string;
}
export const FilterChip: FC<FilterChipProps> = ({ name, value }) => (
  <Chip
    color="primary"
    size="small"
    className="px-2 text-sm mr-2"
    classes={{
      root: "py-1 h-full flex flex-row bg-white bg-opacity-80 text-gray-800",
      label: "break-words whitespace-normal text-clip",
    }}
    label={
      <>
        <span className="uppercase">{name}:</span>{" "}
        <span className="font-bold">{value}</span>
      </>
    }
  />
);
