import { CarbonUnit } from "portal/utils/units/units";
import { classes } from "portal/theme/theme";
import { FormikErrors, yupToFormErrors } from "formik";
import { isNil } from "portal/utils/identity";
import { number, object } from "yup";
import { NumberInput } from "../NumberInput";
import { NumericalRangeValue } from "./filter.types";
import { useTranslation } from "react-i18next";
import React, { FC, useState } from "react";

interface RangeFields {
  min?: number;
  max?: number;
}
interface NumericalRangeProps extends RangeFields {
  className?: string;
  value: NumericalRangeValue;
  onChange: (selection: NumericalRangeValue) => void;
  min?: number;
  max?: number;
  units?: CarbonUnit;
}
export const NumericalRange: FC<NumericalRangeProps> = ({
  className,
  value,
  onChange,
  min,
  max,
  units,
}) => {
  const { t } = useTranslation();

  const [errors, setErrors] = useState<FormikErrors<RangeFields | undefined>>();
  const rangeSchema = object({
    min: number()
      .nullable()
      .test("range", t("utils.form.minGreaterThanMax"), (value, context) => {
        if (isNil(value) || isNil(context.parent.max)) {
          return true;
        }
        return value < context.parent.max;
      })
      .min(min ?? -Infinity)
      .max(max ?? Infinity),
    max: number()
      .nullable()
      .test("range", t("utils.form.maxLessThanMin"), (value, context) => {
        if (isNil(value) || isNil(context.parent.min)) {
          return true;
        }
        return value > context.parent.min;
      })
      .min(min ?? -Infinity)
      .max(max ?? Infinity),
  });

  const validate = (range: NumericalRangeValue): boolean => {
    try {
      rangeSchema.validateSync(
        { min: range[0], max: range[1] },
        { abortEarly: false }
      );
      setErrors(undefined);
      return true;
    } catch (error) {
      const validationErrors = yupToFormErrors<RangeFields>(error);
      setErrors(validationErrors);
      return false;
    }
  };

  const onChangeValidate = (val: number | undefined, index: 0 | 1): void => {
    const rangeValue: NumericalRangeValue = [
      index === 0 ? val : value[0],
      index === 1 ? val : value[1],
    ];
    validate(rangeValue);
    onChange(rangeValue);
  };

  return (
    <div className={classes("flex pt-2", className)}>
      <NumberInput
        label={t("components.filters.NumericalRange.min", {
          units: t(`utils.units.${units}`),
        })}
        value={value[0]}
        onChange={(val) => onChangeValidate(val, 0)}
        min={min}
        max={max}
        error={errors?.min}
      />
      <NumberInput
        label={t("components.filters.NumericalRange.max", {
          units: t(`utils.units.${units}`),
        })}
        value={value[1]}
        onChange={(val) => onChangeValidate(val, 1)}
        min={min}
        max={max}
        error={errors?.max}
      />
    </div>
  );
};
