/* eslint-disable unicorn/no-null */
import {
  dateRangeToReadableString,
  entitiesToReadableString,
  numericalRangeToReadableString,
} from "./filter";
import { DateTime } from "luxon";
import i18n from "portal/i18n";
import timezoneMock from "timezone-mock";

describe("filters", () => {
  beforeAll(() => {
    timezoneMock.register("US/Pacific");
  });
  afterAll(() => {
    timezoneMock.unregister();
  });
  const { t } = i18n;
  const mockStartDay = DateTime.fromObject(
    {
      year: 2022,
      month: 1,
      day: 1,
    },
    {
      zone: "America/Los_Angeles",
    }
  );
  const mockEndDay = DateTime.fromObject(
    {
      year: 2023,
      month: 12,
      day: 31,
    },
    {
      zone: "America/Los_Angeles",
    }
  );
  describe("entitiesToReadableString", () => {
    test("no values", () => {
      const result = entitiesToReadableString([]);
      expect(result).toEqual("");
    });
    test("convert values", () => {
      const result = entitiesToReadableString([
        { id: "1", name: "Corn" },
        { id: "2", name: "Soy" },
      ]);
      expect(result).toEqual("Corn, Soy");
    });
  });
  describe("numericalRangeToReadableString", () => {
    test("no value", () => {
      const result = numericalRangeToReadableString(t, [undefined, undefined]);
      expect(result).toEqual("");
    });
    test("min range only", () => {
      const result = numericalRangeToReadableString(t, [1, undefined]);
      expect(result).toEqual(">= 1");
    });
    test("max range only", () => {
      const result = numericalRangeToReadableString(t, [undefined, 2]);
      expect(result).toEqual("<= 2");
    });
    test("full range", () => {
      const result = numericalRangeToReadableString(t, [1, 2]);
      expect(result).toEqual("1 - 2");
    });
  });
  describe("dateRangeToReadableString", () => {
    test("no value", () => {
      const result = dateRangeToReadableString(t, i18n, undefined);
      expect(result).toEqual("");
    });
    test("null values", () => {
      const result = dateRangeToReadableString(t, i18n, [null, null]);
      expect(result).toEqual("");
    });
    test("min range only", () => {
      const result = dateRangeToReadableString(t, i18n, [mockStartDay, null]);
      expect(result).toEqual(">= 1/1/2022, 12:00:00 AM");
    });
    test("max range only", () => {
      const result = dateRangeToReadableString(t, i18n, [null, mockEndDay]);
      expect(result).toEqual("<= 12/31/2023, 12:00:00 AM");
    });
    test("full range", () => {
      const result = dateRangeToReadableString(t, i18n, [
        mockStartDay,
        mockEndDay,
      ]);
      expect(result).toEqual("1/1/2022, 12:00:00 AM - 12/31/2023, 12:00:00 AM");
    });
  });
});
