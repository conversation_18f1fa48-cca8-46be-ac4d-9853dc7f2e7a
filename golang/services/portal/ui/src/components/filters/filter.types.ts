import { DateRange } from "@mui/x-date-pickers-pro";
import { DateTime } from "luxon";

export interface Entity {
  id: string;
  name: string;
}

export type NumericalRangeValue = [
  min: number | undefined,
  max: number | undefined
];

export type DateRangeValue = DateRange<DateTime> | undefined;

export type DateRangeValueSerialized = [
  string | null | undefined,
  string | null | undefined
];

export interface Filter<T> {
  name: string;
  value?: T;
  options?: T[];
  areOptionsLoading?: boolean;
}
