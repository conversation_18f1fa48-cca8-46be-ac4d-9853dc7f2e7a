import { CircularProgress, Dialog } from "@mui/material";
import { Image } from "./Image";
import { skipToken } from "@reduxjs/toolkit/query";
import { splitS3Url } from "portal/utils/images";
import { ThumbnailImageItem } from "./ThumbnailImage";
import { useGetFullImageQuery } from "portal/state/imageServiceApi";
import React, { FC } from "react";

interface ExpandedImageProps {
  open: boolean;
  onClose: () => void;
  image?: ThumbnailImageItem;
}
export const ExpandedImage: FC<ExpandedImageProps> = ({
  open,
  onClose,
  image,
}) => {
  const {
    currentData: fetchedImageSource,
    isLoading,
    error,
  } = useGetFullImageQuery(open && image ? splitS3Url(image.url) : skipToken, {
    refetchOnMountOrArgChange: true,
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth={false}>
      {fetchedImageSource ? (
        <Image
          className="max-h-[90vh]"
          image={{
            src: fetchedImageSource,
            isSrcLoading: isLoading,
            isSrcError: Boolean(error),
          }}
        />
      ) : (
        <div className="min-w-48 min-h-48 object-cover bg-gray-600 animate-pulse flex items-center justify-center">
          <CircularProgress />
        </div>
      )}
    </Dialog>
  );
};
