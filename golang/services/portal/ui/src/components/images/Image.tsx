import { ButtonBase, Tooltip } from "@mui/material";
import { classes } from "portal/theme/theme";
import { IconImage } from "./IconImage";
import { useDistinctClickTypes } from "portal/utils/hooks/useDistinctClickTypes";
import BrokenImageIcon from "@mui/icons-material/BrokenImage";
import ImageNotSupportedIcon from "@mui/icons-material/ImageNotSupported";
import React, { FunctionComponent, useCallback, useState } from "react";

export interface ImageProperties {
  id?: string;
  alt?: string;
  src?: string;
  isSrcLoading?: boolean;
  isSrcError?: boolean;
  tooltip?: string;
  onClick?: () => void;
  onDoubleClick?: () => void;
  renderOverlay?: () => React.ReactNode;
}
interface ImageProps {
  image?: ImageProperties;
  onImageLoaded?: (id?: string) => void;
  className?: string;
}

enum LoadingState {
  NOT_LOADED,
  SUCCESS,
  ERROR,
}
export const Image: FunctionComponent<ImageProps> = ({
  image,
  onImageLoaded,
  className,
}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>(
    LoadingState.NOT_LOADED
  );

  // reset loading state when source on an image changes
  const [prevSrc, setPrevSrc] = useState<string | undefined>();
  if (image?.src !== prevSrc) {
    setLoadingState(LoadingState.NOT_LOADED);
    setPrevSrc(image?.src);
  }

  const onLoad = useCallback(
    (success: boolean, id?: string) => {
      setLoadingState(success ? LoadingState.SUCCESS : LoadingState.ERROR);
      onImageLoaded?.(id);
    },
    [onImageLoaded]
  );

  const clickHandlers = useDistinctClickTypes(
    image?.onClick,
    image?.onDoubleClick,
    image?.onDoubleClick
  );

  if (!image) {
    return (
      <IconImage
        icon={(className) => <ImageNotSupportedIcon className={className} />}
      />
    );
  }

  if (loadingState === LoadingState.ERROR || image.isSrcError) {
    if (image.isSrcError) {
      console.error("image srcError", image);
    }
    return (
      <IconImage
        icon={(className) => <BrokenImageIcon className={className} />}
      />
    );
  }

  const { id, alt, src, tooltip, renderOverlay } = image;
  return (
    <>
      <Tooltip
        title={
          tooltip ? (
            <div className="whitespace-pre-line">{tooltip}</div>
          ) : undefined
        }
        placement="bottom"
      >
        <ButtonBase {...clickHandlers} className="w-full h-full p-0">
          {src && (
            <img
              className={classes(
                "object-contain w-full h-full relative",
                className
              )}
              src={src}
              alt={alt}
              loading="lazy"
              onLoad={() => {
                onLoad(true, id);
              }}
              onError={() => {
                console.error("<img> onError", image);
                onLoad(false, id);
              }}
            />
          )}
        </ButtonBase>
      </Tooltip>

      {loadingState === LoadingState.NOT_LOADED || image.isSrcLoading ? (
        <div className="absolute inset-0 top-0 right-0 object-cover bg-gray-600 animate-pulse"></div>
      ) : undefined}
      {renderOverlay?.()}
    </>
  );
};
