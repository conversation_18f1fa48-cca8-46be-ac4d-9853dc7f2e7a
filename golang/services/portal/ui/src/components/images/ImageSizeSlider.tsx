import { classes } from "portal/theme/theme";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lide<PERSON>, SvgIcon, Tooltip } from "@mui/material";
import { titleCase } from "portal/utils/strings";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import DecreaseImageIcon from "portal/images/decrease_image.svg?react";
import IncreaseImageIcon from "@mui/icons-material/AddPhotoAlternate";
import React, {
  FunctionComponent,
  PropsWithChildren,
  useCallback,
} from "react";

interface ImageSizeSliderProps {
  className?: string;
  value: number;
  onChange: (size: number) => void;
  max: number;
  min: number;
}
export const ImageSizeSlider: FunctionComponent<ImageSizeSliderProps> = ({
  className,
  value,
  onChange,
  max,
  min,
}) => {
  const { t } = useTranslation();
  const onValueChange = (_: Event, value: number | number[]): void => {
    onChange(value as number);
  };
  const step = 10;

  const onDecrease = useCallback(() => {
    onChange(Math.max(value - step, min));
  }, [min, onChange, value]);

  const onIncrease = useCallback(() => {
    onChange(Math.min(value + step, max));
  }, [max, onChange, value]);

  return (
    <div>
      <div className="hidden xl:block">
        <div className={clsx("flex items-center justify-end gap-1", className)}>
          <ActionIconButton
            tooltip={titleCase(t("components.images.ImageSizeSlider.smaller"))}
            onClick={onDecrease}
          >
            <SvgIcon className="text-lg">
              <DecreaseImageIcon />
            </SvgIcon>
          </ActionIconButton>
          <Slider
            size="small"
            max={max}
            min={min}
            value={value}
            step={step}
            onChange={onValueChange}
          />
          <ActionIconButton
            tooltip={titleCase(t("components.images.ImageSizeSlider.larger"))}
            onClick={onIncrease}
          >
            <IncreaseImageIcon className="text-lg text-inherit" />
          </ActionIconButton>
        </div>
      </div>
      <div className="block xl:hidden">
        <div className="flex text-white items-center flex-wrap">
          <p className="pl-2">{t("components.images.ImageSizeSlider.label")}</p>
          <IconButton
            size="large"
            className="text-inherit outline-1 outline-white"
            onClick={onDecrease}
          >
            <SvgIcon className="text-xl">
              <DecreaseImageIcon />
            </SvgIcon>
          </IconButton>

          <IconButton
            size="large"
            className="text-inherit outline-1 outline-white"
            onClick={onIncrease}
          >
            <IncreaseImageIcon />
          </IconButton>
        </div>
      </div>
    </div>
  );
};

interface ActionIconButtonProps extends PropsWithChildren {
  className?: string;
  tooltip: string;
  disabled?: boolean;
  onClick: () => void;
}
const ActionIconButton: FunctionComponent<ActionIconButtonProps> = ({
  className,
  tooltip,
  disabled,
  onClick,
  children,
}) => {
  const defaultClassnames = "w-6 h-6 text-inherit";
  return (
    <Tooltip
      title={tooltip}
      className={disabled ? undefined : "cursor-pointer"}
    >
      <span>
        <IconButton
          className={classes(className, defaultClassnames)}
          disabled={disabled}
          onClick={onClick}
        >
          {children}
        </IconButton>
      </span>
    </Tooltip>
  );
};
