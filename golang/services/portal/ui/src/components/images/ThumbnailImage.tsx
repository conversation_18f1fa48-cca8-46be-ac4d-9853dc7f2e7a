import {
  CropThumbnailRequest,
  ImageOp,
  useGetCropThumbnailQuery,
} from "portal/state/imageServiceApi";
import { Image, ImageProperties } from "./Image";
import { skipToken } from "@reduxjs/toolkit/query";
import { splitS3Url } from "portal/utils/images";
import React, { FunctionComponent } from "react";

export type ThumbnailImageItem = ImageProperties &
  Omit<CropThumbnailRequest, "bucket" | "key" | "op"> & {
    url: string;
  };

interface ThumbnailImageProps {
  image?: ThumbnailImageItem;
  onImageLoaded?: (id?: string) => void;
  className?: string;
}

export const assembleImageRequestParameters = ({
  url,
  x,
  y,
  width,
  height,
  fill,
}: Pick<
  ThumbnailImageItem,
  "url" | "x" | "y" | "width" | "height" | "fill"
>): CropThumbnailRequest => {
  const { bucket, key } = splitS3Url(url);
  return {
    op: ImageOp.CROP,
    bucket,
    key,
    x,
    y,
    width,
    height,
    fill: fill ?? "#000000",
  };
};

export const ThumbnailImage: FunctionComponent<ThumbnailImageProps> = ({
  image,
  onImageLoaded,
  className,
}) => {
  const {
    data: fetchedImageSource,
    isLoading: isFetchingImageSource,
    error: fetchingImageSourceError,
  } = useGetCropThumbnailQuery(
    image ? assembleImageRequestParameters(image) : skipToken
  );

  return (
    <Image
      image={
        image
          ? {
              ...image,
              src: fetchedImageSource,
              isSrcLoading: isFetchingImageSource,
              isSrcError: Boolean(fetchingImageSourceError),
            }
          : undefined
      }
      onImageLoaded={onImageLoaded}
      className={className}
    />
  );
};
