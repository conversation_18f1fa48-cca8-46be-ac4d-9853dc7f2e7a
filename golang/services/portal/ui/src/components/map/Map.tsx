import { BlockResponse } from "protos/portal/spatial";
import { Bounds } from "common/utils/geo";
import { buildPermission } from "portal/utils/auth";
import { Card, CircularProgress, Tooltip } from "@mui/material";
import { classes } from "portal/theme/theme";
import { debounce } from "portal/utils/timing";
import {
  DEFAULT_MAP_FILTERS,
  FilterData,
  getMapboxDimensions,
  getSafePadding,
} from "portal/utils/map";
import {
  determineBoundsCenter,
  DRAW_STYLE,
  FACILITIES,
  isMapRef,
  mergeBounds,
  Point,
  toLngLatBounds,
} from "portal/utils/geo";
import { entries } from "portal/utils/objects";
import { FeatureFlag, useFeatureFlag } from "portal/utils/hooks/useFeatureFlag";
import { FilterControl } from "./filters/FilterControl";
import { FilterPane } from "./filters/FilterPane";
import { formatMeasurement } from "../measurement/formatters";
import { HealthLog } from "protos/portal/health";
import { HeatmapControl } from "./heatmaps/HeatmapControl";
import { HeatmapLegend } from "./heatmaps/HeatmapLegend";
import { HeatmapPane } from "./heatmaps/HeatmapPane";
import { HISTORY_STATUS_LOADING, useMapHistory } from "./useMapHistory";
import {
  InteractionLayer,
  isPortalFeature,
  MapImageId,
  MapImageResolver,
} from "common/components/map/layers/types";
import { isEqual } from "portal/utils/equality";
import { isUndefined } from "portal/utils/identity";
import {
  LOCALSTORAGE_MAP_FILTERS,
  LOCALSTORAGE_MAP_FULLSCREEN,
} from "portal/utils/localStorage";
import { MeasureControl } from "./draw/MeasureControl";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { RobotSummaryResponse } from "protos/portal/robots";
import { SPATIAL_DEFAULT } from "portal/utils/spatialMetrics";
import { useAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import { useControlledState } from "portal/utils/hooks/useControlledState";
import { useLocalStorage } from "@uidotdev/usehooks";
import { useMapBlocks } from "./useMapBlocks";
import { useFacilities as useMapFacilities } from "./useMapFacilities";
import { useMapRobots } from "./useMapRobots";
import { useSelf } from "portal/state/store";
import { useStableObject } from "portal/utils/hooks/useStable";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExitOutlined";
import FullscreenIcon from "@mui/icons-material/FullscreenOutlined";
import MapBox, {
  GeolocateControl,
  MapboxGeoJSONFeature,
  MapRef,
  NavigationControl,
  PaddingOptions,
} from "react-map-gl";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import mapboxgl from "mapbox-gl";
import React, {
  FunctionComponent,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import ResetBoundsIcon from "@mui/icons-material/CenterFocusStrongOutlined";

const FOCUSED_ZOOM = 12;
const CARBON_HQ = FACILITIES[0];
const EASTERN_WASHINGTON: Point = [
  120.009_048_324_219_38, 46.665_038_285_723_55,
];
const DEFAULT_FOCUS: Point =
  CARBON_HQ?.latitude && CARBON_HQ.longitude
    ? [CARBON_HQ.longitude, CARBON_HQ.latitude]
    : EASTERN_WASHINGTON;

interface Props {
  allowBorders?: boolean /* Shows lines around laserweeder geospatial blocks when enabled */;
  allowEmpty?: boolean /* if map is allowed to have no data */;
  animationDuration?: number /* How long the animation takes */;
  blocks?: BlockResponse[] /* Laserweeder geospatial data */;
  boundsOffset?:
    | PaddingOptions
    | number /* Extra padding around bounds calculated by data */;
  className?: string;
  emptyErrorText?: string /* Text shown when the map has no data and allowEmpty is false */;
  extraControls?: ReactNode;
  extraBounds?: Bounds /* additional bounds to consider when deciding where to auto-center the map */;
  hideFullscreen?: boolean;
  hideHoverInfo?: boolean;
  hideMeasure?: boolean;
  hideRobots?: boolean;
  history?: HealthLog[];
  imageConfig?: { resolver: MapImageResolver; idsToLoad: MapImageId[] };
  interactionLayers?: readonly InteractionLayer[] /* way to control layers you pass into the map */;
  isAutoPanning?: boolean /* When controlled, the consuming component decides whether the map should decide where to center the map */;
  isAutoZooming?: boolean /* When controlled, the consuming component decides whether map should decide the bounds and zoom level */;
  legendClassName?: string;
  loading?: boolean;
  robots?: RobotSummaryResponse[];
  onRobotClick?: (serial: string) => void;
  focusedRobotSerial?: string /* When set, the map focuses only on the robot and doesn't factor in bounding to show all the other data */;
  setIsAutoPanning?: (isAutoPanning: boolean) => void;
  setIsAutoZooming?: (isAutoZooming: boolean) => void;
  includeFacilityBounds?: boolean;
}

const _Map: FunctionComponent<Props> = ({
  allowBorders = false,
  allowEmpty,
  animationDuration = 0, // animation turned off by default
  blocks,
  boundsOffset = 100,
  className,
  emptyErrorText,
  extraControls,
  extraBounds,
  hideFullscreen = false,
  hideHoverInfo = false,
  hideMeasure = false,
  hideRobots = false,
  history,
  imageConfig,
  interactionLayers,
  isAutoPanning: externalIsAutoPanning,
  isAutoZooming: externalIsAutoZooming,
  legendClassName,
  loading = false,
  robots,
  onRobotClick,
  focusedRobotSerial,
  setIsAutoPanning: externalSetIsAutoPanning,
  setIsAutoZooming: externalSetIsAutoZooming,
  includeFacilityBounds = false,
}) => {
  const { t, i18n } = useTranslation();
  const { measurementSystem } = useSelf();
  const { isEnabled: hasSpatial } = useFeatureFlag(FeatureFlag.SPATIAL);

  const [isAutoPanning, setIsAutoPanning] = useControlledState<boolean>(
    externalIsAutoPanning,
    externalSetIsAutoPanning,
    true
  );
  const [isAutoZooming, setIsAutoZooming] = useControlledState<boolean>(
    externalIsAutoZooming,
    externalSetIsAutoZooming,
    true
  );

  const canReadInternalMetrics = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.metrics_internal,
      PermissionDomain.all
    ),
  ]);
  const hasHistory = Array.isArray(history) && history.length > 0;
  const hasBlocks = Array.isArray(blocks) && blocks.length > 0;

  // map state
  const map = useRef<MapRef | null>(null);
  const mapWrapper = useRef<HTMLDivElement | null>(null);
  const [onRenderMap, setOnRenderMap] = useState<mapboxgl.Map>();
  const [isInitialized, setInitialized] = useState<boolean>(false);
  const [draw] = useState<MapboxDraw>(
    new MapboxDraw({ displayControlsDefault: false, styles: DRAW_STYLE })
  );

  // Latch full-screen state on mount so that localStorage change events from
  // other tabs don't affect this one.
  const [isDefaultFullscreen, setDefaultFullscreen] = useLocalStorage<boolean>(
    LOCALSTORAGE_MAP_FULLSCREEN,
    false
  );
  const [isFullscreen, setCurrentlyFullscreen] =
    useState<boolean>(isDefaultFullscreen);
  const setFullscreen = (fullscreen: boolean): void => {
    setDefaultFullscreen(fullscreen);
    setCurrentlyFullscreen(fullscreen);
  };

  // filters
  const [isFilterPaneOpen, setFilterPaneOpen] = useState<boolean>(false);
  const [filters, setFilters] = useLocalStorage<FilterData<boolean>>(
    LOCALSTORAGE_MAP_FILTERS,
    DEFAULT_MAP_FILTERS
  );

  // heatmap
  const [isRelative, setRelative] = useState<boolean>(true);
  const [selectedMetricId, setSelectedMetricId] = useState<string>(
    SPATIAL_DEFAULT.id
  );
  const [isHeatmapPaneOpen, setHeatmapPaneOpen] = useState<boolean>(false);
  const [hoverInfo, setHoverInfo] = useState<
    { feature: MapboxGeoJSONFeature; x: number; y: number } | undefined
  >();
  const [clickInfo, setClickInfo] = useState<
    { feature: MapboxGeoJSONFeature; x: number; y: number } | undefined
  >();
  const [touchStart, setTouchStart] = useState<[number, number] | undefined>(
    undefined
  );
  const [touchEnd, setTouchEnd] = useState<[number, number] | undefined>(
    undefined
  );

  // get history data
  const {
    lineSource,
    lineLayers,
    bounds: historyBounds,
    historyStatus,
    interactiveLayerIds: interactiveHistoryIds,
  } = useMapHistory(history, allowBorders);

  // get history data
  const {
    blockSource,
    blockLayers,
    bounds: blockBounds,
    historyStatus: blockStatus,
    interactiveLayerIds: interactiveBlockIds,
  } = useMapBlocks(blocks, selectedMetricId, isRelative, allowBorders);

  // get robot data
  const {
    markers: robotMarkers,
    focus: robotFocus,
    bounds: robotBounds,
  } = useMapRobots(robots, filters, focusedRobotSerial, onRobotClick);

  // get facility data
  const {
    markers: facilityMarkers,
    focus: facilityFocus,
    bounds: facilityBounds,
  } = useMapFacilities(filters);

  // merge focus
  const focus = useStableObject<Point>(
    robotFocus ?? facilityFocus ?? DEFAULT_FOCUS
  );

  // merge bounds and fit map
  const bounds = useStableObject(
    useMemo<Bounds | undefined>(() => {
      let bounds: Bounds | undefined;
      // history
      bounds = mergeBounds(bounds, historyBounds);
      // blocks
      bounds = mergeBounds(bounds, blockBounds);
      // robots
      if (!hideRobots) {
        bounds = mergeBounds(bounds, robotBounds);
      }
      // facilities
      if (includeFacilityBounds) {
        bounds = mergeBounds(bounds, facilityBounds);
      }
      // extra bounds provided by caller
      if (extraBounds) {
        bounds = mergeBounds(bounds, extraBounds);
      }
      return bounds;
    }, [
      facilityBounds,
      historyBounds,
      blockBounds,
      hideRobots,
      includeFacilityBounds,
      robotBounds,
      extraBounds,
    ])
  );

  // prefer the MapRef, but in the rare conditions where we haven't gotten it yet, fall back to the map reference that is provided by `onRender`
  const availableMap = useMemo(() => map.current ?? onRenderMap, [onRenderMap]);
  const canResetBounds = useMemo(
    () => Boolean(availableMap && isInitialized && isAutoPanning),
    [isAutoPanning, isInitialized, availableMap]
  );

  const resetBounds = useCallback(
    (input: Bounds | undefined): void => {
      if (!availableMap || !canResetBounds) {
        return;
      }
      const mapDimensions = getMapboxDimensions(
        isMapRef(availableMap) ? availableMap.getMap() : availableMap
      );
      const padding = getSafePadding(mapDimensions, boundsOffset);
      const zoom = isAutoZooming ? { zoom: FOCUSED_ZOOM } : {};
      const duration = { duration: animationDuration };
      if (isUndefined(input)) {
        availableMap.easeTo({ center: focus, ...zoom, ...duration });
        return;
      }
      if (!isAutoZooming) {
        // shift the center to reflect the bounds but don't change zoom
        const boundCenter = determineBoundsCenter(input);
        availableMap.easeTo({ center: boundCenter, ...duration });
        return;
      }

      availableMap.stop();
      const bounds = toLngLatBounds(input);
      if (!bounds) {
        return;
      }
      try {
        // Clear out any nonzero padding on the map to avoid issue where it
        // gets added with the padding that we request.
        availableMap.setPadding({ top: 0, right: 0, bottom: 0, left: 0 });
        availableMap.fitBounds(bounds, {
          padding,
          minZoom: FOCUSED_ZOOM,
          ...duration,
        });
      } catch {
        // errors can happen when padding overflows map dimensions despite our best efforts
        // we try to mitigate that with `getSafePadding` but for unknown reasons it doesn't 100% work
        // as a backup -- fit bounds without padding
        console.warn(
          "Map failed to fitBounds with padding. Falling back to no padding",
          bounds,
          padding
        );
        availableMap.fitBounds(bounds, {
          minZoom: FOCUSED_ZOOM,
          ...duration,
        });
      }
    },
    [
      availableMap,
      canResetBounds,
      boundsOffset,
      animationDuration,
      isAutoZooming,
      focus,
    ]
  );

  // define configurations for quick lookup later
  const interactionConfig: Record<string, InteractionLayer> = {};
  if (interactionLayers) {
    for (const layer of interactionLayers) {
      interactionConfig[layer.id] = layer;
    }
  }

  // update the map when focus or bounds shift
  const [prevBounds, setPrevBounds] = useState<Bounds | undefined>();
  const [prevFocus, setPrevFocus] = useState<Point | undefined>();
  const [prevAutoPan, setPrevAutoPan] = useState<boolean | undefined>();
  const [prevAutoZoom, setPrevAutoZoom] = useState<boolean | undefined>();
  const [prevCanResetBounds, setPrevCanResetBounds] = useState<boolean>(false);
  if (
    !isEqual(bounds, prevBounds) ||
    !isEqual(focus, prevFocus) ||
    !isEqual(isAutoPanning, prevAutoPan) ||
    !isEqual(isAutoZooming, prevAutoZoom) ||
    !isEqual(canResetBounds, prevCanResetBounds)
  ) {
    resetBounds(bounds);
    setPrevBounds(bounds);
    setPrevFocus(focus);
    setPrevAutoPan(isAutoPanning);
    setPrevAutoZoom(isAutoZooming);
    setPrevCanResetBounds(canResetBounds);
  }

  useEffect(() => {
    if (imageConfig && isInitialized && mapWrapper.current) {
      for (const id of imageConfig.idsToLoad) {
        if (!availableMap?.hasImage(id)) {
          availableMap?.loadImage(
            imageConfig.resolver(id),
            (error, imageData) => {
              if (error || !imageData) {
                console.error("failed to load image", error);
                return;
              }
              availableMap.addImage(id, imageData);
            }
          );
        }
      }
    }
  }, [imageConfig, availableMap, isInitialized]);

  useEffect(() => {
    const currentMap = availableMap;
    // handles resizing the map if the flexbox height updates so we don't see map deadspace
    if (isInitialized && mapWrapper.current) {
      const resizer = new ResizeObserver(
        debounce(
          () => {
            currentMap?.resize();
          },
          { wait: 100 }
        )
      );
      resizer.observe(mapWrapper.current);

      return () => {
        resizer.disconnect();
      };
    }
  }, [draw, isInitialized, isFullscreen, availableMap]);

  // hack: pretend this is reactive and update whenever we happen to render
  const mapCurrent = isInitialized && !hideMeasure ? availableMap : undefined;
  useEffect(() => {
    if (!mapCurrent) {
      return;
    }
    mapCurrent.addControl(draw);
    return () => {
      mapCurrent.removeControl(draw);
    };
  }, [draw, mapCurrent]);

  const hasData = robotFocus || hasBlocks || hasHistory;
  const isLoading =
    loading ||
    (hasHistory && historyStatus === HISTORY_STATUS_LOADING) ||
    (hasBlocks && blockStatus === HISTORY_STATUS_LOADING);

  const [cursor, setCursor] = useState<string>("");

  return (
    <Card
      ref={mapWrapper}
      classes={{
        root: classes(
          "w-full relative flex items-stretch justify-items-stretch",
          {
            grayscale: !isLoading && !hasData && !allowEmpty,
          },
          className
        ),
      }}
    >
      {!isLoading && !hasData && !allowEmpty && (
        <div className="absolute inset-0 flex items-center justify-center font-bold z-50 bg-darken-500">
          {emptyErrorText ?? t("components.map.errors.empty")}
        </div>
      )}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center font-bold z-50 bg-darken-500">
          <CircularProgress color="inherit" size="1rem" className="mr-2" />
          {t("components.Loading.placeholder")}
        </div>
      )}
      <MapBox
        ref={map}
        onRender={(event) => {
          if (!isInitialized || !onRenderMap) {
            setOnRenderMap(event.target);
            setInitialized(true);
          }
        }}
        onDrag={() => {
          if (isAutoPanning) {
            setIsAutoPanning(false);
          }
          if (isAutoZooming) {
            setIsAutoZooming(false);
          }
        }}
        onWheel={() => {
          if (isAutoZooming) {
            setIsAutoZooming(false);
          }
        }}
        interactiveLayerIds={[
          // combining these together because block and history logic is complicated to touch rn
          ...(interactionLayers?.map((il) => il.id) ?? []),
          ...(hasBlocks ? interactiveBlockIds : interactiveHistoryIds),
        ]}
        mapboxAccessToken={window._jsenv.REACT_APP_MAPBOX_ACCESS_TOKEN}
        mapStyle="mapbox://styles/carbonrobotics/ckz5r36ej004115p4f2wpxw1b"
        initialViewState={{
          longitude: DEFAULT_FOCUS[0],
          latitude: DEFAULT_FOCUS[1],
          zoom: 5,
        }}
        style={{
          background: "black",
          height: isFullscreen ? "100vh" : "auto",
          width: isFullscreen ? "100vw" : "auto",
          position: isFullscreen ? "fixed" : "relative",
          top: isFullscreen ? 0 : "auto",
          left: isFullscreen ? 0 : "auto",
          zIndex: isFullscreen ? 1499 : 0,
          flexGrow: "1",
        }}
        cursor={cursor}
        onMouseMove={(event) => {
          const {
            features,
            point: { x, y },
          } = event;
          const hoveredFeature = features && features[0];
          setHoverInfo(hoveredFeature && { feature: hoveredFeature, x, y });
          const layerId = features?.[0]?.layer.id;
          if (!layerId || !interactionConfig[layerId]) {
            return;
          }
          if (isPortalFeature(features[0])) {
            interactionConfig[layerId].onMouseMove?.(features[0]);
          }
          if (!interactionConfig[layerId].cursor) {
            return;
          }
          setCursor(interactionConfig[layerId].cursor);
        }}
        onMouseEnter={(event) => {
          const { features } = event;
          const layerId = features?.[0]?.layer.id;
          if (!layerId || !interactionConfig[layerId]) {
            return;
          }
          if (isPortalFeature(features[0])) {
            interactionConfig[layerId].onEnter?.(features[0]);
          }
        }}
        onMouseLeave={(event) => {
          const { features } = event;
          const layerId = features?.[0]?.layer.id;
          if (!layerId || !interactionConfig[layerId]) {
            return;
          }
          if (isPortalFeature(features[0])) {
            interactionConfig[layerId].onLeave?.(features[0]);
          }
          if (!interactionConfig[layerId].cursor) {
            return;
          }
          setCursor("");
        }}
        // handle click
        onClick={(event) => {
          const {
            features,
            point: { x, y },
          } = event;
          const hoveredFeature = features && features[0];
          setClickInfo(
            hoveredFeature ? { feature: hoveredFeature, x, y } : undefined
          );
          const layerId = features?.[0]?.layer.id;
          if (!layerId || !interactionConfig[layerId]) {
            return;
          }
          if (isPortalFeature(features[0])) {
            interactionConfig[layerId].onClick?.(features[0]);
          }
        }}
        onTouchStart={(event) => {
          const firstTouch = event.originalEvent.touches[0];
          if (!firstTouch) {
            return;
          }
          // track touch start so we can simulate clicks because Mapbox doesn't
          // do touch clicks correctly
          const startPosition: [number, number] = [
            firstTouch.clientX,
            firstTouch.clientY,
          ];
          setTouchStart(startPosition);
          setTouchEnd(startPosition);
        }}
        onTouchMove={(event) => {
          const firstTouch = event.originalEvent.touches[0];
          if (!firstTouch) {
            return;
          }
          // update touch position
          const currentPosition: [number, number] = [
            firstTouch.clientX,
            firstTouch.clientY,
          ];
          setTouchEnd(currentPosition);

          // close click info if we're dragging
          if (
            !touchStart ||
            Math.abs(touchStart[0] - currentPosition[0]) > 10 ||
            Math.abs(touchStart[1] - currentPosition[1]) > 10
          ) {
            setClickInfo(undefined);
          }
        }}
        onTouchEnd={(event) => {
          // ignore drags
          if (
            !touchStart ||
            !touchEnd ||
            Math.abs(touchStart[0] - touchEnd[0]) > 10 ||
            Math.abs(touchStart[1] - touchEnd[1]) > 10
          ) {
            return;
          }

          const {
            features,
            point: { x, y },
          } = event;
          const hoveredFeature = features && features[0];

          setClickInfo(
            hoveredFeature ? { feature: hoveredFeature, x, y } : undefined
          );
        }}
      >
        {/* markers */}
        {facilityMarkers}
        {!hideRobots && robotMarkers}

        {/* sources */}
        {blockSource}
        {lineSource}

        {/* layers */}
        {blockLayers}
        {lineLayers}

        {/* popups */}
        {(hoverInfo || clickInfo) &&
          !hideHoverInfo &&
          (() => {
            const info = clickInfo ?? hoverInfo;
            if (!info) {
              return;
            }
            return (
              // not actually interactable, just to prevent map clicks
              // eslint-disable-next-line jsx-a11y/no-static-element-interactions
              <div
                className={classes("fixed bg-darken-700 text-white p-2 z-[3]", {
                  "pointer-events-none": hoverInfo && !clickInfo,
                  "cursor-default": clickInfo && !hoverInfo,
                })}
                style={{
                  left:
                    (mapWrapper.current?.getBoundingClientRect().left ?? 0) +
                    info.x,
                  top:
                    (mapWrapper.current?.getBoundingClientRect().top ?? 0) +
                    info.y,
                }}
                onClick={(event) => event.stopPropagation()}
              >
                <div className="italic text-xs mb-4 whitespace-nowrap">
                  {info.feature.properties?._time}
                </div>
                {entries(info.feature.properties).map(([key, value], index) => {
                  if (key.startsWith("_")) {
                    return;
                  }
                  return (
                    <div
                      className={classes("text-sm whitespace-nowrap", {
                        "font-bold": index === 0,
                      })}
                      key={key}
                    >
                      {key}: {value}
                    </div>
                  );
                })}
                <div className="font-mono text-xs mt-4 whitespace-nowrap">
                  {t("components.map.heatmaps.fields.size", {
                    width: formatMeasurement(
                      t,
                      i18n,
                      measurementSystem,
                      info.feature.properties?._width,
                      "ft"
                    ),
                    length: formatMeasurement(
                      t,
                      i18n,
                      measurementSystem,
                      info.feature.properties?._length,
                      "ft"
                    ),
                    area: `${formatMeasurement(
                      t,
                      i18n,
                      measurementSystem,
                      (info.feature.properties?._width ?? 0) *
                        (info.feature.properties?._length ?? 0),
                      "ft2"
                    )}`,
                  })}
                </div>
                <div className="font-mono text-xs whitespace-nowrap">
                  {t("components.map.heatmaps.fields.location", {
                    latitude: info.feature.properties?._latitude,
                    longitude: info.feature.properties?._longitude,
                  })}
                </div>
                {canReadInternalMetrics && info.feature.properties?._id && (
                  <div className="font-mono text-xs whitespace-nowrap">
                    {t("components.map.heatmaps.fields.block", {
                      block: info.feature.properties._id,
                    })}
                  </div>
                )}
              </div>
            );
          })()}

        {/* controls */}
        <GeolocateControl position="top-left" />
        <NavigationControl position="top-left" />
        <FilterControl
          className="mt-[146px] ml-[10px] print:hidden"
          open={isFilterPaneOpen}
          mapFilters={filters}
          setOpen={(isOpen) => {
            setFilterPaneOpen(isOpen);
            setHeatmapPaneOpen(false);
          }}
        />
        {isFilterPaneOpen && (
          <FilterPane
            filters={filters}
            onClose={() => setFilterPaneOpen(false)}
            onChange={(mapFilters) => setFilters(mapFilters)}
            robots={robots ?? []}
          />
        )}
        {hasSpatial && hasBlocks && (
          <>
            <HeatmapControl
              className="mt-[10px] ml-[10px] print:hidden"
              open={isHeatmapPaneOpen}
              setOpen={(isOpen) => {
                setHeatmapPaneOpen(isOpen);
                setFilterPaneOpen(false);
              }}
              selectedMetricId={selectedMetricId}
            />
            <HeatmapLegend
              className={legendClassName}
              isRelative={isRelative}
              setRelative={setRelative}
              onOpenMenu={
                isHeatmapPaneOpen
                  ? undefined
                  : () => {
                      setHeatmapPaneOpen(true);
                      setFilterPaneOpen(false);
                    }
              }
              selectedMetricId={selectedMetricId}
            />
            {isHeatmapPaneOpen && (
              <HeatmapPane
                onClose={() => setHeatmapPaneOpen(false)}
                selectedMetricId={selectedMetricId}
                setSelectedMetricId={setSelectedMetricId}
              />
            )}
          </>
        )}
        {!hideMeasure && (
          <MeasureControl
            className="mt-[10px] ml-[10px] print:hidden"
            draw={draw}
            map={map.current}
            isMapReady={isInitialized}
          />
        )}
        <Tooltip
          title={t("components.map.bounds.reset")}
          placement="right"
          arrow
        >
          <div
            className={classes(
              "mt-[10px] ml-[10px] print:hidden",
              "mapboxgl-ctrl mapboxgl-ctrl-group w-fit"
            )}
          >
            <button
              onClick={() => {
                setIsAutoPanning(true);
                setIsAutoZooming(true);
                resetBounds(bounds);
              }}
            >
              <ResetBoundsIcon className="p-1 mapboxgl-ctrl-icon" />
            </button>
          </div>
        </Tooltip>
        {!hideFullscreen && (
          <Tooltip
            title={
              isFullscreen
                ? t("utils.actions.exitLong", {
                    subject: t("components.map.fullscreen"),
                  })
                : t("components.map.fullscreen")
            }
            placement="right"
            arrow
          >
            <div
              className={classes(
                "mt-[10px] ml-[10px] print:hidden",
                "mapboxgl-ctrl mapboxgl-ctrl-group w-fit"
              )}
            >
              <button onClick={() => setFullscreen(!isFullscreen)}>
                {isFullscreen ? (
                  <FullscreenExitIcon className="p-1 mapboxgl-ctrl-icon" />
                ) : (
                  <FullscreenIcon className="p-1 mapboxgl-ctrl-icon" />
                )}
              </button>
            </div>
          </Tooltip>
        )}
        {extraControls}
      </MapBox>
    </Card>
  );
};

export const Map = withErrorBoundary(
  { i18nKey: "components.map.errors.failed" },
  _Map
);
