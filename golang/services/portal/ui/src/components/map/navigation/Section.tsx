import { classes } from "portal/theme/theme";
import { Divider } from "@mui/material";
import { SectionHeader } from "./SectionHeader";
import React, { FC, PropsWithChildren, ReactNode, useState } from "react";

interface SectionProps extends PropsWithChildren {
  header: ReactNode;
  startCollapsed?: boolean;
  onExpand?: () => void;
  onCollapse?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  isSelected?: boolean;
  isFocused?: boolean;
  isSubsection?: boolean;
  action?: {
    icon: ReactNode;
    onClick: () => void;
  };
}
export const Section: FC<SectionProps> = ({
  header,
  children,
  startCollapsed,
  onExpand,
  onCollapse,
  isSelected,
  isFocused,
  isSubsection,
  onMouseEnter,
  onMouseLeave,
  action,
}: SectionProps) => {
  const [showingContent, setShowingContent] = useState<boolean>(
    Boolean(!startCollapsed)
  );

  const handleExpanded = (expanded: boolean): void => {
    if (expanded) {
      onExpand?.();
    } else {
      onCollapse?.();
    }
    setShowingContent(expanded);
  };
  return (
    <div className={`flex flex-col`}>
      <SectionHeader
        subSection={isSubsection}
        selected={isSelected}
        focused={isFocused}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        expanded={showingContent}
        setExpanded={(expanded) => handleExpanded(expanded)}
        action={action}
      >
        {header}
      </SectionHeader>
      {showingContent && (
        <div
          className={classes(
            isSubsection && "pl-2",
            "flex flex-col overflow-y-auto"
          )}
        >
          {children}
        </div>
      )}
      <Divider />
    </div>
  );
};
