import { But<PERSON> } from "@mui/material";
import { classes } from "portal/theme/theme";
import React, { FC, PropsWithChildren, ReactNode } from "react";

interface SelectionButtonProps extends PropsWithChildren {
  isSelected: boolean;
  isFocused?: boolean;
  subSection?: boolean;
  onClick: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
}
export const SelectionButton: FC<SelectionButtonProps> = ({
  isSelected,
  isFocused,
  subSection,
  onClick,
  onMouseEnter,
  onMouseLeave,
  startIcon,
  endIcon,
  children,
}) => (
  <Button
    className={classes(
      subSection ? "text-gray-300" : "text-white",
      isFocused && (subSection ? "bg-blue-400" : "bg-slate-300"),
      isFocused && (subSection ? "text-white" : "bg-slate-500"),
      subSection && "pl-4",
      isSelected && (subSection ? "bg-blue-800" : "bg-slate-700"),
      isSelected && subSection && "text-white",
      "justify-start"
    )}
    startIcon={startIcon}
    endIcon={endIcon}
    disableElevation
    onClick={onClick}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
    variant={"text"}
    fullWidth
  >
    {children}
  </Button>
);
