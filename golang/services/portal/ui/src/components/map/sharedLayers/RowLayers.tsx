import { FarmMapData } from "common/utils/farm";
import { FarmRowData } from "portal/utils/rows";
import { Layer, Source } from "react-map-gl";
import { LinePaint } from "mapbox-gl";
import { theme } from "portal/theme/theme";
import { useTranslation } from "react-i18next";
import React, { Fragment, FunctionComponent } from "react";

interface RowLayersProps {
  farmMapData: FarmMapData;
  farmRowData: FarmRowData;
  debug?: boolean;
}

export const RowLayers: FunctionComponent<RowLayersProps> = ({
  farmMapData,
  farmRowData,
  debug = false,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Fragment key={`calculated-rows-${farmMapData.id}`}>
        {Array.from(
          farmRowData.calculatedRowsByFieldId.entries(),
          ([fieldId, rowsCollection]) => (
            <Source
              key={`calculated-rows-${fieldId}`}
              id={`calculated-rows-${fieldId}`}
              type="geojson"
              data={rowsCollection}
            >
              <Layer
                id={`calculated-rows-${fieldId}`}
                type="line"
                paint={calculatedRowLinePaint}
                layout={{
                  "line-cap": "round",
                  "line-join": "round",
                }}
              />
            </Source>
          )
        )}
      </Fragment>

      {debug && (
        <>
          <Fragment key={`planting-headings-${farmMapData.id}`}>
            {Array.from(
              farmMapData.plantingHeadingsByFieldId.entries(),
              ([id, plantingHeading]) => (
                <Source
                  key={`planting-heading-${id}`}
                  id={`planting-heading-${id}`}
                  type="geojson"
                  data={{
                    type: "Feature",
                    properties: {
                      fieldId: id,
                    },
                    geometry: plantingHeading,
                  }}
                >
                  <Layer
                    id={`planting-heading-line-${id}`}
                    type="line"
                    paint={plantingHeadingLinePaint}
                  />
                  <Layer
                    id={`planting-heading-symbol-${id}`}
                    type="symbol"
                    layout={{
                      "symbol-placement": "line",
                      "text-field": `${t(
                        "models.fieldDefinitions.fields.plantingHeading"
                      )} - ${id}`,
                      "text-size": 12,
                      "text-offset": [0, -1],
                      "text-allow-overlap": true,
                    }}
                    paint={{
                      "text-color": theme.colors.carbon.gray.dark,
                      "text-halo-color": "#fff",
                      "text-halo-width": 2,
                    }}
                  />
                </Source>
              )
            )}
          </Fragment>

          <Fragment key={`bounding-boxes-${farmMapData.id}`}>
            {Array.from(
              farmRowData.boundingBoxesByFieldId.entries(),
              ([fieldId, boundingBox]) => (
                <Source
                  key={`bounding-box-${fieldId}`}
                  id={`bounding-box-${fieldId}`}
                  type="geojson"
                  data={{
                    type: "Feature",
                    properties: {
                      fieldId,
                      type: "boundingBox",
                    },
                    geometry: boundingBox,
                  }}
                >
                  <Layer
                    id={`bounding-box-${fieldId}`}
                    type="line"
                    paint={boundingBoxLinePaint}
                    layout={{
                      "line-join": "round",
                    }}
                  />
                  <Layer
                    id={`bounding-box-label-${fieldId}`}
                    type="symbol"
                    layout={{
                      "symbol-placement": "line",
                      "text-field": `${t(
                        "views.farms.zoneTypes.field"
                      )} - ${fieldId}`,
                      "text-size": 12,
                      "text-offset": [0, -1],
                      "text-allow-overlap": true,
                    }}
                    paint={{
                      "text-color": theme.colors.carbon.gray.dark,
                      "text-halo-color": "#fff",
                      "text-halo-width": 2,
                    }}
                  />
                </Source>
              )
            )}
          </Fragment>
        </>
      )}
    </>
  );
};

const calculatedRowLinePaint: LinePaint = {
  "line-color": theme.colors.carbon.orange,
  "line-width": 1.5,
  "line-opacity": 0.8,
};

const plantingHeadingLinePaint: LinePaint = {
  "line-color": theme.colors.carbon.gray.dark,
  "line-width": 2,
  "line-dasharray": [2, 1],
  "line-opacity": 1,
};

const boundingBoxLinePaint: LinePaint = {
  "line-color": theme.colors.carbon.orange,
  "line-width": 2,
  "line-dasharray": [4, 2],
  "line-opacity": 1,
};
