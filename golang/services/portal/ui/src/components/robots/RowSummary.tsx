import { Card, CardContent, Radio, Switch } from "@mui/material";
import { classes, STATUS_TEXT } from "portal/theme/theme";
import { LaserState } from "protos/frontend/laser";
import { useTranslation } from "react-i18next";
import React, { FunctionComponent } from "react";

interface ItemProps {
  laser: LaserState;
}

export const LaserSummary: FunctionComponent<ItemProps> = ({ laser }) => (
  <li
    className={classes("flex items-end", {
      [STATUS_TEXT.RED]: laser.error,
      [STATUS_TEXT.GREEN]: laser.firing,
      "bg-gray-500": !laser.error && !laser.enabled,
    })}
  >
    <span className="text-white font-bold text-center w-100">
      {laser.laserDescriptor?.laserId}
    </span>
  </li>
);

/**
 * Row Summary component
 * Pass in laser status
 * Pass in onSelect if it should be selectable
 * Pass in onToggle if you should be able to toggle laser status
 */
interface Props {
  isEnabled?: boolean;
  isSelected?: boolean;
  lasers: LaserState[];
  onSelect?: () => void;
  onToggle?: (isEnabled: boolean) => void;
}

export const RowSummary: FunctionComponent<Props> = ({
  isEnabled = false,
  isSelected = false,
  lasers,
  onSelect,
  onToggle,
}) => {
  const { t } = useTranslation();
  return (
    <Card onClick={() => onSelect?.()}>
      <CardContent
        className={classes("overflow-hidden flex items-center justify-center", {
          "cursor-pointer": onSelect,
        })}
      >
        <ul
          className={classes("flex absolute top-0 left-0 w-full h-full", {
            "opacity-75": onToggle && !isEnabled,
          })}
        >
          {lasers.map((laser) => (
            <LaserSummary laser={laser} key={laser.laserDescriptor?.laserId} />
          ))}
        </ul>
        <div className="rounded bg-gray-600 text-white p-8">
          <span className="mb-4">
            {t("views.fleet.robots.summary.lasers.row", {
              row: lasers[0]?.laserDescriptor?.rowNumber,
            })}
          </span>
          {onToggle && (
            <Switch
              checked={isEnabled}
              onChange={(event, isEnabled) => onToggle(isEnabled)}
            />
          )}
          {onSelect && (
            <Radio checked={isSelected} onChange={() => onSelect()} />
          )}
        </div>
      </CardContent>
    </Card>
  );
};
