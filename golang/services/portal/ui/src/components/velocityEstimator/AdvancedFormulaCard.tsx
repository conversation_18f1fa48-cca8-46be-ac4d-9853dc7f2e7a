import {
  CarbonUnit,
  convert,
  isSpeed,
  isUnitNumberType,
  preferredVehicleSpeedUnit,
  UnitNumberType,
} from "portal/utils/units/units";
import { Card, Grid, InputAdornment, Typography } from "@mui/material";
import { formatMeasurement } from "../measurement/formatters";
import { GRID_ROW } from "./FormulaCard";
import { NumberInput } from "../NumberInput";
import { numberOrEmptyString } from "portal/utils/forms";
import { SMALL_TEXT_FIELD_DARK } from "portal/theme/theme";
import { SmoothingInputSlider } from "./SmoothingInputSlider";
import { TFunction } from "i18next";
import { TVEProfile } from "protos/target_velocity_estimator/target_velocity_estimator";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import i18n from "portal/i18n";
import React, { Dispatch, FunctionComponent, SetStateAction } from "react";

type NumberProps<T> = {
  [P in keyof T]: T[P] extends number ? P : never;
}[keyof T];

const getFormula = (
  t: TFunction
): Array<{
  label: string;
  description: string;
  key: NumberProps<TVEProfile>;
  min?: number;
  max?: number;
  isSlider?: boolean;
  // the implied unit in which the value is stored
  internalUnit: CarbonUnit;
  // if defined, displays the value rounded to this many decimal places rather
  // than exactly
  roundDisplayTo?: number;
}> => [
  {
    label: t("components.velocityEstimator.configs.cruiseOffsetPercent.label"),
    key: "cruiseOffsetPercent",
    description: t(
      "components.velocityEstimator.configs.cruiseOffsetPercent.description"
    ),
    max: 100,
    internalUnit: UnitNumberType.PERCENT,
  },
  {
    label: t("components.velocityEstimator.configs.increaseSmoothing.label"),
    key: "increaseSmoothing",
    description: t(
      "components.velocityEstimator.configs.increaseSmoothing.description"
    ),
    max: 100,
    isSlider: true,
    internalUnit: UnitNumberType.PERCENT,
  },
  {
    label: t("components.velocityEstimator.configs.decreaseSmoothing.label"),
    key: "decreaseSmoothing",
    description: t(
      "components.velocityEstimator.configs.decreaseSmoothing.description"
    ),
    max: 100,
    isSlider: true,
    internalUnit: UnitNumberType.PERCENT,
  },
  {
    label: t("components.velocityEstimator.configs.minVelMph.label"),
    key: "minVelMph",
    description: t(
      "components.velocityEstimator.configs.minVelMph.description"
    ),
    internalUnit: "mph",
    roundDisplayTo: 2,
  },
  {
    label: t("components.velocityEstimator.configs.maxVelMph.label"),
    key: "maxVelMph",
    description: t(
      "components.velocityEstimator.configs.maxVelMph.description"
    ),
    internalUnit: "mph",
    roundDisplayTo: 2,
  },
];

interface Props {
  tveProfile: TVEProfile;
  readOnly?: boolean;
  setUpdatedTve: Dispatch<SetStateAction<TVEProfile | undefined>>;
}

export const AdvancedFormulaCard: FunctionComponent<Props> = ({
  tveProfile,
  readOnly = false,
  setUpdatedTve,
}) => {
  const { t } = useTranslation();
  const localSystem = useSelf().measurementSystem;

  return (
    <Card className="p-8 my-4 flex-shrink-0">
      {/* Header */}
      <Typography variant="h6" className="mb-4">
        {t("components.velocityEstimator.configs.card.advancedFormulaTitle")}
      </Typography>

      {/* Form */}
      <div className="flex flex-col w-full items-center justify-center gap-8">
        {getFormula(t).map(
          ({
            label,
            description,
            key,
            min = 0,
            max = Infinity,
            isSlider = false,
            internalUnit,
            roundDisplayTo,
          }) => {
            let toUnits: CarbonUnit | undefined;

            if (isSpeed(internalUnit)) {
              toUnits = preferredVehicleSpeedUnit[localSystem];
            }

            const displayMeasurement = formatMeasurement(
              t,
              i18n,
              localSystem,
              tveProfile[key],
              internalUnit,
              { toUnits, decimalPlaces: roundDisplayTo }
            );

            const handleChange: (val: number | undefined) => void = (val) => {
              const value = numberOrEmptyString(val);
              let updatedInternalValue: number | "";

              if (Number.isNaN(value) || value === "") {
                // current value doesn't parse as an integer, don't update
                // backing store
                return;
              } else {
                switch (internalUnit) {
                  case UnitNumberType.COUNT: {
                    updatedInternalValue = value;
                    break;
                  }
                  case UnitNumberType.PERCENT: {
                    updatedInternalValue = value / 100;
                    break;
                  }
                  default: {
                    // convert the displayed unit back to the expected internal unit
                    if (
                      displayMeasurement.carbonUnits === undefined ||
                      isUnitNumberType(displayMeasurement.carbonUnits)
                    ) {
                      // this shouldn't happen, make noise about it if it
                      // does and fall back to not changing the value
                      console.error(
                        `displayed unit was unexpected '${displayMeasurement.carbonUnits}' when trying to convert back to internal unit '${internalUnit}'; assuming identity conversion`
                      );
                      updatedInternalValue = value;
                    } else {
                      updatedInternalValue = convert(value)
                        .from(displayMeasurement.carbonUnits)
                        .to(internalUnit);
                    }
                  }
                }
              }

              setUpdatedTve({
                ...tveProfile,
                [key]: updatedInternalValue,
              });
            };

            return isSlider ? (
              <SmoothingInputSlider
                key={key}
                label={label}
                description={description}
                field={key}
                tveProfile={tveProfile}
                setUpdatedTve={setUpdatedTve}
                disabled={readOnly}
              />
            ) : (
              <Grid {...GRID_ROW} key={key}>
                <Grid item xs={12} md={2} className="font-bold" order={1}>
                  {label}
                </Grid>
                <Grid
                  item
                  xs={12}
                  md={2}
                  className="flex flex-col items-center"
                  order={{ xs: 3, md: 2 }}
                >
                  <NumberInput
                    {...SMALL_TEXT_FIELD_DARK}
                    label=""
                    value={Number.parseFloat(displayMeasurement.value)}
                    min={min}
                    max={max}
                    onChange={handleChange}
                    textFieldProps={{
                      disabled: readOnly,
                      className: "w-full text-center",
                      InputProps: {
                        ...SMALL_TEXT_FIELD_DARK.InputProps,
                        endAdornment: (
                          <InputAdornment position="end">
                            {displayMeasurement.units}
                          </InputAdornment>
                        ),
                      },
                    }}
                  />
                </Grid>
                <Grid
                  item
                  xs={12}
                  md={8}
                  className="text-sm"
                  order={{ xs: 2, md: 3 }}
                >
                  {description}
                </Grid>
              </Grid>
            );
          }
        )}
      </div>
    </Card>
  );
};
