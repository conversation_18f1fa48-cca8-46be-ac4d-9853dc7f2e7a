import { BLUE_LOADING_BUTTON, TEXT_FIELD_DARK } from "portal/theme/theme";
import { buildPermission } from "portal/utils/auth";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import { capitalize, titleCase } from "portal/utils/strings";
import { Field, Form, Formik } from "formik";
import { GlobalHotKeys } from "react-hotkeys";
import { LoadingButton } from "@mui/lab";
import { object, string } from "yup";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { TextField } from "formik-mui";
import { TVEProfile } from "protos/target_velocity_estimator/target_velocity_estimator";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useNavigate } from "react-router-dom";
import {
  useSetGlobalTargetVelocityEstimatorMutation,
  useSetTargetVelocityEstimatorMutation,
} from "portal/state/portalApi";
import { useTranslation } from "react-i18next";
import { v4 as uuid } from "uuid";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import AddIcon from "@mui/icons-material/AddOutlined";
import React, { FunctionComponent, useState } from "react";

interface Props {
  hotkey?: string;
  serial?: string;
  basePath: string;
  useSetMutation:
    | typeof useSetTargetVelocityEstimatorMutation
    | typeof useSetGlobalTargetVelocityEstimatorMutation;
  templates: TVEProfile[];
}
const _NewVelocityEstimator: FunctionComponent<Props> = ({
  hotkey,
  serial,
  basePath,
  useSetMutation,
  templates,
}) => {
  const navigate = useNavigate();

  const { t } = useTranslation();

  const [isOpen, setOpen] = useState<boolean>(false);

  const [setVelocityEstimator] = useMutationPopups(useSetMutation(), {
    success: capitalize(
      t("utils.actions.createdLong", {
        subject: t("models.velocityEstimators.velocityEstimator_one"),
      })
    ),
  });

  return (
    <>
      {hotkey && (
        <GlobalHotKeys
          keyMap={{
            NEW_TARGET_VELOCITY_ESTIMATOR: {
              name: titleCase(
                t("utils.actions.newLong", {
                  subject: t("models.velocityEstimators.velocityEstimator_one"),
                })
              ),
              action: "keyup",
              sequence: hotkey,
            },
          }}
          handlers={{
            NEW_TARGET_VELOCITY_ESTIMATOR: () => setOpen(true),
          }}
        />
      )}
      <Button
        className="text-white"
        startIcon={<AddIcon />}
        onClick={() => setOpen(true)}
      >
        <span className="hidden sm:inline">
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.velocityEstimators.velocityEstimator_one"),
            })
          )}
        </span>
        <span className="sm:hidden">{t("utils.actions.new")}</span>
      </Button>
      <Dialog open={isOpen} onClose={() => setOpen(false)}>
        <DialogTitle>
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.velocityEstimators.velocityEstimator_one"),
            })
          )}
        </DialogTitle>
        <Formik
          enableReinitialize
          initialValues={{
            name: "",
            templateUuid: templates[0]?.id,
          }}
          validationSchema={object({
            name: string().required(),
            templateUuid: string().uuid().required(),
          })}
          onSubmit={async ({ name, templateUuid }) => {
            const id = uuid();
            const template = templates.find((t) => t.id === templateUuid);

            if (!template) {
              return;
            }

            await setVelocityEstimator({
              serial,
              targetVelocityEstimator: TVEProfile.fromPartial({
                ...template,
                id,
                name,
                protected: false,
              }),
            });
            navigate(`${basePath}/${id}`);
          }}
        >
          {({ values, submitForm, handleChange, isSubmitting, dirty }) => (
            <Form>
              <DialogContent className="flex flex-col gap-4 pt-2">
                <Field
                  {...TEXT_FIELD_DARK}
                  component={TextField}
                  name="name"
                  label={t("models.velocityEstimators.fields.name")}
                />
                <FormControl fullWidth>
                  <InputLabel id="template-label">
                    {t("utils.form.copyConfigFrom")}
                  </InputLabel>
                  <Select
                    disabled={!templates}
                    labelId="template-label"
                    label={t("utils.form.copyConfigFrom")}
                    id="templateUuid"
                    name="templateUuid"
                    value={values.templateUuid}
                    onChange={handleChange}
                  >
                    {templates.map((template) => (
                      <MenuItem key={template.id} value={template.id}>
                        {template.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </DialogContent>
              <DialogActions>
                <Button
                  variant="text"
                  className="text-white"
                  disabled={isSubmitting}
                  onClick={() => setOpen(false)}
                >
                  {t("utils.actions.cancel")}
                </Button>
                <LoadingButton
                  {...BLUE_LOADING_BUTTON}
                  disabled={!dirty}
                  loading={isSubmitting}
                  onClick={submitForm}
                  startIcon={<AddIcon />}
                >
                  {t("utils.actions.create")}
                </LoadingButton>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
    </>
  );
};

export const NewVelocityEstimator = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.velocity_estimators,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.read,
      PermissionResource.velocity_estimators,
      PermissionDomain.all
    ),
  ],
  _NewVelocityEstimator
);
