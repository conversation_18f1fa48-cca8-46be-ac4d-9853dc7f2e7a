import { buildPermission } from "portal/utils/auth";
import { CarbonDataGrid } from "portal/components/CarbonDataGrid";
import { classes } from "portal/theme/theme";
import { getDefaultTargetVelocityEstimator } from "portal/utils/velocityEstimator";
import { GridColDef } from "@mui/x-data-grid-premium";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { NewVelocityEstimator } from "portal/components/velocityEstimator/NewVelocityEstimator";
import { NoScroll } from "../Page";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { SearchField } from "../header/SearchField";
import { skipToken } from "@reduxjs/toolkit/query";
import { titleCase } from "portal/utils/strings";
import { TVEProfile } from "protos/target_velocity_estimator/target_velocity_estimator";
import {
  useDeleteGlobalTargetVelocityEstimatorMutation,
  useDeleteTargetVelocityEstimatorMutation,
  useGetGlobalTargetVelocityEstimatorQuery,
  useGetRobotQuery,
  useGetTargetVelocityEstimatorQuery,
  useListGlobalTargetVelocityEstimatorsQuery,
  useListTargetVelocityEstimatorsQuery,
  useSetGlobalTargetVelocityEstimatorMutation,
  useSetTargetVelocityEstimatorMutation,
} from "portal/state/portalApi";
import { useFuzzySearch } from "portal/utils/hooks/useFuzzySearch";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { VelocityEstimator } from "portal/components/velocityEstimator/VelocityEstimator";
import { withAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import React, { FunctionComponent } from "react";

const CELL_PADDING_CLASS = "py-4";

const defaultColumn: Partial<GridColDef> = {
  disableColumnMenu: true,
  cellClassName: () => CELL_PADDING_CLASS,
};

interface Props {
  adminEditing?: boolean;
  serial?: string;
  basePath: string;
  useListQuery:
    | typeof useListTargetVelocityEstimatorsQuery
    | typeof useListGlobalTargetVelocityEstimatorsQuery;
  useGetQuery:
    | typeof useGetTargetVelocityEstimatorQuery
    | typeof useGetGlobalTargetVelocityEstimatorQuery;
  useSetMutation:
    | typeof useSetTargetVelocityEstimatorMutation
    | typeof useSetGlobalTargetVelocityEstimatorMutation;
  useDeleteMutation:
    | typeof useDeleteTargetVelocityEstimatorMutation
    | typeof useDeleteGlobalTargetVelocityEstimatorMutation;
}

const _VelocityEstimatorList: FunctionComponent<Props> = ({
  adminEditing = false,
  serial,
  basePath,
  useListQuery,
  useGetQuery,
  useSetMutation,
  useDeleteMutation,
}) => {
  const { t } = useTranslation();

  const { data: summary, isSuccess: isRobotSuccess } = useQueryPopups(
    useGetRobotQuery(serial ? { serial } : skipToken),
    { errorVariant: "warning" }
  );

  const { data: targetVelocityEstimators, isSuccess: hasLoaded } =
    useQueryPopups(useListQuery({ serial }));

  const navigate = useNavigate();
  const { pathname } = useLocation();
  let uuid: string = "";
  if (!pathname.endsWith(basePath)) {
    uuid = pathname.replace(`${basePath}/`, "");
  }

  const { searchText, setSearchText, results } = useFuzzySearch<TVEProfile>({
    items: targetVelocityEstimators ?? [],
  });

  const columns: readonly GridColDef<TVEProfile>[] = [
    {
      ...defaultColumn,
      field: "name",
      headerName: t("models.velocityEstimators.fields.name"),
    },
    {
      ...defaultColumn,
      field: "isActive",
      headerName: t("utils.descriptors.active"),
      cellClassName: ({ row: targetVelocityEstimator }) => {
        const isActive =
          targetVelocityEstimator.id ===
          summary?.robot?.health?.fieldConfig?.activeVelocityEstimatorId;
        return classes(
          CELL_PADDING_CLASS,
          isActive ? "font-bold bg-green-500 text-white" : "text-gray-500"
        );
      },
      valueGetter: (value, targetVelocityEstimator) => {
        if (!isRobotSuccess) {
          return t("components.Loading.placeholder");
        }
        const isActive =
          targetVelocityEstimator.id ===
          summary.robot?.health?.fieldConfig?.activeVelocityEstimatorId;
        return isActive
          ? t("utils.descriptors.active")
          : t("utils.descriptors.inactive");
      },
    },
  ];

  if (uuid) {
    if (targetVelocityEstimators?.some((tve) => tve.id === uuid)) {
      return (
        <VelocityEstimator
          adminEditing={adminEditing}
          parentLink={basePath}
          serial={serial}
          uuid={uuid}
          useGetQuery={useGetQuery}
          useSetMutation={useSetMutation}
          useDeleteMutation={useDeleteMutation}
        />
      );
    } else {
      // check to see if this robot actually owns this TVE, and if not, redirect
      // to the TVE list.
      return <Navigate to={basePath} />;
    }
  }

  return (
    <NoScroll>
      <div className="flex flex-col h-full">
        <CarbonDataGrid<TVEProfile>
          header={
            <>
              <SearchField
                value={searchText}
                onChange={setSearchText}
                label={t("utils.actions.searchLong", {
                  subject: titleCase(
                    t("models.velocityEstimators.velocityEstimator", {
                      count: targetVelocityEstimators?.length ?? 0,
                    })
                  ),
                })}
              />
              <NewVelocityEstimator
                serial={serial}
                basePath={basePath}
                hotkey="n"
                useSetMutation={useSetMutation}
                templates={
                  targetVelocityEstimators ?? [
                    getDefaultTargetVelocityEstimator(t),
                  ]
                }
              />
            </>
          }
          className="flex flex-1"
          rows={results}
          getRowId={({ id }) => id}
          columns={columns}
          columnVisibilityModel={{
            isActive: typeof serial === "string",
          }}
          getRowHeight={() => "auto"}
          hideFooter
          onRowClick={({ row: targetVelocityEstimator }) =>
            navigate(`${basePath}/${targetVelocityEstimator.id}`)
          }
          disableRowSelectionOnClick
          getRowClassName={() => "cursor-pointer"}
          loading={!hasLoaded}
        />
      </div>
    </NoScroll>
  );
};

export const VelocityEstimatorList = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.velocity_estimators,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.read,
      PermissionResource.velocity_estimators,
      PermissionDomain.all
    ),
  ],
  _VelocityEstimatorList
);
