import { _base<PERSON><PERSON><PERSON><PERSON><PERSON> } from "common/state/portalApi/base";
import { combineReducers, StateFromReducersMapObject } from "redux";
import { configureStore, Tuple } from "@reduxjs/toolkit";
import {
  createMigrate,
  FLUSH,
  PAUSE,
  PERSIST,
  PersistedState,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";
import { farmExplorer } from "./farmExplorer";
import { idmApi } from "./idmApi";
import { imageServiceApi } from "./imageServiceApi";
import { jobExplorer } from "common/state/jobExplorer";
import { location } from "./location";
import { measurement } from "./measurement";
import { notifications } from "./notifications";
import { rtcJobsApi } from "common/state/rtcJobsApi";
import { rtcLocatorApi } from "common/state/rtcLocatorApi";
import { rtcLocatorTimeoutMiddleware } from "common/state/middleware/rtcLocatorTimeoutMiddleware";
import { self } from "./self";
import { setupListeners } from "@reduxjs/toolkit/query";
import { userPreferences } from "./userPreferences";
import { useSelector } from "react-redux";
import storage from "redux-persist/lib/storage";

enum AsyncThunkStatus {
  IDLE = "idle",
  PENDING = "pending",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
}

const reducers = combineReducers({
  [idmApi.reducerPath]: idmApi.reducer,
  [farmExplorer.reducerPath]: farmExplorer.reducer,
  [jobExplorer.reducerPath]: jobExplorer.reducer,
  [location.name]: location.reducer,
  [measurement.name]: measurement.reducer,
  [notifications.name]: notifications.reducer,
  [rtcLocatorApi.reducerPath]: rtcLocatorApi.reducer,
  [rtcJobsApi.reducerPath]: rtcJobsApi.reducer,
  [_basePortalApi.reducerPath]: _basePortalApi.reducer,
  [imageServiceApi.reducerPath]: imageServiceApi.reducer,
  [self.name]: self.reducer,
  [userPreferences.name]: userPreferences.reducer,
});

const getPersistedState = (
  version: number,
  state: PersistedState,
  overrides?: Partial<StateFromReducersMapObject<typeof reducers>>
): Exclude<PersistedState, undefined> => ({
  _persist: {
    version,
    rehydrated: false,
  },
  ...state,
  ...overrides,
});

const persistedReducers = persistReducer(
  {
    key: "root",
    version: 2,
    storage,
    blacklist: [
      idmApi.reducerPath,
      location.name,
      farmExplorer.name,
      jobExplorer.name,
      notifications.name,
      rtcLocatorApi.reducerPath,
      rtcJobsApi.reducerPath,
      _basePortalApi.reducerPath,
      imageServiceApi.reducerPath,
      self.name,
    ],
    migrate: createMigrate({
      // we never used version -1 but just in case
      0: (state) => getPersistedState(0, state),
      // we never used version 0 but just in case
      1: (state) => getPersistedState(1, state),
      // clear cycleSlots because convert library changed
      2: (state) =>
        getPersistedState(2, state, {
          [measurement.name]: measurement.getInitialState(),
        }),
    }),
  },
  reducers
);

export const store = configureStore({
  reducer: persistedReducers,
  middleware: (getDefaultMiddleware) =>
    new Tuple(
      ...getDefaultMiddleware({
        serializableCheck: {
          ignoredPaths: ["location"],
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }),
      rtcLocatorTimeoutMiddleware,
      idmApi.middleware,
      _basePortalApi.middleware,
      rtcLocatorApi.middleware,
      rtcJobsApi.middleware,
      imageServiceApi.middleware
    ),
});

export const persistor = persistStore(store);

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppStore = typeof store;

export const useLocation: () => RootState["location"] =
  (): RootState["location"] =>
    useSelector((state: RootState) => state.location);

export const useFarmExplorer: () => RootState["farmExplorer"] =
  (): RootState["farmExplorer"] =>
    useSelector((state: RootState) => state.farmExplorer);

export const useJobExplorer: () => RootState["jobExplorer"] =
  (): RootState["jobExplorer"] =>
    useSelector((state: RootState) => state.jobExplorer);

export const useSelf: () => RootState["self"] = (): RootState["self"] =>
  useSelector((state: RootState) => state.self);

export const useMeasurement: () => RootState["measurement"] =
  (): RootState["measurement"] =>
    useSelector((state: RootState) => state.measurement);

export const useNotifications: () => RootState["notifications"] =
  (): RootState["notifications"] =>
    useSelector((state: RootState) => state.notifications);

export const useUserPreferences: () => RootState["userPreferences"] =
  (): RootState["userPreferences"] =>
    useSelector((state: RootState) => state.userPreferences);

export interface AsyncDataState<T> {
  data?: T;
  status: AsyncThunkStatus;
}

export const asyncDataIdle = <T>(data?: T): AsyncDataState<T> => ({
  data,
  status: AsyncThunkStatus.IDLE,
});

export const asyncDataPending = <T>(data?: T): AsyncDataState<T> => ({
  data,
  status: AsyncThunkStatus.PENDING,
});

export const asyncDataSuccess = <T>(data: T): AsyncDataState<T> => ({
  data,
  status: AsyncThunkStatus.SUCCEEDED,
});

export const asyncDataFailed = <T>(data?: T): AsyncDataState<T> => ({
  data,
  status: AsyncThunkStatus.FAILED,
});
