import { Css<PERSON><PERSON><PERSON>, Theme<PERSON>rovider as MuiThemeProvider } from "@mui/material";
import { getMuiTheme } from "./theme";
import { ThemeMode } from "./theme.types";
import React, {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useMemo,
  useSyncExternalStore,
} from "react";

interface ThemeModeContextValue {
  mode: ThemeMode;
  toggleMode: () => void;
}
const ThemeModeContext = createContext<ThemeModeContextValue>({
  mode: "light", // default should be light because that's what creates no changes from current state in MUI theming
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  toggleMode: () => {},
});

export const useThemeMode = (): ThemeModeContextValue =>
  useContext(ThemeModeContext);

export const AppThemeProvider: FC<PropsWithChildren> = ({ children }) => {
  const subscriber = useMemo(
    () => (listener: () => void) =>
      window.carbonThemeStore?.subscribe(listener),
    []
  );
  const mode = useSyncExternalStore<ThemeMode>(subscriber, () =>
    window.carbonThemeStore?.get()
  );
  const muiTheme = getMuiTheme(mode);

  return (
    <ThemeModeContext.Provider
      value={{
        mode,
        toggleMode: () => {
          window.carbonThemeStore?.setPreference(
            mode === "dark" ? "light" : "dark"
          );
        },
      }}
    >
      <MuiThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeModeContext.Provider>
  );
};
