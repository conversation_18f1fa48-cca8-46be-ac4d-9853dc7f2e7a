import { ThemeListener, ThemeMode, ThemeStore } from "./theme.types";

const STORAGE_KEY = "carbon-theme";

class CarbonThemeStore implements ThemeStore {
  private listeners: Set<ThemeListener> = new Set();
  private mediaQuery: MediaQueryList;

  constructor() {
    this.mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    this.mediaQuery.addEventListener("change", this.handleMediaChange);
    window.addEventListener("storage", this.handleStorageSync);
  }

  get(): ThemeMode {
    if (window._jsenv.REACT_APP_FEATURE_LIGHT_DARK_THEME !== "true") {
      return "light";
    }
    const preference = this.loadPreference();
    return preference ?? (this.mediaQuery.matches ? "dark" : "light");
  }

  setPreference(theme: ThemeMode | undefined): void {
    if (theme) {
      localStorage.setItem(STORAGE_KEY, theme);
    } else {
      localStorage.removeItem(STORAGE_KEY);
    }
    this.emit();
  }

  subscribe(listener: ThemeListener): () => void {
    this.listeners.add(listener);
    listener(this.get());
    return () => this.listeners.delete(listener);
  }

  private emit(): void {
    const current = this.get();
    for (const listener of this.listeners) {
      listener(current);
    }
  }

  private loadPreference(): ThemeMode | undefined {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored === "light" || stored === "dark" ? stored : undefined;
  }

  private handleMediaChange = (): void => {
    if (this.loadPreference() === undefined) {
      this.emit();
    }
  };

  private handleStorageSync = (): void => {
    this.emit();
  };

  destroy(): void {
    this.mediaQuery.removeEventListener("change", this.handleMediaChange);
    window.removeEventListener("storage", this.handleStorageSync);
    this.listeners.clear();
  }
}

const main = (): void => {
  window.carbonThemeStore = new CarbonThemeStore();
  window.carbonThemeStore.subscribe((theme: ThemeMode) => {
    // add a class that tailwind uses to differentiate theming
    document.documentElement.classList.toggle("dark", theme === "dark");
  });
};

main();
