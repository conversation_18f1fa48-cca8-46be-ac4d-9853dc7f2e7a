export type ThemeMode = "light" | "dark";
export type ThemeListener = (theme: ThemeMode) => void;

export interface ThemeStore {
  // Gets the current theme. This may be drawn from the device theme, or may be
  // overridden by user preference. This call is fast and safe to call often.
  get(): ThemeMode;
  // Sets and persists an explicit user preference, overriding the device
  // theme. Pass `undefined` to clear preference.
  setPreference(theme: ThemeMode): void;
  // Registers a listener that's called whenever the theme changes.
  // Returns a function that can be called to unsubscribe.
  subscribe(listener: ThemeListener): () => void;
}
