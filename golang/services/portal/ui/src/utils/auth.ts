export * from "common/utils/auth";
import { GetTokenSilentlyOptions } from "@auth0/auth0-react";

let getAccessTokenSilently: (
  options?: GetTokenSilentlyOptions | undefined
) => Promise<string>;

export const auth = {
  getAccessTokenSilently: () => getAccessTokenSilently(),
  setAccessTokenSilently: (
    function_: (
      options?: GetTokenSilentlyOptions | undefined
    ) => Promise<string>
  ) => (getAccessTokenSilently = function_),
};
