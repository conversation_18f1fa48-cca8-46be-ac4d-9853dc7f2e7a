import { describe, expect, it } from "@jest/globals";
import { includeAncestors } from "./configs";

describe("configs", () => {
  describe("includeAncestors", () => {
    it("works", () => {
      const input = ["a/b/c", "a/b/d/e", "z/z", "z/z"];
      const actual = includeAncestors(input);
      const expected = [
        "",
        "a",
        "a/b",
        "a/b/c",
        "a/b/d",
        "a/b/d/e",
        "z",
        "z/z",
      ];
      expect(actual.sort()).toEqual(expected.sort());
    });
  });
});
