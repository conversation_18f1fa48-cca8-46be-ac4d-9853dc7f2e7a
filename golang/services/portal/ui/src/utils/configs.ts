import {
  ConfigNode,
  ConfigType,
  configType<PERSON>romJSON,
  SchemaNode,
} from "protos/config/api/config_service";
import { ConfigResponse } from "protos/portal/configs";
import { isEmpty, sortBy } from "common/utils/arrays";
import { isKeyOf } from "common/utils/objects";
import { isUndefined } from "common/utils/identity";
import { RobotClass } from "common/utils/robots";
import { TemplateSerial } from "common/utils/configs";

export const TEMPLATE_BY_CLASS: Record<RobotClass, TemplateSerial> = {
  buds: TemplateSerial.BUD,
  reapers: TemplateSerial.REAPER,
  rtcs: TemplateSerial.RTC,
  simulators: TemplateSerial.SIMULATOR,
  slayers: TemplateSerial.SLAYER,
  unknown: TemplateSerial.SIMULATOR,
};

export const eachNode = (
  node: ConfigNode | undefined,
  iteratee: (
    node: ConfigNode,
    path: string,
    parent?: ConfigNode
  ) => boolean | void,
  parentPath: string = "",
  parent?: ConfigNode
): void => {
  if (!node) {
    return;
  }
  const path = parentPath ? `${parentPath}/${node.name}` : node.name;
  const keepGoing = iteratee(node, path, parent);
  if (!keepGoing) {
    return;
  }
  for (const child of node.children) {
    eachNode(child, iteratee, path, node);
  }
};

export const isLeafNode = (node: ConfigNode | undefined): boolean =>
  Boolean(node?.def && "type" in node.def && isEmpty(node.children));

export const isListNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.LIST)
  );

export const isIntNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.INT)
  );

export const isUintNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.UINT)
  );

export const isFloatNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.FLOAT)
  );

export const isNumericNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      [
        configTypeFromJSON(ConfigType.INT),
        configTypeFromJSON(ConfigType.UINT),
        configTypeFromJSON(ConfigType.FLOAT),
      ].includes(node.def.type)
  );

export const isBooleanNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.BOOL)
  );

export const isStringNode = (node: ConfigNode | undefined): boolean =>
  Boolean(
    node?.def &&
      "type" in node.def &&
      node.def.type === configTypeFromJSON(ConfigType.STRING)
  );

export const getValue = <T extends number | string | boolean>(
  node: ConfigNode | undefined
): undefined | T => {
  if (!node || !isLeafNode(node)) {
    return;
  }
  if (isIntNode(node)) {
    return node.value?.int64Val as T;
  } else if (isUintNode(node)) {
    return node.value?.uint64Val as T;
  } else if (isFloatNode(node)) {
    return node.value?.floatVal as T;
  } else if (isStringNode(node)) {
    return node.value?.stringVal as T;
  } else if (isBooleanNode(node)) {
    return node.value?.boolVal as T;
  }
};

export const setValue = <T extends number | string | boolean>(
  node: ConfigNode | undefined,
  value: T
): ConfigNode | undefined => {
  if (!node || !isLeafNode(node)) {
    return;
  }
  node.value = node.value ?? {
    timestampMs: Date.now(),
  };
  if (isIntNode(node)) {
    node.value.int64Val = value as number;
  } else if (isUintNode(node)) {
    node.value.uint64Val = value as number;
  } else if (isFloatNode(node)) {
    node.value.floatVal = value as number;
  } else if (isStringNode(node)) {
    node.value.stringVal = value as string;
  } else if (isBooleanNode(node)) {
    node.value.boolVal = value as boolean;
  }
  return node;
};

export const getParentPath = (path: string): string => {
  const segments = path.split("/");
  return segments.slice(0, -1).join("/");
};

export const getParentNodeFromPath = (
  tree: ConfigNode | undefined,
  path: string
): ConfigNode | undefined => getNodeFromPath(tree, getParentPath(path));

export const getNodeFromPath = (
  tree: ConfigNode | undefined,
  path: string
): ConfigNode | undefined => {
  if (!tree) {
    return;
  }
  const segments = path.split("/");
  let current: ConfigNode | undefined = tree;
  for (const segment of segments) {
    if (!current) {
      continue;
    }
    current = current.children.find((child) => child.name === segment);
  }
  return current;
};

// takes a collection of paths and expands it to include all ancestors
// (without duplicate elements)
//
// i.e., finds the transitive closure under the `getParentPath` relation
export const includeAncestors = (paths: string[]): string[] => {
  const result: Set<string> = new Set();
  for (const path of paths) {
    const theseAncestors = path
      .split("/")
      .map((_, index, segments) => segments.slice(0, index).join("/"))
      .reverse(); // deepest first
    result.add(path);
    for (const ancestor of theseAncestors) {
      if (result.has(ancestor)) {
        // if this is in the set, then all its ancestors are, too
        break;
      }
      result.add(ancestor);
    }
  }
  return [...result];
};

export const getSchemaNodeFromPath = (
  node: SchemaNode,
  path: string
): SchemaNode | undefined => {
  const segments = path.split("/");
  for (const segment of segments) {
    let next: SchemaNode | undefined;
    if (segment === "*") {
      next = node.listItem;
    } else {
      next = node.nodeChildren.find((child) => child.name === segment);
    }
    if (!next) {
      return;
    }
    node = next;
  }
  return node;
};

export const sortByName = (node: ConfigNode): ConfigNode => {
  const { children, ...rest } = node;
  return {
    ...rest,
    children: sortBy(children, "name").map((child) => sortByName(child)),
  };
};

export const sortSchemaByName = (node: SchemaNode): SchemaNode => {
  const { listItem, nodeChildren, ...rest } = node;
  return {
    ...rest,
    listItem: listItem ? sortSchemaByName(listItem) : undefined,
    nodeChildren: sortBy(nodeChildren, "name").map((child) =>
      sortSchemaByName(child)
    ),
  };
};

interface ConfigChanges {
  changedPaths: string[];
  addedPaths: string[];
  removedPaths: string[];
  allChangedPaths: string[];
}

export const getConfigChanges = (
  config?: ConfigNode,
  template?: ConfigNode
): ConfigChanges => {
  const result: ConfigChanges = {
    changedPaths: [],
    addedPaths: [],
    removedPaths: [],
    allChangedPaths: [],
  };
  if (!config || !template) {
    return result;
  }
  for (const root of config.children) {
    eachNode(root, (node, path, parent) => {
      const templateNode = getNodeFromPath(template, path);
      const hasTemplate = !isUndefined(templateNode);
      const isParentList = parent && isListNode(parent);
      const isParentRecommended =
        parent && Boolean(parent.def?.defaultRecommended);
      const isRecommended = Boolean(node.def?.defaultRecommended);
      const hasChangedValue = getValue(node) !== getValue(templateNode);

      if (!hasTemplate && isParentList && isParentRecommended) {
        result.addedPaths.push(path);
      } else if (hasTemplate && isRecommended && hasChangedValue) {
        result.changedPaths.push(path);
      }
      return true;
    });
  }
  for (const root of template.children) {
    eachNode(root, (node, path) => {
      const hasNode = !isUndefined(getNodeFromPath(config, path));
      const isList = isListNode(node);
      const isRecommended = Boolean(node.def?.defaultRecommended);
      if (isList && !isRecommended) {
        return false;
      }
      if (!hasNode) {
        result.removedPaths.push(path);
      }
      return true;
    });
  }
  result.allChangedPaths = [
    ...result.addedPaths,
    ...result.removedPaths,
    ...result.changedPaths,
  ];
  return result;
};

export type SearchIndex = Set<string>; // paths in tree

export const indexConfigTree = (node?: ConfigNode): SearchIndex => {
  const result: SearchIndex = new Set();
  if (node) {
    indexConfigNode(result, node, node.name);
  }
  return result;
};

const indexConfigNode = (
  acc: SearchIndex,
  node: ConfigNode,
  path: string
): void => {
  acc.add(path);
  for (const child of node.children) {
    indexConfigNode(acc, child, `${path}/${child.name}`);
  }
};

export const searchTree = (index: SearchIndex, query: string): string[] => {
  // (this could be `RegExp.escape` once that's widely available)
  const pattern = new RegExp(
    query.replaceAll(/[^0-9a-z*/_.-]/gi, "").replaceAll("*", "\\*"),
    "i"
  );
  const resultPaths: string[] = [];
  for (const path of index) {
    if (pattern.test(path)) {
      resultPaths.push(path);
    }
  }
  return resultPaths;
};

export const indexSchemaTree = (node?: SchemaNode): SearchIndex => {
  const result: SearchIndex = new Set();
  if (node) {
    indexSchemaNode(result, node, node.name);
  }
  return result;
};

const indexSchemaNode = (
  acc: SearchIndex,
  node: SchemaNode,
  path: string
): void => {
  acc.add(path);
  if (node.listItem) {
    indexSchemaNode(acc, node.listItem, `${path}/*`);
  }
  for (const child of node.nodeChildren) {
    indexSchemaNode(acc, child, `${path}/${child.name}`);
  }
};

export const shallowCopyConfigTree = (
  node: ConfigNode | undefined
): ConfigNode | undefined => {
  // we don't exist somehow
  if (!node) {
    return;
  }
  return {
    ...node,
    children: node.children
      .map((child) => shallowCopyConfigTree(child))
      .filter((child) => child !== undefined),
  };
};

export const filterConfigTree = (
  node?: ConfigNode,
  paths: string[] = [],
  parentPath: string = "",
  options: {
    ignoreRoot?: boolean;
    showChildren?: boolean;
    isRoot?: boolean;
  } = {}
): ConfigNode | undefined => {
  const { ignoreRoot = false, showChildren = true, isRoot = true } = options;
  // we don't exist somehow
  if (!node) {
    return;
  }
  const copy = { ...node };
  // full path to this node
  let path = parentPath ? `${parentPath}/${node.name}` : node.name;
  if (ignoreRoot && isRoot) {
    path = "";
  }
  if (node.children.length > 0) {
    // branch node
    const isPartial = Boolean(
      paths.some((contender) => contender.startsWith(path))
    );
    // none of our children are matches, bail here
    if (!isPartial) {
      return;
    }
    // recursively filter children
    copy.children = node.children
      .map((child) =>
        filterConfigTree(child, paths, path, { ...options, isRoot: false })
      )
      .filter((child) => child !== undefined);
    return copy;
  } else {
    // check if us or any of our ancestors are matches
    for (const contender of paths) {
      if (contender === path || (showChildren && path.startsWith(contender))) {
        return copy;
      }
    }
  }
};

export const filterSchemaTree = (
  node: SchemaNode,
  includePaths: string[] = []
): SchemaNode | undefined => {
  return filterSchemaNode(node, node.name, new Set(includePaths));
};

const filterSchemaNode = (
  node: SchemaNode,
  path: string,
  includePaths: Set<string>
): SchemaNode | undefined => {
  node = { ...node };
  if (includePaths.has(path)) {
    return node;
  }
  let descendantMatched = false;
  if (node.listItem) {
    const filtered = filterSchemaNode(node.listItem, `${path}/*`, includePaths);
    if (filtered) {
      descendantMatched = true;
    }
    node.listItem = filtered;
  }
  node.nodeChildren = node.nodeChildren.flatMap((child) => {
    const childName = `${path}/${child.name}`;
    const filtered = filterSchemaNode(child, childName, includePaths);
    if (!filtered) {
      return [];
    }
    descendantMatched = true;
    return [filtered];
  });
  return descendantMatched ? node : undefined;
};

interface SpecialRootNode {
  node: ConfigResponse;
}

export const isSpecialRootNode = (node: unknown): node is SpecialRootNode =>
  isKeyOf(node as Record<any, unknown>, "node");

export const getConfigNode = (config?: ConfigNode): ConfigNode | undefined => {
  if (!config) {
    return;
  }
  return isSpecialRootNode(config)
    ? (config.node as unknown as ConfigNode)
    : config;
};
