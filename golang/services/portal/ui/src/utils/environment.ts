export enum Environment {
  PRODUCTION = "prod",
  STAGING = "staging",
  TEST = "test",
  LOCAL = "local",
  UNKNOWN = "unknown",
}

const HOSTS = {
  production: "customer.cloud.carbonrobotics.com",
  staging: "customer-stg.cloud.carbonrobotics.com",
  test: "customer-test.cloud.carbonrobotics.com",
  local: "localhost:",
};

export const getEnvironment = (): Environment => {
  if (typeof window === "undefined") {
    return Environment.UNKNOWN;
  }
  const host = window.location.host;
  switch (host) {
    case HOSTS.production: {
      return Environment.PRODUCTION;
    }
    case HOSTS.staging: {
      return Environment.STAGING;
    }
    case HOSTS.test: {
      return Environment.TEST;
    }
    default: {
      const host = window.location.host;
      if (host.includes(HOSTS.local)) {
        return Environment.LOCAL;
      }
      return Environment.UNKNOWN;
    }
  }
};

export const isProduction = getEnvironment() === Environment.PRODUCTION;
export const isStaging = getEnvironment() === Environment.STAGING;
export const isTest = getEnvironment() === Environment.TEST;
export const isJestTest = typeof jest !== "undefined";
export const isLocal = getEnvironment() === Environment.LOCAL;
export const isDevelopment = !isProduction && !isStaging;
