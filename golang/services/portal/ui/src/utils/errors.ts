import {
  BaseQueryError,
  BaseQueryFn,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { isObject } from "common/utils/objects";
import { isUndefined } from "common/utils/identity";
import { SerializedError } from "@reduxjs/toolkit";
import { TFunction } from "i18next";

interface ErrorResponse {
  error?: string;
}

export const formatError = (
  t: TFunction,
  error: FetchBaseQueryError | SerializedError | string | undefined
): string | undefined => {
  if (isUndefined(error)) {
    return;
  }
  if (typeof error === "string") {
    return error;
  }
  if ("message" in error) {
    return error.message;
  }
  if ("data" in error && isObject(error.data) && "error" in error.data) {
    return error.data.error;
  }
  if ("error" in error) {
    return error.error;
  }
  if ("status" in error) {
    return String(error.status);
  }
  return t("components.ErrorBoundary.error");
};

export const handleError = <BaseQuery extends BaseQueryFn>(
  t: TFunction,
  isError: boolean,
  error:
    | FetchBaseQueryError
    | SerializedError
    | BaseQueryError<BaseQuery>
    | undefined,
  callback: (message?: string) => void
): void => {
  if (!isError) {
    return;
  }
  const fallback = t("components.ErrorBoundary.error");
  if (!error || typeof error !== "object" || !("data" in error)) {
    callback(fallback);
    return;
  }
  callback((error.data as ErrorResponse).error ?? fallback);
};

// accommodating for RTK returning a plain object for abort errors
export const isAbort = (error: unknown): boolean =>
  Boolean(
    error &&
      typeof error === "object" &&
      (error as { name?: string }).name === "AbortError"
  );

// Use `myPromise.catch(swallowAbort)` to turn an `AbortError` into a silent
// success, while letting any other errors still throw.
export const swallowAbort = (reason: unknown): Promise<void> =>
  isAbort(reason) ? Promise.resolve() : Promise.reject(reason);
