import { addNotification } from "portal/state/notifications";
import { handleError } from "portal/utils/errors";
import { isObject } from "../objects";
import { onPopupStateChange, UseApiPopupsOptions } from "./useApiPopups";
import { TFunction } from "i18next";
import { useDispatch } from "react-redux";

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useDispatch: jest.fn(() => ({
    unwrap: jest.fn(),
  })),
}));

jest.mock("portal/state/notifications", () => ({
  ...jest.requireActual("portal/state/notifications"),
  addNotification: jest.fn(),
}));

jest.mock("portal/utils/errors", () => ({
  ...jest.requireActual("portal/utils/errors"),
  handleError: jest.fn(),
}));

jest.mock("../objects", () => ({
  ...jest.requireActual("../objects"),
  isObject: jest.fn(),
}));

describe("onPopupStateChange", () => {
  const dispatch = jest.fn();
  const t = jest.fn((key) => key) as unknown as TFunction;

  beforeEach(() => {
    jest.clearAllMocks();
    (useDispatch as jest.Mock).mockReturnValue(dispatch);
  });

  it("should dispatch a warning notification if queryLimitReached is true", () => {
    (isObject as unknown as jest.Mock).mockReturnValue(true);
    const data = { queryLimitReached: true };

    onPopupStateChange(t, data, false, dispatch, false, undefined, false, {});

    expect(dispatch).toHaveBeenCalledWith(
      addNotification({
        message: "components.ErrorBoundary.queryLimitReached",
        variant: "warning",
      })
    );
    expect(dispatch).toHaveBeenCalledTimes(1);
  });

  it("should handle error and dispatch an error notification", () => {
    const error = { message: "Test error" };
    const isError = true;
    const showFullErrors = true;
    const options: UseApiPopupsOptions = { errorVariant: "error" };

    onPopupStateChange(
      t,
      {},
      showFullErrors,
      dispatch,
      isError,
      error,
      false,
      options
    );

    expect(handleError).toHaveBeenCalledWith(
      t,
      isError,
      error,
      expect.any(Function)
    );
    const handleErrorCallback = (handleError as jest.Mock).mock.calls[0][3];
    handleErrorCallback("Error message");

    expect(dispatch).toHaveBeenCalledWith(
      addNotification({
        message: "Error message",
        variant: "error",
      })
    );
    expect(dispatch).toHaveBeenCalledTimes(1);
  });

  it("should dispatch a success notification if success is true", () => {
    const success = "Success message";
    const isSuccess = true;
    const options: UseApiPopupsOptions = { success };

    onPopupStateChange(
      t,
      {},
      false,
      dispatch,
      false,
      undefined,
      isSuccess,
      options
    );

    expect(dispatch).toHaveBeenCalledWith(
      addNotification({
        message: success,
        variant: "success",
      })
    );
    expect(dispatch).toHaveBeenCalledTimes(1);
  });

  it("should not dispatch any notification if conditions are not met", () => {
    onPopupStateChange(t, {}, false, dispatch, false, undefined, false, {});

    expect(dispatch).not.toHaveBeenCalled();
  });
});
