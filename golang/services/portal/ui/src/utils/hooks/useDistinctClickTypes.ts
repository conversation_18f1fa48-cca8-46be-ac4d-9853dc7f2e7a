import { DOMAttributes, useCallback, useEffect, useRef } from "react";

/**
 * Supports single click, double click, and long press.
 */
export const useDistinctClickTypes = (
  onClick?: () => void,
  onDoubleClick?: () => void,
  onLongPress?: () => void,
  clickDelay = 250, // Time to wait before determining it's a single click
  longPressDelay = 500 // Time held down to trigger long press
): Pick<
  DOMAttributes<HTMLElement>,
  "onMouseDown" | "onMouseUp" | "onMouseLeave" | "onTouchStart" | "onTouchEnd"
> => {
  const clickTimeout = useRef<NodeJS.Timeout>();
  const longPressTimeout = useRef<NodeJS.Timeout>();
  const longPressed = useRef(false);

  useEffect(() => {
    return () => {
      clearTimeout(clickTimeout.current);
      clearTimeout(longPressTimeout.current);
    };
  }, []);

  const onPressStart = useCallback(() => {
    longPressed.current = false;
    longPressTimeout.current = setTimeout(() => {
      longPressed.current = true;
      onLongPress?.();
    }, longPressDelay);
  }, [onLongPress, longPressDelay]);

  const onPressEnd = useCallback(() => {
    clearTimeout(longPressTimeout.current);

    if (longPressed.current) {
      // Long press was already handled
      return;
    }

    if (clickTimeout.current) {
      // double click
      clearTimeout(clickTimeout.current);
      clickTimeout.current = undefined;
      onDoubleClick?.();
    } else {
      // only a single click
      clickTimeout.current = setTimeout(() => {
        onClick?.();
        clickTimeout.current = undefined;
      }, clickDelay);
    }
  }, [onClick, onDoubleClick, clickDelay]);

  return {
    onMouseDown: onPressStart,
    onTouchStart: onPressStart,
    onMouseUp: onPressEnd,
    onTouchEnd: onPressEnd,
    onMouseLeave: () => clearTimeout(longPressTimeout.current), // cancel long press if pointer leaves
  };
};
