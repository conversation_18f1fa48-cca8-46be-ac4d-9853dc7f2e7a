import * as Cmp from "portal/utils/comparators";
import {
  bbox,
  BBox,
  bboxPolygon,
  bearing,
  destination,
  distance,
  length,
  lineIntersect,
  lineString,
} from "@turf/turf";
import { createParallelLine } from "portal/utils/geo";
import { FarmMapData } from "common/utils/farm";
import {
  Feature,
  FeatureCollection,
  Geometry,
  LineString,
  Polygon,
  Position,
} from "geojson";
import { Id } from "protos/geo/geo";

const DEFAULT_ROW_WIDTH = 6.7056; // 264 inches converted to meters

export interface FarmRowData {
  id?: Id;
  boundingBoxesByFieldId: Map<string, Polygon>;
  calculatedRowsByFieldId: Map<string, FeatureCollection>;
}

/**
 * Generates data pertaining to specific rows within the fields of a farm. Currently,
 * this includes field bounding boxes (useful for visualization and debugging) as well
 * as a complete set of row lines calculated from the planting heading line stored with
 * the field. If no planting heading has been set, no rows can be calculated.
 */
export const getFarmRowData = (
  farmMapData: FarmMapData,
  rowWidthMeters?: number
): FarmRowData => {
  const boundingBoxesByFieldId = new Map<string, Polygon>();
  const calculatedRowsByFieldId = new Map<string, FeatureCollection>();

  // Visit each field on the farm
  for (const field of farmMapData.fields.features) {
    if (!field.id || field.geometry.type !== "GeometryCollection") {
      continue;
    }
    const id = String(field.id);
    const fieldRows: Feature[] = [];

    // Calculate a bounding box for the field, which will be returned for visualization
    // and debugging purposes, but also used to efficiently calculate row lines below.
    const boundingBox = bbox(field.geometry);
    boundingBoxesByFieldId.set(id, bboxPolygon(boundingBox).geometry);

    // We can't generate any rows if we don't have a planting heading
    const plantingHeading = farmMapData.plantingHeadingsByFieldId.get(id);
    if (!plantingHeading) {
      continue;
    }

    // Generate parallel rows to completely fill each polygon in the field
    const fieldBoundaries = field.geometry.geometries.filter(
      (g: Geometry) => g.type === "Polygon"
    );
    for (const boundary of fieldBoundaries) {
      const calculatedRows = generateRows(
        id,
        plantingHeading,
        boundary,
        boundingBox,
        rowWidthMeters ?? DEFAULT_ROW_WIDTH
      );
      fieldRows.push(...calculatedRows);
    }

    calculatedRowsByFieldId.set(id, {
      type: "FeatureCollection",
      features: fieldRows,
    });
  }

  return {
    id: farmMapData.id,
    boundingBoxesByFieldId,
    calculatedRowsByFieldId,
  };
};

/**
 * Generates rows that fill a field's boundary polygon, based on a planting heading
 * (a line defined by two points within a field captured in the center of a row and
 * in the direction that the field is or will be planted).
 */
function generateRows(
  fieldId: string,
  plantingHeading: LineString,
  fieldBoundary: Polygon,
  boundingBox: BBox,
  spacing: number
): Feature[] {
  const start = plantingHeading.coordinates[0];
  const end = plantingHeading.coordinates[1];
  if (start === undefined || end === undefined) {
    return [];
  }
  const bearingForward = bearing(start, end);
  const bearingBackward = bearing(end, start);

  // Determine the amount we should extend the planting line to make
  // sure all generated lines are guaranteed to cross the entire field.
  const bottomLeft = [boundingBox[0], boundingBox[1]];
  const topRight = [boundingBox[2], boundingBox[3]];
  const maxLen = length(lineString([bottomLeft, topRight]), {
    units: "meters",
  });

  // Extend the planting line to cross the entire field
  const newStart = destination(start, maxLen, bearingBackward, {
    units: "meters",
  }).geometry.coordinates;
  const newEnd = destination(end, maxLen, bearingForward, {
    units: "meters",
  }).geometry.coordinates;

  // Generate rows in the center and both directions to fill the field
  const centerRows = clipRowToFieldBoundary(
    fieldId,
    newStart,
    newEnd,
    fieldBoundary
  );
  const leftRows = generateParallelRows(
    fieldId,
    newStart,
    newEnd,
    fieldBoundary,
    spacing
  );
  const rightRows = generateParallelRows(
    fieldId,
    newStart,
    newEnd,
    fieldBoundary,
    -spacing
  );

  return [...centerRows, ...leftRows, ...rightRows];
}

function generateParallelRows(
  fieldId: string,
  plantingHeadingStart: Position,
  plantingHeadingEnd: Position,
  fieldBoundary: Polygon,
  spacing: number
): Feature[] {
  const rows: Feature[] = [];

  for (let i = 1; ; i++) {
    const coordinates = createParallelLine(
      plantingHeadingStart,
      plantingHeadingEnd,
      i * spacing
    );
    const clippedRows = clipRowToFieldBoundary(
      fieldId,
      coordinates[0],
      coordinates[1],
      fieldBoundary
    );
    if (clippedRows.length === 0) {
      // Ran off the side of the field
      break;
    }
    rows.push(...clippedRows);
  }

  return rows;
}

function clipRowToFieldBoundary(
  fieldId: string,
  rowStart: Position,
  rowEnd: Position,
  fieldBoundary: Polygon
): Feature[] {
  const clippedRows: Feature[] = [];

  const row: LineString = {
    type: "LineString",
    coordinates: [rowStart, rowEnd],
  };

  // Intersect with the field boundary. If there are two intersection points, we are
  // crossing the field and can clip to that segment. If there are more than two, then
  // the field is irregularly shaped and we are crossing the field at more than one spot
  // (for example, a circular field with a pie slice removed). In that case, create a
  // row for every distinct field crossing.
  const results = lineIntersect(row, fieldBoundary);

  // Sort the intersection points based on their distance from the start of the line.
  // lineIntersect does *not* guarantee a defined order on its results
  const sortedResults = results.features.sort(
    Cmp.comparing((p) => distance(p, rowStart))
  );

  for (let i = 0; i < sortedResults.length - 1; i += 2) {
    const clipStart = sortedResults[i]?.geometry.coordinates;
    const clipEnd = sortedResults[i + 1]?.geometry.coordinates;
    if (clipStart && clipEnd) {
      clippedRows.push({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [clipStart, clipEnd],
        },
        properties: {
          fieldId,
        },
      });
    }
  }

  return clippedRows;
}
