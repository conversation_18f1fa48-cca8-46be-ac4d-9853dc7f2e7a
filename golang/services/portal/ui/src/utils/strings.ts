export * from "common/utils/strings";
import {
  BASE_LANGUAGES,
  PortalLanguage,
  portalLanguageOrDefault,
} from "portal/i18nConstants";
import { i18n as I18n, TFunction } from "i18next";
import humanizeDuration, {
  SupportedLanguage as HumanizeLanguage,
  Options,
} from "humanize-duration";

export const formatList = (t: TFunction, list: string[]): string => {
  if (list.length === 0) {
    return "";
  }
  if (list.length === 1) {
    return t("utils.lists.1", { a: list[0] });
  }
  if (list.length === 2) {
    return t("utils.lists.2", { a: list[0], b: list[1] });
  }
  const first = list.shift() ?? "";
  const last = list.pop();
  let result = first;
  for (const item of list) {
    result = t("utils.lists.3+", { a: result, b: item });
  }
  result = t("utils.lists.+3", { b: result, c: last });
  return result;
};

export const formatDuration = (
  milliseconds: number | undefined,
  i18n: I18n,
  options: Options = {}
): string => {
  return humanizeDuration(milliseconds ?? 0, {
    largest: options.largest ?? 1,
    round: options.round ?? false,
    maxDecimalPoints: options.maxDecimalPoints ?? 1,
    language: toHumanizeLanguage(portalLanguageOrDefault(i18n.language)),
    ...options,
  });
};

// Humanize supports all our Portal languages. If we add more Portal languages
// that Humanize doesn't support, this'll be a compile-time error.
export const toHumanizeLanguage = (
  language: PortalLanguage
): HumanizeLanguage => {
  return BASE_LANGUAGES[language];
};
