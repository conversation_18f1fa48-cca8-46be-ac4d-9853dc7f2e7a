import {
  createRobotOutline,
  RobotOutline,
} from "common/components/map/RobotOutline";
import { Layer, Source } from "react-map-gl";
import { theme } from "portal/theme/theme";
import { useLocation } from "portal/state/store";
import React, { FC, useMemo } from "react";

interface RobotOutlineLayersProps {
  serials: string[];
}

export const RobotOutlineLayers: FC<RobotOutlineLayersProps> = ({
  serials,
}) => {
  const { history: locationHistory } = useLocation();
  const selectedRobotOutlines = useMemo(() => {
    const outlines: RobotOutline[] = [];
    for (const serial of serials) {
      const records = locationHistory[serial];
      if (!records) {
        continue;
      }

      // Get the last known position and heading. Don't render an outline if we
      // can't be sure of the last location and heading so we don't mislead users.
      const last = records.at(-1)?.point;
      const headingDegrees = records.at(-1)?.headingDegrees;
      if (!last || headingDegrees === undefined) {
        continue;
      }

      const outline = createRobotOutline(last.lng, last.lat, headingDegrees);
      outlines.push({
        serial,
        outline,
      });
    }
    return outlines;
  }, [serials, locationHistory]);

  return (
    <>
      {selectedRobotOutlines.map((outline) => (
        <Source
          key={`outline-${outline.serial}`}
          id={`outline-${outline.serial}`}
          type="geojson"
          data={{
            type: "Feature",
            properties: {},
            geometry: outline.outline,
          }}
        >
          <Layer
            id={`outline-${outline.serial}`}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
              "line-dasharray": [2, 1],
            }}
          />
        </Source>
      ))}
    </>
  );
};
