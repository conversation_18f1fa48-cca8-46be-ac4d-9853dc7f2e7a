import { buildPermission } from "portal/utils/auth";
import { Button, CircularProgress, Paper, Typography } from "@mui/material";
import {
  BUTTON,
  classes,
  GREEN_BUTTON,
  RED_BUTTON,
  WHITE_BUTTON,
  YELLOW_BUTTON,
} from "portal/theme/theme";
import { capitalize } from "portal/utils/strings";
import { ConfirmationDialog } from "portal/components/ConfirmationDialog";
import { FullViewportPage } from "portal/components/Page";
import { getJobOrchestratePath, Path } from "portal/utils/routing";
import { Header } from "portal/components/header/Header";
import {
  jobIsCancellable,
  jobIsPauseable,
  jobIsStartable,
} from "portal/utils/jobs";
import { Job as JobProto, ObjectiveAssignment, State } from "protos/rtc/jobs";
import {
  Navigate,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { ObjectiveReorderList } from "./ObjectiveReorderList";
import { OrchestrationMap } from "./orchestrate/OrchestrationMap";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { RobotSelectionList } from "./RobotSelectionList";
import {
  setAssignmentsByTractorSerial,
  setFarm,
} from "common/state/jobExplorer";
import { skipToken } from "@reduxjs/toolkit/query";
import { SummaryCardItem } from "portal/components/SummaryCardItem";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import {
  useChangeJobStateMutation,
  useDeleteJobMutation,
  useGetJobQuery,
  useListAssignmentsQuery,
} from "common/state/rtcJobsApi";
import {
  useCustomerStatus,
  useFarmStatus,
  useJobStateStatus,
  useJobTimesStatus,
  useJobTypeStatus,
} from "./JobStatus";
import {
  useDeviceLocations,
  useTractorLocations,
} from "common/hooks/useLiveRtcLocation";
import { useDispatch } from "react-redux";
import { useEditJobDialog } from "./SetJobForm";
import { useGetFarmQuery } from "common/state/portalApi/farm";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import DashboardIcon from "@mui/icons-material/Dashboard";
import EditIcon from "@mui/icons-material/Edit";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import React, { FC, useEffect, useState } from "react";
import StopIcon from "@mui/icons-material/Stop";

export const JobDetailsPage: FC = () => {
  const { t } = useTranslation();
  const { jobId } = useParams();
  const [searchParams] = useSearchParams();
  const {
    data: job,
    isLoading,
    isSuccess,
    isError,
  } = useQueryPopups(useGetJobQuery(jobId ? { id: jobId } : skipToken));
  const dispatch = useDispatch();
  const { data: farm } = useQueryPopups(
    useGetFarmQuery(job ? job.farmId : skipToken)
  );
  useEffect(() => {
    dispatch(setFarm(farm));
  }, [farm, dispatch]);

  // todo: re-evaluate if we move to specifying co-located zones
  const field = farm?.zones.find((z) => z.id?.id === job?.fieldId);
  const centerPivotId = field?.contents?.field?.centerPivot?.endpointDeviceId;

  useTractorLocations(
    job?.robotWhitelist?.entries.map((a) => a.robotSerial) ?? []
  );
  useDeviceLocations(centerPivotId ? [centerPivotId] : []);

  const { data: assignments } = useQueryPopups(
    useListAssignmentsQuery({
      objectiveIds: job?.objectives.map((o) => `${o.id}`) ?? [],
    })
  );

  useEffect(() => {
    const assignmentsByTractorId: Record<string, ObjectiveAssignment[]> = {};

    for (const assignment of assignments?.assignments ?? []) {
      const currentList = assignmentsByTractorId[assignment.robotSerial];
      if (currentList) {
        assignmentsByTractorId[assignment.robotSerial]?.push(assignment);
      } else {
        assignmentsByTractorId[assignment.robotSerial] = [assignment];
      }
    }

    dispatch(setAssignmentsByTractorSerial(assignmentsByTractorId));
  }, [assignments, dispatch]);
  const parentLink = searchParams.get("returnTo") ?? Path.JOBS;

  if (isError) {
    return <Navigate to={parentLink} />;
  }

  return (
    <>
      <Header
        title={capitalize(t("models.jobs.job_one"))}
        pageTitle={`${
          job?.name ?? t("utils.descriptors.loading")
        } - ${capitalize(t("models.jobs.job_one"))}`}
        parentLink={parentLink}
      />
      <FullViewportPage maxWidth={false}>
        <div className="pt-20 pb-10 flex flex-col h-full">
          {isLoading && <CircularProgress variant="indeterminate" />}
          {isSuccess && <JobView job={job} />}
        </div>
      </FullViewportPage>
    </>
  );
};

interface JobViewProps {
  job: JobProto;
}

const JobView: FC<JobViewProps> = ({ job }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const canEditJob = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.all
    ),
  ]);

  const { renderDialog: renderEditJobDialog, open: openEditJobDialog } =
    useEditJobDialog(job);

  const summaryCardItems = [
    ...useCustomerStatus(job),
    ...useFarmStatus(job, canEditJob ? openEditJobDialog : undefined),
    ...useJobTypeStatus(job, canEditJob ? openEditJobDialog : undefined),
    ...useJobStateStatus(job),
    ...useJobTimesStatus(job),
  ];

  const [confirmCancel, setConfirmCancel] = useState<boolean>(false);
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);
  const [changeJobState] = useMutationPopups(useChangeJobStateMutation());

  function startJob(): void {
    // the start button is overloaded between readying a pending job and
    // resuming a paused job; figure out which one is appropriate to do
    if (job.state === State.PAUSED) {
      changeJobState({ id: job.id, state: State.IN_PROGRESS });
    } else if (job.state === State.PENDING) {
      changeJobState({ id: job.id, state: State.READY });
    } else {
      console.error(`job started in unexpected state ${job.state}`);
    }
  }

  const [deleteJob] = useMutationPopups(useDeleteJobMutation());

  const jobIsOrchestratable =
    job.state === State.READY || job.state === State.IN_PROGRESS;

  const editButtonStyle = {
    ...BUTTON,
    className: classes(BUTTON.className, "ml-4"),
  };

  const deleteButtonStyle = {
    ...RED_BUTTON,
    className: classes(RED_BUTTON.className, "w-1/3 mx-4 my-2 self-center"),
  };

  const orchestrateButtonStyle = {
    ...WHITE_BUTTON,
    className: classes(WHITE_BUTTON.className, "col-span-full"),
  };

  return (
    <div className="grid grid-cols-[1fr_4fr] grid-rows-[auto_minmax(0,1fr)] gap-y-3 w-full h-full">
      {canEditJob && renderEditJobDialog()}
      {confirmCancel && (
        <ConfirmationDialog
          title={t("components.ConfirmationDialog.title")}
          description={t(
            "views.autotractor.jobDetails.cancelDialog.description"
          )}
          destructive
          yesText={t("utils.actions.cancelLong", {
            subject: t("models.jobs.job_one"),
          })}
          noText={t("utils.actions.leaveUnchanged")}
          onClose={() => setConfirmCancel(false)}
          onYes={() => changeJobState({ id: job.id, state: State.CANCELLED })}
        />
      )}
      {confirmDelete && (
        <ConfirmationDialog
          title={t("components.ConfirmationDialog.title")}
          description={t("components.ConfirmationDialog.delete.description", {
            subject: capitalize(t("models.jobs.job_one")),
          })}
          destructive
          yesText={t("utils.actions.delete")}
          onClose={() => setConfirmDelete(false)}
          onYes={async () => {
            const result = await deleteJob({ id: job.id });
            if (result.error === undefined) {
              navigate(Path.JOBS);
            }
          }}
        />
      )}
      <Typography variant="h1" className="col-span-full text-4xl">
        {job.name}
        {canEditJob && (
          <Button
            {...editButtonStyle}
            startIcon={<EditIcon />}
            onClick={openEditJobDialog}
          >
            {t("utils.actions.editLong", { subject: t("models.jobs.job_one") })}
          </Button>
        )}
      </Typography>
      <Paper className="flex flex-col h-full gap-2 pt-2">
        {summaryCardItems.map((props, index) => (
          <SummaryCardItem
            {...props}
            key={`summary-${index}`}
            className="mx-4 min-h-8 items-center"
          />
        ))}
        {canEditJob && (
          <div className="grid grid-cols-3 gap-4 mx-4 my-2">
            <Button
              {...GREEN_BUTTON}
              disabled={!jobIsStartable(job)}
              startIcon={<PlayArrowIcon />}
              onClick={startJob}
            >
              {t("utils.actions.run")}
            </Button>
            <Button
              {...YELLOW_BUTTON}
              disabled={!jobIsPauseable(job)}
              startIcon={<PauseIcon />}
              onClick={() =>
                changeJobState({ id: job.id, state: State.PAUSED })
              }
            >
              {t("utils.actions.pause")}
            </Button>
            <Button
              {...RED_BUTTON}
              disabled={!jobIsCancellable(job)}
              startIcon={<StopIcon />}
              onClick={() => setConfirmCancel(true)}
            >
              {t("utils.actions.cancel")}
            </Button>
            <Button
              {...orchestrateButtonStyle}
              disabled={!jobIsOrchestratable}
              startIcon={<DashboardIcon />}
              onClick={() => navigate(getJobOrchestratePath(job.id))}
            >
              {t("views.autotractor.actions.orchestrateView")}
            </Button>
          </div>
        )}
        <div className="h-full overflow-y-auto">
          <RobotSelectionList job={job} />
          <ObjectiveReorderList jobId={job.id} objectives={job.objectives} />
        </div>
        {canEditJob && (
          <Button {...deleteButtonStyle} onClick={() => setConfirmDelete(true)}>
            {t("utils.actions.deleteLong", {
              subject: t("models.jobs.job_one"),
            })}
          </Button>
        )}
      </Paper>
      <div className="flex h-full w-full">
        <OrchestrationMap job={job} hideTasks />
      </div>
    </div>
  );
};
