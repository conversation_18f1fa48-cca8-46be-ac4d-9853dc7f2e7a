import { buildPermission, getCustomerId } from "portal/utils/auth";
import { Button } from "@mui/material";
import { capitalize, titleCase } from "portal/utils/strings";
import { CarbonDataGrid } from "portal/components/CarbonDataGrid";
import { CustomerResponse } from "protos/portal/customers";
import { CustomerSelector } from "portal/components/customers/CustomerSelector";
import { getJobPath } from "portal/utils/routing";
import { GridColDef } from "@mui/x-data-grid-premium";
import { Header } from "portal/components/header/Header";
import { Job } from "protos/rtc/jobs";
import { NoScroll, Page } from "portal/components/Page";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import { useListCustomersQuery } from "portal/state/portalApi";
import { useListJobsQuery } from "common/state/rtcJobsApi";
import { useNavigate } from "react-router-dom";
import { useNewJobDialog } from "./SetJobForm";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import AddIcon from "@mui/icons-material/Add";
import React, { FunctionComponent, useMemo, useState } from "react";

const getRowId = (job: Job): number => {
  return job.id;
};

export const JobList: FunctionComponent = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useSelf();
  const userCustomerId = getCustomerId(user);

  const { data: jobsResponse, isLoading } = useQueryPopups(
    useListJobsQuery({ pageSize: 100 })
  );
  const { data: allCustomers } = useQueryPopups(useListCustomersQuery());

  const canReadCustomers = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.customers,
      PermissionDomain.all
    ),
  ]);

  const canCreateJob = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.all
    ),
  ]);

  const { renderDialog: renderNewJobDialog, open: openNewJobDialog } =
    useNewJobDialog();

  const [customerId, setCustomerId] = useState<number | undefined>(undefined);
  const customersById = useMemo(() => {
    const result = new Map<number, CustomerResponse>();
    for (const customer of allCustomers ?? []) {
      const id = customer.db?.id;
      if (id !== undefined) {
        result.set(id, customer);
      }
    }
    return result;
  }, [allCustomers]);

  const filteredJobs = useMemo(() => {
    if (!jobsResponse?.jobs) {
      return [];
    }
    if (canReadCustomers && customerId === undefined) {
      return jobsResponse.jobs;
    }
    return jobsResponse.jobs.filter((job) => {
      if (canReadCustomers) {
        return job.customerId === customerId?.toString();
      }
      return job.customerId === userCustomerId?.toString();
    });
  }, [jobsResponse, customerId, canReadCustomers, userCustomerId]);

  const columns = useMemo<GridColDef<Job>[]>(() => {
    const defaultColumn: Partial<GridColDef> = {
      disableColumnMenu: true,
      flex: 1,
    };
    return [
      {
        ...defaultColumn,
        field: "name",
        headerName: capitalize(t("utils.descriptors.name")),
      },
      {
        ...defaultColumn,
        field: "customerId",
        headerName: capitalize(t("models.customers.customer_one")),
        valueFormatter: (customerId: string) => {
          const numId = Number.parseInt(customerId);
          return (
            customersById.get(numId)?.name ?? t("utils.descriptors.unknown")
          );
        },
      },
    ];
  }, [t, customersById]);

  return (
    <>
      {renderNewJobDialog()}
      <Header title={capitalize(t("models.jobs.job_other"))}>
        {canCreateJob && (
          <>
            <Button
              className="text-white ml-auto"
              startIcon={<AddIcon />}
              onClick={openNewJobDialog}
            >
              <span className="hidden sm:inline">
                {titleCase(
                  t("utils.actions.newLong", {
                    subject: t("models.jobs.job_one"),
                  })
                )}
              </span>
              <span className="sm:hidden">{t("utils.actions.new")}</span>
            </Button>
          </>
        )}
      </Header>
      <Page>
        <NoScroll>
          <CarbonDataGrid<Job>
            columns={columns}
            rows={filteredJobs}
            loading={isLoading}
            getRowId={getRowId}
            getRowClassName={() => "cursor-pointer"}
            onRowClick={({ row: job }) => navigate(getJobPath(job.id))}
            disableRowSelectionOnClick
            hideFooter
            header={
              canReadCustomers && (
                <CustomerSelector
                  small
                  value={customerId}
                  onChange={setCustomerId}
                  emptyText={t("utils.table.showAll", {
                    objects: t("models.customers.customer_other"),
                  })}
                  className="md:max-w-80"
                />
              )
            }
          />
        </NoScroll>
      </Page>
    </>
  );
};
