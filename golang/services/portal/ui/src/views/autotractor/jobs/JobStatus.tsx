/* eslint-disable camelcase */
import { DateTime } from "luxon";
import { getFarmPath } from "portal/utils/routing";
import { IconButton, Tooltip } from "@mui/material";
import { Job_JobType, Job as JobProto, State } from "protos/rtc/jobs";
import { JobSelectionType } from "common/state/jobExplorer";
import { Link } from "react-router-dom";
import { SummaryCardItemProps } from "portal/components/SummaryCardItem";
import { useGetFarmQuery } from "common/state/portalApi/farm";
import { useListCustomersQuery } from "portal/state/portalApi";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import AccessTimeIcon from "@mui/icons-material/AccessTimeOutlined";
import AutorenewIcon from "@mui/icons-material/Autorenew";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CustomerIcon from "portal/images/icons/farm.svg?react";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import EditIcon from "@mui/icons-material/Edit";
import ErrorIcon from "@mui/icons-material/Error";
import FiberNewIcon from "@mui/icons-material/FiberNew";
import FieldIcon from "portal/images/icons/field.svg?react";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import JobIcon from "@mui/icons-material/AssignmentOutlined";
import NavigateNextIcon from "@mui/icons-material/NavigateNextOutlined";
import PauseCircleFilledIcon from "@mui/icons-material/PauseCircleFilled";
import React, { FC, ReactNode } from "react";
import TaskAltIcon from "@mui/icons-material/TaskAlt";

const EditJobDialogActionButton: FC<{ openEditJobDialog: () => void }> = ({
  openEditJobDialog,
}) => {
  const { t } = useTranslation();

  return (
    <Tooltip
      title={t("utils.actions.editLong", { subject: t("models.jobs.job_one") })}
      arrow
    >
      <IconButton
        className="text-zinc-400 hover:text-white"
        size="medium"
        onClick={openEditJobDialog}
      >
        <EditIcon style={{ color: "inherit", fontSize: "inherit" }} />
      </IconButton>
    </Tooltip>
  );
};

export function useCustomerStatus(job: JobProto): SummaryCardItemProps[] {
  const { t } = useTranslation();
  const { isLoading, data: customers } = useQueryPopups(
    useListCustomersQuery()
  );

  const customerIdNumber = Number.parseInt(job.customerId);
  const customer = Number.isNaN(customerIdNumber)
    ? undefined
    : customers?.find((customer) => customer.db?.id === customerIdNumber);
  const customerText = customer
    ? t("views.autotractor.jobDetails.customer.withName", {
        name: customer.name,
      })
    : t("views.autotractor.jobDetails.customer.unknown");

  return [
    {
      icon: <CustomerIcon />,
      text: isLoading ? t("components.Loading.placeholder") : customerText,
    },
  ];
}

export function useFarmStatus(
  job: JobProto,
  openEditJobDialog: (() => void) | undefined
): SummaryCardItemProps[] {
  const { t } = useTranslation();
  const { isLoading, data: farm } = useQueryPopups(useGetFarmQuery(job.farmId));

  const editFieldActions = openEditJobDialog
    ? [
        <EditJobDialogActionButton
          key="edit"
          openEditJobDialog={openEditJobDialog}
        />,
      ]
    : [];

  if (farm) {
    const farmLinkOutButton = farm.id && (
      <Tooltip
        key="link"
        title={t("views.autotractor.jobDetails.openInFarmView")}
        arrow
      >
        <IconButton
          className="text-zinc-400 hover:text-white"
          size="medium"
          component={Link}
          to={getFarmPath(farm.id.id)}
        >
          <NavigateNextIcon style={{ color: "inherit", fontSize: "inherit" }} />
        </IconButton>
      </Tooltip>
    );
    const farmProps: SummaryCardItemProps = {
      icon: <CustomerIcon />,
      text: t("views.autotractor.jobDetails.farm.withName", {
        name: farm.name,
      }),
      actions: farmLinkOutButton ? [farmLinkOutButton] : [],
    };

    const zone =
      job.fieldId && farm.zones.find((zone) => zone.id?.id === job.fieldId);

    if (zone) {
      const fieldLinkOutButton = farm.id && (
        <Tooltip
          key="link"
          title={t("views.autotractor.jobDetails.openInFarmView")}
          arrow
        >
          <IconButton
            className="hover:text-white"
            size="medium"
            component={Link}
            to={getFarmPath(farm.id.id)}
            state={{ type: JobSelectionType.ZONE, selection: zone }}
          >
            <NavigateNextIcon style={{ fontSize: "inherit" }} />
          </IconButton>
        </Tooltip>
      );

      return [
        farmProps,
        {
          icon: <FieldIcon width={24} />,
          text: t("views.autotractor.jobDetails.field.withName", {
            name: zone.name,
          }),
          actions: fieldLinkOutButton
            ? [...editFieldActions, fieldLinkOutButton]
            : editFieldActions,
        },
      ];
    } else {
      return [
        farmProps,
        {
          icon: <FieldIcon width={24} />,
          text: t("views.autotractor.jobDetails.field.unknown"),
          actions: editFieldActions,
        },
      ];
    }
  } else {
    return [
      {
        icon: <CustomerIcon />,
        text: isLoading
          ? t("components.Loading.placeholder")
          : t("views.autotractor.jobDetails.farm.unknown"),
        actions: [],
      },
      {
        icon: <FieldIcon width={24} />,
        text: isLoading
          ? t("components.Loading.placeholder")
          : t("views.autotractor.jobDetails.field.unknown"),
        actions: editFieldActions,
      },
    ];
  }
}

export function useJobTypeStatus(
  job: JobProto,
  openEditJobDialog: (() => void) | undefined
): SummaryCardItemProps[] {
  const { t } = useTranslation();

  let typeText: string | undefined;

  switch (job.type) {
    case Job_JobType.LASER_WEED: {
      typeText = t("models.autotractor.jobTypes.laserWeed");
      break;
    }
    default: {
      typeText = t("models.autotractor.jobTypes.unrecognized", {
        value: job.type,
      });
      break;
    }
  }

  return [
    {
      icon: <JobIcon />,
      text: t("views.autotractor.jobDetails.type", {
        type: typeText,
      }),
      actions: openEditJobDialog
        ? [
            <EditJobDialogActionButton
              key="edit"
              openEditJobDialog={openEditJobDialog}
            />,
          ]
        : [],
    },
  ];
}

export function useJobStateStatus(job: JobProto): SummaryCardItemProps[] {
  const { t } = useTranslation();

  let icon: ReactNode | undefined;
  let stateText: string | undefined;

  switch (job.state) {
    case State.PENDING: {
      icon = <HourglassEmptyIcon />;
      stateText = t("models.autotractor.states.pending");
      break;
    }
    case State.READY: {
      icon = <TaskAltIcon />;
      stateText = t("models.autotractor.states.ready");
      break;
    }
    case State.IN_PROGRESS: {
      icon = <AutorenewIcon className="animate-spin" />;
      stateText = t("models.autotractor.states.inProgress");
      break;
    }
    case State.COMPLETED: {
      icon = <CheckCircleIcon />;
      stateText = t("models.autotractor.states.completed");
      break;
    }
    case State.CANCELLED: {
      icon = <CancelIcon />;
      stateText = t("models.autotractor.states.cancelled");
      break;
    }
    case State.PAUSED: {
      icon = <PauseCircleFilledIcon />;
      stateText = t("models.autotractor.states.paused");
      break;
    }
    case State.FAILED: {
      icon = <ErrorIcon />;
      stateText = t("models.autotractor.states.failed");
      break;
    }
    case State.ACKNOWLEDGED: {
      icon = <DoneAllIcon />;
      stateText = t("models.autotractor.states.acknowledged");
      break;
    }
    case State.NEW: {
      icon = <FiberNewIcon />;
      stateText = t("models.autotractor.states.new");
      break;
    }
    default: /* UNRECOGNIZED, STATE_UNSPECIFIED */ {
      icon = <HelpOutlineIcon />;
      stateText = t("models.autotractor.states.unrecognized", {
        value: job.state,
      });
      break;
    }
  }

  return [
    {
      icon,
      text: t("views.autotractor.jobDetails.state", { state: stateText }),
    },
  ];
}

export function useJobTimesStatus(job: JobProto): SummaryCardItemProps[] {
  const { t } = useTranslation();
  const items: SummaryCardItemProps[] = [];
  const startDate = job.startedAt ? DateTime.fromISO(job.startedAt) : undefined;
  const endDate = job.endedAt ? DateTime.fromISO(job.endedAt) : undefined;

  if (startDate) {
    items.push({
      icon: <AccessTimeIcon />,
      text: t("views.autotractor.jobDetails.jobStarted", {
        time: startDate.toLocaleString(DateTime.DATETIME_SHORT),
      }),
    });
  }

  if (endDate) {
    items.push({
      icon: <AccessTimeIcon />,
      text: t("views.autotractor.jobDetails.jobFinished", {
        time: endDate.toLocaleString(DateTime.DATETIME_SHORT),
      }),
    });
  }

  return items;
}
