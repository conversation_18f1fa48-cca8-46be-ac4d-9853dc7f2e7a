import { buildPermission } from "portal/utils/auth";
import { JobDetailsPage } from "./JobDetailsPage";
import { JobList } from "./JobList";
import { OrchestratePage } from "./orchestrate/OrchestratePage";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { useRoutes } from "react-router-dom";
import { withAuthenticationRequired } from "@auth0/auth0-react";
import { withAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import React, { FunctionComponent } from "react";

const _Jobs: FunctionComponent = () => {
  return useRoutes([
    { path: "/", element: <JobList /> },
    { path: "/:jobId", element: <JobDetailsPage /> },
    { path: "/:jobId/orchestrate", element: <OrchestratePage /> },
  ]);
};

export const Jobs = withAuthenticationRequired(
  withAuthorizationRequired(
    [
      buildPermission(
        PermissionAction.read,
        PermissionResource.autotractor_jobs,
        PermissionDomain.customer
      ),
      buildPermission(
        PermissionAction.read,
        PermissionResource.autotractor_jobs,
        PermissionDomain.all
      ),
    ],
    _Jobs
  )
);
