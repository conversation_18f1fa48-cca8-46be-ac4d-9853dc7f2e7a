import { Objective as ObjectiveProto } from "protos/rtc/jobs";
import { SortableContainer } from "portal/components/Sortable";

import { CircularProgress, Typography } from "@mui/material";
import { classes } from "portal/theme/theme";
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { JobSelectionType, setFocus } from "common/state/jobExplorer";
import { reprioritize } from "./reprioritize";
import { Section } from "portal/components/map/navigation/Section";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { t } from "i18next";
import { useDispatch } from "react-redux";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useUpdateObjectivePrioritiesMutation } from "common/state/rtcJobsApi";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import React, { FC, useCallback, useState } from "react";

interface ObjectiveReorderListProps {
  jobId: number;
  objectives: ObjectiveProto[];
}

export const ObjectiveReorderList: FC<ObjectiveReorderListProps> = ({
  jobId,
  objectives,
}) => {
  const [readOnly, setReadOnly] = useState<boolean>(false);
  const [updateObjectivePriorities] = useMutationPopups(
    useUpdateObjectivePrioritiesMutation()
  );
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 6,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const dispatch = useDispatch();
  const focusObjective = useCallback(
    (objective: ObjectiveProto): void => {
      dispatch(
        setFocus({ type: JobSelectionType.OBJECTIVE, selection: objective })
      );
    },
    [dispatch]
  );

  interface SortableObjective {
    id: string;
    index: number;
    objective: ObjectiveProto;
  }

  const render: (o: SortableObjective) => JSX.Element = ({
    index,
    objective,
  }) => {
    return (
      <div
        className="flex flex-row items-center my-2 cursor-grab hover:bg-lighten-100"
        onMouseOver={() => focusObjective(objective)}
        onFocus={() => focusObjective(objective)}
      >
        <DragIndicatorIcon
          className={classes("ml-4 mr-2", { "opacity-0": readOnly })}
        />
        <span className={classes({ "text-zinc-400": readOnly })}>
          {index + 1}. {objective.name}
        </span>
      </div>
    );
  };

  const sortableObjectives: SortableObjective[] = objectives.map(
    (objective, index) => ({
      id: `obj-${objective.id}`,
      index,
      objective,
    })
  );

  async function handleDragEnd(event: DragEndEvent): Promise<void> {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sortableObjectives.findIndex(
        ({ id }) => id === active.id
      );
      const newIndex = sortableObjectives.findIndex(({ id }) => id === over.id);

      if (oldIndex === -1) {
        console.error(
          `ObjectiveReorderList: couldn't find active id ${active.id}`
        );
        return;
      }

      if (newIndex === -1) {
        console.error(`ObjectiveReorderList: couldn't find over id ${over.id}`);
        return;
      }

      const { modifiedPriorities } = reprioritize(
        objectives,
        oldIndex,
        newIndex
      );

      // turn off drag-drop while we do the potentially non-atomic update to
      // the objective priority list on the server
      setReadOnly(true);
      await updateObjectivePriorities({
        jobId,
        changes: [...modifiedPriorities.entries()].map(([id, priority]) => ({
          id,
          priority,
        })),
      });
      setReadOnly(false);
    }
  }

  return (
    <Section
      header={
        <div className="flex flex-row items-center">
          <Typography>{t("models.autotractor.objective_other")}</Typography>
          {readOnly && (
            <CircularProgress
              className="ml-4"
              size="1rem"
              variant="indeterminate"
            />
          )}
        </div>
      }
    >
      <div className="overflow-x-hidden">
        <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
          <SortableContainer
            id="objectives"
            items={sortableObjectives}
            render={render}
            disabled={readOnly}
          />
        </DndContext>
      </div>
    </Section>
  );
};
