import {
  Checkbox,
  CircularProgress,
  FormControlLabel,
  Typography,
} from "@mui/material";
import { Job as JobProto } from "protos/rtc/jobs";
import { Section } from "portal/components/map/navigation/Section";
import { useListRobotsQuery } from "common/state/rtcLocatorApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { useUpdateAvailableRobotsMutation } from "common/state/rtcJobsApi";
import React, { FC } from "react";

interface RobotSelectionListProps {
  job: JobProto;
}

export const RobotSelectionList: FC<RobotSelectionListProps> = ({ job }) => {
  const { t } = useTranslation();

  const { data: robots } = useQueryPopups(useListRobotsQuery({}));
  const [updateAvailableRobots, updateAvailableRobotsResult] =
    useMutationPopups(useUpdateAvailableRobotsMutation());
  const availableSerials = new Set(
    job.robotWhitelist?.entries.map((entry) => entry.robotSerial) ?? []
  );

  return (
    <Section
      header={
        <div className="flex flex-row items-center">
          <Typography>{t("models.robots.robot_other")}</Typography>
          {updateAvailableRobotsResult.isLoading && (
            <CircularProgress
              className="ml-4"
              size="1rem"
              variant="indeterminate"
            />
          )}
        </div>
      }
    >
      <div className="flex-1">
        {robots &&
          robots.robots.map((bot) => (
            <div className="flex flex-row items-center pl-4" key={bot.serial}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={availableSerials.has(bot.serial)}
                    onChange={async (event, checked) => {
                      let newAvailableSerials: Set<string>;

                      if (checked) {
                        newAvailableSerials = availableSerials.add(bot.serial);
                      } else {
                        newAvailableSerials = availableSerials;
                        newAvailableSerials.delete(bot.serial);
                      }

                      await updateAvailableRobots({
                        jobId: job.id,
                        serials: [...newAvailableSerials],
                      });
                    }}
                  />
                }
                label={bot.serial}
              />
            </div>
          ))}
      </div>
    </Section>
  );
};
