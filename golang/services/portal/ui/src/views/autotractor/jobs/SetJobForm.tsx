/* eslint-disable camelcase */
import * as yup from "yup";
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  Input,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import { Form, Formik } from "formik";
import { getCustomerId } from "portal/utils/auth";
import { Job, Job_JobType, job_JobTypeFromJSON, State } from "protos/rtc/jobs";
import { t } from "i18next";
import { titleCase } from "portal/utils/strings";
import {
  useCreateJobMutation,
  useUpdateJobMutation,
} from "common/state/rtcJobsApi";
import { useListFarmsQuery } from "common/state/portalApi/farm";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import React, { FC, ReactNode, useCallback, useState } from "react";

interface SetJobFormProps {
  initialJob?: Partial<Job>;
  mode: "new-job" | "edit-pending" | "edit-active";
  onSubmit: (job: Partial<Job>) => void;
  onCancel: () => void;
}

const SetJobForm: FC<SetJobFormProps> = ({
  onSubmit,
  onCancel,
  initialJob,
  mode,
}) => {
  const showFarm = mode === "new-job";
  const showZone = mode === "new-job" || mode === "edit-pending";
  const { data: farms, isLoading: farmsLoading } = useListFarmsQuery(
    {
      contents: true,
    },
    { skip: !showFarm && !showZone }
  );

  const effectiveInitialJob: Partial<Job> = initialJob ?? {
    type: Job_JobType.LASER_WEED,
    farmId: "",
    fieldId: "",
    name: "",
  };

  const validationSchema = yup.object({
    type: yup
      .mixed<Job_JobType>()
      .oneOf(Object.values(Job_JobType).map((t) => job_JobTypeFromJSON(t)))
      .required(),
    farmId: yup.string().required(),
    fieldId: yup.string().required(),
    name: yup.string().required(),
  });

  return farmsLoading ? (
    <div className="flex flex-row justify-center">
      <CircularProgress variant="indeterminate" className="mr-2" />
      <Typography className="text-3xl">
        {t("components.Loading.placeholder")}
      </Typography>
    </div>
  ) : (
    <div className="flex flex-col gap-4 p-4">
      <Formik
        initialValues={effectiveInitialJob}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          onSubmit(values);
        }}
      >
        {({
          values,
          handleChange,
          handleBlur,
          submitForm,
          handleReset,
          isSubmitting,
          isValid,
        }) => (
          <Form className="flex flex-col gap-4">
            <FormControl fullWidth>
              <InputLabel id="name-label">
                {titleCase(t("utils.descriptors.name"))}
              </InputLabel>
              <Input
                id="name"
                type="string"
                value={values.name}
                onChange={handleChange}
                onBlur={handleBlur}
              />
            </FormControl>
            <FormControl fullWidth>
              <InputLabel id="type-label">
                {titleCase(t("utils.descriptors.type_one"))}
              </InputLabel>
              <Select
                title={titleCase(t("utils.descriptors.type_one"))}
                labelId="type-label"
                id="type-selector"
                value={values.type}
                label={titleCase(t("utils.descriptors.type_one"))}
                onChange={handleChange}
              >
                <MenuItem value={Job_JobType.LASER_WEED}>
                  {t("models.autotractor.jobTypes.laserWeed")}
                </MenuItem>
              </Select>
            </FormControl>
            {showFarm && (
              <FormControl fullWidth>
                <InputLabel id="farm-label">
                  {titleCase(t("models.farms.farm_one"))}
                </InputLabel>
                <Select
                  title={titleCase(t("models.farms.farm_one"))}
                  labelId="farm-label"
                  id="farmId"
                  name="farmId"
                  value={values.farmId}
                  label={titleCase(t("models.farms.farm_one"))}
                  onChange={handleChange}
                >
                  {farms?.map((f) => (
                    <MenuItem key={f.id?.id} value={f.id?.id}>
                      {f.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
            {showZone && (
              <FormControl fullWidth>
                <InputLabel id="zone-label">
                  {titleCase(t("models.farms.zone_one"))}
                </InputLabel>
                <Select
                  disabled={!values.farmId}
                  title={titleCase(t("models.farms.zone_one"))}
                  labelId="zone-label"
                  id="fieldId"
                  name="fieldId"
                  value={values.fieldId}
                  label={titleCase(t("models.farms.zone_one"))}
                  onChange={handleChange}
                >
                  {values.farmId &&
                    farms
                      ?.find((f) => f.id?.id === values.farmId)
                      ?.zones.filter((z) => Boolean(z.contents?.field)) // todo: should we allow jobs on non-fields?
                      .map((z) => (
                        <MenuItem key={z.id?.id} value={z.id?.id}>
                          {z.name}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            )}
            <div className="flex">
              <Button
                fullWidth
                variant="contained"
                disabled={!isValid || isSubmitting}
                type="submit"
                onClick={submitForm}
              >
                {mode === "new-job"
                  ? t("utils.actions.create")
                  : t("utils.actions.save")}
              </Button>
              <Button
                fullWidth
                color="info"
                onClick={() => {
                  onCancel();
                  handleReset();
                }}
              >
                {t("utils.actions.cancel")}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export function useNewJobDialog(initialJob?: Partial<Job>): {
  renderDialog: () => ReactNode;
  open: () => void;
  close: () => void;
} {
  const { user } = useSelf();
  const userCustomerId = getCustomerId(user);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [createJob] = useMutationPopups(useCreateJobMutation());

  const handleCreateJob = useCallback(
    async (j: Partial<Job>): Promise<void> => {
      const result = await createJob({
        job: {
          ...j,
          customerId: userCustomerId?.toString(),
        },
        generateObjectives: true,
      });
      if (!result.error) {
        setIsOpen(false);
      }
    },
    [createJob, userCustomerId]
  );

  const handleCancelJob = useCallback((): void => {
    setIsOpen(false);
  }, []);

  return {
    renderDialog: () => (
      <Dialog open={isOpen} fullWidth>
        <DialogTitle>
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.jobs.job_one"),
            })
          )}
        </DialogTitle>
        <DialogContent>
          <SetJobForm
            initialJob={initialJob}
            onCancel={handleCancelJob}
            onSubmit={handleCreateJob}
            mode="new-job"
          />
        </DialogContent>
      </Dialog>
    ),
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
}

export function useEditJobDialog(initialJob: Job): {
  renderDialog: () => ReactNode;
  open: () => void;
  close: () => void;
} {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [updateJob] = useMutationPopups(useUpdateJobMutation());

  // after a job has been readied, don't allow the user to move it between
  // fields; this requires regenerating the job objectives, which can't be
  // done at that point.
  const mode =
    initialJob.state === State.PENDING ? "edit-pending" : "edit-active";

  const handleUpdateJob = async (j: Partial<Job>): Promise<void> => {
    const updatedJob = { ...initialJob, ...j };
    const result = await updateJob({
      job: updatedJob,
      generateObjectives:
        initialJob.state === State.PENDING &&
        j.fieldId !== undefined &&
        j.fieldId !== initialJob.fieldId,
    });
    if (!result.error) {
      setIsOpen(false);
    }
  };

  const handleCancelJob = (): void => {
    setIsOpen(false);
  };

  return {
    renderDialog: () => (
      <Dialog open={isOpen} fullWidth>
        <DialogTitle>
          {titleCase(
            t("utils.actions.editLong", {
              subject: t("models.jobs.job_one"),
            })
          )}
        </DialogTitle>
        <DialogContent>
          <SetJobForm
            initialJob={initialJob}
            onCancel={handleCancelJob}
            onSubmit={handleUpdateJob}
            mode={mode}
          />
        </DialogContent>
      </Dialog>
    ),
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
}
