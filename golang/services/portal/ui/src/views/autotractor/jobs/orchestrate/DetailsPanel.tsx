import { CenterPivotDetails } from "./details/CenterPivotDetails";
import { Divider, IconButton, Paper, Typography } from "@mui/material";
import { JobDetails } from "./details/JobDetails";
import { ObjectiveDetails } from "./details/ObjectiveDetails";
import { ObstacleDetails } from "./details/ObstacleDetails";
import { setFocus, setSelection } from "common/state/jobExplorer";
import { TaskDetails } from "./details/TaskDetails";
import { TractorDetails } from "./details/TractorDetails";
import { useDispatch } from "react-redux";
import { useJobExplorer } from "portal/state/store";
import { useTranslation } from "react-i18next";
import CloseIcon from "@mui/icons-material/Close";
import React, { FC, ReactNode } from "react";

export const DetailsPanel: FC = () => {
  const { t } = useTranslation();
  const { showDetails, selection } = useJobExplorer();
  const dispatch = useDispatch();

  const getDetailComponent = (): ReactNode => {
    switch (selection?.type) {
      case "zone": {
        return (
          selection.selection.contents?.obstacle && (
            <ObstacleDetails obstacle={selection.selection} />
          )
        );
      }
      case "objective": {
        return <ObjectiveDetails objective={selection.selection} />;
      }
      case "centerPivot": {
        return <CenterPivotDetails centerPivot={selection.selection} />;
      }
      case "tractor": {
        return <TractorDetails tractor={selection.selection} />;
      }
      case "job": {
        return <JobDetails job={selection.selection} />;
      }
      case "task": {
        return <TaskDetails task={selection.selection} />;
      }
      default:
    }
  };
  const getSelectionName = (): ReactNode => {
    switch (selection?.type) {
      case "job":
      case "zone":
      case "task":
      case "objective": {
        return selection.selection.name;
      }
      case "tractor": {
        return selection.selection.serial;
      }
      case "centerPivot": {
        return t("views.farms.detailsPanel.centerPivot");
      }
      default:
    }
  };

  return (
    showDetails && (
      <Paper className="flex flex-col gap-2 w-full h-full pt-2">
        <div className="flex justify-between px-2">
          <Typography
            variant="h4"
            className="break-all max-h-36 overflow-y-auto"
          >
            {getSelectionName()}
          </Typography>
          <IconButton
            color="info"
            className="flex my-auto"
            onClick={() => {
              dispatch(setSelection(undefined));
              dispatch(setFocus(undefined));
            }}
          >
            <CloseIcon />
          </IconButton>
        </div>
        <Divider />
        <div className="h-full overflow-y-auto">{getDetailComponent()}</div>
      </Paper>
    )
  );
};
