/* eslint-disable jsx-a11y/no-static-element-interactions */
import { AutoSizer, List } from "react-virtualized";
import { classes } from "portal/theme/theme";
import {
  JobSelectionType,
  setFocus,
  setSelection,
} from "common/state/jobExplorer";
import { LinearProgress, Typography } from "@mui/material";
import { Objective } from "protos/rtc/jobs";
import { SelectionButton } from "portal/components/map/navigation/SelectionButton";
import { useDispatch } from "react-redux";
import { useJobExplorer } from "portal/state/store";
import DoneIcon from "@mui/icons-material/Done";
import React, { CSSProperties, FC, ReactNode } from "react";
interface ObjectivesSelectionListProps {
  objectives: Objective[];
}
// use this if you have more than a handful of objectives. Virtualizes the list so you can show many many objectives
export const ObjectivesSelectionList: FC<ObjectivesSelectionListProps> = ({
  objectives,
}: ObjectivesSelectionListProps) => {
  const dispatch = useDispatch();

  const { focus, selection } = useJobExplorer();

  const isObjectiveFocused = (id: number): boolean => {
    return (
      focus?.type === JobSelectionType.OBJECTIVE && focus.selection.id === id
    );
  };
  const isObjectiveSelected = (id: number): boolean => {
    return (
      selection?.type === JobSelectionType.OBJECTIVE &&
      selection.selection.id === id
    );
  };

  const renderRow = ({
    style,
    index,
    key,
  }: {
    style: CSSProperties;
    index: number;
    key: string;
  }): ReactNode => {
    const objective = objectives[index];
    return (
      <div key={key} style={style}>
        {objective && (
          <div
            key={objective.id}
            className={classes(
              "flex flex-col cursor-pointer",
              isObjectiveFocused(objective.id) && "bg-blue-400",
              isObjectiveSelected(objective.id) && "bg-blue-800"
            )}
            onMouseEnter={() =>
              dispatch(
                setFocus({
                  type: JobSelectionType.OBJECTIVE,
                  selection: objective,
                })
              )
            }
            onMouseLeave={() => dispatch(setFocus(undefined))}
            onClick={() =>
              dispatch(
                setSelection({
                  type: JobSelectionType.OBJECTIVE,
                  selection: objective,
                })
              )
            }
          >
            <div>
              <SelectionButton
                subSection
                onClick={() =>
                  dispatch(
                    setSelection({
                      type: JobSelectionType.OBJECTIVE,
                      selection: objective,
                    })
                  )
                }
                isSelected={isObjectiveSelected(objective.id)}
                isFocused={isObjectiveFocused(objective.id)}
                endIcon={
                  objective.progressPercent === 100 && (
                    <DoneIcon className="fill-green" />
                  )
                }
              >
                {objective.name}
              </SelectionButton>
            </div>
            <div className="flex items-center gap-2 px-4">
              <div className="w-full">
                <LinearProgress
                  variant="determinate"
                  color="success"
                  value={objective.progressPercent}
                />
              </div>
              <Typography>{`${objective.progressPercent}%`}</Typography>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <AutoSizer className="min-h-96">
      {({ height, width }) => (
        <List
          height={height}
          width={width}
          rowHeight={58}
          rowCount={objectives.length}
          rowRenderer={renderRow}
        />
      )}
    </AutoSizer>
  );
};
