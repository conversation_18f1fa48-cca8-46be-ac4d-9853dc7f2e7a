import { AVAILABLE_COLORS } from "common/utils/rtcJobs";
import { capitalize } from "portal/utils/strings";
import { CircularProgress, IconButton, Paper } from "@mui/material";
import { derefPoint, getFieldBounds } from "common/utils/farm";
import { getSequence } from "portal/utils/robots";
import {
  hideTractor,
  JobSelectionType,
  setFocus,
  setHiddenTractors,
  setSelection,
  setTractorColor,
} from "common/state/jobExplorer";
import { isWithinBounds } from "portal/utils/geo";
import { NumberedTractorIcon } from "common/components/map/NumberedTractorIcon";
import { ObjectivesSelectionList } from "./ObjectiveSelectionList";
import { Section } from "portal/components/map/navigation/Section";
import { SelectionButton } from "portal/components/map/navigation/SelectionButton";
import { skipToken } from "@reduxjs/toolkit/query";
import { theme } from "portal/theme/theme";
import { useDispatch } from "react-redux";
import { useFarmMapData } from "common/hooks/useFarmMapData";
import { useJobExplorer } from "portal/state/store";
import { useListRobotsQuery } from "common/state/rtcLocatorApi";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { Zone } from "protos/portal/farm";
import { ZoneIcon } from "portal/components/ZoneIcon";
import CenterPivotIcon from "common/images/icons/irrigation.svg?react";
import ContentPasteIcon from "@mui/icons-material/ContentPaste";
import FarmIcon from "portal/images/icons/farm.svg?react";
import JobIcon from "@mui/icons-material/AssignmentOutlined";
import React, { FC, useMemo } from "react";
import RefreshIcon from "@mui/icons-material/Refresh";
import TractorIcon from "portal/images/rtc_icon_border.svg?react";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import VisibilityOnIcon from "@mui/icons-material/Visibility";

export const SelectionPanel: FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { hiddenTractors, focus, selection, job, colorsBySerial, farm } =
    useJobExplorer();

  const {
    data: availableTractors,
    refetch: refetchTractors,
    isLoading: tractorsLoading,
    isFetching: tractorsFetching,
  } = useQueryPopups(
    useListRobotsQuery(
      job
        ? {
            serials: job.robotWhitelist?.entries.map((r) => r.robotSerial),
          }
        : skipToken
    )
  );

  if (availableTractors?.robots) {
    for (const tractor of availableTractors.robots) {
      const i = getSequence(tractor.serial) % AVAILABLE_COLORS.length;
      if (!colorsBySerial[tractor.serial]) {
        dispatch(
          setTractorColor({
            serial: tractor.serial,
            color: AVAILABLE_COLORS[i] ?? "#FF0000",
          })
        );
      }
    }
  }

  const isTractorFocused = (serial: string): boolean => {
    return (
      focus?.type === JobSelectionType.TRACTOR &&
      focus.selection.serial === serial
    );
  };
  const isTractorSelected = (serial: string): boolean => {
    return (
      selection?.type === JobSelectionType.TRACTOR &&
      selection.selection.serial === serial
    );
  };
  const toggleVisibility = (serial: string): void => {
    if (hiddenTractors.includes(serial)) {
      dispatch(setHiddenTractors(hiddenTractors.filter((s) => s !== serial)));
    } else {
      dispatch(hideTractor(serial));
    }
  };
  const sortedObjectives = job?.objectives
    ? job.objectives.toSorted((a, b) => a.priority - b.priority)
    : undefined;

  const farmMapData = useFarmMapData(farm);

  const field = useMemo(() => {
    return farm?.zones.find((z) => z.id?.id === job?.fieldId);
  }, [farm, job]);
  const centerPivot = useMemo(() => {
    // todo: change to it's own zone when the farm definition supports it
    return field?.contents?.field?.centerPivot?.center && farmMapData
      ? {
          ...field.contents.field.centerPivot,
          center: derefPoint(
            farmMapData.pointsById,
            field.contents.field.centerPivot.center
          ),
        }
      : undefined;
  }, [field, farmMapData]);

  const relevantZones = useMemo(() => {
    // co-located zones
    // todo: as of writing, jobs only specify a field. We may consider changing it to specify
    // multiple zones
    const zones: Zone[] = [];
    if (field && farmMapData) {
      const fieldBounds = getFieldBounds(field, farmMapData.pointsById);
      if (farm?.zones && fieldBounds) {
        for (const zone of farm.zones) {
          if (
            zone.id?.id &&
            zone.areas[0]?.polygon?.boundary?.points[0]?.id?.id
          ) {
            const ptId = zone.areas[0].polygon.boundary.points[0].id.id;
            const pt = farmMapData.pointsById[ptId];
            const PADDING_METERS = 100;
            if (pt && isWithinBounds(pt, fieldBounds, PADDING_METERS)) {
              zones.push(zone);
            }
          }
        }
      }
    }
    return zones.length > 0 ? zones : [];
  }, [farm, field, farmMapData]);

  return (
    <Paper className="flex flex-col w-full">
      <SelectionButton
        startIcon={<JobIcon className="h-6 w-6 ml-1" />}
        isSelected={
          selection?.type === JobSelectionType.JOB &&
          selection.selection.id === job?.id
        }
        onClick={() =>
          dispatch(
            setSelection(
              job ? { type: JobSelectionType.JOB, selection: job } : undefined
            )
          )
        }
      >
        {t("models.autotractor.job_one")}
      </SelectionButton>
      <Section
        header={
          <div className="flex gap-2 ml-0.5">
            <FarmIcon className="h-5 w-5 fill-white flex my-auto" />
            {t("models.farms.farm_one")}
          </div>
        }
      >
        {centerPivot && (
          <SelectionButton
            subSection
            startIcon={<CenterPivotIcon className="h-4 w-4 fill-white" />}
            isFocused={
              focus?.type === JobSelectionType.CENTER_PIVOT &&
              focus.selection.endpointDeviceId === centerPivot.endpointDeviceId
            }
            isSelected={
              selection?.type === JobSelectionType.CENTER_PIVOT &&
              selection.selection.endpointDeviceId ===
                centerPivot.endpointDeviceId
            }
            onMouseEnter={() =>
              dispatch(
                setFocus({
                  type: JobSelectionType.CENTER_PIVOT,
                  selection: centerPivot,
                })
              )
            }
            onMouseLeave={() => dispatch(setFocus(undefined))}
            onClick={() =>
              dispatch(
                setSelection({
                  type: JobSelectionType.CENTER_PIVOT,
                  selection: centerPivot,
                })
              )
            }
          >
            {t("views.farms.selectionPanel.centerPivot")}
          </SelectionButton>
        )}
        {relevantZones.map((z) => (
          <SelectionButton
            key={z.id?.id}
            subSection
            startIcon={<ZoneIcon zone={z} />}
            isSelected={
              selection?.type === JobSelectionType.ZONE &&
              selection.selection.id?.id === z.id?.id
            }
            isFocused={
              focus?.type === JobSelectionType.ZONE &&
              focus.selection.id?.id === z.id?.id
            }
            onClick={() =>
              dispatch(
                setSelection({ type: JobSelectionType.ZONE, selection: z })
              )
            }
            onMouseEnter={() =>
              dispatch(setFocus({ type: JobSelectionType.ZONE, selection: z }))
            }
            onMouseLeave={() => dispatch(setFocus(undefined))}
          >
            {z.name}
          </SelectionButton>
        ))}
      </Section>
      <div className="h-full overflow-y-auto">
        <Section
          header={
            <div className="flex my-auto gap-2">
              <TractorIcon className="h-6 w-6 text-slate-700 flex my-auto px-0.5" />
              {capitalize(t("views.autotractor.selection.tractors"))}
            </div>
          }
          action={{
            icon:
              tractorsLoading || tractorsFetching ? (
                <CircularProgress className="h-5 w-5" />
              ) : (
                <RefreshIcon className="text-slate-400 h-5 w-5 " />
              ),
            onClick: () => refetchTractors(),
          }}
        >
          {availableTractors?.robots.map((t) => (
            <div
              key={t.serial}
              className="flex relative"
              onMouseEnter={() =>
                dispatch(
                  setFocus({ type: JobSelectionType.TRACTOR, selection: t })
                )
              }
              onMouseLeave={() => dispatch(setFocus(undefined))}
            >
              <SelectionButton
                subSection
                isFocused={isTractorFocused(t.serial)}
                isSelected={isTractorSelected(t.serial)}
                onClick={() =>
                  dispatch(
                    setSelection({
                      type: JobSelectionType.TRACTOR,
                      selection: t,
                    })
                  )
                }
                startIcon={
                  <NumberedTractorIcon
                    serial={t.serial}
                    color={colorsBySerial[t.serial] ?? theme.colors.white}
                    fontSize={16}
                  />
                }
              >
                {t.serial}
              </SelectionButton>
              {isTractorFocused(t.serial) && (
                <IconButton
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-white z-10"
                  onClick={() => toggleVisibility(t.serial)}
                >
                  {hiddenTractors.includes(t.serial) && (
                    <VisibilityOffIcon className="h-4 w-4" />
                  )}
                  {isTractorFocused(t.serial) &&
                    !hiddenTractors.includes(t.serial) && (
                      <VisibilityOnIcon className="h-4 w-4" />
                    )}
                </IconButton>
              )}
            </div>
          ))}
        </Section>
        <Section
          header={
            <div className="flex gap-2">
              <ContentPasteIcon color="info" className="flex my-auto" />
              {capitalize(t("models.autotractor.objective_other"))}
            </div>
          }
        >
          {sortedObjectives && (
            <ObjectivesSelectionList objectives={sortedObjectives} />
          )}
        </Section>
      </div>
    </Paper>
  );
};
