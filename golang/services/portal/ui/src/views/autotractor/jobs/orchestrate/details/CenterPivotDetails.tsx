/* eslint-disable jsx-a11y/no-static-element-interactions */
import { CenterPivot } from "protos/portal/farm";
import { classes } from "portal/theme/theme";
import { LinearProgress, Typography } from "@mui/material";

import { DateTime } from "luxon";
import { getLastCheckedString, getLastUpdatedString } from "portal/utils/dates";
import {
  PIVOT_POLL_INTERVAL_S,
  PIVOT_STALE_THRESHOLD_S,
} from "common/utils/rtcJobs";
import { useJobExplorer } from "portal/state/store";
import { useTranslation } from "react-i18next";
import React, { FC, useEffect, useState } from "react";

interface CenterPivotDetailsProps {
  centerPivot: CenterPivot;
}

const STATIC_POLL_THRESHOLD_S = PIVOT_POLL_INTERVAL_S + 1;
const LAST_UPDATED_THRESHOLD = PIVOT_STALE_THRESHOLD_S;
const STATIC_POLL_UPPER_THRESHOLD_M = 10; // 10 minutes
const LAST_UPDATED_UPPER_THRESHOLD_M = 10; // 10 minutes

export const CenterPivotDetails: FC<CenterPivotDetailsProps> = ({
  centerPivot,
}) => {
  const { t } = useTranslation();

  const { liveDeviceData, deviceIsMoving, deviceIsStale } = useJobExplorer();
  const liveData = liveDeviceData[centerPivot.endpointDeviceId];
  const [now, setNow] = useState<DateTime>(DateTime.now());
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(DateTime.now());
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, []);

  const lastCheckedSeconds = liveData?.timestamp
    ? now.toSeconds() - DateTime.fromISO(liveData.timestamp).toSeconds()
    : undefined;
  const lastCheckedString = getLastCheckedString({
    lastCheckedSeconds,
    staticPollThreshold: STATIC_POLL_THRESHOLD_S,
    staticPollUpperThreshold: STATIC_POLL_UPPER_THRESHOLD_M,
    t,
  });
  const lastUpdatedSeconds = liveData?.timestamp
    ? now.toSeconds() - DateTime.fromISO(liveData.timestamp).toSeconds()
    : Number.POSITIVE_INFINITY;

  const lastUpdatedString = getLastUpdatedString({
    lastUpdatedSeconds,
    lastUpdatedThreshold: LAST_UPDATED_THRESHOLD,
    lastUpdatedUpperThreshold: LAST_UPDATED_UPPER_THRESHOLD_M,
    t,
  });
  const isMoving = deviceIsMoving[centerPivot.endpointDeviceId];
  const isStale = deviceIsStale[centerPivot.endpointDeviceId];

  return (
    <div>
      <LinearProgress
        variant={isMoving ? "indeterminate" : "determinate"}
        value={100}
        color={
          // eslint-disable-next-line no-nested-ternary
          isMoving ? "warning" : isStale ? "secondary" : "success"
        }
      />
      <div className="flex flex-col p-4 gap-1">
        <div className="flex justify-between">
          <Typography
            variant="h6"
            className={classes(
              "flex my-auto",
              isStale ? "text-slate-500" : "text-green-500"
            )}
          >
            {isStale
              ? t("utils.descriptors.inactive")
              : t("utils.descriptors.active")}
          </Typography>
          <Typography className="flex my-auto">
            {`${new Date(liveData?.timestamp ?? "").toLocaleDateString()}
            ${new Date(liveData?.timestamp ?? "").toLocaleTimeString()}`}
          </Typography>
        </div>
        <Typography
          className="text-slate-500 flex my-auto justify-end "
          fontSize={"small"}
        >
          {lastUpdatedString}
        </Typography>
        <Typography
          className="text-slate-500 flex my-auto justify-end "
          fontSize={"small"}
        >
          {lastCheckedString}
        </Typography>
      </div>
    </div>
  );
};
