import { capitalize } from "portal/utils/strings";
import { classes } from "portal/theme/theme";
import { DateTime } from "luxon";
// eslint-disable-next-line camelcase
import { Job } from "protos/rtc/jobs";
import { Typography } from "@mui/material";
import { useJobExplorer } from "portal/state/store";
import { useJobStateStatus, useJobTypeStatus } from "../../JobStatus";
import { useTranslation } from "react-i18next";
import React, { FC, ReactNode } from "react";

interface JobDetailsProps {
  job: Job;
}

export const JobDetails: FC<JobDetailsProps> = ({ job }) => {
  const { t } = useTranslation();
  const rowElement = (
    label: ReactNode,
    content: ReactNode,
    multiLine?: boolean
  ): ReactNode => (
    <div
      className={classes(
        "flex justify-between px-2 gap-4",
        multiLine && "flex-col"
      )}
    >
      <div>{label}</div>
      <div>{content}</div>
    </div>
  );
  const { farm } = useJobExplorer();

  const startDate = job.startedAt
    ? DateTime.fromISO(job.startedAt).toJSDate().toLocaleDateString()
    : t("utils.descriptors.none");

  const farmName = farm?.name;
  const fieldName = farm?.zones.find((z) => z.id?.id === job.fieldId)?.name;

  const coalesceNone = (a?: ReactNode): ReactNode => {
    return a ?? t("utils.descriptors.none");
  };

  const [stateStatus] = useJobStateStatus(job);
  const [jobType] = useJobTypeStatus(job, undefined);

  return (
    <div className="flex flex-col gap-2">
      <Typography variant="h6" className="p-2">
        {stateStatus?.text}
      </Typography>
      {rowElement(
        capitalize(t("models.farms.zone_one")),
        coalesceNone(fieldName)
      )}
      {rowElement(
        capitalize(t("models.farms.farm_one")),
        coalesceNone(farmName)
      )}
      {rowElement(
        capitalize(t("components.DateRangePicker.startDate")),
        startDate
      )}
      {rowElement(capitalize(t("utils.descriptors.type_one")), jobType?.text)}
    </div>
  );
};
