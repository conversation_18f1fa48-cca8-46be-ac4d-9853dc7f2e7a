import {
  <PERSON>ton,
  CircularProgress,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import { capitalize } from "portal/utils/strings";
import { classes } from "portal/theme/theme";
import { DateTime } from "luxon";
import { JobSelectionType, setSelection } from "common/state/jobExplorer";
import { jobStateIcon, jobStateIconColor } from "common/utils/job";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import InterventionIcon from "@mui/icons-material/NewReleases";
// eslint-disable-next-line camelcase
import { Objective, Objective_ObjectiveType, State } from "protos/rtc/jobs";
import { skipToken } from "@reduxjs/toolkit/query";
import { useDispatch } from "react-redux";
import {
  useGetObjectiveQuery,
  useListInterventionsQuery,
  useListTasksQuery,
  useRemoveAssignmentMutation,
  useSetAssignmentMutation,
  useUpdateObjectiveMutation,
} from "common/state/rtcJobsApi";
import { useJobExplorer } from "portal/state/store";
import { useListRobotsQuery } from "common/state/rtcLocatorApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import AssignmentIcon from "@mui/icons-material/ContentPasteGo";
import CloseIcon from "@mui/icons-material/Close";
import React, { FC } from "react";
import ReplayIcon from "@mui/icons-material/Replay";

interface ObjectiveDetailsProps {
  objective: Objective;
}
export const ObjectiveDetails: FC<ObjectiveDetailsProps> = ({ objective }) => {
  const { t, i18n } = useTranslation();
  const { job } = useJobExplorer();
  const dispatch = useDispatch();

  const [setAssignment, { isLoading: assignmentLoading }] = useMutationPopups(
    useSetAssignmentMutation()
  );

  const [removeAssignment, { isLoading: removalLoading }] = useMutationPopups(
    useRemoveAssignmentMutation()
  );

  const {
    data: objectiveResponse,
    isLoading: detailsLoading,
    isFetching: detailsFetching,
    isError: detailsError,
    refetch: refetchDetails,
  } = useQueryPopups(
    useGetObjectiveQuery(
      {
        id: objective.id,
      },
      { pollingInterval: 5000, refetchOnMountOrArgChange: 5 }
    )
  );
  const [updateObjective] = useMutationPopups(useUpdateObjectiveMutation());
  const details = objectiveResponse ?? objective;

  const {
    data: availableTractors,
    isLoading: tractorsLoading,
    isFetching: tractorsFetching,
  } = useQueryPopups(
    useListRobotsQuery({
      serials: job?.robotWhitelist?.entries.map((r) => r.robotSerial),
    })
  );

  const assignmentIsLoading =
    detailsLoading ||
    detailsFetching ||
    assignmentLoading ||
    tractorsLoading ||
    removalLoading ||
    tractorsFetching;

  const objectiveTypeToString =
    // eslint-disable-next-line camelcase
    details.type === Objective_ObjectiveType.LASER_WEED_ROW
      ? t("models.autotractor.objectiveTypes.laserWeedRow")
      : t("utils.descriptors.unknown");

  const { data: tasks } = useQueryPopups(
    useListTasksQuery(
      { objectiveIds: [objective.id] },
      { pollingInterval: 5000, refetchOnMountOrArgChange: 5 }
    )
  );

  const taskIds = tasks?.tasks.map((i) => i.id).join(",") ?? undefined;

  const { data: interventions } = useQueryPopups(
    useListInterventionsQuery(
      taskIds
        ? {
            taskIds,
          }
        : skipToken,
      { pollingInterval: 5000, refetchOnMountOrArgChange: 5 }
    )
  );

  return (
    <div className="flex flex-col gap-4 h-full">
      <Typography className="px-4 text-slate-400">
        {details.description}
      </Typography>
      <div className="flex">
        {detailsError && (
          <div className="flex my-auto pl-2">
            <Tooltip
              title={t("views.autotractor.jobDetails.assignmentsFailed")}
            >
              <IconButton color="error" onClick={() => refetchDetails()}>
                <ReplayIcon />
              </IconButton>
            </Tooltip>
          </div>
        )}
        <FormControl fullWidth className="mx-4 flex">
          <InputLabel id="assignment-label">
            {capitalize(t("models.autotractor.assignment_one"))}
          </InputLabel>
          <Select
            fullWidth
            labelId="assignment-label"
            label={
              assignmentIsLoading
                ? t("utils.descriptors.loading")
                : capitalize(t("models.autotractor.assignment_one"))
            }
            value={details.assignment?.robotSerial ?? ""}
            type="string"
            disabled={assignmentIsLoading}
            onChange={(e) =>
              e.target.value &&
              setAssignment({
                objectiveId: details.id,
                robotSerial: e.target.value,
              })
            }
            startAdornment={
              <div className="flex gap-2 mr-2">
                <AssignmentIcon color="info" className="flex my-auto" />
                {assignmentIsLoading && (
                  <CircularProgress
                    className="flex my-auto ml-2 h-6 w-6"
                    variant="indeterminate"
                  />
                )}
              </div>
            }
            endAdornment={
              details.assignment?.robotSerial && (
                <IconButton
                  className="mr-4"
                  onClick={() =>
                    removeAssignment({
                      objectiveId: details.id,
                    })
                  }
                  disabled={assignmentIsLoading}
                  color="info"
                >
                  <CloseIcon />
                </IconButton>
              )
            }
          >
            {availableTractors?.robots.map((t) => (
              <MenuItem key={t.serial} value={t.serial}>
                {t.serial}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <div className="flex justify-between px-4">
        <Typography>{t("utils.descriptors.type_one")}</Typography>
        <Typography>{objectiveTypeToString}</Typography>
      </div>
      <div className="px-4">
        <div className="flex justify-between">
          <Typography>{t("utils.descriptors.progress")}</Typography>
          <Typography>{`${details.progressPercent}${t(
            "utils.units.%"
          )}`}</Typography>
        </div>
        <LinearProgress
          variant="determinate"
          className="px-4 h-5"
          color="success"
          value={details.progressPercent}
        />
      </div>
      <div className="px-4">
        {tasks?.tasks.map((t) => {
          const linkedInterventions = interventions?.interventions.filter(
            (i) => i.taskId === t.id
          );
          const Icon = jobStateIcon(t.state);
          const iconColor = jobStateIconColor(t.state);
          return (
            <div key={t.id} className="flex flex-col">
              {t.startedAt && (
                <div className="flex gap-2 ml-4">
                  <AccessTimeIcon fontSize="small" className="fill-gray-500" />
                  <Typography
                    variant="caption"
                    color="GrayText"
                    className="flex my-auto"
                  >
                    {DateTime.fromISO(t.startedAt).toLocaleString(
                      {
                        timeStyle: "medium",
                      },
                      { locale: i18n.language }
                    )}
                  </Typography>
                </div>
              )}
              <div className="flex break-auto-all">
                {<Icon className="flex my-auto" color={iconColor} />}
                <Button
                  onClick={() =>
                    dispatch(
                      setSelection({
                        type: JobSelectionType.TASK,
                        selection: t,
                      })
                    )
                  }
                  variant="text"
                  size="small"
                  fullWidth
                  color={iconColor}
                  className={classes(
                    t.state === State.COMPLETED &&
                      "line-through text-green-500",
                    t.state === State.FAILED && "text-red-500"
                  )}
                >
                  {t.name}
                </Button>
              </div>
              <div className="flex flex-col">
                {linkedInterventions?.map((i) => (
                  <div key={i.id} className="flex mx-4 gap-2">
                    <InterventionIcon
                      className={classes(
                        "flex my-auto h-4 w-4",
                        i.state === State.COMPLETED && "text-green-300"
                      )}
                    />
                    <div
                      className={classes(
                        "text-sm",
                        i.state === State.COMPLETED &&
                          "line-through text-green-300"
                      )}
                    >
                      {i.description}
                    </div>
                  </div>
                ))}
              </div>
              {t.endedAt && (
                <div className="flex gap-2 ml-4">
                  <AccessTimeIcon fontSize="small" className="fill-gray-500" />
                  <Typography
                    variant="caption"
                    color="GrayText"
                    className="flex my-auto"
                  >
                    {DateTime.fromISO(t.endedAt).toLocaleString(
                      {
                        timeStyle: "medium",
                      },
                      { locale: i18n.language }
                    )}
                  </Typography>
                </div>
              )}
              <Divider className="mx-4 py-1" />
            </div>
          );
        })}
      </div>
      <div className="flex mt-auto p-2">
        <Button
          variant="contained"
          fullWidth
          disabled={details.progressPercent === 100}
          onClick={() =>
            updateObjective({ id: details.id, progressPercent: 100 })
          }
        >
          {t("views.autotractor.actions.markComplete")}
        </Button>
      </div>
    </div>
  );
};
