import { Button } from "@mui/material";
import { classes } from "common/theme/theme";
import { DateTime } from "luxon";
import { getTaskTypeName, jobStateIcon } from "common/utils/job";
import { Section } from "portal/components/map/navigation/Section";
import { State, stateToJSON, Task } from "protos/rtc/jobs";
import {
  useGetTaskQuery,
  useListInterventionsQuery,
  useUpdateTaskMutation,
} from "common/state/rtcJobsApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import InterventionIcon from "@mui/icons-material/NewReleases";
import ManualIcon from "@mui/icons-material/Engineering";
import React, { FC, useEffect, useState } from "react";

interface TaskDetailsProps {
  task: Task;
}
export const TaskDetails: FC<TaskDetailsProps> = ({ task: inputTask }) => {
  const { t, i18n } = useTranslation();

  const { data: taskResponse } = useQueryPopups(
    useGetTaskQuery(
      { id: inputTask.id },
      { refetchOnMountOrArgChange: 5, pollingInterval: 5000 }
    )
  );
  const [updateTask] = useMutationPopups(useUpdateTaskMutation());
  const task = taskResponse ?? inputTask;

  const { data: interventions } = useQueryPopups(
    useListInterventionsQuery(
      { taskIds: `${task.id}` },
      { refetchOnMountOrArgChange: 5, pollingInterval: 5000 }
    )
  );

  // used to show a live-updating timer for how long the task has been active for.
  const [now, setNow] = useState(DateTime.now());
  const endTime = task.endedAt ? DateTime.fromISO(task.endedAt) : now;

  useEffect(() => {
    const nowTimer = setInterval(() => {
      setNow(DateTime.now());
    }, 1000);
    return () => {
      clearInterval(nowTimer);
    };
  }, []);
  const JobStateIcon = jobStateIcon(task.state);

  return (
    <div className="flex flex-col gap-2 h-full">
      <h2 className="flex gap-1 mx-4">
        <JobStateIcon className="flex my-auto" />
        <div>{stateToJSON(task.state)}</div>
      </h2>
      <div className="flex justify-between mx-4">
        <div>{t("utils.descriptors.id")}</div>
        {task.id}
      </div>
      <div className="flex justify-between mx-4">
        <div>{t("utils.descriptors.type_one")}</div>
        {getTaskTypeName(t, task)}
      </div>
      <div className="flex justify-between mx-4">
        <div>{t("utils.descriptors.startedAt")}</div>
        {task.startedAt &&
          DateTime.fromISO(task.startedAt).toLocaleString(
            {
              dateStyle: "short",
              timeStyle: "medium",
            },
            { locale: i18n.language }
          )}
      </div>
      <div className="flex justify-between mx-4">
        <div>{t("utils.descriptors.endedAt")}</div>
        {task.endedAt &&
          DateTime.fromISO(task.endedAt).toLocaleString(
            {
              dateStyle: "short",
              timeStyle: "medium",
            },
            { locale: i18n.language }
          )}
      </div>
      <div className="flex justify-between mx-4">
        <div>{t("utils.descriptors.duration")}</div>
        {task.startedAt
          ? endTime.diff(DateTime.fromISO(task.startedAt)).toFormat("hh:mm:ss")
          : "Not Started"}
      </div>
      <div className="flex justify-between mx-4">
        <div className="flex gap-2">
          <ManualIcon />
          <div>{t("models.autotractor.manuallyAssisted")}</div>
        </div>
        {task.manuallyAssisted ? <CheckIcon /> : <CloseIcon />}
      </div>
      <Section header={t("models.autotractor.intervention_other")}>
        <div className="flex flex-col p-2">
          {interventions?.interventions.map((i) => (
            <div key={i.id} className="flex mx-2 gap-4">
              <InterventionIcon
                fontSize="small"
                className={classes(
                  "flex my-auto",
                  i.state === State.COMPLETED && "text-green-300"
                )}
              />
              <div
                className={classes(
                  i.state === State.COMPLETED && "line-through text-green-300"
                )}
              >
                {i.description}
                {i.taskId}
              </div>
            </div>
          ))}
        </div>
      </Section>
      <div className="flex flex-col mt-auto p-2">
        <Button
          variant="contained"
          disabled={task.state === State.COMPLETED}
          onClick={() => updateTask({ ...task, state: State.COMPLETED })}
        >
          {t("views.autotractor.actions.markComplete")}
        </Button>
      </div>
    </div>
  );
};
