import {
  AVAILABLE_COLORS,
  TRACTOR_STALE_THRESHOLD_S,
} from "common/utils/rtcJobs";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  LinearProgress,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import { capitalize } from "portal/utils/strings";
import { classes } from "portal/theme/theme";
import { DateTime } from "luxon";
import { getLastUpdatedString } from "portal/utils/dates";
import { Section } from "portal/components/map/navigation/Section";
import { selectTractorData, setTractorColor } from "common/state/jobExplorer";
import { skipToken } from "@reduxjs/toolkit/query";
import { TractorObjectiveQueue } from "./TractorObjectiveQueue";
import {
  useDeleteActiveObjectiveMutation,
  useGetActiveObjectiveQuery,
  useListObjectivesQuery,
  useRemoveAssignmentMutation,
  useSetAssignmentMutation,
} from "common/state/rtcJobsApi";
import { useDispatch, useSelector } from "react-redux";
import { useJobExplorer } from "portal/state/store";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import AddIcon from "@mui/icons-material/Add";
import AssignmentIcon from "@mui/icons-material/ContentPasteGo";
import CloseIcon from "@mui/icons-material/Close";
import ObjectiveIcon from "@mui/icons-material/ContentPaste";
import React, { FC, useEffect, useState } from "react";

interface TractorDetailsProps {
  tractor: { serial: string };
}

const LAST_UPDATED_THRESHOLD = TRACTOR_STALE_THRESHOLD_S;
const LAST_UPDATED_UPPER_THRESHOLD_M = 10; // 10 minutes

export const TractorDetails: FC<TractorDetailsProps> = ({ tractor }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [setAssignment] = useMutationPopups(useSetAssignmentMutation());
  const { job, colorsBySerial, tractorIsMoving, tractorIsStale } =
    useJobExplorer();
  const liveInfo = useSelector((state) =>
    selectTractorData(state, tractor.serial)
  );
  const tractorData = liveInfo?.data;

  const { data: objectivesWithAssignments } = useQueryPopups(
    useListObjectivesQuery(
      job
        ? {
            jobIds: [job.id],
          }
        : skipToken,
      { pollingInterval: 5000, refetchOnMountOrArgChange: 5 }
    )
  );
  const { data: activeObjectiveResponse, error } = useGetActiveObjectiveQuery(
    { serial: tractor.serial },
    { pollingInterval: 5000, refetchOnMountOrArgChange: 5 }
  );
  let activeObjective = activeObjectiveResponse;
  if (error && "status" in error && error.status === 404) {
    activeObjective = undefined;
  }

  const [now, setNow] = useState<DateTime>(DateTime.now());
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(DateTime.now());
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, []);

  const [unassigningActiveObjective, setUnassigningActiveObjective] =
    useState<boolean>(false);

  const lastUpdatedSeconds = liveInfo?.timestamp
    ? now.toSeconds() - DateTime.fromISO(liveInfo.timestamp).toSeconds()
    : Number.POSITIVE_INFINITY;

  const lastUpdatedString = getLastUpdatedString({
    lastUpdatedSeconds,
    lastUpdatedThreshold: LAST_UPDATED_THRESHOLD,
    lastUpdatedUpperThreshold: LAST_UPDATED_UPPER_THRESHOLD_M,
    t,
  });

  const unassignedObjectives = objectivesWithAssignments?.objectives
    .filter((o) => !o.assignment)
    .sort((a, b) => a.priority - b.priority);

  const isMoving = tractorIsMoving[tractor.serial];
  const isStale = tractorIsStale[tractor.serial];

  const [deleteActiveObjective] = useMutationPopups(
    useDeleteActiveObjectiveMutation()
  );
  const [removeAssignment] = useMutationPopups(useRemoveAssignmentMutation());

  const handleUnassignment = (): void => {
    if (!tractor.serial) {
      return;
    }
    deleteActiveObjective({ serial: tractor.serial }).then(() => {
      if (activeObjective?.id) {
        removeAssignment({ objectiveId: activeObjective.id }).then(() => {
          setUnassigningActiveObjective(false);
        });
      }
    });
  };

  return (
    <div className="h-full">
      <LinearProgress
        variant={isMoving ? "indeterminate" : "determinate"}
        value={100}
        color={isStale ? "secondary" : "success"}
      />
      <div className="flex flex-col p-4 gap-1">
        <div className="flex justify-between">
          <Typography
            variant="h6"
            className={classes(
              "flex my-auto",
              isStale ? "text-slate-500" : "text-green-500"
            )}
          >
            {isStale
              ? t("utils.descriptors.inactive")
              : t("utils.descriptors.active")}
          </Typography>
          <Typography
            className="text-slate-500 flex my-auto justify-end "
            fontSize={"small"}
          >
            {lastUpdatedString}
          </Typography>
        </div>
        <div className="flex gap-4 justify-between">
          <Select
            className="w-fit"
            value={colorsBySerial[tractor.serial]}
            onChange={(e) =>
              dispatch(
                setTractorColor({
                  serial: tractor.serial,
                  color: e.target.value,
                })
              )
            }
          >
            {AVAILABLE_COLORS.map((c) => (
              <MenuItem key={c} value={c}>
                <div className="flex w-5 h-5" style={{ backgroundColor: c }} />
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
      <Divider />
      <div className="flex gap-2 p-4">
        <ObjectiveIcon color="info" className="flex my-auto" />
        <Typography
          className={classes(
            "flex my-auto",
            !activeObjective && "text-slate-500"
          )}
        >
          {activeObjective?.name ?? capitalize(t("utils.descriptors.none"))}
        </Typography>
        {activeObjective && (
          <IconButton
            color="inherit"
            className="flex my-auto ml-auto"
            onClick={() => setUnassigningActiveObjective(true)}
          >
            <CloseIcon />
          </IconButton>
        )}
        <Dialog open={unassigningActiveObjective}>
          <DialogTitle>{t("components.ConfirmationDialog.title")}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t("views.autotractor.unassignDialog.body")}
            </DialogContentText>
            <DialogActions>
              <Button
                variant="contained"
                color="info"
                onClick={() => setUnassigningActiveObjective(false)}
              >
                {t("utils.actions.cancel")}
              </Button>
              <Button
                variant="contained"
                color="error"
                onClick={() => handleUnassignment()}
              >
                {t("utils.actions.confirm")}
              </Button>
            </DialogActions>
          </DialogContent>
        </Dialog>
      </div>
      <Divider />
      <div className="h-full overflow-y-auto">
        <Section
          header={
            <div className="flex my-auto gap-2">
              <AssignmentIcon color="info" className="flex my-auto" />
              <Typography className="flex my-auto">
                {t("models.autotractor.assignment_other")}
              </Typography>
            </div>
          }
        >
          <div className="">
            <TractorObjectiveQueue
              tractor={
                tractorData
                  ? { ...tractorData, serial: tractor.serial }
                  : undefined
              }
            />
          </div>
        </Section>
        <Section
          header={
            <Typography>
              {t("views.autotractor.suggestedAssignments")}
            </Typography>
          }
        >
          <div className="flex flex-col overflow-y-auto">
            {unassignedObjectives &&
              unassignedObjectives.map((o) => (
                <div key={o.id}>
                  <Button
                    className={classes(
                      "hover:bg-slate-600",
                      "flex gap-2 mx-auto p-2 cursor-pointer"
                    )}
                    color="info"
                    fullWidth
                    onClick={() =>
                      setAssignment({
                        objectiveId: o.id,
                        robotSerial: tractor.serial,
                      })
                    }
                  >
                    <AddIcon color="info" />
                    <AssignmentIcon className="flex my-auto" />
                    <Typography className="flex my-auto">{o.name}</Typography>
                  </Button>
                </div>
              ))}
          </div>
        </Section>
      </div>
    </div>
  );
};
