import {
  CircularProgress,
  Divider,
  IconButton,
  Typography,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Path } from "portal/utils/routing";
import { RobotData } from "protos/rtc/location_history";
import { skipToken } from "@reduxjs/toolkit/query";
import { t } from "i18next";
import {
  useGetActiveObjectiveQuery,
  useListAssignmentsQuery,
  useListJobsQuery,
  useListObjectivesQuery,
  useRemoveAssignmentMutation,
} from "common/state/rtcJobsApi";
import { useJobExplorer } from "portal/state/store";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import CloseIcon from "@mui/icons-material/Close";
import JobIcon from "@mui/icons-material/AssignmentOutlined";
import React, { FC, useMemo } from "react";
interface TractorObjectiveQueueProps {
  tractor: (RobotData & { serial: string }) | undefined;
}
export const TractorObjectiveQueue: FC<TractorObjectiveQueueProps> = ({
  tractor,
}) => {
  const { job } = useJobExplorer();
  const {
    data: assignmentResponse,
    isLoading: assignmentsLoading,
    isFetching: assignmentsFetching,
  } = useQueryPopups(
    useListAssignmentsQuery(
      tractor?.serial
        ? {
            robotSerials: [tractor.serial],
          }
        : skipToken,
      { pollingInterval: 5000 }
    )
  );
  const { data: activeObjective } = useGetActiveObjectiveQuery(
    tractor?.serial ? { serial: tractor.serial } : skipToken,
    { pollingInterval: 5000 }
  );
  console.log(activeObjective);
  const objectiveIds = assignmentResponse?.assignments.map(
    (a) => a.objectiveId
  );

  const { data: objectives } = useQueryPopups(
    useListObjectivesQuery({
      ids: objectiveIds,
    })
  );

  const jobIds = objectives?.objectives.map((o) => o.jobId);
  const { data: allJobs } = useQueryPopups(useListJobsQuery({ ids: jobIds }));

  const tractorAssignments = useMemo(() => {
    if (!assignmentResponse) {
      return;
    }
    const filtered = assignmentResponse.assignments.filter(
      (a) =>
        a.robotSerial === tractor?.serial &&
        a.objectiveId !== activeObjective?.id
    );
    const withObjectivesAndJob = filtered.map((a) => {
      const objective = objectives
        ? objectives.objectives.find((o) => o.id === a.objectiveId)
        : undefined;
      const job =
        allJobs?.jobs.find((j) => j.id === objective?.jobId) ?? undefined;
      return {
        ...a,
        objective,
        job,
      };
    });

    const withoutUnstartedJobs = withObjectivesAndJob.filter(
      (a) => a.job?.startedAt
    );

    // so that these are consistent, we specify an ordering for ties.
    return withoutUnstartedJobs.sort((a, b) => {
      const jobPriority = (a.job?.priority ?? 0) - (b.job?.priority ?? 0);
      if (jobPriority !== 0) {
        return jobPriority;
      }

      const startDateA = a.job?.startedAt
        ? new Date(a.job.startedAt).getTime()
        : 0;
      const startDateB = b.job?.startedAt
        ? new Date(b.job.startedAt).getTime()
        : 0;
      const startDate = startDateA - startDateB;
      if (startDate !== 0) {
        return startDate;
      }

      const objPriority =
        (a.objective?.priority ?? 0) - (b.objective?.priority ?? 0);
      if (objPriority !== 0) {
        return objPriority;
      }

      const jobId = (b.job?.id ?? 0) - (a.job?.id ?? 0);
      if (jobId !== 0) {
        return jobId;
      }

      return a.objectiveId - b.objectiveId;
    });
  }, [assignmentResponse, tractor, objectives, allJobs, activeObjective]);
  // =======

  const [removeAssignment] = useMutationPopups(useRemoveAssignmentMutation());

  return (
    <div className="flex flex-col">
      {tractorAssignments?.length === 0 ||
        (!tractorAssignments && (
          <div>
            <Typography className="flex my-auto px-4 text-slate-400">
              {t("utils.descriptors.none")}
            </Typography>
            {(assignmentsLoading || assignmentsFetching) && (
              <CircularProgress />
            )}
          </div>
        ))}
      {tractorAssignments?.map((assignment, i) => (
        <div key={assignment.id} className="flex flex-col ">
          <div className="flex justify-between mx-4">
            <div className="flex gap-4">
              <Typography className="flex my-auto">{i + 1}</Typography>
              <Typography className="flex my-auto">
                {assignment.objective?.name}
              </Typography>
              {tractor?.active &&
                tractor.objectiveId === assignment.objectiveId && (
                  <span className="flex my-auto">
                    <CircularProgress color="info" className="h-5 w-5 " />
                  </span>
                )}
            </div>
            <div className="flex justify-between gap-4">
              {assignment.job?.id !== job?.id && (
                <Link
                  target="_blank"
                  to={`${Path.JOBS}/${assignment.job?.id}/orchestrate`}
                  className="flex my-auto gap-1"
                >
                  <JobIcon color="warning" className="flex my-auto" />
                  <Typography>{assignment.job?.name}</Typography>
                </Link>
              )}
              <IconButton
                color="info"
                className="flex my-auto"
                onClick={() =>
                  removeAssignment({ objectiveId: assignment.objectiveId })
                }
              >
                <CloseIcon />
              </IconButton>
            </div>
          </div>
          <Divider />
        </div>
      ))}
    </div>
  );
};
