import { Bounds } from "portal/utils/geo";
import { FarmMapData, PointFeature, ZoneFeature } from "common/utils/farm";
import { Farm as FarmProto } from "protos/portal/farm";
import { Map as PortalMap } from "portal/components/map/Map";
import { useFarmLayers } from "common/components/map/layers/FarmLayers";
import { viteImageResolver } from "common/utils/map/imageResolver.vite";
import React, { FunctionComponent } from "react";

interface FarmMapProps {
  farm: FarmProto;
  selectedPointId: string | undefined;
  selectedZoneId: string | undefined;
  focusedPointId: string | undefined;
  focusedZoneId: string | undefined;
  shownPoints?: string[];
  farmMapData: FarmMapData;
  className?: string;
  onPointClick?: (feature?: PointFeature) => void;
  onPointHover?: (feature?: PointFeature) => void;
  onZoneClick?: (feature?: ZoneFeature) => void;
  onZoneHover?: (feature?: ZoneFeature) => void;
  bounds?: Bounds;
}
export const FarmMap: FunctionComponent<FarmMapProps> = ({
  selectedPointId,
  selectedZoneId,
  focusedPointId,
  focusedZoneId,
  shownPoints,
  farmMapData,
  className,
  onPointClick,
  onPointHover,
  onZoneClick,
  onZoneHover,
  bounds = farmMapData.bounds,
}) => {
  const {
    idsToLoad,
    interactionLayers,
    Layers: FarmLayers,
  } = useFarmLayers({
    clickPoint: onPointClick,
    hoverPoint: onPointHover,
    clickZone: onZoneClick,
    hoverZone: onZoneHover,
    leaveZone: () => onZoneHover?.(undefined),
  });

  return (
    <PortalMap
      className={className}
      hideRobots
      hideMeasure
      hideHoverInfo
      allowEmpty
      imageConfig={{
        resolver: viteImageResolver,
        idsToLoad,
      }}
      interactionLayers={interactionLayers}
      extraControls={
        <FarmLayers
          farmMapData={farmMapData}
          shownPoints={shownPoints ?? []}
          selectedPointId={selectedPointId}
          focusedPointId={focusedPointId}
          focusedZoneId={focusedZoneId}
          selectedZoneId={selectedZoneId}
        />
      }
      extraBounds={bounds}
    />
  );
};
