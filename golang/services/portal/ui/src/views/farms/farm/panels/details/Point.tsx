import { classes } from "portal/theme/theme";

import { FixType, Point as PointProto } from "protos/geo/geo";
import { setFocus, setSelection } from "portal/state/farmExplorer";
import { Trans, useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useFarmExplorer } from "portal/state/store";
import React, { FC } from "react";

interface PointProps {
  point?: PointProto;
  disabled?: boolean;
}

export interface FixInfo {
  color: string;
  text: string;
}

export const Point: FC<PointProps> = ({ point, disabled }: PointProps) => {
  const { t } = useTranslation();

  const dispatch = useDispatch();
  const selectPoint = (p: PointProto): void => {
    dispatch(setSelection({ type: "point", selection: p }));
  };
  const focusPoint = (p?: PointProto): void => {
    if (p) {
      dispatch(setFocus({ type: "point", selection: p }));
    } else {
      dispatch(setFocus(undefined));
    }
  };
  const { selection, focus } = useFarmExplorer();
  const selected =
    selection?.type === "point" && selection.selection.id?.id === point?.id?.id;
  const focused =
    focus?.type === "point" && focus.selection.id?.id === point?.id?.id;

  if (!point) {
    return;
  }
  const pointId = point.id?.id;

  const getFixInfo = (point: PointProto): FixInfo => {
    switch (point.captureInfo?.fixType) {
      case FixType.RTK_FIXED: {
        return {
          color: "bg-green-500",
          text: t("views.farms.fixTypes.rtkFixed"),
        };
      }
      case FixType.RTK_FLOAT: {
        return {
          color: "bg-tailwind-yellow-300 text-black",
          text: t("views.farms.fixTypes.rtkFloat"),
        };
      }
      case FixType.GNSS:
      case FixType.DIFFERENTIAL_GNSS: {
        return {
          color: "bg-tailwind-orange-400 text-black",
          text: t("views.farms.fixTypes.gps"),
        };
      }
      case undefined:
      case FixType.NO_FIX: {
        return {
          color: "bg-red-700",
          text: t("views.farms.fixTypes.none"),
        };
      }
      default: {
        return {
          color: "bg-gray-600",
          text: t("views.farms.fixTypes.unknown"),
        };
      }
    }
  };

  const fixInfo = getFixInfo(point);
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div
      className={classes(
        "flex flex-col",
        !disabled && "cursor-pointer",
        selected && "bg-blue-800",
        focused && "bg-slate-500"
      )}
      key={pointId}
      onClick={() => selectPoint(point)}
      onFocus={() => focusPoint(point)}
      onMouseEnter={() => focusPoint(point)}
      onMouseLeave={() => focusPoint(undefined)}
      onBlur={() => focusPoint(undefined)}
    >
      <div className="flex">
        {point.name ? (
          <span>{point.name}</span>
        ) : (
          <Trans
            t={t}
            i18nKey="views.farms.unnamedPoint"
            values={{ pointId }}
            components={[<span key="pointId" className="font-mono text-xs" />]}
          />
        )}
      </div>
      <div className="flex gap-2">
        <span className="font-mono text-xs">
          {point.lng.toFixed(6)}, {point.lat.toFixed(6)}
        </span>
        <span
          className={classes(
            "inline-block py-1 px-2 rounded-sm uppercase font-bold text-xs my-auto",
            fixInfo.color
          )}
        >
          {fixInfo.text}
        </span>
      </div>
    </div>
  );
};
