import { FieldData } from "protos/portal/farm";
import { PointSelection } from "./PointSelection";
import { Section } from "portal/components/map/navigation/Section";
import { t } from "i18next";
import CenterPivotIcon from "common/images/icons/irrigation.svg?react";
import React, { FC } from "react";

interface FieldDataProps {
  fieldData: FieldData;
}

export const FieldSelection: FC<FieldDataProps> = ({ fieldData }) => {
  return (
    <div>
      <Section
        startCollapsed
        isSubsection
        header={
          <span className="flex gap-1">
            <CenterPivotIcon className="flex my-auto fill-white w-5 h-5" />
            {t("views.farms.selectionPanel.centerPivot")}
          </span>
        }
      >
        <PointSelection pointData={fieldData.centerPivot?.center} />
      </Section>
      <Section
        startCollapsed
        isSubsection
        header={t("views.farms.selectionPanel.plantingHeading")}
      >
        <PointSelection pointData={fieldData.plantingHeading?.abLine?.a} />
        <PointSelection pointData={fieldData.plantingHeading?.abLine?.b} />
      </Section>
    </div>
  );
};
