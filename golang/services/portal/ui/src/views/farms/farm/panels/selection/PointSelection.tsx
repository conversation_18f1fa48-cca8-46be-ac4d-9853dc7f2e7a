import { classes } from "portal/theme/theme";
import { FixType, Point } from "protos/geo/geo";
import { SelectionButton } from "portal/components/map/navigation/SelectionButton";
import { setFocus, setSelection } from "portal/state/farmExplorer";
import { Typography } from "@mui/material";
import { useDispatch } from "react-redux";
import { useFarmExplorer } from "portal/state/store";
import GPSPointIcon from "common/images/icons/gps-point.png";
import React, { FC, useMemo } from "react";
import RTKFixedIcon from "common/images/icons/rtk-fixed.png";
import RTKFloatIcon from "common/images/icons/rtk-float.png";

interface PointDataProps {
  pointData?: Point;
}

export const PointSelection: FC<PointDataProps> = ({ pointData }) => {
  const dispatch = useDispatch();
  const { pointsById, selection, focus, shownPointIds } = useFarmExplorer();
  const point = pointData?.id?.id ? pointsById?.[pointData.id.id] : undefined;
  const getImg = (): string => {
    switch (point?.captureInfo?.fixType) {
      case FixType.RTK_FIXED: {
        return RTKFixedIcon;
      }
      case FixType.RTK_FLOAT: {
        return RTKFloatIcon;
      }
      default: {
        return GPSPointIcon;
      }
    }
  };
  const isShown = useMemo(() => {
    return point?.id?.id && shownPointIds.includes(point.id.id);
  }, [shownPointIds, point]);
  return (
    <SelectionButton
      subSection
      isSelected={
        selection?.type === "point" &&
        selection.selection.id?.id === pointData?.id?.id
      }
      isFocused={
        focus?.type === "point" && focus.selection.id?.id === pointData?.id?.id
      }
      onClick={() =>
        point && dispatch(setSelection({ type: "point", selection: point }))
      }
      onMouseEnter={() =>
        point && dispatch(setFocus({ type: "point", selection: point }))
      }
      onMouseLeave={() => dispatch(setFocus())}
    >
      <div className="flex gap-2">
        <img
          className="flex my-auto"
          src={getImg()}
          alt="point"
          width={16}
          height={16}
        />
        <Typography
          className={classes(
            "flex my-auto",
            isShown ? "text-white" : "text-gray-500"
          )}
        >
          {point?.name}
        </Typography>
      </div>
    </SelectionButton>
  );
};
