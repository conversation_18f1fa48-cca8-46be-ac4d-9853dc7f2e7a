import {
  addShownPointIds,
  removeShownPointIds,
} from "portal/state/farmExplorer";
import { AutoSizer, List } from "react-virtualized";
import { derefPoint } from "common/utils/farm";
import { Point as GeoPoint } from "protos/geo/geo";
import { PointSelection } from "./PointSelection";
import { SelectionButton } from "portal/components/map/navigation/SelectionButton";
import { t } from "i18next";
import { Typography } from "@mui/material";
import { useDispatch } from "react-redux";
import { useFarmExplorer } from "portal/state/store";
import React, { CSSProperties, FC, ReactNode, useMemo } from "react";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

interface PointsSelectionListProps {
  points: (GeoPoint | undefined)[];
}
// use this if you have more than a handful of points. Virtualizes the list so you can show many many points
export const PointsSelectionList: FC<PointsSelectionListProps> = ({
  points,
}: PointsSelectionListProps) => {
  const dispatch = useDispatch();
  const { pointsById, shownPointIds } = useFarmExplorer();
  const allPoints = points.flatMap((p) => (p?.id?.id ? [p.id.id] : []));
  const showingAllPoints =
    points.filter((p) => p?.id?.id && shownPointIds.includes(p.id.id))
      .length === points.length;

  const derefedPoints: GeoPoint[] = useMemo(() => {
    let derefed: GeoPoint[] = [];
    if (pointsById) {
      derefed = points.flatMap((p) => (p ? [derefPoint(pointsById, p)] : []));
    }
    return derefed;
  }, [pointsById, points]);

  const renderRow = ({
    style,
    index,
    key,
  }: {
    style: CSSProperties;
    index: number;
    key: string;
  }): ReactNode => {
    const point = derefedPoints[index];
    return (
      <div key={key} style={style}>
        {point && (
          <PointSelection
            key={`${point.id?.id}-${index}-p`}
            pointData={point}
          />
        )}
      </div>
    );
  };

  return (
    <div className="h-96">
      <SelectionButton
        isSelected={showingAllPoints}
        isFocused={false}
        onClick={() =>
          showingAllPoints
            ? dispatch(removeShownPointIds(allPoints))
            : dispatch(addShownPointIds(allPoints))
        }
      >
        <span className="flex gap-2">
          <Typography className="flex my-auto" fontSize={"small"}>
            {showingAllPoints
              ? t("views.farms.actions.hideThesePoints")
              : t("views.farms.actions.showThesePoints")}
          </Typography>
          {showingAllPoints ? (
            <VisibilityOffIcon className="h-4 w-4 flex my-auto" />
          ) : (
            <VisibilityIcon className="h-4 w-4 flex my-auto" />
          )}
        </span>
      </SelectionButton>
      <AutoSizer>
        {({ height, width }) => (
          <List
            height={height}
            width={width}
            rowHeight={36}
            rowCount={derefedPoints.length}
            rowRenderer={renderRow}
          />
        )}
      </AutoSizer>
    </div>
  );
};
