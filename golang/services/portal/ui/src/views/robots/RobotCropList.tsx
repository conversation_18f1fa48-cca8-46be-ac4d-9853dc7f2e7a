import { buildPermission } from "portal/utils/auth";
import { capitalize } from "portal/utils/strings";
import { CarbonDataGrid } from "portal/components/CarbonDataGrid";
import { Checkbox, FormControlLabel } from "@mui/material";
import {
  classes,
  GREEN_LOADING_BUTTON,
  RED_LOADING_BUTTON,
} from "portal/theme/theme";
import { ConfigCrop } from "protos/portal/configs";
import { CropEditor } from "portal/components/CropEditor";
import { findWhere } from "portal/utils/arrays";
import { getConfigKey } from "portal/utils/crops";
import { getCropPath, getRobotPath, RobotSubpath } from "portal/utils/routing";
import {
  GridActionsColDef,
  GridColDef,
  GridRowSelectionModel,
} from "@mui/x-data-grid-premium";
import { isUndefined } from "portal/utils/identity";
import { LoadingButton } from "@mui/lab";
import { ModelName } from "portal/components/ModelName";
import { NoScroll } from "portal/components/Page";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { SearchField } from "portal/components/header/SearchField";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import { useCrops } from "portal/utils/hooks/useCrops";
import { useFuzzySearch } from "portal/utils/hooks/useFuzzySearch";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useSetConfigValueMutation } from "portal/state/portalApi";
import { useTranslation } from "react-i18next";
import { withAuthenticationRequired } from "@auth0/auth0-react";
import DisableIcon from "@mui/icons-material/CloseOutlined";
import EnableIcon from "@mui/icons-material/CheckOutlined";
import PinnedIcon from "@mui/icons-material/PushPinOutlined";
import React, { FunctionComponent, useMemo, useState } from "react";

const defaultColumn: Partial<GridColDef> = {
  sortable: true,
  disableColumnMenu: true,
};

const _RobotCropList: FunctionComponent = () => {
  const { serial } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const canReadAdvancedCrops = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.crops_advanced,
      PermissionDomain.all
    ),
  ]);
  const canEnableCrops = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.crops_advanced,
      PermissionDomain.all
    ),
  ]);

  const { sortedCrops: crops, isFetching } = useCrops({ serial });

  const [showArchived, setShowArchived] = useState<boolean>(false);
  const allCrops = useMemo(() => {
    if (!canEnableCrops) {
      return crops?.enabled;
    }
    return showArchived
      ? [...(crops?.all ?? []), ...(crops?.archived ?? [])]
      : crops?.all;
  }, [canEnableCrops, crops, showArchived]);

  // selected crop
  const { pathname } = useLocation();
  let cropId: string = "";
  const basePath = getRobotPath(serial, RobotSubpath.CROPS);
  if (!pathname.endsWith(basePath)) {
    cropId = pathname.replace(`${basePath}/`, "");
  }
  const selectedCrop = findWhere(allCrops, { id: cropId });

  const { searchText, setSearchText, results } = useFuzzySearch<ConfigCrop>({
    items: allCrops ?? [],
    options: {
      ignoreLocation: true,
      keys: ["id", "commonName", "pinned", "recommended"],
    },
  });

  const columns: Array<GridColDef<ConfigCrop> | GridActionsColDef> = [
    {
      ...defaultColumn,
      field: "commonName",
      headerName: t("models.crops.crop_one"),
      cellClassName: () => "font-bold",
      valueGetter: (value, crop) => crop.commonName,
      width: 250,
    },
    {
      ...defaultColumn,
      field: "id",
      headerName: t("models.crops.fields.id"),
      cellClassName: () => "font-mono text-xs/inherit",
      width: 260,
    },
    {
      ...defaultColumn,
      field: "isEnabled",
      headerName: t("utils.descriptors.enabled"),
      width: 100,
      valueGetter: (value, crop) =>
        crop.isEnabled
          ? t("utils.descriptors.enabled")
          : t("utils.descriptors.disabled"),
      cellClassName: ({ row: crop }) =>
        classes("text-center", {
          "font-bold": crop.isEnabled,
          "text-green-500": crop.isEnabled,
          "text-gray-400": !crop.isEnabled,
        }),
    },
    {
      ...defaultColumn,
      field: "pinned",
      headerName: t("models.crops.fields.pinned"),
      width: 250,
      cellClassName: ({ row: crop }) =>
        classes({
          "text-yellow-500": crop.isEnabled,
        }),
      renderCell: ({ row: crop }) =>
        canReadAdvancedCrops ? (
          <ModelName modelId={crop.pinned}>
            <span className="font-mono text-xs/inherit cursor-pointer text-yellow-500">
              {crop.pinned}
            </span>
          </ModelName>
        ) : (
          <PinnedIcon />
        ),
    },
    {
      ...defaultColumn,
      field: "recommended",
      headerName: t("models.crops.fields.recommended"),
      width: 250,
      cellClassName: ({ row: crop }) =>
        classes("text-mono text-xs/inherit", {
          "text-red-500": crop.pinned && crop.pinned !== crop.recommended,
        }),
      renderCell: ({ row: crop }) => (
        <ModelName modelId={crop.recommended}>{crop.recommended}</ModelName>
      ),
    },
  ];

  const [selectedIds, setSelectedIds] = useState<GridRowSelectionModel>([]);

  const [setConfigValue] = useMutationPopups(useSetConfigValueMutation(), {
    success: capitalize(
      t("utils.actions.savedLong", {
        subject: capitalize(t("models.configs.config_one")),
      })
    ),
  });

  const [disabledLoading, setDisabledLoading] = useState<boolean>(false);
  const [enabledLoading, setEnabledLoading] = useState<boolean>(false);
  const toggleSelected = async (enabled: boolean): Promise<void> => {
    if (!serial) {
      return;
    }
    const promises: Promise<any>[] = [];
    for (const id of selectedIds.filter((id) => {
      // allow disabling of all crops
      if (!enabled) {
        return true;
      }
      // only allow enabling of non-archived crops
      const archivedCrop = findWhere(crops?.archived, { id });
      return !archivedCrop;
    })) {
      promises.push(
        setConfigValue({
          serial,
          path: [getConfigKey(serial, String(id)), "enabled"].join("/"),
          value: enabled,
        })
      );
    }
    await Promise.all(promises);
  };

  return (
    <NoScroll>
      <CarbonDataGrid<ConfigCrop>
        header={
          <>
            <SearchField
              value={searchText}
              onChange={setSearchText}
              label={t("utils.actions.searchLong", {
                subject: capitalize(
                  t("models.crops.crop", { count: crops?.all.length ?? 0 })
                ),
              })}
            />
            {canEnableCrops && (
              <div className="flex gap-2">
                <FormControlLabel
                  className="flex-shrink-0"
                  control={
                    <Checkbox
                      checked={showArchived}
                      color="default"
                      onChange={(event, checked) => setShowArchived(checked)}
                    />
                  }
                  label={
                    <span className="whitespace-nowrap">
                      <span className="hidden sm:inline">
                        {t("utils.actions.showLong", {
                          subject: t(
                            "models.crops.fields.confidence.values.archived"
                          ),
                        })}
                      </span>
                      <span className="sm:hidden">
                        {t("models.crops.fields.confidence.values.archived")}
                      </span>
                    </span>
                  }
                />
                <LoadingButton
                  {...RED_LOADING_BUTTON}
                  loading={disabledLoading}
                  disabled={enabledLoading || selectedIds.length === 0}
                  onClick={async () => {
                    setDisabledLoading(true);
                    await toggleSelected(false);
                    setDisabledLoading(false);
                  }}
                  startIcon={<DisableIcon />}
                >
                  <span className="hidden sm:inline">
                    {t("utils.actions.disableLong", {
                      subject: t("utils.table.selected"),
                    })}
                  </span>
                  <span className="sm:hidden">{t("utils.table.selected")}</span>
                </LoadingButton>
                <LoadingButton
                  {...GREEN_LOADING_BUTTON}
                  loading={enabledLoading}
                  disabled={disabledLoading || selectedIds.length === 0}
                  onClick={async () => {
                    setEnabledLoading(true);
                    await toggleSelected(true);
                    setEnabledLoading(false);
                  }}
                  startIcon={<EnableIcon />}
                >
                  <span className="hidden sm:inline">
                    {t("utils.actions.enableLong", {
                      subject: t("utils.table.selected"),
                    })}
                  </span>
                  <span className="sm:hidden">{t("utils.table.selected")}</span>
                </LoadingButton>
              </div>
            )}
          </>
        }
        dimensionClasses={classes("h-auto flex-0", {
          "mr-80": !isUndefined(selectedCrop),
        })}
        className={classes("flex flex-1")}
        loading={isFetching}
        rows={results}
        columns={columns}
        columnVisibilityModel={{
          isEnabled: canEnableCrops,
          id: canReadAdvancedCrops,
          recommended: canReadAdvancedCrops,
        }}
        initialState={{
          sorting: {
            sortModel: [{ field: "isEnabled", sort: "desc" }],
          },
        }}
        hideFooter
        getRowId={(row: ConfigCrop) => row.id}
        getRowClassName={({ row: crop }) =>
          classes({
            "cursor-pointer": canEnableCrops,
            "bg-blue-500": crop.id === selectedCrop?.id,
          })
        }
        onRowClick={({ row: crop }) =>
          navigate(getCropPath(serial ?? "", crop.id))
        }
        disableRowSelectionOnClick
        checkboxSelection={canEnableCrops}
        onRowSelectionModelChange={(newRowSelectionModel) =>
          setSelectedIds(newRowSelectionModel)
        }
        rowSelectionModel={selectedIds}
      />
      <CropEditor
        serial={serial}
        configCrop={selectedCrop}
        open={!isUndefined(selectedCrop)}
        onClose={() => navigate(basePath)}
      />
    </NoScroll>
  );
};

export const RobotCropList = withAuthenticationRequired(_RobotCropList);
