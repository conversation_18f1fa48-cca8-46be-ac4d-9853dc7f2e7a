import AspectRatioPlugin from "@tailwindcss/aspect-ratio";
import TypographyPlugin from "@tailwindcss/typography";

import { tailwindTheme } from "../../../../ui/common/src/theme/tailwind.theme";

/** @type {import('tailwindcss').Config} */
export default {
  corePlugins: {
    preflight: false,
  },
  darkMode: "class",
  important: true,
  content: ["./src/**/*.{html,ts,tsx}", "../../../../ui/common/**/*.{ts,tsx}"],
  separator: ":",
  theme: {
    extend: { ...tailwindTheme },
    minWidth: ({ theme }) => ({
      ...theme("spacing"),
      "1/4": "25%",
      "1/3": "33%",
      "1/2": "50%",
      full: "100%",
    }),
    maxWidth: ({ theme }) => ({
      ...theme("spacing"),
      "1/4": "25%",
      "1/3": "calc(100% / 3)",
      "1/2": "50%",
      full: "100%",
    }),
    minHeight: ({ theme }) => ({
      ...theme("spacing"),
      "1/4": "25%",
      "1/2": "50%",
      full: "100%",
    }),
    maxHeight: ({ theme }) => ({
      ...theme("spacing"),
      "1/4": "25%",
      "1/2": "50%",
      full: "100%",
    }),
    // https://www.npmjs.com/package/@tailwindcss/aspect-ratio#compatibility-with-default-aspect-ratio-utilities
    aspectRatio: {
      auto: "auto",
      square: "1 / 1",
      video: "16 / 9",
      1: "1",
      2: "2",
      3: "3",
      4: "4",
      5: "5",
      6: "6",
      7: "7",
      8: "8",
      9: "9",
      10: "10",
      11: "11",
      12: "12",
      13: "13",
      14: "14",
      15: "15",
      16: "16",
    },
  },
  plugins: [AspectRatioPlugin, TypographyPlugin],
};
