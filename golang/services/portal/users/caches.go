package users

import (
	"context"
	"time"

	"github.com/auth0/go-auth0/management"
	ekocache "github.com/eko/gocache/lib/v4/cache"
	log "github.com/sirupsen/logrus"

	crcache "github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/crgo/auth/middleware"
)

func InitializeUserCache(caches *crcache.Caches, auth0 *management.Management, domain string) (crcache.Cache[string, *crcache.CachedUser], ekocache.CacheInterface[*crcache.CachedUser]) {
	loader := func(ctx context.Context, userID string) (*crcache.CachedUser, error) {
		user, err := middleware.ReadUserByID(ctx, auth0, domain, userID)
		if user == nil || err != nil {
			log.WithError(err).Errorf("Failed to get user: %s", userID)
			if auth0Error, ok := err.(management.Error); ok {
				if auth0Error.Status() == 404 {
					err = nil // don't return the error because we want to cache the nil
				}
			}
			return nil, err
		}

		permissions, err := getPermissions(ctx, auth0, userID)
		if err != nil {
			log.WithError(err).WithField("userId", userID).Errorf("Failed to get user permissions")
			return nil, err
		}
		return &crcache.CachedUser{Auth0User: user, Permissions: permissions}, nil
	}
	return crcache.NewLoadableAndDirectClient(crcache.LoadableOptions[string, *crcache.CachedUser]{
		Name:    "UserCache",
		Expiry:  30 * time.Minute,
		Cleanup: 1 * time.Hour,
		Load:    loader,
	})
}

func getPermissions(ctx context.Context, auth0 *management.Management, userID string) (permissions []string, err error) {
	perPage := management.PerPage(100)
	pageNumber := 0
	for {
		page, err := auth0.User.Permissions(ctx, userID, perPage, management.Page(pageNumber))
		if err != nil {
			return nil, err
		}
		for _, p := range page.Permissions {
			name := p.GetName()
			if name != "" {
				permissions = append(permissions, name)
			}
		}
		if !page.HasNext() {
			break
		}
		pageNumber++
	}
	return permissions, nil
}

func InitializeUserIDByEmailCache(cached *crcache.Caches, auth0 *management.Management, domain string) crcache.Cache[string, *string] {
	loader := func(ctx context.Context, email string) (*string, error) {
		user, err := middleware.ReadUserByEmail(ctx, auth0, domain, email)
		if user == nil || err != nil {
			log.WithError(err).Errorf("Failed to get user id: %s", email)
			return nil, err
		}
		return user.ID, nil
	}
	return crcache.NewLoadable(crcache.LoadableOptions[string, *string]{
		Name:    "UserIDByEmailCache",
		Expiry:  30 * time.Minute,
		Cleanup: 1 * time.Hour,
		Load:    loader,
	})
}
