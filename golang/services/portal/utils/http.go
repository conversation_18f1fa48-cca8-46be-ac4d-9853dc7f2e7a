package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/crgo/carbon"
)

type FeatureFlag int

const (
	MetricValidCrops FeatureFlag = iota
	MetricOperatorEffectiveness
	ImplicitDefaultValues

	numFeatureFlags int = iota
)

func (flag FeatureFlag) String() string {
	switch flag {
	case MetricValidCrops:
		return "MetricValidCrops"
	case MetricOperatorEffectiveness:
		return "MetricOperatorEffectiveness"
	case ImplicitDefaultValues:
		return "ImplicitDefaultValues"
	default:
		return fmt.Sprintf("%d", int(flag))
	}
}

var featureFlagsByName = func() map[string]FeatureFlag {
	m := make(map[string]FeatureFlag)
	for i := range numFeatureFlags {
		flag := FeatureFlag(i)
		m[flag.String()] = flag
	}
	return m
}()

func GetFeatureFlags(ctx *gin.Context) []FeatureFlag {
	encodedFeatureFlags := ctx.GetHeader(carbon.FeatureFlagsHTTPHeader)
	var stringFeatureFlags []string
	json.Unmarshal([]byte(encodedFeatureFlags), &stringFeatureFlags)
	featureFlags := make([]FeatureFlag, len(stringFeatureFlags))
	for i, stringFlag := range stringFeatureFlags {
		featureFlags[i] = featureFlagsByName[stringFlag]
	}

	return featureFlags
}

func HasFeatureFlag(ctx *gin.Context, flag FeatureFlag) bool {
	overrides, _ := ctx.Value(featureFlagOverridesKey{}).(map[FeatureFlag]bool)
	if override, ok := overrides[flag]; ok {
		return override
	}
	flags := GetFeatureFlags(ctx)
	return slices.Contains(flags, flag)
}

// ParseFeatureFlagOverrides parses a string of comma-separated key=value
// pairs, like "Flag1=true,Flag2=false". Each key should be the name of a
// FeatureFlag, and each value should be a boolean per strconv.ParseBool.
// Unrecognized feature flags will be ignored and accumulated into a separate
// result.
func ParseFeatureFlagOverrides(input string) (overrides map[FeatureFlag]bool, unknownKeys []string, err error) {
	return parseOverrides[FeatureFlag](input, featureFlagsByName)
}

// parseOverrides is generic so that test cases can be stable even as the
// actual set of feature flags changes.
func parseOverrides[T interface {
	comparable
	String() string
}](input string, byName map[string]T) (overrides map[T]bool, unknownKeys []string, err error) {
	overrides = make(map[T]bool)
	for i, part := range strings.Split(input, ",") {
		part = strings.TrimSpace(part)
		if len(part) == 0 {
			continue
		}
		rawName, rawValue, ok := strings.Cut(part, "=")
		if !ok {
			return nil, nil, fmt.Errorf("invalid part %q at index %v", part, i)
		}
		flag, ok := byName[rawName]
		if !ok {
			unknownKeys = append(unknownKeys, rawName)
			continue
		}
		value, err := strconv.ParseBool(rawValue)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid value for flag %s: %w", flag, err)
		}
		overrides[flag] = value
	}
	return overrides, unknownKeys, nil
}

// NewFeatureFlagOverrideHandler wraps an http.Handler with a set of feature
// flag overrides. When this handler is invoked, it calls the wrapped handler
// with a context such that if HasFeatureFlag is called with any flag listed in
// overrides, the override value will be used instead of the value from HTTP
// headers.
func NewFeatureFlagOverrideHandler(inner http.Handler, overrides map[FeatureFlag]bool) http.Handler {
	return featureFlagOverrideHandler{inner: inner, overrides: overrides}
}

type featureFlagOverrideHandler struct {
	inner     http.Handler
	overrides map[FeatureFlag]bool
}

type featureFlagOverridesKey struct{}

func (h featureFlagOverrideHandler) ServeHTTP(res http.ResponseWriter, req *http.Request) {
	ctx := context.WithValue(req.Context(), featureFlagOverridesKey{}, h.overrides)
	h.inner.ServeHTTP(res, req.WithContext(ctx))
}
