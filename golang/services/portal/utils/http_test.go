package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type sampleFlag int

const (
	flagOne sampleFlag = iota
	flagTwo
)

var sampleFlagsByName = map[string]sampleFlag{
	"FlagOne": flagOne,
	"FlagTwo": flagTwo,
}

func (f sampleFlag) String() string {
	for string, value := range sampleFlagsByName {
		if f == value {
			return string
		}
	}
	return "<invalid sampleFlag>"
}

func TestParseFeatureFlagOverrides(t *testing.T) {
	for _, test := range []struct {
		name        string
		input       string
		overrides   map[sampleFlag]bool
		unknownKeys []string
		expectedErr string
	}{
		{
			name:      "empty input",
			input:     "",
			overrides: map[sampleFlag]bool{},
		},
		{
			name:      "single Flag=true value",
			input:     "FlagOne=true",
			overrides: map[sampleFlag]bool{flagOne: true},
		},
		{
			name:      "single Flag=false value",
			input:     "FlagOne=false",
			overrides: map[sampleFlag]bool{flagOne: false},
		},
		{
			name:      "multiple values with space separator",
			input:     "FlagOne=false, FlagTwo=true",
			overrides: map[sampleFlag]bool{flagOne: false, flagTwo: true},
		},
		{
			name:        "part without equals sign",
			input:       "bad",
			expectedErr: `invalid part "bad" at index 0`,
		},
		{
			name:        "invalid boolean value",
			input:       "FlagOne=bad",
			expectedErr: "invalid value for flag FlagOne",
		},
		{
			name:        "unknown feature flag",
			input:       "FlagOne=true, FlagSomethingElse=false",
			overrides:   map[sampleFlag]bool{flagOne: true},
			unknownKeys: []string{"FlagSomethingElse"},
		},
	} {
		t.Run(test.name, func(t *testing.T) {
			overrides, unknownKeys, err := parseOverrides(test.input, sampleFlagsByName)
			if test.expectedErr != "" {
				assert.Nil(t, overrides)
				assert.Nil(t, unknownKeys)
				assert.ErrorContains(t, err, test.expectedErr)
			} else {
				assert.Equal(t, test.overrides, overrides)
				assert.Equal(t, test.unknownKeys, unknownKeys)
				assert.NoError(t, err)
			}
		})
	}
}
