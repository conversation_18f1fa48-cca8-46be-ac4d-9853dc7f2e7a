package cache

import (
	"bytes"
	"context"
	"io"
	"os"
	"slices"
	"testing"

	"github.com/aws/aws-sdk-go/service/cloudwatchlogs"
	"github.com/aws/aws-sdk-go/service/s3"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	awssdk "github.com/aws/aws-sdk-go/aws"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/crgo/config/schema"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	"github.com/carbonrobotics/cloud/golang/pkg/aws"
	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
)

func component(branches ...*robot_syncer.ComponentBranch) *robot_syncer.KeyComponent {
	return &robot_syncer.KeyComponent{Branches: branches}
}

func literal(s string) *robot_syncer.ComponentBranch {
	return &robot_syncer.ComponentBranch{
		Branch: &robot_syncer.ComponentBranch_Literal{
			Literal: s,
		},
	}
}

func wildcard(prefix, suffix string) *robot_syncer.ComponentBranch {
	return &robot_syncer.ComponentBranch{
		Branch: &robot_syncer.ComponentBranch_Wildcard{
			Wildcard: &robot_syncer.Pattern{Prefix: prefix, Suffix: suffix},
		},
	}
}

func TestComposePathsFromKeySpec(t *testing.T) {
	// parse test config from testdata
	schemaReader, err := schema.NewReader("../backend/shared_config_schemas", "roots")
	assert.NoError(t, err)
	assert.NotNil(t, schemaReader)
	robotSerial, err := carbon.ParseSerialWithClass("slayer1", "slayers")
	assert.NoError(t, err)
	assert.NotNil(t, robotSerial)

	// configure s3 facade
	mockS3 := new(mocks.S3Requester)
	mockS3.On("PutObject", mock.Anything, mock.Anything, mock.Anything).Return(&s3.PutObjectOutput{}, nil)
	s3f := awslib.NewAWSS3Facade(mockS3)

	// create a config cache
	c := &ConfigCache{
		schemaReader: schemaReader,
		s3Facade:     s3f,
	}
	// unmarshal the test config
	configSchema, err := schemaReader.GetConfigSchema(robotSerial.Class())
	assert.NoError(t, err)
	assert.NotNil(t, configSchema)

	jsonData, err := os.ReadFile(ROOT_NODE_TEST_CONFIG)
	assert.NoError(t, err)

	root, err := config.UnmarshalConfigFromJson(jsonData, configSchema)
	assert.NoError(t, err)
	assert.NotNil(t, root)

	tests := []struct {
		name        string
		keySpec     *robot_syncer.KeySpec
		expected    []string
		expectedErr bool
	}{
		{
			name: "single literal",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{component(literal("command"))},
			},
			expected:    []string{"command"},
			expectedErr: false,
		},
		{
			name: "non existent key",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{component(literal("non-existent"))},
			},
			expected:    []string{},
			expectedErr: true,
		},
		{
			name: "single literal with complex path",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(literal("command")),
					component(literal("commander")),
					component(literal("dashboard")),
					component(literal("metrics")),
					component(literal("efficiency")),
					component(literal("enabled")),
				},
			},
			expected:    []string{"command/commander/dashboard/metrics/efficiency/enabled"},
			expectedErr: false,
		},
		{
			name: "multiple explicit branches",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(literal("row1"), literal("row2"), literal("row3")),
				},
			},
			expected:    []string{"row1", "row2", "row3"},
			expectedErr: false,
		},
		{
			name: "wildcard with prefix",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(wildcard("row", "")),
					component(literal("aimbot")),
				},
			},
			expected:    []string{"row1/aimbot", "row2/aimbot", "row3/aimbot"},
			expectedErr: false,
		},
		{
			name: "wildcard with suffix",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(wildcard("", "w1")),
					component(literal("aimbot")),
				},
			},
			expected:    []string{"row1/aimbot"},
			expectedErr: false,
		},
		{
			name: "wildcard *",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(wildcard("row", "")),
					component(literal("cv")),
					component(wildcard("", "")),
				},
			},
			expected: []string{
				"row1/cv/auto_fix_connection_timer",
				"row1/cv/cameras",
				"row1/cv/embedding_classifier",
				"row1/cv/embedding_manager",
				"row2/cv/auto_fix_connection_timer",
				"row2/cv/cameras",
				"row2/cv/embedding_classifier",
				"row2/cv/embedding_manager",
				"row3/cv/auto_fix_connection_timer",
				"row3/cv/cameras",
				"row3/cv/embedding_classifier",
				"row3/cv/embedding_manager",
			},
			expectedErr: false,
		},
		{
			name: "wildcard with no matching children",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(wildcard("rows", "")),
					component(literal("aimbot")),
				},
			},
			expected:    []string{},
			expectedErr: false,
		},
		{
			name: "wildcard additional branches (deduplication test)",
			keySpec: &robot_syncer.KeySpec{
				Components: []*robot_syncer.KeyComponent{
					component(wildcard("row", ""), literal("row1")),
					component(literal("aimbot")),
				},
			},
			expected:    []string{"row1/aimbot", "row2/aimbot", "row3/aimbot"},
			expectedErr: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			paths, err := c.composePathsFromKeySpec(context.Background(), root, test.keySpec)
			if test.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				log.Infof("Paths: %v", paths)
				log.Infof("Expected: %v", test.expected)
				assert.True(t, slices.Equal(test.expected, paths))
			}
		})
	}
}

func TestMatchesPattern(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		pattern  *robot_syncer.Pattern
		expected bool
	}{
		{
			name:     "empty pattern",
			s:        "test",
			pattern:  &robot_syncer.Pattern{},
			expected: true,
		},
		{
			name:     "prefix pattern",
			s:        "test",
			pattern:  &robot_syncer.Pattern{Prefix: "te"},
			expected: true,
		},
		{
			name:     "suffix pattern",
			s:        "test",
			pattern:  &robot_syncer.Pattern{Suffix: "st"},
			expected: true,
		},
		{
			name:     "prefix and suffix pattern",
			s:        "test",
			pattern:  &robot_syncer.Pattern{Prefix: "te", Suffix: "st"},
			expected: true,
		},
		{
			name:     "prefix and suffix pattern overlapping",
			s:        "test",
			pattern:  &robot_syncer.Pattern{Prefix: "test", Suffix: "test"},
			expected: false,
		},
		{
			name:     "no match",
			s:        "test",
			pattern:  &robot_syncer.Pattern{Prefix: "no"},
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := matchesPattern(test.s, test.pattern)
			assert.Equal(t, test.expected, result)
		})
	}
}

func actionSet(value *config_service.ConfigValue) *robot_syncer.Action {
	return &robot_syncer.Action{
		Action: &robot_syncer.Action_Set{
			Set: &robot_syncer.SetAction{
				Value: value},
		},
	}
}

func stringVal(val string) *config_service.ConfigValue {
	return &config_service.ConfigValue{
		Value: &config_service.ConfigValue_StringVal{StringVal: val},
	}
}

func TestLogAndNotifyEditRecords(t *testing.T) {
	testRobotSerial, err := carbon.ParseSerial("slayer1234")
	assert.NoError(t, err)
	testLogGroupName := "test-log-group"
	testTimestamp := uint64(1234567890)
	testUserID := "test-user"

	requestorMock := new(mocks.CWLRequester)
	requestorMock.Mock.On("DescribeLogStreams", mock.AnythingOfType("*cloudwatchlogs.DescribeLogStreamsInput")).
		Return(&cloudwatchlogs.DescribeLogStreamsOutput{
			LogStreams: []*cloudwatchlogs.LogStream{
				{
					LogStreamName: awssdk.String(testRobotSerial.String()),
				},
			},
			NextToken: nil,
		}, nil)

	mockS3 := new(mocks.S3Requester)
	mockS3.Mock.On("GetObject", mock.AnythingOfType("*s3.GetObjectInput")).
		Return(&s3.GetObjectOutput{
			Body: io.NopCloser(bytes.NewReader([]byte("{}"))),
		}, nil)
	mockS3.Mock.On("PutObject", mock.AnythingOfType("*s3.PutObjectInput")).
		Return(&s3.PutObjectOutput{}, nil)

	auditLogger := audit.NewAuditLogger(aws.NewCWLFacade(requestorMock), testLogGroupName)
	auditLogger.RobotStreams[testRobotSerial.String()] = int64(testTimestamp)
	streamCache := &StreamCache{}
	configCache := &ConfigCache{
		keyedMutex: NewKeyedMutex(),
		s3Facade:   aws.NewAWSS3Facade(mockS3),
	}

	tests := []struct {
		name        string
		editRecords []*robot_syncer.EditRecord
		oldValues   map[string]string
		expected    int // expected number of unique audit logs
	}{
		{
			name: "set operations sanity check (different keys)",
			editRecords: []*robot_syncer.EditRecord{
				{
					Action:  actionSet(stringVal("new_value")),
					Key:     "test/key1",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
				{
					Action:  actionSet(stringVal("newer_value")),
					Key:     "test/key2",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
			},
			oldValues: map[string]string{},
			expected:  2,
		},
		{
			name: "deduplicate set operations",
			editRecords: []*robot_syncer.EditRecord{
				{
					Action:  actionSet(stringVal("new_value")),
					Key:     "test/key",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
				{
					Action:  actionSet(stringVal("newer_value")),
					Key:     "test/key",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
			},
			oldValues: map[string]string{},
			expected:  1, // Only the latest set operation should be logged
		},
		{
			name: "do not deduplicate add then remove operations",
			editRecords: []*robot_syncer.EditRecord{
				{
					Action: &robot_syncer.Action{
						Action: &robot_syncer.Action_Add{
							Add: &robot_syncer.AddAction{
								Name: "item1",
							},
						},
					},
					Key:     "test/list",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
				{
					Action: &robot_syncer.Action{
						Action: &robot_syncer.Action_Remove{
							Remove: &robot_syncer.RemoveAction{
								Name: "item1",
							},
						},
					},
					Key:     "test/list",
					Outcome: robot_syncer.Outcome_SUCCESS,
				},
			},
			oldValues: map[string]string{},
			expected:  2,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			var loggedEvents []*cloudwatchlogs.InputLogEvent
			requestorMock.Mock.ExpectedCalls = nil
			requestorMock.Mock.On("PutLogEvents", mock.AnythingOfType("*cloudwatchlogs.PutLogEventsInput")).
				Run(func(args mock.Arguments) {
					input := args.Get(0).(*cloudwatchlogs.PutLogEventsInput)
					loggedEvents = input.LogEvents
				}).
				Return(&cloudwatchlogs.PutLogEventsOutput{}, nil)

			configCache.logAndNotifyEditRecords(
				auditLogger,
				streamCache,
				testRobotSerial,
				testTimestamp,
				testUserID,
				test.editRecords,
				test.oldValues,
			)

			assert.Equal(t, test.expected, len(loggedEvents), "Expected %d log events, got %d", test.expected, len(loggedEvents))
		})
	}
}
