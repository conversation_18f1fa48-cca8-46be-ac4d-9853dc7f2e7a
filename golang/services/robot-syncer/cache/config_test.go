package cache

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/crgo/config/schema"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

const TEST_BUCKET_NAME = "carbon-robot-syncer-testing"
const TEST_SHARED_CONFIG_SCHEMA_DIR = "../backend/test_config_schemas"
const ROOT_NODE_TEST_CONFIG = "testdata/test_robot_config.json"

// NOTE This test uses your local AWS credentials
func TestFetchConfigFromS3(t *testing.T) {
	// performs full e2e test parsing a config from S3 and validating it against a schema
	t.SkipNow()
	testRobotSerial, err := carbon.ParseSerialWithClass("slayer1", "slayers")
	assert.NoError(t, err)

	s3Facade := NewMockS3(t) // TODO:(smt) this previously created a session with local creds.
	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)

	schema, err := schemaReader.GetConfigSchema(carbon.ClassSlayers)
	assert.NoError(t, err)
	assert.NotNil(t, schema)

	root, err := fetchConfigFromS3(s3Facade, TEST_BUCKET_NAME, testRobotSerial, schemaReader)
	assert.NoError(t, err)
	assert.NotNil(t, root)
}

// NOTE This test uses your local AWS credentials
func TestWriteConfigToS3(t *testing.T) {
	// performs full e2e test writing a config to S3 and validating it against a schema
	t.SkipNow()
	testRobotSerial, err := carbon.ParseSerialWithClass("slayer1", "slayers")
	assert.NoError(t, err)
	testConfigEntryPath := "command/commander/alarm/cv_runtime_latency_threshold_ms"

	s3f := NewMockS3(t) // TODO:(smt) this previously created a session with local creds.
	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)

	configCache := NewConfigCache(context.Background(), s3f, TEST_BUCKET_NAME, schemaReader)

	// load simulator0 into cache from S3
	root, err := fetchConfigFromS3(s3f, TEST_BUCKET_NAME, testRobotSerial, schemaReader)
	assert.NoError(t, err)
	assert.NotNil(t, root)

	timeNow := uint64(time.Now().UnixMilli())

	// update config
	configCache.UpdateConfigNode(context.TODO(), testRobotSerial, testConfigEntryPath, 3000.0, 12341234, true)

	// get updated config from S3
	node, err := configCache.GetConfigNodeFromPath(context.TODO(), testRobotSerial, testConfigEntryPath)
	assert.NoError(t, err)
	assert.NotNil(t, node)
	assert.GreaterOrEqual(t, timeNow, node.Value.TimestampMs)
}

func TestMockFetchConfigFromS3(t *testing.T) {
	// performs full e2e test parsing a config from S3 and validating it against a schema
	// except it reads the JSON from a local file and mocks the S3 call
	testSerial, err := carbon.ParseSerialWithClass("simulator0", "simulators")
	assert.NoError(t, err)
	jsonData, err := os.ReadFile(ROOT_NODE_TEST_CONFIG)
	assert.NoError(t, err)

	readCLoser := io.NopCloser(bytes.NewReader(jsonData))

	// configure s3 facade
	mockS3 := new(mocks.S3Requester)
	mockS3.On("GetObject", mock.Anything, mock.Anything, mock.Anything).Return(&s3.GetObjectOutput{
		Body: readCLoser,
	}, nil)
	s3f := awslib.NewAWSS3Facade(mockS3)

	// fetch schema
	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)
	schema, err := schemaReader.GetConfigSchema(carbon.ClassSimulators)
	assert.NoError(t, err)
	assert.NotNil(t, schema)

	// fetch config from mocked S3
	root, err := fetchConfigFromS3(s3f, TEST_BUCKET_NAME, testSerial, schemaReader)
	assert.NoError(t, err)
	assert.NotNil(t, root)

	expectedRoot, err := config.UnmarshalConfigFromJson(jsonData, schema)
	assert.NoError(t, err)
	assert.NotNil(t, expectedRoot)

	jsonBytes, err := config.MarshalConfigToJson(root)
	assert.NoError(t, err)
	assert.NotNil(t, jsonBytes)
	expectedJsonBytes, err := config.MarshalConfigToJson(expectedRoot)
	assert.NoError(t, err)
	assert.NotNil(t, expectedJsonBytes)
	require.JSONEq(t, string(jsonBytes), string(expectedJsonBytes))
}

func TestGetAllRobotSerials(t *testing.T) {
	// performs full e2e test fetching all robot serials from S3
	t.SkipNow()

	s3f := NewMockS3(t) // TODO:(smt) this previously created a session with local creds.

	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)

	configCache := NewConfigCache(context.Background(), s3f, "carbon-robot-syncer-staging", schemaReader)

	serials := configCache.getAllRobotSerials()
	assert.NoError(t, err)
	assert.NotEmpty(t, serials)
	log.Infof("Serials: %v", serials)
}
