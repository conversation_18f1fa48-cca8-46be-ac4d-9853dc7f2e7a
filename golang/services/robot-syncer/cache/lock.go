package cache

import (
	"sync"
)

type KeyedMutex struct {
	mu    sync.Mutex
	locks map[string]*sync.RWMutex
}

func NewKeyedMutex() *KeyedMutex {
	return &KeyedMutex{
		locks: make(map[string]*sync.RWMutex),
	}
}

func (km *KeyedMutex) Get(key string) *sync.RWMutex {
	km.mu.Lock()
	defer km.mu.Unlock()
	lock, ok := km.locks[key]
	if !ok {
		lock = &sync.RWMutex{}
		km.locks[key] = lock
	}
	return lock
}
