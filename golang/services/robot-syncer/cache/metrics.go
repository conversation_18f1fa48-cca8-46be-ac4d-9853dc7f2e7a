package cache

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

var (
	// Config metrics
	configCacheReadsFromS3 = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "config_cache_reads_from_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer config cache reads from S3",
	})
	configCacheWritesToS3 = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "config_cache_writes_to_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer config cache writes to S3",
	})
	unsyncedKeysPerRobot = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name:      "unsynced_keys_per_robot",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "number of unsynced keys per robot",
	},
		[]string{"serial"},
	)

	// Profile metrics
	profileCacheReadsFromS3 = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "profile_cache_reads_from_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer profile cache reads from S3",
	})
	profileCacheWritesToS3 = promauto.NewCounter(prometheus.CounterOpts{
		Name:      "profile_cache_writes_to_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer profile cache writes to S3",
	})
	carbonProvidedProfiles = promauto.NewGauge(prometheus.GaugeOpts{
		Name:      "carbon_provided_profiles",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of carbon provided profiles",
	})
	profileCount = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name:      "profile_count",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of non carbon provide profiles",
	},
		[]string{"serial", "profile_type"},
	)

	// Per robot metrics
	readsFromS3 = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name:      "reads_from_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer reads from S3 per serial",
	},
		[]string{"serial"},
	)
	writesToS3 = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name:      "writes_to_s3",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot syncer writes to S3",
	},
		[]string{"serial"},
	)
)
