// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"io"
	"time"

	"github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/crgo/carbon"
	mock "github.com/stretchr/testify/mock"
)

// NewMockS3 creates a new instance of MockS3. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockS3(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockS3 {
	mock := &MockS3{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockS3 is an autogenerated mock type for the S3 type
type MockS3 struct {
	mock.Mock
}

type MockS3_Expecter struct {
	mock *mock.Mock
}

func (_m *MockS3) EXPECT() *MockS3_Expecter {
	return &MockS3_Expecter{mock: &_m.Mock}
}

// DeleteObject provides a mock function for the type MockS3
func (_mock *MockS3) DeleteObject(bucket string, key string) error {
	ret := _mock.Called(bucket, key)

	if len(ret) == 0 {
		panic("no return value specified for DeleteObject")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = returnFunc(bucket, key)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockS3_DeleteObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteObject'
type MockS3_DeleteObject_Call struct {
	*mock.Call
}

// DeleteObject is a helper method to define mock.On call
//   - bucket string
//   - key string
func (_e *MockS3_Expecter) DeleteObject(bucket interface{}, key interface{}) *MockS3_DeleteObject_Call {
	return &MockS3_DeleteObject_Call{Call: _e.mock.On("DeleteObject", bucket, key)}
}

func (_c *MockS3_DeleteObject_Call) Run(run func(bucket string, key string)) *MockS3_DeleteObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockS3_DeleteObject_Call) Return(err error) *MockS3_DeleteObject_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockS3_DeleteObject_Call) RunAndReturn(run func(bucket string, key string) error) *MockS3_DeleteObject_Call {
	_c.Call.Return(run)
	return _c
}

// GetClosestObjectVersion provides a mock function for the type MockS3
func (_mock *MockS3) GetClosestObjectVersion(bucket string, key string, timestamp time.Time, out io.Writer) error {
	ret := _mock.Called(bucket, key, timestamp, out)

	if len(ret) == 0 {
		panic("no return value specified for GetClosestObjectVersion")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string, time.Time, io.Writer) error); ok {
		r0 = returnFunc(bucket, key, timestamp, out)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockS3_GetClosestObjectVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClosestObjectVersion'
type MockS3_GetClosestObjectVersion_Call struct {
	*mock.Call
}

// GetClosestObjectVersion is a helper method to define mock.On call
//   - bucket string
//   - key string
//   - timestamp time.Time
//   - out io.Writer
func (_e *MockS3_Expecter) GetClosestObjectVersion(bucket interface{}, key interface{}, timestamp interface{}, out interface{}) *MockS3_GetClosestObjectVersion_Call {
	return &MockS3_GetClosestObjectVersion_Call{Call: _e.mock.On("GetClosestObjectVersion", bucket, key, timestamp, out)}
}

func (_c *MockS3_GetClosestObjectVersion_Call) Run(run func(bucket string, key string, timestamp time.Time, out io.Writer)) *MockS3_GetClosestObjectVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 time.Time
		if args[2] != nil {
			arg2 = args[2].(time.Time)
		}
		var arg3 io.Writer
		if args[3] != nil {
			arg3 = args[3].(io.Writer)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockS3_GetClosestObjectVersion_Call) Return(err error) *MockS3_GetClosestObjectVersion_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockS3_GetClosestObjectVersion_Call) RunAndReturn(run func(bucket string, key string, timestamp time.Time, out io.Writer) error) *MockS3_GetClosestObjectVersion_Call {
	_c.Call.Return(run)
	return _c
}

// GetFileChecksum provides a mock function for the type MockS3
func (_mock *MockS3) GetFileChecksum(bucket string, key string) (string, error) {
	ret := _mock.Called(bucket, key)

	if len(ret) == 0 {
		panic("no return value specified for GetFileChecksum")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string, string) (string, error)); ok {
		return returnFunc(bucket, key)
	}
	if returnFunc, ok := ret.Get(0).(func(string, string) string); ok {
		r0 = returnFunc(bucket, key)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = returnFunc(bucket, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockS3_GetFileChecksum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileChecksum'
type MockS3_GetFileChecksum_Call struct {
	*mock.Call
}

// GetFileChecksum is a helper method to define mock.On call
//   - bucket string
//   - key string
func (_e *MockS3_Expecter) GetFileChecksum(bucket interface{}, key interface{}) *MockS3_GetFileChecksum_Call {
	return &MockS3_GetFileChecksum_Call{Call: _e.mock.On("GetFileChecksum", bucket, key)}
}

func (_c *MockS3_GetFileChecksum_Call) Run(run func(bucket string, key string)) *MockS3_GetFileChecksum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockS3_GetFileChecksum_Call) Return(s string, err error) *MockS3_GetFileChecksum_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockS3_GetFileChecksum_Call) RunAndReturn(run func(bucket string, key string) (string, error)) *MockS3_GetFileChecksum_Call {
	_c.Call.Return(run)
	return _c
}

// GetObject provides a mock function for the type MockS3
func (_mock *MockS3) GetObject(bucket string, key string, out io.Writer) error {
	ret := _mock.Called(bucket, key, out)

	if len(ret) == 0 {
		panic("no return value specified for GetObject")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string, io.Writer) error); ok {
		r0 = returnFunc(bucket, key, out)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockS3_GetObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetObject'
type MockS3_GetObject_Call struct {
	*mock.Call
}

// GetObject is a helper method to define mock.On call
//   - bucket string
//   - key string
//   - out io.Writer
func (_e *MockS3_Expecter) GetObject(bucket interface{}, key interface{}, out interface{}) *MockS3_GetObject_Call {
	return &MockS3_GetObject_Call{Call: _e.mock.On("GetObject", bucket, key, out)}
}

func (_c *MockS3_GetObject_Call) Run(run func(bucket string, key string, out io.Writer)) *MockS3_GetObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 io.Writer
		if args[2] != nil {
			arg2 = args[2].(io.Writer)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockS3_GetObject_Call) Return(err error) *MockS3_GetObject_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockS3_GetObject_Call) RunAndReturn(run func(bucket string, key string, out io.Writer) error) *MockS3_GetObject_Call {
	_c.Call.Return(run)
	return _c
}

// ListFileNames provides a mock function for the type MockS3
func (_mock *MockS3) ListFileNames(lfn aws.ListSubPrefixNamesInput) ([]string, error) {
	ret := _mock.Called(lfn)

	if len(ret) == 0 {
		panic("no return value specified for ListFileNames")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(aws.ListSubPrefixNamesInput) ([]string, error)); ok {
		return returnFunc(lfn)
	}
	if returnFunc, ok := ret.Get(0).(func(aws.ListSubPrefixNamesInput) []string); ok {
		r0 = returnFunc(lfn)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(aws.ListSubPrefixNamesInput) error); ok {
		r1 = returnFunc(lfn)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockS3_ListFileNames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListFileNames'
type MockS3_ListFileNames_Call struct {
	*mock.Call
}

// ListFileNames is a helper method to define mock.On call
//   - lfn aws.ListSubPrefixNamesInput
func (_e *MockS3_Expecter) ListFileNames(lfn interface{}) *MockS3_ListFileNames_Call {
	return &MockS3_ListFileNames_Call{Call: _e.mock.On("ListFileNames", lfn)}
}

func (_c *MockS3_ListFileNames_Call) Run(run func(lfn aws.ListSubPrefixNamesInput)) *MockS3_ListFileNames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 aws.ListSubPrefixNamesInput
		if args[0] != nil {
			arg0 = args[0].(aws.ListSubPrefixNamesInput)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockS3_ListFileNames_Call) Return(strings []string, err error) *MockS3_ListFileNames_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockS3_ListFileNames_Call) RunAndReturn(run func(lfn aws.ListSubPrefixNamesInput) ([]string, error)) *MockS3_ListFileNames_Call {
	_c.Call.Return(run)
	return _c
}

// ListSubPrefixNames provides a mock function for the type MockS3
func (_mock *MockS3) ListSubPrefixNames(lsp aws.ListSubPrefixNamesInput) ([]string, error) {
	ret := _mock.Called(lsp)

	if len(ret) == 0 {
		panic("no return value specified for ListSubPrefixNames")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(aws.ListSubPrefixNamesInput) ([]string, error)); ok {
		return returnFunc(lsp)
	}
	if returnFunc, ok := ret.Get(0).(func(aws.ListSubPrefixNamesInput) []string); ok {
		r0 = returnFunc(lsp)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(aws.ListSubPrefixNamesInput) error); ok {
		r1 = returnFunc(lsp)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockS3_ListSubPrefixNames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSubPrefixNames'
type MockS3_ListSubPrefixNames_Call struct {
	*mock.Call
}

// ListSubPrefixNames is a helper method to define mock.On call
//   - lsp aws.ListSubPrefixNamesInput
func (_e *MockS3_Expecter) ListSubPrefixNames(lsp interface{}) *MockS3_ListSubPrefixNames_Call {
	return &MockS3_ListSubPrefixNames_Call{Call: _e.mock.On("ListSubPrefixNames", lsp)}
}

func (_c *MockS3_ListSubPrefixNames_Call) Run(run func(lsp aws.ListSubPrefixNamesInput)) *MockS3_ListSubPrefixNames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 aws.ListSubPrefixNamesInput
		if args[0] != nil {
			arg0 = args[0].(aws.ListSubPrefixNamesInput)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockS3_ListSubPrefixNames_Call) Return(strings []string, err error) *MockS3_ListSubPrefixNames_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockS3_ListSubPrefixNames_Call) RunAndReturn(run func(lsp aws.ListSubPrefixNamesInput) ([]string, error)) *MockS3_ListSubPrefixNames_Call {
	_c.Call.Return(run)
	return _c
}

// ObjectExists provides a mock function for the type MockS3
func (_mock *MockS3) ObjectExists(bucket string, key string) (bool, error) {
	ret := _mock.Called(bucket, key)

	if len(ret) == 0 {
		panic("no return value specified for ObjectExists")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string, string) (bool, error)); ok {
		return returnFunc(bucket, key)
	}
	if returnFunc, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = returnFunc(bucket, key)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = returnFunc(bucket, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockS3_ObjectExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ObjectExists'
type MockS3_ObjectExists_Call struct {
	*mock.Call
}

// ObjectExists is a helper method to define mock.On call
//   - bucket string
//   - key string
func (_e *MockS3_Expecter) ObjectExists(bucket interface{}, key interface{}) *MockS3_ObjectExists_Call {
	return &MockS3_ObjectExists_Call{Call: _e.mock.On("ObjectExists", bucket, key)}
}

func (_c *MockS3_ObjectExists_Call) Run(run func(bucket string, key string)) *MockS3_ObjectExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockS3_ObjectExists_Call) Return(b bool, err error) *MockS3_ObjectExists_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockS3_ObjectExists_Call) RunAndReturn(run func(bucket string, key string) (bool, error)) *MockS3_ObjectExists_Call {
	_c.Call.Return(run)
	return _c
}

// PreSignedGetObjectURL provides a mock function for the type MockS3
func (_mock *MockS3) PreSignedGetObjectURL(bucket string, key string, ttl time.Duration) (string, error) {
	ret := _mock.Called(bucket, key, ttl)

	if len(ret) == 0 {
		panic("no return value specified for PreSignedGetObjectURL")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string, string, time.Duration) (string, error)); ok {
		return returnFunc(bucket, key, ttl)
	}
	if returnFunc, ok := ret.Get(0).(func(string, string, time.Duration) string); ok {
		r0 = returnFunc(bucket, key, ttl)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(string, string, time.Duration) error); ok {
		r1 = returnFunc(bucket, key, ttl)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockS3_PreSignedGetObjectURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreSignedGetObjectURL'
type MockS3_PreSignedGetObjectURL_Call struct {
	*mock.Call
}

// PreSignedGetObjectURL is a helper method to define mock.On call
//   - bucket string
//   - key string
//   - ttl time.Duration
func (_e *MockS3_Expecter) PreSignedGetObjectURL(bucket interface{}, key interface{}, ttl interface{}) *MockS3_PreSignedGetObjectURL_Call {
	return &MockS3_PreSignedGetObjectURL_Call{Call: _e.mock.On("PreSignedGetObjectURL", bucket, key, ttl)}
}

func (_c *MockS3_PreSignedGetObjectURL_Call) Run(run func(bucket string, key string, ttl time.Duration)) *MockS3_PreSignedGetObjectURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 time.Duration
		if args[2] != nil {
			arg2 = args[2].(time.Duration)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockS3_PreSignedGetObjectURL_Call) Return(s string, err error) *MockS3_PreSignedGetObjectURL_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockS3_PreSignedGetObjectURL_Call) RunAndReturn(run func(bucket string, key string, ttl time.Duration) (string, error)) *MockS3_PreSignedGetObjectURL_Call {
	_c.Call.Return(run)
	return _c
}

// PutObject provides a mock function for the type MockS3
func (_mock *MockS3) PutObject(bucket string, key string, content io.Reader) error {
	ret := _mock.Called(bucket, key, content)

	if len(ret) == 0 {
		panic("no return value specified for PutObject")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string, io.Reader) error); ok {
		r0 = returnFunc(bucket, key, content)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockS3_PutObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutObject'
type MockS3_PutObject_Call struct {
	*mock.Call
}

// PutObject is a helper method to define mock.On call
//   - bucket string
//   - key string
//   - content io.Reader
func (_e *MockS3_Expecter) PutObject(bucket interface{}, key interface{}, content interface{}) *MockS3_PutObject_Call {
	return &MockS3_PutObject_Call{Call: _e.mock.On("PutObject", bucket, key, content)}
}

func (_c *MockS3_PutObject_Call) Run(run func(bucket string, key string, content io.Reader)) *MockS3_PutObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 io.Reader
		if args[2] != nil {
			arg2 = args[2].(io.Reader)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockS3_PutObject_Call) Return(err error) *MockS3_PutObject_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockS3_PutObject_Call) RunAndReturn(run func(bucket string, key string, content io.Reader) error) *MockS3_PutObject_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRobotNotifier creates a new instance of MockRobotNotifier. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRobotNotifier(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRobotNotifier {
	mock := &MockRobotNotifier{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockRobotNotifier is an autogenerated mock type for the RobotNotifier type
type MockRobotNotifier struct {
	mock.Mock
}

type MockRobotNotifier_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRobotNotifier) EXPECT() *MockRobotNotifier_Expecter {
	return &MockRobotNotifier_Expecter{mock: &_m.Mock}
}

// NotifyRobot provides a mock function for the type MockRobotNotifier
func (_mock *MockRobotNotifier) NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) bool {
	ret := _mock.Called(serial, key, ts)

	if len(ret) == 0 {
		panic("no return value specified for NotifyRobot")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial, string, uint64) bool); ok {
		r0 = returnFunc(serial, key, ts)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// MockRobotNotifier_NotifyRobot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NotifyRobot'
type MockRobotNotifier_NotifyRobot_Call struct {
	*mock.Call
}

// NotifyRobot is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
//   - key string
//   - ts uint64
func (_e *MockRobotNotifier_Expecter) NotifyRobot(serial interface{}, key interface{}, ts interface{}) *MockRobotNotifier_NotifyRobot_Call {
	return &MockRobotNotifier_NotifyRobot_Call{Call: _e.mock.On("NotifyRobot", serial, key, ts)}
}

func (_c *MockRobotNotifier_NotifyRobot_Call) Run(run func(serial *carbon.ValidSerial, key string, ts uint64)) *MockRobotNotifier_NotifyRobot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 uint64
		if args[2] != nil {
			arg2 = args[2].(uint64)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockRobotNotifier_NotifyRobot_Call) Return(success bool) *MockRobotNotifier_NotifyRobot_Call {
	_c.Call.Return(success)
	return _c
}

func (_c *MockRobotNotifier_NotifyRobot_Call) RunAndReturn(run func(serial *carbon.ValidSerial, key string, ts uint64) bool) *MockRobotNotifier_NotifyRobot_Call {
	_c.Call.Return(run)
	return _c
}
