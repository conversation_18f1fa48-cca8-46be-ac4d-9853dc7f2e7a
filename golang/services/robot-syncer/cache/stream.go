package cache

import (
	"context"
	"errors"
	"io"
	"path"
	"sync"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
)

// only supports one active subscription per robot serial at a time
type StreamCache struct {
	activeRobotStreamsMu sync.RWMutex
	activeRobotStreams   map[string]string // map of serial to stream id to denote which stream is active.

	streams sync.Map // map[streamID string]*StreamWithContext
}

func (sc *StreamCache) setActiveRobot(serial string, streamID string) {
	sc.activeRobotStreamsMu.Lock()
	defer sc.activeRobotStreamsMu.Unlock()
	sc.activeRobotStreams[serial] = streamID
}

func (sc *StreamCache) GetActiveStream(serial *carbon.ValidSerial) (*StreamWithContext, bool) {
	if serial == nil {
		return nil, false
	}
	sc.activeRobotStreamsMu.RLock()
	defer sc.activeRobotStreamsMu.RUnlock()
	if streamID, ok := sc.activeRobotStreams[serial.String()]; ok {
		return sc.GetStream(streamID)
	}
	return nil, false
}

type StreamWithContext struct {
	streamID string
	serial   *carbon.ValidSerial
	stream   config_service.ConfigNotificationService_SubscribeServer
	cancel   context.CancelFunc
}

func (sc *StreamWithContext) ID() string {
	return sc.streamID
}

func (sc *StreamWithContext) Run(streamCtx context.Context) {
	ctx, cancel := context.WithCancel(streamCtx)
	sc.cancel = cancel

	<-ctx.Done()
	log.WithError(ctx.Err()).Infof("Client %s (id: %s) disconnected", sc.serial.String(), sc.ID())
}

func (sc *StreamWithContext) Close() {
	if sc.cancel != nil {
		sc.cancel()
	}
}

func NewStreamCache() *StreamCache {
	return &StreamCache{
		activeRobotStreams: make(map[string]string),
	}
}

// AddStream adds the connected grpc stream to the stream cache, we only have one stream per robot serial, but we use a stream id (guid)
// to distinguish multiple streams for the same serial for disambiguation of connection lifecycle.
func (sc *StreamCache) AddStream(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) *StreamWithContext {
	log.Infof("Creating stream for %s (id: %s)", serial, streamID)
	oldStream, ok := sc.GetActiveStream(serial)
	if ok {
		log.Infof("Replacing existing stream (id: %s) for %s", oldStream.ID(), serial)
		oldStream.Close()
	}

	streamWithContext := &StreamWithContext{
		streamID: streamID,
		serial:   serial,
		stream:   stream,
	}
	sc.streams.Store(streamID, streamWithContext)
	sc.setActiveRobot(serial.String(), streamID)
	return streamWithContext
}

func (sc *StreamCache) GetStream(streamID string) (*StreamWithContext, bool) {
	stream, ok := sc.streams.Load(streamID)
	if !ok {
		return nil, false
	}

	return stream.(*StreamWithContext), true
}

func (sc *StreamCache) Delete(streamID string) {
	sc.streams.Delete(streamID)
}

func (sc *StreamCache) NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) (success bool) {
	log.Debugf("Notifying robot %s of key %s", serial, key)
	streamContext, ok := sc.GetActiveStream(serial)
	if ok {
		if err := streamContext.stream.Send(&config_service.SubscriptionNotifyMessage{
			SubscriptionKey: path.Join(serial.Class().String(), serial.String()),
			NotifyKey:       key,
		}); err != nil {
			log.WithError(err).Errorf("Failed to send notification for %s, adding to unsynced keys map", key)
			if errors.Is(err, io.EOF) {
				log.Debugf("Client %s closed its connection", serial)
			}
			return false
		}
		return true
	}

	log.Warnf("No stream found for %s", serial)
	return false
}
