package cache

import (
	"bytes"
	"context"
	"fmt"
	"path"
	"strings"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/crgo/config/schema"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

// Config templates are maintained per robot classification. They live in the
// same S3 bucket as the robot configs, only they are stored directly under
// the classification folders (e.g. "buds/template.json", "slayers/template.json", etc).

func (c *ConfigCache) initializeTemplates() error {
	var initErr error
	for _, class := range carbon.SupportedRobotClassifications() {
		_, err := c.getTemplate(class)
		if err != nil {
			initErr = fmt.Errorf("error fetching template for %s: %v", class, err)
			log.Warn(err)
			continue
		}
	}
	return initErr
}

func fetchTemplateFromS3(s3f S3, s3Bucket string, class carbon.Classification, schemaReader *schema.Reader) (*config_service.ConfigNode, error) {
	log.Debugf("Loading %s template from S3", class)
	configCacheReadsFromS3.Inc()
	readsFromS3.WithLabelValues(class.String()).Inc()
	templateFilename := path.Join(class.String(), constants.ROBOT_CONFIG_TEMPLATE_FILENAME)
	buf := &bytes.Buffer{}
	err := s3f.GetObject(s3Bucket, templateFilename, buf)
	if err != nil {
		return nil, err
	}

	schema, err := schemaReader.GetConfigSchema(class)
	if err != nil {
		return nil, fmt.Errorf("failed to get %s config schema: %w", class, err)
	}

	configBytes := buf.Bytes()
	root, err := config.UnmarshalConfigFromJson(configBytes, schema)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal robot config from json: %w", err)
	}

	return root, nil
}

func (c *ConfigCache) GetTemplateJSON(class carbon.Classification) ([]byte, error) {
	mu := c.keyedMutex.Get(class.String())
	mu.RLock()
	defer mu.RUnlock()

	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return nil, err
	}

	jsonData, err := config.MarshalConfigToJson(cachedTemplate)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal template to json: %w", err)
	}
	return []byte(jsonData), nil
}

/**
 * getTemplate returns the template for the given class. This function
 * handles writeSafeCacheItem creation and locking. It creates an empty cache
 * entry and locks it immediately to ensure that no other goroutines can
 * concurrently store the same cache item. If the template is not in the cache,
 * it attempts to load it from S3. If the template is found in the cache, the
 * newly created cache entry is discarded and the existing cache entry is
 * returned.
 **/
func (c *ConfigCache) getTemplate(class carbon.Classification) (*config_service.ConfigNode, error) {
	// immediately locked to ensure we don't have multiple goroutines trying to concurrently load the same config
	cacheEntryRaw, exists := c.templateCache.Load(class.String())
	if exists && cacheEntryRaw == nil {
		c.templateCache.Delete(class.String())
		return nil, fmt.Errorf("cached template for %s is nil. Refresh RoSy's template cache entry for %s once a template has been created or added to S3", class, class)
	}

	if !exists {
		// not in cache, attempt to load from S3
		root, err := fetchTemplateFromS3(c.s3Facade, c.s3Bucket, class, c.schemaReader)
		if err != nil {
			log.Warnf("Failed to fetch %s template, caching nil config", class)
			c.templateCache.Store(class.String(), nil)
			return nil, fmt.Errorf("failed to fetch %s template: %w", class, err)
		}

		// rename the root node to the class (default is it is named after the class)
		root.Name = class.String()
		c.templateCache.Store(class.String(), root)
		return root, nil
	}

	return cacheEntryRaw.(*config_service.ConfigNode), nil
}

func (c *ConfigCache) GetTemplateConfigNodeFromPath(ctx context.Context, class carbon.Classification, path string) (*config_service.ConfigNode, error) {
	mu := c.keyedMutex.Get(class.String())
	mu.RLock()
	defer mu.RUnlock()

	errorPrefix := fmt.Sprintf("unable to fetch template config node from path: %s", path)
	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return nil, fmt.Errorf("%s -- %v", errorPrefix, err)
	}

	return config.GetNodeFromPath(cachedTemplate, path)
}

func (c *ConfigCache) UpdateTemplateNode(ctx context.Context, class carbon.Classification, key string, value any, ts uint64) error {
	mu := c.keyedMutex.Get(class.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to set %s template value", class)
	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	schemaRoot, err := c.schemaReader.GetConfigSchema(class)
	if err != nil {
		return fmt.Errorf("failed to get %s config schema: %w", class, err)
	}

	schemaNode, err := c.schemaReader.GetNodeSchemaFromPath(schemaRoot, class.String(), key)
	if err != nil {
		return fmt.Errorf("failed to get node schema for path %s: %w", key, err)
	}

	node, err := config.GetNodeFromPath(cachedTemplate, key)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	configValue, err := anyToConfigValue(value, ts, errorPrefix, node.Def.Type)
	if err != nil {
		return err
	}

	_, err = config.UpdateConfigNodeValue(schemaNode, node, configValue, true)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	if err := c.writeTemplate(class, cachedTemplate); err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	return nil
}

func (c *ConfigCache) AddToTemplateList(ctx context.Context, class carbon.Classification, path, name string, ts uint64) error {
	mu := c.keyedMutex.Get(class.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to add %s template list key", class)
	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	parentNode, err := config.GetNodeFromPath(cachedTemplate, path)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	if err = config.AddToConfigNodeList(c.schemaReader, parentNode, class, path, name, ts); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, path, err)
	}

	if err := c.writeTemplate(class, cachedTemplate); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, path, err)
	}

	return nil
}

func (c *ConfigCache) RemoveFromTemplateList(ctx context.Context, class carbon.Classification, configPath string, ts uint64) error {
	mu := c.keyedMutex.Get(class.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to delete %s template list key", class)
	segments := strings.Split(configPath, "/")
	parentPath := path.Join(segments[:len(segments)-1]...)

	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	parentNode, err := config.GetNodeFromPath(cachedTemplate, parentPath)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, configPath, err)
	}

	if err = config.RemoveFromConfigNodeList(ctx, parentNode, configPath, errorPrefix, ts); err != nil {
		return err
	}

	if err := c.writeTemplate(class, cachedTemplate); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, configPath, err)
	}

	return nil
}

func (c *ConfigCache) writeTemplate(class carbon.Classification, cachedTemplate *config_service.ConfigNode) error {
	// N.B. this function assumes you have already write locked the associated cache entry
	s3Path := path.Join(class.String(), constants.ROBOT_CONFIG_TEMPLATE_FILENAME)
	templateBytes, err := config.MarshalConfigToJson(cachedTemplate)
	if err != nil {
		return fmt.Errorf("error marshalling template for %s: %v", class, err)
	}

	reader := strings.NewReader(templateBytes)
	if err = c.s3Facade.PutObject(c.s3Bucket, s3Path, reader); err != nil {
		return fmt.Errorf("error writing template to S3 for %s/%s: %v", c.s3Bucket, s3Path, err)
	}

	configCacheWritesToS3.Inc()
	c.cache.Store(class.String(), cachedTemplate)
	return nil
}

func (c *ConfigCache) RefreshTemplateCache(ctx context.Context, class carbon.Classification) error {
	mu := c.keyedMutex.Get(class.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to refresh %s config template cache", class)
	cachedTemplate, err := c.getTemplate(class)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	root, err := fetchTemplateFromS3(c.s3Facade, c.s3Bucket, class, c.schemaReader)
	if err != nil {
		log.Warnf("Failed to fetch %s template, caching nil template", class)
		c.cache.Store(class.String(), nil)
		return fmt.Errorf("failed to fetch %s template: %w", class, err)
	}

	// rename the root node to the class (default is it is named after the class)
	root.Name = class.String()
	cachedTemplate = root
	c.cache.Store(class.String(), cachedTemplate)
	return nil
}
