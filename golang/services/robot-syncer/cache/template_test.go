package cache

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/schema"
)

func TestGetTemplate(t *testing.T) {
	// mock out S3 GetObject call
	jsonData, err := os.ReadFile(ROOT_NODE_TEST_CONFIG)
	assert.NoError(t, err)

	readCLoser := io.NopCloser(bytes.NewReader(jsonData))

	// configure s3 facade
	mockS3 := new(mocks.S3Requester)
	mockS3.On("GetObject", mock.Anything, mock.Anything, mock.Anything).Return(&s3.GetObjectOutput{
		Body: readCLoser,
	}, nil)
	s3f := awslib.NewAWSS3Facade(mockS3)

	// fetch schema
	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)

	// Create cache
	cache := &ConfigCache{
		s3Bucket:     "carbon-robot-syncer-testing",
		s3Facade:     s3f,
		schemaReader: schemaReader,
	}
	// TODO:(smt) we should probably validate errors here
	_ = cache.initializeTemplates()
	//assert.NoError(t, err)
	assert.NotNil(t, cache)

	// Get template
	template, err := cache.getTemplate(carbon.ClassSimulators)
	assert.NoError(t, err)
	assert.NotNil(t, template)
}
