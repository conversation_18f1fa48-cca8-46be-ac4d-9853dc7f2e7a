package cache

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/service/s3"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"

	"github.com/carbonrobotics/cloud/golang/pkg/iterable"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

/**
 * UnsyncedListRemovals are used by Robot Syncer as part of the sync process.
 * Upon stream connection, a robot will call GetTree and reconcile all changes
 * made to the remote (cloud) config with its local config. If there are any
 * keys in the local config that have timestamps newer than the remote config,
 * the robot will call SetTree to update the remote config.
 *
 * This gets slightly more complicated when a key is removed from a list node
 * in the remote (cloud) config. There are two ways this can shake out:
 *
 * 1. The key removal from the remote config was the most recent change to the
 *    list node between the two configs. In this case, the robot config will
 *    be updated with the removal on its initial GetTree call.
 * 2. A change was made to the robot config after the key was removed from the
 *    remote config. In this case, the robot config will keep the removed key
 *    in its local config, assuming it was an addition made on the robot side
 *    while the robot was offline, and update the timestamp of the list node.
 *    The robot will call SetTree with the list node's key. Robot Syncer uses
 *    the unsyncedListRemovals cache to decide whether to remove the key from
 *    the remote config. If the key is in the cache, it will be removed from
 *    the remote config and, after updating the timestamp of the list node,
 *    RoSy notifies the robot of the change.
 *
 * UnsyncedListRemovals are maintained per robot classification, per serial. They live under the
 * the classification/<serial> key prefix (e.g. "slayer/slayer101/unsynced_config_list_removals.json").
 *
 * Intentionally, all exported function pass config.UnsyncedKey while all internal functions
 * use *writeSafeUnsyncedKeys. This is to ensure that the caller can modify the UnsyncedKey
 * contents without acquiring the lock. To achieve this we must always deepcopy UnsyncedKey
 * contents before returning it to the caller.
 **/

func (c *ConfigCache) initializeUnsyncedListRemovalsCache(ctx context.Context, serials []*carbon.ValidSerial) {
	err := iterable.Concurrently(ctx, serials, 100, func(serial *carbon.ValidSerial) error {
		c.getUnsyncedListRemovals(serial)
		return nil
	})
	if err != nil {
		log.WithError(err).Error("errors initializing unsynced list removals cache")
	}
}

func (c *ConfigCache) writeUnsyncedListRemovals(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) error {
	c.unsyncedListRemovals.Store(serial.String(), unsyncedKeys)
	return c.writeUnsyncedListRemovalsToS3(serial, unsyncedKeys)
}

func (c *ConfigCache) writeUnsyncedListRemovalsToS3(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) error {
	configCacheWritesToS3.Inc()
	writesToS3.WithLabelValues(serial.String()).Inc()
	jsonData, err := json.Marshal(unsyncedKeys)
	if err != nil {
		return fmt.Errorf("failed to marshal unsyncedListRemovals to json: %w", err)
	}
	data := bytes.NewReader(jsonData)

	key := path.Join(serial.Class().String(), serial.String(), constants.UNSYNCED_CONFIG_LIST_REMOVALS_FILENAME)
	err = c.s3Facade.PutObject(c.s3Bucket, key, data)
	if err != nil {
		return fmt.Errorf("failed to write %s unsyncedListRemovals to S3: %w", serial, err)
	}

	return nil
}

func (c *ConfigCache) fetchUnsyncedListRemovalsFromS3(s3f S3, s3Bucket string, serial *carbon.ValidSerial) ([]config.UnsyncedKey, error) {
	log.Debugf("Loading unsynced list removals for %s", serial)
	configCacheReadsFromS3.Inc()
	readsFromS3.WithLabelValues(serial.String()).Inc()
	robotConfigFilename := path.Join(serial.Class().String(), serial.String(), constants.UNSYNCED_CONFIG_LIST_REMOVALS_FILENAME)
	buf := &bytes.Buffer{}
	err := s3f.GetObject(s3Bucket, robotConfigFilename, buf)
	if err != nil {
		return nil, err
	}

	var unsyncedKeys []config.UnsyncedKey
	err = json.Unmarshal(buf.Bytes(), &unsyncedKeys)
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			if aerr.Code() == s3.ErrCodeNoSuchKey {
				return []config.UnsyncedKey{}, nil
			}
		}

		return nil, fmt.Errorf("failed to unmarshal unsynced list removals from json: %w", err)
	}

	return unsyncedKeys, nil
}

func (c *ConfigCache) AddUnsyncedListRemovals(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	cachedUnsyncedKeys := c.getUnsyncedListRemovals(serial)

	for _, entry := range unsyncedKeys {
		foundKey := false
		for _, key := range cachedUnsyncedKeys {
			// if the key already exists, update the timestamp
			if key.Key == entry.Key && key.Ts < entry.Ts {
				key = entry
				foundKey = true
				break
			}
		}
		if !foundKey {
			cachedUnsyncedKeys = append(cachedUnsyncedKeys, entry)
		}
	}

	c.unsyncedListRemovals.Store(serial.String(), cachedUnsyncedKeys)
	err := c.writeUnsyncedListRemovalsToS3(serial, cachedUnsyncedKeys)
	if err != nil {
		log.WithError(err).Errorf("Failed to write unsynced keys for %s to S3", serial)
		return
	}
}

/**
 * getUnsyncedListRemovals returns the unsynced list removals for the given serial.
 * This function handles writeSafeCacheItem creation and locking. It creates an empty
 * cache entry and locks it immediately to ensure that no other goroutines can
 * concurrently store the same cache item. If the unsynced keys are not in the cache,
 * it attempts to load them from S3. If the unsynced keys are found in the cache, the
 * newly created cache entry is discarded and the existing cache entry is
 * returned.
 **/
func (c *ConfigCache) getUnsyncedListRemovals(serial *carbon.ValidSerial) []config.UnsyncedKey {
	emptyCacheItem := make([]config.UnsyncedKey, 0)

	cacheEntryRaw, exists := c.unsyncedListRemovals.LoadOrStore(serial.String(), emptyCacheItem)
	if cacheEntryRaw == nil {
		c.unsyncedKeys.Delete(serial.String())
		return emptyCacheItem
	}

	unsyncedKeys, ok := cacheEntryRaw.([]config.UnsyncedKey)
	if !ok {
		unsyncedKeys = emptyCacheItem
	}

	if !exists {
		contents, err := c.fetchUnsyncedListRemovalsFromS3(c.s3Facade, c.s3Bucket, serial)
		if err != nil {
			log.WithError(err).Warnf("Failed to load unsynced list removals for %s, setting to []", serial)
			unsyncedKeys = emptyCacheItem
			c.writeUnsyncedListRemovalsToS3(serial, unsyncedKeys)
		} else {
			unsyncedKeys = contents
		}
		c.unsyncedListRemovals.Store(serial.String(), unsyncedKeys)
		return unsyncedKeys
	}

	return unsyncedKeys
}

func (c *ConfigCache) GetUnsyncedListRemovals(serial *carbon.ValidSerial) []config.UnsyncedKey {
	mu := c.keyedMutex.Get(serial.String())
	mu.RLock()
	defer mu.RUnlock()

	unsyncedKeys := c.getUnsyncedListRemovals(serial)

	unsyncedKeysCopy := make([]config.UnsyncedKey, len(unsyncedKeys))
	copy(unsyncedKeysCopy, unsyncedKeys)
	return unsyncedKeysCopy
}

func (c *ConfigCache) ReconcileUnsyncedListRemovals(serial *carbon.ValidSerial, absolutPath string) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	// removes any unsynced list removals that are prefixed by the given path
	unsyncedKeys := c.getUnsyncedListRemovals(serial)
	remaining := make([]config.UnsyncedKey, 0)
	for _, key := range unsyncedKeys {
		if time.Now().After(time.UnixMilli(int64(key.Ts)).Add(constants.UNSYNCED_LIST_REMOVALS_EXPIRATION)) {
			continue
		}

		if !strings.HasPrefix(key.Key, absolutPath) {
			remaining = append(remaining, key)
		}
	}
	return c.writeUnsyncedListRemovals(serial, remaining)
}

func (c *ConfigCache) refreshUnsyncedListRemovalsCache(serial *carbon.ValidSerial) error {
	// ensure writes can't occur while we are refreshing
	cacheEntry := c.getUnsyncedListRemovals(serial)

	unsyncedKeys, err := c.fetchUnsyncedListRemovalsFromS3(c.s3Facade, c.s3Bucket, serial)
	if err != nil {
		return err
	}

	cacheEntry = unsyncedKeys
	c.unsyncedListRemovals.Store(serial.String(), cacheEntry)
	return nil
}
