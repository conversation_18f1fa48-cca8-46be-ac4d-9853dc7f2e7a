package cache

import (
	"bytes"
	"encoding/json"
	"io"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

func TestReconcileUnsyncedListRemovals(t *testing.T) {
	tests := []struct {
		name              string
		unsycnedKeys      []config.UnsyncedKey
		key               string
		expectedRemaining int
	}{
		{
			name:              "no unsynced keys",
			unsycnedKeys:      []config.UnsyncedKey{},
			key:               "simulators/devserver0",
			expectedRemaining: 0,
		},
		{
			name: "exact match unsynced key",
			unsycnedKeys: []config.UnsyncedKey{
				{
					Key: "simulators/devserver0",
					Ts:  uint64(time.Now().UnixMilli()),
				},
			},
			key:               "simulators/devserver0",
			expectedRemaining: 0,
		},
		{
			name: "unsynced key with prefix",
			unsycnedKeys: []config.UnsyncedKey{
				{
					Key: "simulators/devserver0/asdf/1234",
					Ts:  uint64(time.Now().UnixMilli()),
				},
				{
					Key: "simulators/devserver0/asdf/5678",
					Ts:  uint64(time.Now().UnixMilli()),
				},
			},
			key:               "simulators/devserver0",
			expectedRemaining: 0,
		},
		{
			name: "unsynced key with different prefix",
			unsycnedKeys: []config.UnsyncedKey{
				{
					Key: "simulators/devserver0/asdf/1234",
					Ts:  uint64(time.Now().UnixMilli()),
				},
				{
					Key: "simulators/devserver0/asdf/5678",
					Ts:  uint64(time.Now().UnixMilli()),
				},
			},
			key:               "simulators/devserver0/notmatching",
			expectedRemaining: 2,
		},
		{
			name: "some unsynced key with different prefix",
			unsycnedKeys: []config.UnsyncedKey{
				{
					Key: "simulators/devserver0/asdf/1234",
					Ts:  uint64(time.Now().UnixMilli()),
				},
				{
					Key: "simulators/devserver0/notmatching",
					Ts:  uint64(time.Now().UnixMilli()),
				},
				{
					Key: "simulators/devserver0/asdf/5678",
					Ts:  uint64(time.Now().UnixMilli()),
				},
			},
			key:               "simulators/devserver0/asdf",
			expectedRemaining: 1,
		},
		{
			name: "remove unsynced keys before expiration",
			unsycnedKeys: []config.UnsyncedKey{
				{
					Key: "simulators/devserver0/unrelated/expired",
					Ts:  uint64(time.Now().UnixMilli()) - uint64(constants.UNSYNCED_LIST_REMOVALS_EXPIRATION.Milliseconds()) - 1000,
				},
				{
					Key: "simulators/devserver0/notmatching",
					Ts:  uint64(time.Now().UnixMilli()),
				},
				{
					Key: "simulators/devserver0/asdf/5678",
					Ts:  uint64(time.Now().UnixMilli()),
				},
			},
			key:               "simulators/devserver0/asdf",
			expectedRemaining: 1,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			serial, err := carbon.ParseSerialWithClass("devserver0", "simulators")
			assert.NoError(t, err)
			// configure s3 facade
			mockS3 := new(mocks.S3Requester)
			mockS3.On("GetObject", mock.AnythingOfType("*s3.GetObjectInput")).Run(func(args mock.Arguments) {
				input := args.Get(0).(*s3.GetObjectInput)
				assert.Equal(t, "test-bucket", *input.Bucket)
				assert.Equal(t, "simulators/devserver0/unsynced_config_list_removals.json", *input.Key)
			}).Return(&s3.GetObjectOutput{
				Body: io.NopCloser(bytes.NewBuffer(func() []byte {
					data, _ := json.Marshal(test.unsycnedKeys)
					return data
				}())),
			}, nil)
			mockS3.On("PutObject", mock.AnythingOfType("*s3.PutObjectInput")).Return(&s3.PutObjectOutput{}, nil)
			s3f := awslib.NewAWSS3Facade(mockS3)

			// configure cache and store unsynced keys
			cache := &ConfigCache{
				keyedMutex: NewKeyedMutex(),
				s3Facade:   s3f,
				s3Bucket:   "test-bucket",
			}

			// store unsynced list removals
			unsyncedKeys := make([]config.UnsyncedKey, 0)
			unsyncedKeys = test.unsycnedKeys
			cache.unsyncedListRemovals.Store(serial.String(), unsyncedKeys)

			// reconcile unsynced list removals
			cache.ReconcileUnsyncedListRemovals(serial, test.key)
			keys, ok := cache.unsyncedListRemovals.Load(serial.String())
			assert.True(t, ok)
			assert.Len(t, keys.([]config.UnsyncedKey), test.expectedRemaining)
		})
	}
}
