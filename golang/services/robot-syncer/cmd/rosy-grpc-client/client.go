package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/alecthomas/kingpin/v2"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/credentials/oauth"
	"google.golang.org/grpc/metadata"
)

// GRPC Test client

var (
	app        = kingpin.New("grpc_client", "A test grpc client for robot-syncer")
	addr       = app.Flag("addr", "Target Address").Default("0.0.0.0:50051").String()
	insec      = app.Flag("insecure", "Allow insecure connections to the server").Bool()
	token      = app.Flag("token", "Token").String()
	ctxTimeout = app.Flag("timeout", "Context Timeout").Default("10s").Duration()

	//	Ping(context.Context, *PingRequest) (*PongResponse, error)
	pingCmd  = app.Command("ping", "send ping to config service")
	pingCmdX = pingCmd.Flag("x", "ping value").Int32()

	//	SetValue(context.Context, *SetValueRequest) (*SetValueResponse, error)
	setValueCmd   = app.Command("set-value", "SetValue")
	setValueKey   = setValueCmd.Flag("key", "config key").String()
	setValueValue = setValueCmd.Flag("value", "proto json see related config_client_test.go").String()

	//	GetTree(context.Context, *GetTreeRequest) (*GetTreeResponse, error)
	getTreeCmd = app.Command("get-tree", "GetTree")
	getTreeKey = getTreeCmd.Flag("key", "config key").String()

	//	SetTree(context.Context, *SetTreeRequest) (*SetTreeResponse, error)
	setTreeCmd  = app.Command("set-tree", "SetTree")
	setTreeKey  = setTreeCmd.Flag("key", "config key").String()
	setTreeNode = setTreeCmd.Flag("node", "proto json see related config_client_test.go").String()

	//	GetLeaves(context.Context, *GetLeavesRequest) (*GetLeavesResponse, error)
	getLeavesCmd = app.Command("get-leaves", "GetLeaves")
	getLeavesKey = getLeavesCmd.Flag("key", "config key").String()

	//	AddToList(context.Context, *AddToListRequest) (*AddToListResponse, error)
	addToListCmd  = app.Command("add-to-list", "AddToList")
	addToListKey  = addToListCmd.Flag("key", "config key").String()
	addToListName = addToListCmd.Flag("name", "name of new list item").String()

	//	RemoveFromList(context.Context, *RemoveFromListRequest) (*RemoveFromListResponse, error)
	removeFromListCmd = app.Command("remove-from-list", "RemoveFromList")
	removeToListKey   = removeFromListCmd.Flag("key", "config key").String()
	removeToListName  = removeFromListCmd.Flag("name", "name of list item to remove").String()

	//	UpgradeCloudConfig(context.Context, *UpgradeCloudConfigRequest) (*UpgradeCloudConfigResponse, error)
	upgradeCloudConfigCmd          = app.Command("upgrade-cloud-config", "UpgradeCloudConfig")
	upgradeCloudConfigRobot        = upgradeCloudConfigCmd.Flag("robot", "").String()
	upgradeCloudConfigToTemplate   = upgradeCloudConfigCmd.Flag("target-template", "").String()
	upgradeCloudConfigFromTemplate = upgradeCloudConfigCmd.Flag("source-template", "").String()
)

func main() {
	appCtx, cancel := context.WithCancel(context.Background())
	defer cancel()

	parsedCmd := kingpin.MustParse(app.Parse(os.Args[1:]))
	opts := make([]grpc.DialOption, 0)
	if *insec {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if *token != "" {
			appCtx = metadata.AppendToOutgoingContext(appCtx, "authorization", fmt.Sprintf("Bearer %s", *token))
		}
	} else {
		opts = append(opts, grpc.WithTransportCredentials(credentials.NewClientTLSFromCert(nil, "")))
		if *token != "" {
			opts = append(opts, grpc.WithPerRPCCredentials(oauth.TokenSource{TokenSource: oauth.TokenSource{TokenSource: oauth2.StaticTokenSource(&oauth2.Token{AccessToken: *token})}}))
		}
	}

	conn, err := grpc.NewClient(*addr, opts...)
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	defer conn.Close()

	configClient := NewConfigClient(conn)

	ctx, cancel := context.WithTimeout(appCtx, *ctxTimeout)
	defer cancel()
	var result string
	switch parsedCmd {
	case pingCmd.FullCommand():
		var xResp int32
		xResp, err = configClient.Ping(ctx, *pingCmdX)
		result = fmt.Sprintf("X: %d", xResp)
	case setValueCmd.FullCommand():
		err = configClient.SetValue(ctx, *setValueKey, *setValueValue)
	case getTreeCmd.FullCommand():
		var data string
		data, err = configClient.GetTree(ctx, *getTreeKey)
		result = data
	case setTreeCmd.FullCommand():
		err = configClient.SetTree(ctx, *setTreeKey, *setTreeNode)
	case getLeavesCmd.FullCommand():
		result, err = configClient.GetLeaves(ctx, *getLeavesKey)
	case addToListCmd.FullCommand():
		err = configClient.AddToList(ctx, *addToListKey, *addToListName)
	case removeFromListCmd.FullCommand():
		err = configClient.RemoveFromList(ctx, *removeToListKey, *removeToListName)
	case upgradeCloudConfigCmd.FullCommand():
		err = configClient.UpgradeCloudConfig(ctx, *upgradeCloudConfigRobot, *upgradeCloudConfigFromTemplate, *upgradeCloudConfigToTemplate)
	}
	if err != nil {
		log.Fatal(err)
	}
	if len(result) > 0 {
		// resulting output
		fmt.Println(result)
	}
}
