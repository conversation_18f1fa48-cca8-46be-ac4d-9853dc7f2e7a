package main

import (
	"context"
	"fmt"
	"time"

	pb "github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
)

type ConfigClient struct {
	client pb.ConfigServiceClient
}

func NewConfigClient(conn *grpc.ClientConn) *ConfigClient {
	return &ConfigClient{client: pb.NewConfigServiceClient(conn)}
}

func (c *ConfigClient) Ping(ctx context.Context, x int32) (int32, error) {
	res := &pb.PingRequest{X: x}
	resp, err := c.client.Ping(ctx, res)
	if err != nil {
		return 0, err
	}
	return resp.X, nil
}

func (c *ConfigClient) SetValue(ctx context.Context, key, value string) error {
	val := &pb.ConfigValue{}
	if err := protojson.Unmarshal([]byte(value), val); err != nil {
		return fmt.Errorf("failed to unmarshal config value: %s - error: %w", value, err)
	}
	val.TimestampMs = uint64(time.Now().UnixMilli())
	req := &pb.SetValueRequest{
		Key:   key,
		Value: val,
	}
	_, err := c.client.SetValue(ctx, req)
	return err
}

func (c *ConfigClient) GetTree(ctx context.Context, key string) (string, error) {
	req := &pb.GetTreeRequest{Key: key}
	resp, err := c.client.GetTree(ctx, req)
	if err != nil {
		return "", err
	}
	return resp.String(), nil
}

func (c *ConfigClient) SetTree(ctx context.Context, key, node string) error {
	configNode := &pb.ConfigNode{}
	if err := protojson.Unmarshal([]byte(node), configNode); err != nil {
		return fmt.Errorf("failed to unmarshal config value: %s - error: %w", node, err)
	}
	req := &pb.SetTreeRequest{Key: key, Node: configNode}
	_, err := c.client.SetTree(ctx, req)
	return err
}

func (c *ConfigClient) GetLeaves(ctx context.Context, key string) (string, error) {
	req := &pb.GetLeavesRequest{Key: key}
	resp, err := c.client.GetLeaves(ctx, req)
	if err != nil {
		return "", err
	}
	return resp.String(), err
}

func (c *ConfigClient) AddToList(ctx context.Context, key, name string) error {
	req := &pb.AddToListRequest{Key: key, Name: name}
	_, err := c.client.AddToList(ctx, req)
	return err
}

func (c *ConfigClient) RemoveFromList(ctx context.Context, key, name string) error {
	req := &pb.RemoveFromListRequest{Key: key, Name: name}
	_, err := c.client.RemoveFromList(ctx, req)
	return err
}

func (c *ConfigClient) UpgradeCloudConfig(ctx context.Context, robot, sourceTemplate, targetTemplate string) error {
	req := &pb.UpgradeCloudConfigRequest{Robot: robot, ToTemplate: targetTemplate, FromTemplate: sourceTemplate}
	_, err := c.client.UpgradeCloudConfig(ctx, req)
	return err
}
