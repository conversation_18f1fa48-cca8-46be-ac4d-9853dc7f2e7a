package main

import (
	"fmt"
	"testing"
	"time"

	pb "github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"google.golang.org/protobuf/encoding/protojson"
)

func TestConfigValueOutputSample(t *testing.T) {
	t.Skip("for dev help purposes")
	nowTimestampMs := uint64(time.Now().UnixMilli())
	fmt.Println("Example Config Values for ")

	int64Value := &pb.ConfigValue{Value: &pb.ConfigValue_Int64Val{Int64Val: 123}, TimestampMs: nowTimestampMs}
	b, _ := protojson.Marshal(int64Value)
	fmt.Println("int64:", string(b))
	uint64Value := &pb.ConfigValue{Value: &pb.ConfigValue_Uint64Val{Uint64Val: 123}, TimestampMs: nowTimestampMs}
	b, _ = protojson.Marshal(uint64Value)
	fmt.Println("uint64:", string(b))
	boolValue := &pb.ConfigValue{Value: &pb.ConfigValue_BoolVal{BoolVal: true}, TimestampMs: nowTimestampMs}
	b, _ = protojson.Marshal(boolValue)
	fmt.Println("bool:", string(b))
	floatValue := &pb.ConfigValue{Value: &pb.ConfigValue_FloatVal{FloatVal: 123.1}, TimestampMs: nowTimestampMs}
	b, _ = protojson.Marshal(floatValue)
	fmt.Println("float:", string(b))
	stringValue := &pb.ConfigValue{Value: &pb.ConfigValue_StringVal{StringVal: "one-two"}, TimestampMs: nowTimestampMs}
	b, _ = protojson.Marshal(stringValue)
	fmt.Println("string:", string(b))
}

func TestConfigNodeOutputSample(t *testing.T) {
	t.Skip("for dev help purposes")
	nowTimestampMs := uint64(time.Now().UnixMilli())
	fmt.Println("Example Config Nodes for ")

	single := &pb.ConfigNode{
		Name: "node-name",
		Value: &pb.ConfigValue{
			Value:       &pb.ConfigValue_StringVal{StringVal: "string-value"},
			TimestampMs: nowTimestampMs,
		},
		Def: &pb.ConfigDef{
			Type:       pb.ConfigType_NODE,
			Complexity: pb.ConfigComplexity_USER,
			Extra: &pb.ConfigDef_StringDef{StringDef: &pb.StringConfigDef{
				SizeLimit: 1024,
				Choices:   []string{"one", "two", "three"},
			}},
			Hint:               "this is a string hint test",
			DefaultRecommended: true,
			DefaultValue: &pb.ConfigValue{
				Value:       &pb.ConfigValue_StringVal{StringVal: "one"},
				TimestampMs: nowTimestampMs,
			},
			Units: "s",
		},
		Children: nil,
	}
	b, _ := protojson.Marshal(single)
	fmt.Println("single string node", string(b))
}
