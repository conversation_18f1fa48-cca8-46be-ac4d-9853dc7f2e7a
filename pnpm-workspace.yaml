packages:
  - "golang/services/portal/ui"
  - "ui/common"
  - "ui/rtc"

onlyBuiltDependencies:
  - "@sentry/cli"
  - esbuild
  - protobufjs

publicHoistPattern:
  # protos doesn't have its own node_modules directory, so typescript's module
  # resolution logic escapes into the cloud/ root.  when we were using yarn
  # workspaces, we lucked out in that yarn decided to hoist this package into
  # cloud/node_modules/ of its own volition, but pnpm requires us to explicitly
  # hoist it into the place where typescript expects to find it.
  - "@bufbuild/protobuf"

fetchTimeout: 600000

catalog:
  "@jest/globals": ^29.5.0
  "@emotion/react": ^11.11.4
  "@mui/icons-material": ^5.16.6
  "@mui/material": ^5.16.6
  "@mui/system": ^5.16.6
  "@mui/x-license-pro": ^6.10.2
  "@reduxjs/toolkit": ^2.4.0
  "@turf/bearing": ^6.5.0
  "@turf/buffer": ^6.5.0
  "@turf/distance": ^6.5.0
  "@turf/helpers": ^6.5.0
  "@turf/invariant": ^6.5.0
  "@turf/destination": ^7.2.0
  "@types/color": ^3.0.3
  "@types/geojson": ^7946.0.13
  "@types/luxon": ^3.6.2
  "@types/mapbox-gl": ^3.4.1
  "@types/react": ^18.2.37
  "@types/react-dom": ^18.2.4
  "@typescript-eslint/eslint-plugin": ^7.0.1
  "@typescript-eslint/parser": ^7.14.0
  clsx: ^1.2.1
  color: ^4.2.3
  eslint: ^8.56.0
  eslint-config-prettier: ^8.8.0
  eslint-plugin-i18n-json: ^4.0.0
  eslint-plugin-i18next: ^6.0.3
  eslint-plugin-import: ^2.27.5
  eslint-plugin-jest: ^27.7.0
  eslint-plugin-jsx-a11y: ^6.7.1
  eslint-plugin-prettier: ^4.2.1
  eslint-plugin-react: ^7.32.2
  eslint-plugin-react-hooks: ^4.6.0
  eslint-plugin-sonarjs: ^0.19.0
  eslint-plugin-sort-imports-es6-autofix: ^0.6.0
  eslint-plugin-unicorn: ^47.0.0
  eslint-plugin-unused-imports: ^3.2.0
  i18next: ^23.5.1
  luxon: ^3.4.3
  mapbox-gl: ^3.3.0
  react: ^18.2.0
  react-dom: ^18.2.0
  react-map-gl: ^7.1.7
  react-redux: ^8.0.5
  react-router: ^6.18.0
  react-router-dom: ^6.18.0
  reconnecting-websocket: ^4.4.0
  prettier: 2.8.8
  tailwind-merge: ^2.1.0
  tailwindcss: ^3.3.2
  yup: ^1.3.2
