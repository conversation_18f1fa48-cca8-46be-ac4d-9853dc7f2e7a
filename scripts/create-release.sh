#!/usr/bin/env bash

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[1;34m'
NC='\033[0m' # No Color

###############################################################################
# Terminology:
#
# App ID         - The identifier for a specific application in the codebase
#                 Example: rosy, portal
#
# Release Branch - A branch created for a specific major.minor release series
#                 Example: release/rosy-v1.2
#
# Release Series - A collection of releases sharing the same major.minor version
#                 Example: rosy-v1.2
#
# Release        - A specific versioned release with a complete version number
#                 Example: rosy-v1.2.3
#
# Tag            - A git tag marking a specific release point in the codebase
#                 Example: rosy-v1.2.3
#
# Version        - The semantic version number without the app prefix
#                 Example: v1.2.3
###############################################################################

# Print usage information
print_usage() {
    cat <<EOF
Usage: $0 [OPTIONS] <app_id> [release_series]

Create a new release for the specified app. If no release series is provided, the script will increment the
minor version of the latest release for the specified app id. When no release branch is found matching the
major.minor release versions, the script will create a new release branch from master.

Options:
  -h, --help    Show this help message

Arguments:
  app_id          The target app ID (e.g., portal, rosy)
  release_series  The release series to base the new release off (e.g. portal-v1.34, optional)

Examples:
For all examples, assume the latest rosy release is rosy-v0.2.1.

 - Create a new rosy release with the next minor version: (rosy-v0.3.0)
  $0 rosy

 - Create the next patch release from an existing release branch: (rosy-v0.2.2)
  $0 rosy rosy-v0.2
EOF
}

# Check for help flag
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    print_usage
    exit 0
fi

die() {
    printf >&2 "${RED}fatal:${NC} %s\n" "$*"
    exit 1
}

APP_ID=$1
RELEASE_SERIES=$2

tmpdir=
cleanup() {
    if [ -n "${tmpdir}" ]; then
        rm -r "${tmpdir}"
    fi
}
trap cleanup EXIT
tmpdir="$(mktemp -d)"

###############################################################################
# Pre-Script Checks
###############################################################################

# make sure gh is installed
if ! command -v gh &> /dev/null; then
     echo -e "${RED}gh not found. The GitHub CLI is required.${NC}"
    echo "https://cli.github.com/"
    exit 1
fi

# make sure we can auth
if [ -z "$GITHUB_TOKEN" ]; then
     echo -e "${RED}no GITHUB_TOKEN found in environment${NC}"
    exit 1
fi

if [ -z "$APP_ID" ]; then
     echo -e "${RED}no APP_ID specified${NC}"
    exit 1
fi

# cd to the repo root
cd "$(dirname "$0")"
REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT"
git fetch origin --tags

# validate release series format <app_id>-vX.Y
if [ -n "$RELEASE_SERIES" ] && [[ ! "$RELEASE_SERIES" =~ ^${APP_ID}-v[0-9]+(\.[0-9]+)?$ ]]; then
    echo -e "${RED}Error:${NC} Release series must be in format ${APP_ID}-vX or ${APP_ID}-vX.Y"
    exit 1
fi

###############################################################################
# Script Setup
###############################################################################


PREV_RELEASE="$(git tag | grep "${RELEASE_SERIES:-"${APP_ID}-v"}" | sort -V | tail -1)"
PREV_VERSION="${PREV_RELEASE#"${APP_ID}-v"}"
PREV_MAJOR="${PREV_VERSION%%.*}"
PREV_MINOR="${PREV_VERSION#*.}"; PREV_MINOR="${PREV_MINOR%.*}"
PREV_PATCH="${PREV_VERSION##*.}"

if [ -z "${RELEASE_SERIES}" ]; then
    if [ -z "${PREV_VERSION}" ]; then
         echo -e "${YELLOW}No existing releases found for $APP_ID${NC}"
         read -r -p "$(echo -e "${YELLOW}What release version would you like to create? (e.g. ${APP_ID}-v0.1)? ${NC}")" NEXT_RELEASE
        if [[ ! "$NEXT_RELEASE" =~ ^${APP_ID}-v[0-9]+(\.[0-9]+)?$ ]]; then
            echo -e "${RED}Error:${NC} release version must be in format ${APP_ID}-vX.Y"
            exit 1
        fi
    else
        NEXT_RELEASE="${APP_ID}-v${PREV_MAJOR}.$((PREV_MINOR + 1)).0"
         echo -e "${GREEN}Bumping minor version:${NC} ${PREV_RELEASE} -> ${NEXT_RELEASE}"
    fi
else
    if [ -z "${PREV_VERSION}" ]; then
        NEXT_RELEASE="${RELEASE_SERIES}.0"
        echo -e "${GREEN}Creating first release in ${RELEASE_SERIES} series:${NC} ${NEXT_RELEASE}"
    else
        NEXT_RELEASE="${APP_ID}-v${PREV_MAJOR}.${PREV_MINOR}.$((PREV_PATCH + 1))"
        echo -e "${GREEN}Bumping patch version:${NC} ${PREV_RELEASE} -> ${NEXT_RELEASE}"
    fi
fi

# Check if tag already exists
if git rev-parse --verify "$NEXT_RELEASE" >/dev/null 2>&1; then
     echo -e "${RED}Tag $NEXT_RELEASE already exists${NC}" >&2
    exit 1
fi

echo
echo -e "${GREEN}Previous release version:       ${NC} $PREV_RELEASE"
echo -e "${GREEN}Proceeding with release version:${NC} $NEXT_RELEASE"
echo

###############################################################################
# Validate or Create Release Branch
###############################################################################

VERSION="${NEXT_RELEASE#"${APP_ID}"-v}"
MAJOR_MINOR="${VERSION%.*}"
BRANCH_NAME="release/${APP_ID}-v${MAJOR_MINOR}"
MATCHING_BRANCH=$(git show-ref refs/remotes/origin/"${BRANCH_NAME}" || true)

# Check if a branch exists matching the pattern release/APP_ID-vMAJOR.MINOR
if [ -z "$MATCHING_BRANCH" ]; then
    echo -e "${YELLOW}No release branch found: $BRANCH_NAME${NC}"
    read -r -p "$(echo -e "${YELLOW}Create release branch off of master? (y/N) ${NC}")" response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Release process aborted.${NC}"
        exit 1
    fi

    # Create new branch from origin/master
    echo -e "${GREEN}Creating release branch:${NC} $BRANCH_NAME"
    git branch "$BRANCH_NAME" origin/master

    # Push the new branch to origin
    echo -e "${GREEN}Pushing release branch to origin...${NC}"
    git push -u origin "$BRANCH_NAME"
else
    echo -e "${GREEN}Found existing release branch:${NC} ${BRANCH_NAME}"
fi

read -r -p "$(echo -e "${YELLOW}Proceed with creating release $NEXT_RELEASE off $BRANCH_NAME? (y/N) ${NC}")" response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo -e "${RED}Release process aborted.${NC}"
    exit 1
fi

###############################################################################
# Cut Tag, Create Release, and Generate Release Notes
###############################################################################

echo -e "${GREEN}Creating tag ${NEXT_RELEASE} off ${BRANCH_NAME}${NC}"
git tag "$NEXT_RELEASE" "origin/$BRANCH_NAME"
git push origin "$NEXT_RELEASE"

# Create a release
REPO_PATH="carbonrobotics/cloud"

# Check if release already exists
if gh release view "$NEXT_RELEASE" >/dev/null 2>&1; then
    echo -e "${YELLOW}Release ${NEXT_RELEASE} already exists. Nothing to do.${NC}"
else
    echo -e "${GREEN}Creating release ${NEXT_RELEASE}${NC}"

    # best effort to get app specific folders; sets empty array if app id not supported
    app_specific_folders=()
    case "$APP_ID" in
        imgsvc-lambda)
            app_specific_folders=(
                "golang/go.*"
                "golang/pkg"
                "golang/lambda/image_service"
            )
            ;;
        portal)
            app_specific_folders=(
                "golang/go.*"
                "golang/pkg"
                "golang/services/portal"
                "ui/common"
            )
            ;;
        rosy)
            app_specific_folders=(
                "golang/go.*"
                "golang/pkg"
                "golang/services/robot-syncer"
            )
            ;;
        rtc-ui)
            app_specific_folders=(
                "ui/common"
                "ui/rtc"
            )
            ;;
    esac

    relnotes_file="${tmpdir}/relnotes"
    cat <<EOF >"${relnotes_file}"
## Changes since ${PREV_RELEASE}

$(git log "${PREV_RELEASE}"..."${NEXT_RELEASE}" --pretty=format:"- %s ([%h](https://github.com/${REPO_PATH}/commit/%H)) by %aN" -- "${app_specific_folders[@]}")

Full Changelog: [${PREV_RELEASE}...${NEXT_RELEASE}](https://github.com/${REPO_PATH}/compare/${PREV_RELEASE}...${NEXT_RELEASE})
EOF

    if ! ${RELNOTES_EDITOR:-${EDITOR:-vi}} "${relnotes_file}"; then
        die "editor exited with failure while editing release notes"
    fi
    gh release create "$NEXT_RELEASE" --target "$BRANCH_NAME" --verify-tag --title "${NEXT_RELEASE}" --notes-file "${relnotes_file}"
fi
