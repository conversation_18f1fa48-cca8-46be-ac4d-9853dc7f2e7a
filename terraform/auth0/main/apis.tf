resource "auth0_resource_server" "auth0_management" {
  allow_offline_access = false
  identifier           = "https://${var.auth0_domain}/api/v2/"
  name                 = "Auth0 Management API"
}

resource "auth0_resource_server" "portal" {
  allow_offline_access = true
  enforce_policies     = true
  identifier           = var.portal.audience
  name                 = "Ops Center"
  token_dialect        = "access_token_authz"
}

resource "auth0_resource_server" "robot" {
  allow_offline_access = true
  enforce_policies     = false
  identifier           = var.robot.audience
  name                 = "Robot"
  token_dialect        = "access_token"
  token_lifetime       = 2592000
}

resource "auth0_resource_server" "robot_syncer" {
  allow_offline_access = false
  enforce_policies     = false
  identifier           = var.rosy.audience
  name                 = "Robot Syncer"
  token_dialect        = "access_token"
}

resource "auth0_resource_server" "veselka" {
  enforce_policies = true
  identifier       = var.veselka.audience
  name             = "Veselka"
  token_dialect    = "access_token_authz"
}

resource "auth0_resource_server" "datacenter" {
  identifier = var.datacenter.audience
  name       = "Datacenter"
}

resource "auth0_resource_server" "machine_learning" {
  identifier = var.ml.audience
  name       = "Machine Learning"
}
