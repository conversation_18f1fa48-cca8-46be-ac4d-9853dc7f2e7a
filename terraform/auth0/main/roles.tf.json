{"resource": {"auth0_resource_server_scopes": {"veselka": [{"resource_server_identifier": "${auth0_resource_server.veselka.identifier}", "scopes": [{"name": "veselka:admin"}, {"name": "veselka:review"}, {"name": "veselka:label"}]}], "portal": [{"resource_server_identifier": "${auth0_resource_server.portal.identifier}", "scopes": [{"name": "read:admin_alarms:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:admin_cloud:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:alarms_customer:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:alarms_customer:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:alarms_customer:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:alarms_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:almanacs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:almanacs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:almanacs:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:almanacs:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:autotractor_drive:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:autotractor_drive:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:autotractor_jobs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:autotractor_jobs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:banding_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:calibration:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:calibration:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:calibration:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:cameras:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:cameras:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:cameras:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:captcha:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:captcha:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:captcha:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:capture:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:capture:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:capture:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:chat:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:chat:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:chat:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:configs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:configs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:configs:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:crops_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:customers:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:customers:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:diagnostics:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:diagnostics:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:diagnostics:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:discriminators:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:discriminators:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:discriminators:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:farms:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:farms:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:feeds:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:feeds:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:feeds:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:guides:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:hardware:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:hardware:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:images:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:images:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:jobs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:jobs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:jobs:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:jobs_select:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:jobs_select:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_disable:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_disable:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_enable:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_row:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_row:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:lasers_row:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics_customer:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics_customer:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:metrics_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:mode:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:mode:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:mode:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_nickname:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_nickname:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_nickname:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_pinned:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_pinned:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:models_pinned:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_map:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_map:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_map:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_settings:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_settings:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:operator_settings:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:plant_category_profiles:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:plant_category_profiles:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:plant_category_profiles:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:portal_globals:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:portal_settings:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:profiles:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:profiles:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:profiles:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:quick_tune:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:quick_tune:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:quick_tune:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:reports:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:reports:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robot_ui:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robots:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robots:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robots_assign:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robots_health:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:robots_status:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:shortcuts_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:software_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:software_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:software_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:software_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thinning_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thresholds:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thresholds:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:thresholds:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:uploads:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:users:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:users:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:users_invite:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:users_permissions:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:velocity_estimators:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:velocity_estimators:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:velocity_estimators:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:velocity_estimators:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:veselka:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "read:vizualization_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:admin_alarms:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:admin_cloud:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:alarms_customer:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:alarms_customer:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:alarms_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:almanacs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:almanacs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:almanacs:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:almanacs:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:autotractor_drive:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:autotractor_jobs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:autotractor_jobs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:banding_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:calibration:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:calibration:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:calibration:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:cameras:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:cameras:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:cameras:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:captcha:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:captcha:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:cap<PERSON><PERSON>:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:capture:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:capture:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:capture:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:capture:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:chat:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:configs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:configs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:configs:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:crops_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:customers:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:customers:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:diagnostics:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:diagnostics:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:diagnostics:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:diagnostics:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:discriminators:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:discriminators:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:discriminators:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:farms:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:farms:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:feeds:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:guides:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:hardware:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:hardware:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:images:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs_select:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs_select:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:jobs_select:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_disable:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_disable:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_disable:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_enable:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_row:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_row:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:lasers_row:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:metrics:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:metrics_customer:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:metrics_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:mode:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:mode:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:mode:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_nickname:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_nickname:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_nickname:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_pinned:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_pinned:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:models_pinned:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:operator_map:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:operator_settings:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:operator_settings:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:operator_settings:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:plant_category_profiles:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:plant_category_profiles:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:plant_category_profiles:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:portal_globals:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:portal_settings:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:profiles:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:profiles:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:profiles:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:quick_tune:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:quick_tune:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:quick_tune:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:reports:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:reports:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robot_ui:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robots:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robots_assign:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robots_health:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robots_status:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:robots_status:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:shortcuts_internal:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:software_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:software_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:software_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:software_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_advanced:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_advanced:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_basic:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thinning_basic:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:thresholds:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:uploads:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users:self", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users_invite:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users_invite:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users_permissions:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:users_permissions:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:velocity_estimators:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:velocity_estimators:customer", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:velocity_estimators:robot", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:velocity_estimators:templates", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:veselka:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:vizualization_advanced:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}, {"name": "update:vizualization_basic:all", "description": "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit"}]}]}, "auth0_role": {"carbon_basic": [{"description": "[carbon_basic] Full read and reporting access to the fleet via Ops Center and internal features on operator app", "name": "Carbon (Basic)"}], "carbon_tech": [{"description": "[carbon_tech] Full technical access to the fleet via Ops Center and internal features on operator app", "name": "Carbon (Technical)"}], "farm_manager": [{"description": "[farm_manager] Advanced Operator permissions plus reporting, planning, and fleet management via Ops Center", "name": "Farm Manager"}], "operator_advanced": [{"description": "[operator_advanced] Broader set of permissions for advanced Laserweeder operators with technical skills and crop/week knowledge", "name": "Operator (Advanced)"}], "operator_basic": [{"description": "[operator_basic] Minimal set of permissions required to operate a Laserweeder", "name": "Operator (Basic)"}], "veselka_admin": [{"description": "[veselka_admin] <PERSON><PERSON><PERSON><PERSON>min", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "veselka_labeler": [{"description": "[veselka_labeler] Label only", "name": "<PERSON><PERSON><PERSON><PERSON> Labeler"}], "veselka_reviewer": [{"description": "[veselka_reviewer] Label & Review access", "name": "Veselka Reviewer"}]}, "auth0_role_permissions": {"carbon_basic": [{"permissions": [{"name": "read:alarms_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:alarms_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:almanacs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:almanacs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_settings:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_settings:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:calibration:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:calibration:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:cameras:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cameras:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:capture:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:capture:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:chat:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:chat:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_pinned:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_pinned:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_nickname:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_nickname:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thresholds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:diagnostics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:diagnostics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:guides:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:guides:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:captcha:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cap<PERSON><PERSON>:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_row:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_row:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_disable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_disable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:feeds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:feeds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:hardware:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:hardware:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:quick_tune:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:quick_tune:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:velocity_estimators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:mode:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:mode:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs_select:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs_select:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:shortcuts_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:customers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:customers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_invite:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users_permissions:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_permissions:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:reports:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:reports:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_assign:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_status:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:veselka:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:discriminators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:discriminators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_map:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:farms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:farms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:images:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:autotractor_jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:plant_category_profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_globals:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_drive:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}], "role_id": "${auth0_role.carbon_basic.id}", "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "carbon_tech": [{"permissions": [{"name": "read:alarms_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:alarms_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:alarms_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:alarms_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:almanacs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:almanacs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:almanacs:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:almanacs:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_settings:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_settings:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:calibration:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:calibration:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:cameras:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cameras:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:capture:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:capture:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:chat:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:chat:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:configs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:configs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:configs:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:configs:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_pinned:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_pinned:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_nickname:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_nickname:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thresholds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thresholds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:diagnostics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:diagnostics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:guides:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:guides:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:captcha:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:captcha:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_row:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_row:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_disable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_disable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_enable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_enable:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:feeds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:feeds:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:hardware:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:hardware:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:quick_tune:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:quick_tune:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robot_ui:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robot_ui:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:velocity_estimators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:velocity_estimators:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:vizualization_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:vizualization_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:metrics:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:mode:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:mode:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs_select:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs_select:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:shortcuts_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:shortcuts_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:admin_alarms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:admin_alarms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:admin_cloud:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:admin_cloud:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:customers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:customers:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users_invite:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_invite:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users_permissions:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_permissions:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:reports:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:reports:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:reports:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots_assign:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_assign:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots_status:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_status:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots_health:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_health:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:veselka:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:veselka:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:metrics_internal:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:metrics_customer:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops_basic:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops_advanced:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:discriminators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:discriminators:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_map:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_map:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:farms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:farms:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:images:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:images:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:autotractor_jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:plant_category_profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_globals:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:portal_globals:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_drive:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:autotractor_drive:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:profiles:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}], "role_id": "${auth0_role.carbon_tech.id}", "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "farm_manager": [{"permissions": [{"name": "read:alarms_customer:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:alarms_customer:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:almanacs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:almanacs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_settings:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_settings:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:calibration:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:calibration:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:cameras:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cameras:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:capture:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:capture:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:chat:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:chat:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:configs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:configs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_pinned:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_pinned:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_nickname:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_nickname:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thresholds:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:diagnostics:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:diagnostics:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:guides:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:captcha:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:captcha:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cap<PERSON><PERSON>:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_row:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_row:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_disable:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_disable:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:feeds:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:hardware:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:hardware:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:quick_tune:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:quick_tune:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:velocity_estimators:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_advanced:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:mode:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:mode:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs_select:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs_select:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:customers:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:customers:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:users:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_invite:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:users_permissions:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:reports:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:reports:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:robots:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:robots_status:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics_customer:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops_basic:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:discriminators:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:discriminators:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_map:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:farms:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:farms:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:images:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_jobs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:autotractor_jobs:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:plant_category_profiles:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:plant_category_profiles:templates", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_globals:all", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:autotractor_drive:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:profiles:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:profiles:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}], "role_id": "${auth0_role.farm_manager.id}", "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "operator_advanced": [{"permissions": [{"name": "read:alarms_customer:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:almanacs:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:almanacs:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_settings:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_settings:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:calibration:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:calibration:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:cameras:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cameras:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:capture:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:capture:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:chat:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:chat:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_pinned:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_pinned:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_nickname:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_nickname:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thresholds:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:diagnostics:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:diagnostics:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:captcha:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:cap<PERSON><PERSON>:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_row:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_row:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_disable:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:feeds:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:quick_tune:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:quick_tune:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:software_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:velocity_estimators:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_advanced:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:mode:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:mode:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs_select:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:portal_settings:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:discriminators:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:discriminators:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_map:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:uploads:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:farms:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:farms:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:profiles:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}], "role_id": "${auth0_role.operator_advanced.id}", "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "operator_basic": [{"permissions": [{"name": "read:alarms_customer:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_settings:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:operator_settings:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:banding_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:banding_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:calibration:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:cameras:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:capture:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:capture:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:chat:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:chat:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:crops:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:crops:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_pinned:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:models_pinned:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:models_nickname:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:diagnostics:self", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:jobs:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_row:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_row:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:lasers_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:lasers_disable:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:feeds:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:quick_tune:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:thinning_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:thinning_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:software_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:velocity_estimators:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:vizualization_basic:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:metrics:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:mode:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:mode:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "update:jobs_select:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:operator_map:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:farms:customer", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}, {"name": "read:profiles:robot", "resource_server_identifier": "${auth0_resource_server.portal.identifier}"}], "role_id": "${auth0_role.operator_basic.id}", "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "veselka_admin": [{"role_id": "${auth0_role.veselka_admin.id}", "permissions": [{"name": "veselka:admin", "resource_server_identifier": "${auth0_resource_server.veselka.identifier}"}], "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "veselka_reviewer": [{"role_id": "${auth0_role.veselka_reviewer.id}", "permissions": [{"name": "veselka:review", "resource_server_identifier": "${auth0_resource_server.veselka.identifier}"}], "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}], "veselka_labeler": [{"role_id": "${auth0_role.veselka_labeler.id}", "permissions": [{"name": "veselka:label", "resource_server_identifier": "${auth0_resource_server.veselka.identifier}"}], "depends_on": ["auth0_resource_server_scopes.portal", "auth0_resource_server_scopes.veselka"]}]}}}