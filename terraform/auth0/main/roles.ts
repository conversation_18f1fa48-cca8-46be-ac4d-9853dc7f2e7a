import fs from "node:fs";
import {
  PermissionAction,
  permissionActionToJSON,
  PermissionDomain,
  permissionDomainToJSON,
  PermissionResource,
  permissionResourceToJSON,
  userDisplayRoleToJSON,
  UserDisplayRole,
  permissionResourceFromJSON,
} from "protos/portal/auth";

// make things more readable
const Action = PermissionAction;
const Resource = PermissionResource;
const Domain = PermissionDomain;

enum API {
  portal = "auth0_resource_server.portal.identifier",
  veselka = "auth0_resource_server.veselka.identifier",
}

interface Role {
  name: string;
  api: API;
  permissions: string[];
}

const permissions = new Set<string>();

// TODO delete after transition
permissions.add("read:profiles:robot");
permissions.add("read:profiles:customer");
permissions.add("read:profiles:all");
permissions.add("update:profiles:robot");
permissions.add("update:profiles:customer");
permissions.add("update:profiles:all");
permissions.add("update:plant_category_profiles:templates");

const buildPermission = (
  action: PermissionAction,
  resource: PermissionResource,
  domain?: PermissionDomain | undefined,
): string => {
  const permission: string[] = [
    permissionActionToJSON(action),
    permissionResourceToJSON(resource),
  ];
  if (domain) {
    permission.push(permissionDomainToJSON(domain));
  }
  const permissionString = permission.join(":");
  permissions.add(permissionString);
  return permissionString;
};

type NaturalPermissions = Record<
  Exclude<
    PermissionResource,
    PermissionResource.UNRECOGNIZED | PermissionResource.unknown
  >,
  Array<{ can: PermissionAction; for: PermissionDomain }>
>;

const serializePermissions = (permissions: NaturalPermissions): string[] => {
  const keys = Object.keys(permissions).map((key) =>
    permissionResourceFromJSON(Number(key)),
  ) as Exclude<
    PermissionResource,
    PermissionResource.UNRECOGNIZED | PermissionResource.unknown
  >[];
  return keys.flatMap((resource) =>
    permissions[resource].map(({ can, for: domain }) =>
      buildPermission(can, resource, domain),
    ),
  );
};

const operatorBasicPermissions: NaturalPermissions = {
  [Resource.admin_alarms]: [],
  [Resource.admin_cloud]: [],
  [Resource.alarms_customer]: [{ can: Action.read, for: Domain.robot }],
  [Resource.alarms_internal]: [],
  [Resource.almanacs]: [],
  [Resource.autotractor_jobs]: [],
  [Resource.banding]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.banding_advanced]: [],
  [Resource.banding_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.calibration]: [{ can: Action.read, for: Domain.robot }],
  [Resource.cameras]: [{ can: Action.read, for: Domain.robot }],
  [Resource.captcha]: [],
  [Resource.capture]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.chat]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.configs]: [],
  [Resource.crops]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.crops_advanced]: [],
  [Resource.crops_basic]: [],
  [Resource.customers]: [],
  [Resource.diagnostics]: [{ can: Action.update, for: Domain.self }],
  [Resource.discriminators]: [],
  [Resource.farms]: [{ can: Action.read, for: Domain.customer }],
  [Resource.feeds]: [{ can: Action.read, for: Domain.robot }],
  [Resource.portal_globals]: [],
  [Resource.guides]: [],
  [Resource.hardware]: [],
  [Resource.images]: [],
  [Resource.jobs]: [{ can: Action.read, for: Domain.robot }],
  [Resource.jobs_select]: [{ can: Action.update, for: Domain.robot }],
  [Resource.lasers]: [],
  [Resource.lasers_advanced]: [],
  [Resource.lasers_basic]: [{ can: Action.read, for: Domain.robot }],
  [Resource.lasers_disable]: [{ can: Action.update, for: Domain.robot }],
  [Resource.lasers_enable]: [],
  [Resource.lasers_row]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.metrics]: [{ can: Action.read, for: Domain.robot }],
  [Resource.metrics_customer]: [],
  [Resource.metrics_internal]: [],
  [Resource.mode]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.models_advanced]: [],
  [Resource.models_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.models_nickname]: [{ can: Action.read, for: Domain.robot }],
  [Resource.models_pinned]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.operator_map]: [{ can: Action.read, for: Domain.robot }],
  [Resource.operator_settings]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.plant_category_profiles]: [],
  [Resource.portal_settings]: [],
  [Resource.quick_tune]: [{ can: Action.read, for: Domain.robot }],
  [Resource.reports]: [],
  [Resource.robot_ui]: [],
  [Resource.robots]: [],
  [Resource.robots_assign]: [],
  [Resource.robots_health]: [],
  [Resource.robots_status]: [],
  [Resource.shortcuts_internal]: [],
  [Resource.software_advanced]: [],
  [Resource.software_basic]: [{ can: Action.read, for: Domain.robot }],
  [Resource.thinning]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thinning_advanced]: [],
  [Resource.thinning_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thresholds]: [],
  [Resource.uploads]: [],
  [Resource.users]: [],
  [Resource.users_invite]: [],
  [Resource.users_permissions]: [],
  [Resource.velocity_estimators]: [{ can: Action.read, for: Domain.robot }],
  [Resource.veselka]: [],
  [Resource.vizualization_advanced]: [],
  [Resource.vizualization_basic]: [{ can: Action.read, for: Domain.robot }],
};

const operatorAdvancedPermissions: NaturalPermissions = {
  [Resource.admin_alarms]: [],
  [Resource.admin_cloud]: [],
  [Resource.alarms_customer]: [{ can: Action.read, for: Domain.robot }],
  [Resource.alarms_internal]: [],
  [Resource.almanacs]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.autotractor_jobs]: [],
  [Resource.banding]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.banding_advanced]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.banding_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.calibration]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.cameras]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.captcha]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.capture]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.chat]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.configs]: [],
  [Resource.crops]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.crops_advanced]: [],
  [Resource.crops_basic]: [],
  [Resource.customers]: [],
  [Resource.diagnostics]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.discriminators]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.farms]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.feeds]: [{ can: Action.read, for: Domain.robot }],
  [Resource.portal_globals]: [],
  [Resource.guides]: [],
  [Resource.hardware]: [],
  [Resource.images]: [],
  [Resource.jobs]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.jobs_select]: [{ can: Action.update, for: Domain.robot }],
  [Resource.lasers]: [],
  [Resource.lasers_advanced]: [{ can: Action.read, for: Domain.robot }],
  [Resource.lasers_basic]: [{ can: Action.read, for: Domain.robot }],
  [Resource.lasers_disable]: [{ can: Action.update, for: Domain.robot }],
  [Resource.lasers_enable]: [],
  [Resource.lasers_row]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.metrics]: [{ can: Action.read, for: Domain.robot }],
  [Resource.metrics_customer]: [],
  [Resource.metrics_internal]: [],
  [Resource.mode]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.models_advanced]: [],
  [Resource.models_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.models_nickname]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.models_pinned]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.operator_map]: [{ can: Action.read, for: Domain.robot }],
  [Resource.operator_settings]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.plant_category_profiles]: [],
  [Resource.portal_settings]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.quick_tune]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.reports]: [],
  [Resource.robot_ui]: [],
  [Resource.robots]: [],
  [Resource.robots_assign]: [],
  [Resource.robots_health]: [],
  [Resource.robots_status]: [],
  [Resource.shortcuts_internal]: [],
  [Resource.software_advanced]: [],
  [Resource.software_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thinning]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thinning_advanced]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thinning_basic]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.thresholds]: [{ can: Action.read, for: Domain.robot }],
  [Resource.uploads]: [{ can: Action.update, for: Domain.self }],
  [Resource.users]: [],
  [Resource.users_invite]: [],
  [Resource.users_permissions]: [],
  [Resource.velocity_estimators]: [
    { can: Action.read, for: Domain.robot },
    { can: Action.update, for: Domain.robot },
  ],
  [Resource.veselka]: [],
  [Resource.vizualization_advanced]: [{ can: Action.read, for: Domain.robot }],
  [Resource.vizualization_basic]: [{ can: Action.read, for: Domain.robot }],
};

const farmManagerPermissions: NaturalPermissions = {
  [Resource.admin_alarms]: [],
  [Resource.admin_cloud]: [],
  [Resource.alarms_customer]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.alarms_internal]: [],
  [Resource.almanacs]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.autotractor_drive]: [{ can: Action.read, for: Domain.customer }],
  [Resource.autotractor_jobs]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.banding]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.banding_advanced]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.banding_basic]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.calibration]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.cameras]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.captcha]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.capture]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.chat]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.configs]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.crops]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.crops_advanced]: [],
  [Resource.crops_basic]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.customers]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.diagnostics]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.discriminators]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.farms]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.feeds]: [{ can: Action.read, for: Domain.customer }],
  [Resource.portal_globals]: [{ can: Action.read, for: Domain.all }],
  [Resource.guides]: [{ can: Action.read, for: Domain.all }],
  [Resource.hardware]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.images]: [{ can: Action.read, for: Domain.customer }],
  [Resource.jobs]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
    // XXX: This definitely should be only customer, but JobSummary only
    // authorizes you if you have read:jobs:all. Fix that, then this.
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.jobs_select]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.lasers]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.lasers_advanced]: [{ can: Action.read, for: Domain.customer }],
  [Resource.lasers_basic]: [{ can: Action.read, for: Domain.customer }],
  [Resource.lasers_disable]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.lasers_enable]: [],
  [Resource.lasers_row]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.metrics]: [{ can: Action.read, for: Domain.customer }],
  [Resource.metrics_customer]: [{ can: Action.read, for: Domain.customer }],
  [Resource.metrics_internal]: [],
  [Resource.mode]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.models_advanced]: [],
  [Resource.models_basic]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.models_nickname]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.models_pinned]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.operator_map]: [{ can: Action.read, for: Domain.customer }],
  [Resource.operator_settings]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.plant_category_profiles]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
    { can: Action.read, for: Domain.templates },
  ],
  [Resource.portal_settings]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.quick_tune]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.reports]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.robot_ui]: [],
  [Resource.robots]: [{ can: Action.read, for: Domain.customer }],
  [Resource.robots_assign]: [],
  [Resource.robots_health]: [],
  [Resource.robots_status]: [{ can: Action.update, for: Domain.customer }],
  [Resource.shortcuts_internal]: [],
  [Resource.software_advanced]: [],
  [Resource.software_basic]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.thinning]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.thinning_advanced]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.thinning_basic]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.thresholds]: [{ can: Action.read, for: Domain.customer }],
  [Resource.uploads]: [{ can: Action.update, for: Domain.self }],
  [Resource.users]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.users_invite]: [{ can: Action.update, for: Domain.customer }],
  [Resource.users_permissions]: [{ can: Action.update, for: Domain.customer }],
  [Resource.velocity_estimators]: [
    { can: Action.read, for: Domain.customer },
    { can: Action.update, for: Domain.customer },
  ],
  [Resource.veselka]: [],
  [Resource.vizualization_advanced]: [
    { can: Action.read, for: Domain.customer },
  ],
  [Resource.vizualization_basic]: [{ can: Action.read, for: Domain.customer }],
};

const carbonBasicPermissions: NaturalPermissions = {
  [Resource.admin_alarms]: [],
  [Resource.admin_cloud]: [],
  [Resource.alarms_customer]: [{ can: Action.read, for: Domain.all }],
  [Resource.alarms_internal]: [{ can: Action.read, for: Domain.all }],
  [Resource.almanacs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.autotractor_drive]: [{ can: Action.read, for: Domain.all }],
  [Resource.autotractor_jobs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.calibration]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.cameras]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.captcha]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.capture]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.chat]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.configs]: [],
  [Resource.crops]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.crops_advanced]: [{ can: Action.read, for: Domain.all }],
  [Resource.crops_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.customers]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.diagnostics]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.discriminators]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.farms]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.feeds]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.portal_globals]: [{ can: Action.read, for: Domain.all }],
  [Resource.guides]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.hardware]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.images]: [{ can: Action.read, for: Domain.all }],
  [Resource.jobs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.jobs_select]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_advanced]: [{ can: Action.read, for: Domain.all }],
  [Resource.lasers_basic]: [{ can: Action.read, for: Domain.all }],
  [Resource.lasers_disable]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_enable]: [],
  [Resource.lasers_row]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.metrics]: [{ can: Action.read, for: Domain.all }],
  [Resource.metrics_customer]: [{ can: Action.read, for: Domain.all }],
  [Resource.metrics_internal]: [],
  [Resource.mode]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_advanced]: [],
  [Resource.models_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_nickname]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_pinned]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.operator_map]: [{ can: Action.read, for: Domain.all }],
  [Resource.operator_settings]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.plant_category_profiles]: [
    { can: Action.read, for: Domain.templates },
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.portal_settings]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.quick_tune]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.reports]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.robot_ui]: [],
  [Resource.robots]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.robots_assign]: [{ can: Action.update, for: Domain.all }],
  [Resource.robots_health]: [],
  [Resource.robots_status]: [{ can: Action.update, for: Domain.all }],
  [Resource.shortcuts_internal]: [{ can: Action.read, for: Domain.all }],
  [Resource.software_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.software_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thresholds]: [{ can: Action.read, for: Domain.all }],
  [Resource.uploads]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.users]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.users_invite]: [{ can: Action.update, for: Domain.all }],
  [Resource.users_permissions]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.velocity_estimators]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.veselka]: [{ can: Action.read, for: Domain.all }],
  [Resource.vizualization_advanced]: [{ can: Action.read, for: Domain.all }],
  [Resource.vizualization_basic]: [{ can: Action.read, for: Domain.all }],
};

const carbonTechPermissions: NaturalPermissions = {
  [Resource.admin_alarms]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.admin_cloud]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.alarms_customer]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.alarms_internal]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.almanacs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
    { can: Action.read, for: Domain.templates },
    { can: Action.update, for: Domain.templates },
  ],
  [Resource.autotractor_drive]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.autotractor_jobs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.banding_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.calibration]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.cameras]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.captcha]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.capture]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.chat]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.configs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
    { can: Action.read, for: Domain.templates },
    { can: Action.update, for: Domain.templates },
  ],
  [Resource.crops]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.crops_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.crops_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.customers]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.diagnostics]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.discriminators]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.farms]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.feeds]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.portal_globals]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.guides]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.hardware]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.images]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.jobs]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.jobs_select]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_disable]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_enable]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.lasers_row]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.metrics]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.metrics_customer]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.metrics_internal]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.mode]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_nickname]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.models_pinned]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.operator_map]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.operator_settings]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.plant_category_profiles]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
    { can: Action.read, for: Domain.templates },
  ],
  [Resource.portal_settings]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.quick_tune]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.reports]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.robot_ui]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.robots]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.robots_assign]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.robots_health]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.robots_status]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.shortcuts_internal]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.software_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.software_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thinning_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.thresholds]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.uploads]: [
    { can: Action.read, for: Domain.self },
    { can: Action.update, for: Domain.self },
  ],
  [Resource.users]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.users_invite]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.users_permissions]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.velocity_estimators]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
    { can: Action.read, for: Domain.templates },
    { can: Action.update, for: Domain.templates },
  ],
  [Resource.veselka]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.vizualization_advanced]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
  [Resource.vizualization_basic]: [
    { can: Action.read, for: Domain.all },
    { can: Action.update, for: Domain.all },
  ],
};

const ROLES: Role[] = [
  {
    name: userDisplayRoleToJSON(UserDisplayRole.operator_basic),
    api: API.portal,
    permissions: [
      ...serializePermissions(operatorBasicPermissions),
      // TODO delete after transition
      "read:profiles:robot",
    ],
  },
  {
    name: userDisplayRoleToJSON(UserDisplayRole.operator_advanced),
    api: API.portal,
    permissions: [
      ...serializePermissions(operatorAdvancedPermissions),
      // TODO delete after transition
      "read:profiles:robot",
    ],
  },
  {
    name: userDisplayRoleToJSON(UserDisplayRole.farm_manager),
    api: API.portal,
    permissions: [
      ...serializePermissions(farmManagerPermissions),
      // TODO delete after transition
      "read:profiles:customer",
      "update:profiles:customer",
    ],
  },
  {
    name: userDisplayRoleToJSON(UserDisplayRole.carbon_basic),
    api: API.portal,
    permissions: [
      ...serializePermissions(carbonBasicPermissions),
      // TODO delete after transition
      "read:profiles:all",
    ],
  },
  {
    name: userDisplayRoleToJSON(UserDisplayRole.carbon_tech),
    api: API.portal,
    permissions: [
      ...serializePermissions(carbonTechPermissions),
      // TODO delete after transition
      "read:profiles:all",
      "update:profiles:all",
    ],
  },
  {
    name: "veselka_admin",
    api: API.veselka,
    permissions: ["veselka:admin"],
  },
  {
    name: "veselka_reviewer",
    api: API.veselka,
    permissions: ["veselka:review"],
  },
  {
    name: "veselka_labeler",
    api: API.veselka,
    permissions: ["veselka:label"],
  },
];

interface ResourceRole {
  description: string;
  name: string;
}
interface ResourcePermissions {
  role_id: string;
  permissions: Array<{
    name: string;
    resource_server_identifier: string;
  }>;
  depends_on: string[];
}

interface RolesFile {
  resource: {
    auth0_resource_server_scopes: {
      portal: Array<{
        resource_server_identifier: string;
        scopes: Array<{
          name: string;
          description: string;
        }>;
      }>;
    };
    auth0_role: Record<string, ResourceRole[]>;
    auth0_role_permissions: Record<string, ResourcePermissions[]>;
  };
}

(() => {
  console.log("[ROLES] Embedding permissions into roles...");
  const file = fs.readFileSync("./roles.tf.json");
  const rawJSON = file.toString();
  const json: RolesFile = JSON.parse(rawJSON);

  // update portal permissions
  const scopes = json.resource.auth0_resource_server_scopes.portal?.[0];
  if (!scopes) {
    console.error("[ROLES] No portal scopes found!");
    return;
  }
  scopes.scopes = [...permissions.keys()].sort().map((permission) => ({
    name: permission,
    description:
      "See https://docs.google.com/spreadsheets/d/1yjQ9BSRRwE7nhjLDICofx4jAEEtjOOc8-b4HIC1XO6Y/edit",
  }));

  // update existing roles
  const foundPermissions: string[] = [];
  for (const [name, resource] of Object.entries(
    json.resource.auth0_role_permissions,
  )) {
    // get the role definition
    const role = ROLES.find((role) => role.name === name);
    // delete roles that are not longer defined
    if (!role) {
      console.log("[ROLES] Deleting role", name);
      delete json.resource.auth0_role_permissions[name];
      continue;
    }
    // update existing role
    foundPermissions.push(name);
    const definition = resource[0];
    if (!definition) continue;
    definition.role_id = "${auth0_role." + name + ".id}";
    definition.depends_on = [
      "auth0_resource_server_scopes.portal",
      "auth0_resource_server_scopes.veselka",
    ];
    definition.permissions = role.permissions.map((permission) => ({
      name: permission,
      resource_server_identifier: "${" + role.api + "}",
    }));
    console.log("[ROLES] Updated", name);
  }

  // add new roles
  for (const role of ROLES) {
    if (foundPermissions.includes(role.name)) continue;
    console.log("[ROLES] Adding", role.name);
    json.resource.auth0_role_permissions[role.name] = [
      {
        role_id: "${auth0_role." + role.name + ".id}",
        permissions: role.permissions.sort().map((permission) => ({
          name: permission,
          resource_server_identifier: "${" + role.api + "}",
        })),
        depends_on: [
          "auth0_resource_server_scopes.portal",
          "auth0_resource_server_scopes.veselka",
        ],
      },
    ];
  }

  fs.writeFileSync("./roles.tf.json", JSON.stringify(json, null, 2));
  console.log("[ROLES] Permissions embedded!");
})();
