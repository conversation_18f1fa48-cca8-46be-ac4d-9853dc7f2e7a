# https://github.com/argoproj/argo-helm/blob/argo-cd-5.13.8/charts/argo-cd/values.yaml
global:
  domain: "${argocd_host}"

configs:
  cm:
    url: "https://${argocd_host}"
    admin.enabled: ${admin_enabled}
    dex.config: |
      connectors:
        - type: github
          id: github
          name: GitHub
          config:
            clientID: ${github_client_id}
            clientSecret: $argocd-dex-auth:dex.github.clientSecret
            orgs:
              - name: ${github_org}

# above requires secret, ex:
#apiVersion: v1
#kind: Secret
#metadata:
#  name: argocd-dex-auth
#  namespace: argocd
#  labels:
#    app.kubernetes.io/part-of: argocd
#type: Opaque
#data:
#  dex.github.clientSecret: Oxxxxx==

  params:
    server.insecure: true
    server.enable.gzip: true
    reposerver.enable.git.submodule: false

  rbac:
    policy.default: role:readonly
    policy.csv: |
      p, role:deployers, *, *, *, allow
      p, role:deployers, applicationsets, create, *, deny
      p, role:deployers, applicationsets, delete, *, deny
      p, role:deployers, applications, create, *, deny
      p, role:deployers, applications, delete, *, deny
      p, role:deployers, clusters, create, *, deny
      p, role:deployers, clusters, delete, *, deny
      
      g, carbonrobotics:ArgoCD-Admins, role:admin
      g, carbonrobotics:ArgoCD-Deployers, role:deployers
      

controller:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"

dex:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"

redis:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"

server:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"
  ingress:
    enabled: ${ingress_enabled}
    hostname: "${argocd_host}"
    controller: aws
    ingressClassName: alb
    annotations:
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/backend-protocol: HTTP
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      external-dns.alpha.kubernetes.io/hostname: "${argocd_host}."
    aws:
      serviceType: ClusterIP
      backendProtocolVersion: GRPC

#  ingressGrpc:
#    enabled: true
#    isAWSALB: true

repoServer:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"

applicationSet:
  nodeSelector:
    Worker: default

  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"

notifications:
  nodeSelector:
    Worker: default

  argocdUrl: "https://${argocd_host}"
  metrics:
    enabled: ${enable_metrics}
    serviceMonitor:
      enabled: true
      selector:
        release: "kube-prometheus-stack"
  notifiers:
    service.slack: |
      token: $slack-token

  templates:
    template.app-deployed: |
      email:
        subject: New version of an application {{.app.metadata.name}} is up and running.
      message: |
        {{if eq .serviceType "slack"}}:white_check_mark:{{end}} Application {{.app.metadata.name}} is now running new version of deployments manifests.
      slack:
        attachments: |
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#18be52",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            },
            {
              "title": "Revision",
              "value": "{{.app.status.sync.revision}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]
    template.app-health-degraded: |
      email:
        subject: Application {{.app.metadata.name}} has degraded.
      message: |
        {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} has degraded.
        Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      slack:
        attachments: |-
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link": "{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#f4c030",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]
    template.app-sync-failed: |
      email:
        subject: Failed to sync application {{.app.metadata.name}}.
      message: |
        {{if eq .serviceType "slack"}}:exclamation:{{end}}  The sync operation of application {{.app.metadata.name}} has failed at {{.app.status.operationState.finishedAt}} with the following error: {{.app.status.operationState.message}}
        Sync operation details are available at: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      slack:
        attachments: |-
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#E96D76",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]
    template.app-sync-running: |
      email:
        subject: Start syncing application {{.app.metadata.name}}.
      message: |
        The sync operation of application {{.app.metadata.name}} has started at {{.app.status.operationState.startedAt}}.
        Sync operation details are available at: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      slack:
        attachments: |-
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#0DADEA",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]
    template.app-sync-status-unknown: |
      email:
        subject: Application {{.app.metadata.name}} sync status is 'Unknown'
      message: |
        {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} sync is 'Unknown'.
        Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
        {{if ne .serviceType "slack"}}
        {{range $c := .app.status.conditions}}
            * {{$c.message}}
        {{end}}
        {{end}}
      slack:
        attachments: |-
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#E96D76",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]
    template.app-sync-succeeded: |
      email:
        subject: Application {{.app.metadata.name}} has been successfully synced.
      message: |
        {{if eq .serviceType "slack"}}:white_check_mark:{{end}} Application {{.app.metadata.name}} has been successfully synced at {{.app.status.operationState.finishedAt}}.
        Sync operation details are available at: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
      slack:
        attachments: |-
          [{
            "title": "{{ .app.metadata.name}}",
            "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
            "color": "#18be52",
            "fields": [
            {
              "title": "Sync Status",
              "value": "{{.app.status.sync.status}}",
              "short": true
            },
            {
              "title": "Repository",
              "value": "{{.app.spec.source.repoURL}}",
              "short": true
            }
            {{range $index, $c := .app.status.conditions}}
            {{if not $index}},{{end}}
            {{if $index}},{{end}}
            {
              "title": "{{$c.type}}",
              "value": "{{$c.message}}",
              "short": true
            }
            {{end}}
            ]
          }]

  # -- The trigger defines the condition when the notification should be sent
  ## For more information: https://argocd-notifications.readthedocs.io/en/stable/triggers/
  triggers:
    trigger.on-deployed: |
     - description: Application is synced and healthy. Triggered once per commit.
       oncePer: app.status.sync.revision
       send:
       - app-deployed
       when: app.status.operationState.phase in ['Succeeded'] and app.status.health.status == 'Healthy'
    trigger.on-health-degraded: |
     - description: Application has degraded
       send:
       - app-health-degraded
       when: app.status.health.status == 'Degraded'
    trigger.on-sync-failed: |
     - description: Application syncing has failed
       send:
       - app-sync-failed
       when: app.status.operationState.phase in ['Error', 'Failed']
    trigger.on-sync-running: |
     - description: Application is being synced
       send:
       - app-sync-running
       when: app.status.operationState.phase in ['Running']
    trigger.on-sync-status-unknown: |
     - description: Application status is 'Unknown'
       send:
       - app-sync-status-unknown
       when: app.status.sync.status == 'Unknown'
    trigger.on-sync-succeeded: |
     - description: Application syncing has succeeded
       send:
       - app-sync-succeeded
       when: app.status.operationState.phase in ['Succeeded']
    # For more information: https://argocd-notifications.readthedocs.io/en/stable/triggers/#default-triggers
    defaultTriggers: |
     - on-sync-status-unknown
