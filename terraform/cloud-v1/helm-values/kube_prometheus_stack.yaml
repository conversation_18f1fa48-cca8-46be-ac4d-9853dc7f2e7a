# https://github.com/prometheus-community/helm-charts/blob/kube-prometheus-stack-41.6.1/charts/kube-prometheus-stack/values.yaml
# Create default rules for monitoring the cluster
# Disable rules for unreachable components
defaultRules:
  create: true
  rules:
    etcd: false
    kubeScheduler: false
    kubernetesApps: false

additionalPrometheusRulesMap:
  kubernetes-apps:
    groups:
    - name: kubernetes-apps
      rules:
        - alert: KubePodCrashLooping
          annotations:
            description: 'Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is in waiting state (reason: "CrashLoopBackOff").'
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubepodcrashlooping
            summary: Pod is crash looping.
          expr: max_over_time(kube_pod_container_status_waiting_reason{reason="CrashLoopBackOff",job="kube-state-metrics", namespace!~"testing|staging"}[5m]) >= 1
          for: 15m
          labels:
            severity: warning
        - alert: KubePodNotReady
          annotations:
            description: Pod {{ $labels.namespace }}/{{ $labels.pod }} has been in a non-ready state for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubepodnotready
            summary: Pod has been in a non-ready state for more than 15 minutes.
          expr: |-
            sum by (namespace, pod, cluster) (
              max by(namespace, pod, cluster) (
                kube_pod_status_phase{job="kube-state-metrics", namespace!~"actions-runner-controller|testing|staging", phase=~"Pending|Unknown|Failed"}
              ) * on(namespace, pod, cluster) group_left(owner_kind) topk by(namespace, pod, cluster) (
                1, max by(namespace, pod, owner_kind, cluster) (kube_pod_owner{owner_kind!="Job"})
              )
            ) > 0
          for: 15m
          labels:
            severity: warning
        - alert: KubeDeploymentGenerationMismatch
          annotations:
            description: Deployment generation for {{ $labels.namespace }}/{{ $labels.deployment }} does not match, this indicates that the Deployment has failed but has not been rolled back.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedeploymentgenerationmismatch
            summary: Deployment generation mismatch due to possible roll-back
          expr: |-
            kube_deployment_status_observed_generation{job="kube-state-metrics", namespace=~".*"}
              !=
            kube_deployment_metadata_generation{job="kube-state-metrics", namespace=~".*"}
          for: 15m
          labels:
            severity: warning
        - alert: KubeDeploymentReplicasMismatch
          annotations:
            description: Deployment {{ $labels.namespace }}/{{ $labels.deployment }} has not matched the expected number of replicas for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedeploymentreplicasmismatch
            summary: Deployment has not matched the expected number of replicas.
          expr: |-
            (
              kube_deployment_spec_replicas{job="kube-state-metrics", namespace=~".*"}
                >
              kube_deployment_status_replicas_available{job="kube-state-metrics", namespace=~".*"}
            ) and (
              changes(kube_deployment_status_replicas_updated{job="kube-state-metrics", namespace=~".*"}[10m])
                ==
              0
            )
          for: 15m
          labels:
            severity: warning
        - alert: KubeDeploymentRolloutStuck
          annotations:
            description: Rollout of deployment {{ $labels.namespace }}/{{ $labels.deployment }} is not progressing for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedeploymentrolloutstuck
            summary: Deployment rollout is not progressing.
          expr: |-
            kube_deployment_status_condition{condition="Progressing", status="false",job="kube-state-metrics", namespace=~".*"}
            != 0
          for: 15m
          labels:
            severity: warning
        - alert: KubeStatefulSetReplicasMismatch
          annotations:
            description: StatefulSet {{ $labels.namespace }}/{{ $labels.statefulset }} has not matched the expected number of replicas for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubestatefulsetreplicasmismatch
            summary: StatefulSet has not matched the expected number of replicas.
          expr: |-
            (
              kube_statefulset_status_replicas_ready{job="kube-state-metrics", namespace!~"actions-runner-controller|testing|staging"}
                !=
              kube_statefulset_status_replicas{job="kube-state-metrics", namespace!~"actions-runner-controller|testing|staging"}
            ) and (
              changes(kube_statefulset_status_replicas_updated{job="kube-state-metrics", namespace!~"actions-runner-controller|testing|staging"}[10m])
                ==
              0
            )
          for: 15m
          labels:
            severity: warning
        - alert: KubeStatefulSetGenerationMismatch
          annotations:
            description: StatefulSet generation for {{ $labels.namespace }}/{{ $labels.statefulset }} does not match, this indicates that the StatefulSet has failed but has not been rolled back.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubestatefulsetgenerationmismatch
            summary: StatefulSet generation mismatch due to possible roll-back
          expr: |-
            kube_statefulset_status_observed_generation{job="kube-state-metrics", namespace=~".*"}
              !=
            kube_statefulset_metadata_generation{job="kube-state-metrics", namespace=~".*"}
          for: 15m
          labels:
            severity: warning
        - alert: KubeStatefulSetUpdateNotRolledOut
          annotations:
            description: StatefulSet {{ $labels.namespace }}/{{ $labels.statefulset }} update has not been rolled out.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubestatefulsetupdatenotrolledout
            summary: StatefulSet update has not been rolled out.
          expr: |-
            (
              max without (revision) (
                kube_statefulset_status_current_revision{job="kube-state-metrics", namespace=~".*"}
                  unless
                kube_statefulset_status_update_revision{job="kube-state-metrics", namespace=~".*"}
              )
                *
              (
                kube_statefulset_replicas{job="kube-state-metrics", namespace=~".*"}
                  !=
                kube_statefulset_status_replicas_updated{job="kube-state-metrics", namespace=~".*"}
              )
            )  and (
              changes(kube_statefulset_status_replicas_updated{job="kube-state-metrics", namespace=~".*"}[5m])
                ==
              0
            )
          for: 15m
          labels:
            severity: warning
        - alert: KubeDaemonSetRolloutStuck
          annotations:
            description: DaemonSet {{ $labels.namespace }}/{{ $labels.daemonset }} has not finished or progressed for at least 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedaemonsetrolloutstuck
            summary: DaemonSet rollout is stuck.
          expr: |-
            (
              (
                kube_daemonset_status_current_number_scheduled{job="kube-state-metrics", namespace=~".*"}
                 !=
                kube_daemonset_status_desired_number_scheduled{job="kube-state-metrics", namespace=~".*"}
              ) or (
                kube_daemonset_status_number_misscheduled{job="kube-state-metrics", namespace=~".*"}
                 !=
                0
              ) or (
                kube_daemonset_status_updated_number_scheduled{job="kube-state-metrics", namespace=~".*"}
                 !=
                kube_daemonset_status_desired_number_scheduled{job="kube-state-metrics", namespace=~".*"}
              ) or (
                kube_daemonset_status_number_available{job="kube-state-metrics", namespace=~".*"}
                 !=
                kube_daemonset_status_desired_number_scheduled{job="kube-state-metrics", namespace=~".*"}
              )
            ) and (
              changes(kube_daemonset_status_updated_number_scheduled{job="kube-state-metrics", namespace=~".*"}[5m])
                ==
              0
            )
          for: 15m
          labels:
            severity: warning
        - alert: KubeContainerWaiting
          annotations:
            description: pod/{{ $labels.pod }} in namespace {{ $labels.namespace }} on container {{ $labels.container}} has been in waiting state for longer than 1 hour.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubecontainerwaiting
            summary: Pod container waiting longer than 1 hour
          expr: sum by (namespace, pod, container, cluster) (kube_pod_container_status_waiting_reason{job="kube-state-metrics",
            namespace=~".*"}) > 0
          for: 1h
          labels:
            severity: warning
        - alert: KubeDaemonSetNotScheduled
          annotations:
            description: '{{ $value }} Pods of DaemonSet {{ $labels.namespace }}/{{ $labels.daemonset }} are not scheduled.'
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedaemonsetnotscheduled
            summary: DaemonSet pods are not scheduled.
          expr: |-
            kube_daemonset_status_desired_number_scheduled{job="kube-state-metrics", namespace=~".*"}
              -
            kube_daemonset_status_current_number_scheduled{job="kube-state-metrics", namespace=~".*"} > 0
          for: 10m
          labels:
            severity: warning
        - alert: KubeDaemonSetMisScheduled
          annotations:
            description: '{{ $value }} Pods of DaemonSet {{ $labels.namespace }}/{{ $labels.daemonset }} are running where they are not supposed to run.'
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubedaemonsetmisscheduled
            summary: DaemonSet pods are misscheduled.
          expr: kube_daemonset_status_number_misscheduled{job="kube-state-metrics", namespace=~".*"}
            > 0
          for: 15m
          labels:
            severity: warning
        - alert: KubeJobNotCompleted
          annotations:
            description: Job {{ $labels.namespace }}/{{ $labels.job_name }} is taking more than {{ "43200" | humanizeDuration }} to complete.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubejobnotcompleted
            summary: Job did not complete in time
          expr: |-
            time() - max by(namespace, job_name, cluster) (kube_job_status_start_time{job="kube-state-metrics", namespace=~".*"}
              and
            kube_job_status_active{job="kube-state-metrics", namespace=~".*"} > 0) > 43200
          labels:
            severity: warning
        - alert: KubeJobFailed
          annotations:
            description: Job {{ $labels.namespace }}/{{ $labels.job_name }} failed to complete. Removing failed job after investigation should clear this alert.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubejobfailed
            summary: Job failed to complete.
          expr: kube_job_failed{job="kube-state-metrics", namespace=~".*"}  > 0
          for: 15m
          labels:
            severity: warning
        - alert: KubeHpaReplicasMismatch
          annotations:
            description: HPA {{ $labels.namespace }}/{{ $labels.horizontalpodautoscaler }} has not matched the desired number of replicas for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubehpareplicasmismatch
            summary: HPA has not matched desired number of replicas.
          expr: |-
            (kube_horizontalpodautoscaler_status_desired_replicas{job="kube-state-metrics", namespace=~".*"}
              !=
            kube_horizontalpodautoscaler_status_current_replicas{job="kube-state-metrics", namespace=~".*"})
              and
            (kube_horizontalpodautoscaler_status_current_replicas{job="kube-state-metrics", namespace=~".*"}
              >
            kube_horizontalpodautoscaler_spec_min_replicas{job="kube-state-metrics", namespace=~".*"})
              and
            (kube_horizontalpodautoscaler_status_current_replicas{job="kube-state-metrics", namespace=~".*"}
              <
            kube_horizontalpodautoscaler_spec_max_replicas{job="kube-state-metrics", namespace=~".*"})
              and
            changes(kube_horizontalpodautoscaler_status_current_replicas{job="kube-state-metrics", namespace=~".*"}[15m]) == 0
          for: 15m
          labels:
            severity: warning
        - alert: KubeHpaMaxedOut
          annotations:
            description: HPA {{ $labels.namespace }}/{{ $labels.horizontalpodautoscaler }} has been running at max replicas for longer than 15 minutes.
            runbook_url: https://runbooks.prometheus-operator.dev/runbooks/kubernetes/kubehpamaxedout
            summary: HPA is running at max replicas
          expr: |-
            kube_horizontalpodautoscaler_status_current_replicas{job="kube-state-metrics", namespace=~".*"}
              ==
            kube_horizontalpodautoscaler_spec_max_replicas{job="kube-state-metrics", namespace=~".*"}
          for: 15m
          labels:
            severity: warning

# Disable component scraping for the kube controller manager, etcd, and kube-scheduler
# These components are not reachable on EKS
kubeControllerManager:
  enabled: false
kubeEtcd:
  enabled: false
kubeScheduler:
  enabled: false

kube-state-metrics:
  nodeSelector:
    Worker: default

prometheusOperator:
  nodeSelector:
    Worker: default

alertmanager:
  config:
    global:
      resolve_timeout: 5m
    receivers:
      - name: "null"
      - name: cloud_pd
        pagerduty_configs:
          - service_key: ${cloud_pd_service_key}
      - name: sw_cloud_apps_pd
        pagerduty_configs:
          - service_key: ${sw_cloud_apps_service_key}
      - name: sw_mechanical_pd
        pagerduty_configs:
          - service_key: ${sw_mechanical_service_key}
      - name: manufacturing_pd
        pagerduty_configs:
          - service_key: ${manufacturing_service_key}
      - name: sw_robot_pd
        pagerduty_configs:
          - service_key: ${sw_robot_service_key}
      - name: deeplearning_pd
        pagerduty_configs:
          - service_key: ${deeplearning_service_key}
    route:
      group_by:
        - job
      group_interval: 5m
      group_wait: 30s
      receiver: cloud_pd
      repeat_interval: 12h
      routes:
        - receiver: "null"
          matchers:
            - alertname =~ "InfoInhibitor|Watchdog"
        - receiver: sw_mechanical_pd
          matchers:
            - pod =~ "mechanical.*"
        - receiver: manufacturing_pd
          matchers:
            - pod =~ "manufacturing.*"
        - receiver: deeplearning_pd
          matchers:
            - pod =~ "carbon-analytics.*|carbon-panelytics.*|comparison-tool.*|veselka-analytics.*"
        - receiver: sw_cloud_apps_pd
          matchers:
            - namespace =~ "rtc-prod|production"
    templates:
      - /etc/alertmanager/config/*.tmpl

  alertmanagerSpec:
    nodeSelector:
      Worker: default

prometheus:
  thanosService:
    enabled: true
  thanosServiceMonitor:
    enable: true

  extraSecret:
    name: thanos-objstore-config
    data:
      thanos.yaml: |
        type: s3
        config:
          aws_sdk_auth: true
          bucket: ${thanos_s3_bucket}
          region: ${region}
          endpoint: "s3.${region}.amazonaws.com"

  prometheusSpec:
    enableAdminAPI: true
    thanos:
      image: quay.io/thanos/thanos:v0.32.2
      objectStorageConfig:
        existingSecret:
          key: thanos.yaml
          name: thanos-objstore-config

    nodeSelector:
      Worker: default

    # Prometheus StorageSpec for persistent data on AWS EBS
    # ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/user-guides/storage.md
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp2
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi

    additionalScrapeConfigs:
      - job_name: kubernetes-service-endpoints
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - action: keep
            regex: true
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_scrape
          - action: replace
            regex: (https?)
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_scheme
            target_label: __scheme__
          - action: replace
            regex: (.+)
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_path
            target_label: __metrics_path__
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              - __address__
              - __meta_kubernetes_service_annotation_prometheus_io_port
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_service_name
            target_label: kubernetes_name
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: kubernetes_node
      - job_name: kubernetes-service-endpoints-slow
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - action: keep
            regex: true
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
          - action: replace
            regex: (https?)
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_scheme
            target_label: __scheme__
          - action: replace
            regex: (.+)
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_path
            target_label: __metrics_path__
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              - __address__
              - __meta_kubernetes_service_annotation_prometheus_io_port
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_service_name
            target_label: kubernetes_name
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: kubernetes_node
        scrape_interval: 5m
        scrape_timeout: 30s
      - job_name: kubernetes-services
        kubernetes_sd_configs:
          - role: service
        metrics_path: /probe
        params:
          module:
            - http_2xx
        relabel_configs:
          - action: keep
            regex: true
            source_labels:
              - __meta_kubernetes_service_annotation_prometheus_io_probe
          - source_labels:
              - __address__
            target_label: __param_target
          - replacement: blackbox
            target_label: __address__
          - source_labels:
              - __param_target
            target_label: instance
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - source_labels:
              - __meta_kubernetes_service_name
            target_label: kubernetes_name
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: keep
            regex: true
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_scrape
          - action: replace
            regex: (https?)
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_scheme
            target_label: __scheme__
          - action: replace
            regex: (.+)
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_path
            target_label: __metrics_path__
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              - __address__
              - __meta_kubernetes_pod_annotation_prometheus_io_port
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: kubernetes_pod_name
          - action: drop
            regex: Pending|Succeeded|Failed
            source_labels:
              - __meta_kubernetes_pod_phase
          - action: drop
            regex: loki
            source_labels:
              - app
      - job_name: kubernetes-pods-slow
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: keep
            regex: true
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_scrape_slow
          - action: replace
            regex: (https?)
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_scheme
            target_label: __scheme__
          - action: replace
            regex: (.+)
            source_labels:
              - __meta_kubernetes_pod_annotation_prometheus_io_path
            target_label: __metrics_path__
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              - __address__
              - __meta_kubernetes_pod_annotation_prometheus_io_port
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: kubernetes_pod_name
          - action: drop
            regex: Pending|Succeeded|Failed
            source_labels:
              - __meta_kubernetes_pod_phase
        scrape_interval: 5m
        scrape_timeout: 30s

grafana:
  image:
    repository: 645516868483.dkr.ecr.us-west-2.amazonaws.com/grafana/grafana
#    !! Warning !! older charts use repository, but this changes to registry at some point after grafana chart 6.60.*
#    registry: 645516868483.dkr.ecr.us-west-2.amazonaws.com

  nodeSelector:
    Worker: default

%{ if enable_google_oauth == true || enable_smtp == true }
  extraSecretMounts:
    - name: grafana-extra
      mountPath: /etc/secrets/grafana-extra
      secretName: grafana-extra
      readOnly: true
      defaultMode: "0444"
#      subPath: ""

# REQUIRES SECRET, ex
# apiVersion: v1
# kind: Secret
# metadata:
#   name: grafana-extra
# type: Opaque
# stringData:
#   google_client_id: <value>
#   google_client_secret: <value>
#   google_allowed_domains: <value>
#   database_url: postgres://user:secret@host:port/database
%{ endif }

  grafana.ini:
    dashboards:
      min_refresh_interval: 1m
    security:
      cookie_secure: true
    server:
      root_url: "https://${grafana_host}"
      enable_gzip: true
# database/rds
    database:
      url: $__file{/etc/secrets/grafana-extra/database_url}
#      url: postgres://grafanadb:<EMAIL>:5432/grafanadb
%{ if enable_google_oauth == true }
    auth.google:
      enabled: ${enable_google_oauth}
      client_id: $__file{/etc/secrets/grafana-extra/google_client_id}
      client_secret: $__file{/etc/secrets/grafana-extra/google_client_secret}
      scopes: https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email
      auth_url: https://accounts.google.com/o/oauth2/auth
      token_url: https://accounts.google.com/o/oauth2/token
      allowed_domains: $__file{/etc/secrets/grafana-extra/google_allowed_domains}
      allow_sign_up: true
%{ endif }
%{ if enable_smtp == true }
    smtp:
      enabled: true
      host: ${smtp_host}
      user: ${smtp_user}
      password: $__file{/etc/secrets/grafana-extra/smtp_pass}
%{ endif }
    users:
      auto_assign_org: true
      auto_assign_org_role: viewer
