{"name": "common", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "UNLICENSED", "devDependencies": {"@emotion/react": "catalog:", "@jest/globals": "catalog:", "@mui/icons-material": "catalog:", "@mui/material": "catalog:", "@mui/system": "catalog:", "@reduxjs/toolkit": "catalog:", "@turf/bearing": "catalog:", "@turf/buffer": "catalog:", "@turf/destination": "catalog:", "@turf/distance": "catalog:", "@turf/helpers": "catalog:", "@turf/invariant": "catalog:", "@types/color": "catalog:", "@types/geojson": "catalog:", "@types/luxon": "catalog:", "@types/mapbox-gl": "catalog:", "@types/react": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "clsx": "catalog:", "color": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-i18n-json": "catalog:", "eslint-plugin-i18next": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jest": "catalog:", "eslint-plugin-jsx-a11y": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-sonarjs": "catalog:", "eslint-plugin-sort-imports-es6-autofix": "catalog:", "eslint-plugin-unicorn": "catalog:", "eslint-plugin-unused-imports": "catalog:", "i18next": "catalog:", "luxon": "catalog:", "prettier": "catalog:", "react": "catalog:", "react-map-gl": "catalog:", "react-redux": "catalog:", "reconnecting-websocket": "catalog:", "tailwind-merge": "catalog:", "tailwindcss": "catalog:", "yup": "catalog:"}}