import { SharedWebsocket } from "common/utils/sharedWebsocket";
import React, { createContext, FC, PropsWithChildren, useContext } from "react";

export const WebSocketContext = createContext<SharedWebsocket>(
  new SharedWebsocket()
);

export const WebSocketProvider: FC<PropsWithChildren> = ({ children }) => {
  const sharedWebsocket = useContext(WebSocketContext);
  return (
    <WebSocketContext.Provider value={sharedWebsocket}>
      {children}
    </WebSocketContext.Provider>
  );
};
