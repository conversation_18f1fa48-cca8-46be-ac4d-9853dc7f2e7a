import { FixType, Point } from "protos/geo/geo";
import { Mark<PERSON> } from "react-map-gl";
import { Tooltip } from "@mui/material";
import React, { FunctionComponent, PropsWithChildren } from "react";

interface AccuracyMarkerProps {
  point: Point;
}
export const AccuracyMarker: FunctionComponent<AccuracyMarkerProps> = ({
  point,
}) => {
  return (
    <Marker
      longitude={point.lng}
      latitude={point.lat}
      style={{ cursor: "pointer" }}
    >
      <MarkerTooltip name={point.name} id={point.id?.id}>
        <PointMarkerIcon point={point} className="block" />
      </MarkerTooltip>
    </Marker>
  );
};

interface PointMarkerIconProps {
  point: Point;
  className?: string;
}
const PointMarkerIcon: FunctionComponent<PointMarkerIconProps> = ({
  point,
  className,
}) => {
  const innerColor = "white";
  const outerColor = "black";

  let contents;
  switch (point.captureInfo?.fixType) {
    case FixType.RTK_FIXED: {
      // X marks the spot
      const PATH = "M2,2L10,10M2,10L10,2";
      contents = (
        <g fill="none" strokeLinecap="round">
          <path d={PATH} stroke={outerColor} strokeWidth="4" />
          <path d={PATH} stroke={innerColor} strokeWidth="2" />
        </g>
      );
      break;
    }
    case FixType.RTK_FLOAT: {
      // diamond... encircles the spot. between an X and an O.
      const PATH = "M2,6L6,2L10,6L6,10z";
      contents = (
        <g fill="none" strokeLinecap="round" strokeLinejoin="round">
          <path d={PATH} stroke={outerColor} strokeWidth="4" />
          <path d={PATH} stroke={innerColor} strokeWidth="2" />
        </g>
      );
      break;
    }
    default: {
      // circle indicates "somewhere around here"
      contents = (
        <g fill="none">
          <circle cx={6} cy={6} r={4} stroke={outerColor} strokeWidth="4" />
          <circle cx={6} cy={6} r={4} stroke={innerColor} strokeWidth="2" />
        </g>
      );
      break;
    }
  }

  return (
    <svg viewBox="0 0 12 12" width={14} height={14} className={className}>
      {contents}
    </svg>
  );
};

interface MarkerTooltipProps extends PropsWithChildren {
  name?: string;
  id?: string;
}
export const MarkerTooltip: FunctionComponent<MarkerTooltipProps> = ({
  name,
  id,
  children,
}) => (
  <Tooltip
    arrow
    title={
      name ? (
        <span>{name}</span>
      ) : (
        <span className="font-mono text-xs">{id}</span>
      )
    }
  >
    <div>{children}</div>
  </Tooltip>
);
