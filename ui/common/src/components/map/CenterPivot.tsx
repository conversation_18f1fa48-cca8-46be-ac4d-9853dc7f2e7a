import { CenterPivot as CenterPivotType } from "protos/portal/farm";
import { DateTime } from "luxon";
import { destinationAlongLine } from "common/utils/geo";
import { FeatureCollection } from "geojson";
import { Layer, Marker, Source } from "react-map-gl";
import { MarkerTooltip } from "./AccuracyMarker";
import { Point } from "protos/geo/geo";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import { useGetDeviceLocationHistoryQuery } from "common/state/rtcLocatorApi";
import React, { FunctionComponent, useEffect, useMemo, useState } from "react";

const STALE_DEVICE_TIMEOUT_MIN = 5;
const DEVICE_UPDATE_INTERVAL_MIN = 5 / 60;
interface CenterPivotProps {
  centerPivot: CenterPivotType;
}
export const CenterPivot: FunctionComponent<CenterPivotProps> = ({
  centerPivot,
}) => {
  const { center, endpointDeviceId, lengthMeters } = centerPivot;
  const { data: latestEndpointLocation } = useGetDeviceLocationHistoryQuery(
    {
      deviceId: endpointDeviceId,
      pageSize: 1,
      desc: true,
    },
    { pollingInterval: DEVICE_UPDATE_INTERVAL_MIN * 60 * 1000 }
  );

  const endPoint = latestEndpointLocation?.history?.records[0];
  const [isDeviceStale, setIsDeviceStale] = useState(false);

  useEffect(() => {
    const lastUpdated = endPoint?.timestamp;
    if (lastUpdated) {
      const recordStaleness = (): void => {
        const isStale =
          DateTime.now().diff(DateTime.fromISO(lastUpdated), "minutes")
            .minutes > STALE_DEVICE_TIMEOUT_MIN;
        setIsDeviceStale(isStale);
      };
      // run once on initialization
      recordStaleness();

      // Check to see if the device is stale condition every minute
      const intervalId = setInterval(() => {
        recordStaleness();
      }, 60 * 1000);

      return () => clearInterval(intervalId);
    }
  }, [endPoint?.timestamp]);

  const lineGeoJson = useMemo(
    () =>
      center && endPoint?.point
        ? createLine(center, endPoint.point, lengthMeters)
        : undefined,
    [center, endPoint?.point, lengthMeters]
  );

  if (!center) {
    return;
  }

  const markerStyle = { cursor: "pointer" };
  const staleColor = theme.colors.carbon.gray.dark;
  return (
    <>
      {/* The center point */}
      <Marker longitude={center.lng} latitude={center.lat} style={markerStyle}>
        <MarkerTooltip name={center.name} id={center.id?.id}>
          <CircleIcon
            className="block"
            fillColor={theme.colors.carbon.map.farms.centerPivot}
          />
        </MarkerTooltip>
      </Marker>

      {endPoint?.point && (
        <>
          {/* the end point */}
          <Marker
            longitude={endPoint.point.lng}
            latitude={endPoint.point.lat}
            style={markerStyle}
          >
            <MarkerTooltip id={endPoint.deviceId}>
              <CircleIcon
                className="block"
                fillColor={
                  isDeviceStale
                    ? staleColor
                    : theme.colors.carbon.map.farms.centerPivot
                }
              />
            </MarkerTooltip>
          </Marker>
          {/* the connecting line */}
          {lineGeoJson && (
            <Source
              id={`pivot-irrigation-${center.id?.id}-endpoint-source`}
              type="geojson"
              data={lineGeoJson}
            >
              <Layer
                id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer`}
                type="line"
                paint={{
                  "line-color": isDeviceStale ? staleColor : theme.colors.black,
                  "line-width": 4,
                  ...(isDeviceStale ? { "line-dasharray": [1, 1] } : {}),
                }}
              />
            </Source>
          )}
        </>
      )}
    </>
  );
};

interface CircleIconProps {
  className?: string;
  fillColor: string;
}
const CircleIcon: FunctionComponent<CircleIconProps> = ({
  className,
  fillColor,
}) => (
  <svg viewBox="0 0 12 12" width={14} height={14} className={className}>
    <g fill="none">
      <circle
        cx={6}
        cy={6}
        r={6}
        fill={fillColor}
        stroke="black"
        strokeWidth={2}
      />
    </g>
  </svg>
);

const createLine = (
  pointA: Point,
  pointB: Point,
  lengthM?: number
): FeatureCollection => {
  const posA = [pointA.lng, pointA.lat];
  const posB = [pointB.lng, pointB.lat];
  const coordinates = lengthM
    ? destinationAlongLine(posA, posB, lengthM)
    : [posA, posB];
  return {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates,
        },
        properties: {},
      },
    ],
  };
};
