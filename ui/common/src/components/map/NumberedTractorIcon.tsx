import { classes } from "common/theme/theme";
import { getSequence } from "common/utils/robots";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import React, { FC } from "react";
interface NumberedTractorIconProps {
  serial: string;
  color?: string;
  fontSize?: number;
}
export const NumberedTractorIcon: FC<NumberedTractorIconProps> = ({
  color = theme.colors.white,
  serial,
  fontSize = 14,
}) => (
  <div className="rounded-sm h-6 w-6">
    <div className={"bg-black p-0.5 w-full h-full"}>
      <div
        className="w-full h-full bg-opacity-20"
        style={{
          backgroundColor: color,
          padding: "1px",
        }}
      >
        <div
          className={classes("flex w-full h-full bg-black p-0.5 bg-opacity-70")}
        >
          <svg
            textAnchor="middle"
            dominantBaseline="central"
            paintOrder={"stroke"}
          >
            <text
              fontWeight={"bolder"}
              fill={color}
              x={"50%"}
              y={"50%"}
              fontSize={fontSize}
            >
              {getSequence(serial)}
            </text>
          </svg>
        </div>
      </div>
    </div>
  </div>
);
