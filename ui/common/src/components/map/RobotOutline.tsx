import { Polygon } from "geojson";

export interface RobotOutline {
  serial: string;
  outline: Polygon;
}

// WGS 84 constants describing the shape of the earth
const EQUATOR_CIRCUMFERENCE_M = 6_378_137 * Math.PI * 2;
const MERIDIAN_CIRCUMFERENCE_M = 6_356_752 * Math.PI * 2;

export const createRobotOutline = (
  centerLng: number,
  centerLat: number,
  headingDegrees: number
): Polygon => {
  // Tractor dimensions (narrow tractor in front with front guard, wider implement behind)
  // These are hard-coded to the dimensions of the first gen Autotractor pulling a Slayer,
  // but should be pulled dynamically from the tractor and implement config in the future.
  const implWidthMeters = 6.7056;
  const implLengthMeters = 3.753_49;
  const tractorWidthMeters = 2.55;
  const tractorLengthMeters = 6.300_47;
  const guardWidthMeters = 3.5138;

  // Distance from front of tractor to GPS location. Note that GPS locations we receive
  // are offset from the actual GPS receiver to line up with the front axle of the tractor.
  const gpsLengthMeters = 3.124_05;

  // Define outline points in Cartesian space, north-up, 1 unit = 1 meter.
  type Point = [number, number];
  // prettier-ignore
  let points: Point[] = [
    // Starting at back-left corner of impl, moving counter-clockwise. Also includes a
    // center-line extending from back to slightly in front of the tractor as a row guide.
    [-implWidthMeters / 2, 0],                                                  // Back-left of implement
    [0, 0],                                                                     //
    [0, (implLengthMeters + tractorLengthMeters) * 1.05],                       // Center-line extending to front
    [0, 0],                                                                     //
    [implWidthMeters / 2, 0],                                                   // Back-right of implement
    [implWidthMeters / 2, implLengthMeters],                                    // Front-right of implement
    [tractorWidthMeters / 4, implLengthMeters],                                 // Right implement hitch cutout
    [tractorWidthMeters / 4, implLengthMeters + (tractorLengthMeters * 0.05)],  //
    [tractorWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.05)],  //
    [tractorWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.7)],   // Right guard hitch cutout
    [tractorWidthMeters / 8, implLengthMeters + (tractorLengthMeters * 0.7)],   //
    [tractorWidthMeters / 8, implLengthMeters + (tractorLengthMeters * 0.95)],  //
    [guardWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.95)],    //
    [guardWidthMeters / 2, implLengthMeters + tractorLengthMeters],             // Front-right of guard
    [-guardWidthMeters / 2, implLengthMeters + tractorLengthMeters],            // Front-left of guard
    [-guardWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.95)],   // Left guard hitch cutout
    [-tractorWidthMeters / 8, implLengthMeters + (tractorLengthMeters * 0.95)], //
    [-tractorWidthMeters / 8, implLengthMeters + (tractorLengthMeters * 0.7)],  //
    [-tractorWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.7)],  //
    [-tractorWidthMeters / 2, implLengthMeters + (tractorLengthMeters * 0.05)], // Left implement hitch cutout
    [-tractorWidthMeters / 4, implLengthMeters + (tractorLengthMeters * 0.05)], //
    [-tractorWidthMeters / 4, implLengthMeters],                                //
    [-tractorWidthMeters / 2, implLengthMeters],                                // Left transition to tractor
    [-implWidthMeters / 2, implLengthMeters],                                   // Front-left of implement
    [-implWidthMeters / 2, 0],                                                  // Back to start to close polygon
  ];

  // Transform origin to coincide with the GPS receiver
  const gpsPositionMeters =
    implLengthMeters + tractorLengthMeters - gpsLengthMeters;
  points = points.map(([x, y]) => {
    return [x, y - gpsPositionMeters];
  });

  // Rotate to face tractor heading
  const headingRadians = -headingDegrees * (Math.PI / 180);
  const cosHeading = Math.cos(headingRadians);
  const sinHeading = Math.sin(headingRadians);
  points = points.map(([x, y]) => {
    return [cosHeading * x - sinHeading * y, sinHeading * x + cosHeading * y];
  });

  // Project onto globe
  const centerLatRadians = centerLat * (Math.PI / 180);
  const metersPerDegreeLat = MERIDIAN_CIRCUMFERENCE_M / 360;
  const metersPerDegreeLng =
    (EQUATOR_CIRCUMFERENCE_M * Math.cos(centerLatRadians)) / 360;
  const projectedPoints = points.map(([x, y]) => {
    return [
      centerLng + x / metersPerDegreeLng,
      centerLat + y / metersPerDegreeLat,
    ];
  });

  return {
    type: "Polygon",
    coordinates: [projectedPoints],
  };
};
