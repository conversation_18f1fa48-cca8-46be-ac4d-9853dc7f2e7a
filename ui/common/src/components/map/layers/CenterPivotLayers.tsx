/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
  AutotractorPropsType,
  Domain,
  InteractionLayer,
  LayerConfig,
} from "common/components/map/layers/types";
import { CENTER_PIVOT_FRESH, classes } from "common/theme/theme";
import { CenterPivotFeature, getCenterPivotFeature } from "common/utils/farm";
import { CenterPivot as CenterPivotType } from "protos/portal/farm";
import { destinationAlongLine } from "common/utils/geo";
import { Layer, Marker, Source } from "react-map-gl";
import {
  LiveRtcDeviceResponse,
  useStalePercentage,
} from "common/hooks/useLiveRtcLocation";
import { MarkerTooltip } from "common/components/map/AccuracyMarker";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";

import {
  PIVOT_POLL_INTERVAL_S,
  PIVOT_STALE_THRESHOLD_S,
} from "common/utils/rtcJobs";
import { Point } from "protos/geo/geo";
import { SvgIconComponent } from "common/components/types";
import Color from "color";
import React, { FunctionComponent, useMemo } from "react";

interface CenterPivotLayersProps {
  centerPivot: CenterPivotType;
  liveData: LiveRtcDeviceResponse;
  warnOnMove?: boolean;
  onClick?: () => void;
  isFocused?: boolean;
  isSelected?: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  beforeId?: string;
  PivotIcon: SvgIconComponent;
}

export const CenterPivotLayers: FunctionComponent<CenterPivotLayersProps> = ({
  centerPivot,
  liveData,
  warnOnMove,
  onClick,
  isFocused,
  isSelected,
  onMouseEnter,
  onMouseLeave,
  beforeId,
  PivotIcon,
}) => {
  const { center, endpointDeviceId, lengthMeters } = centerPivot;
  const {
    location: endPoint,
    isMoving,
    isStale,
    lastCheckedTimestamp,
  } = liveData;

  const stalePercentage = useStalePercentage(
    PIVOT_POLL_INTERVAL_S,
    PIVOT_STALE_THRESHOLD_S,
    lastCheckedTimestamp
  );

  const lineGeoJson = useMemo(
    () =>
      center && endPoint?.point
        ? createLine(centerPivot, center, endPoint.point, lengthMeters)
        : undefined,
    [center, endPoint?.point, lengthMeters, centerPivot]
  );

  if (!center) {
    return;
  }

  const markerStyle = { cursor: "pointer" };
  const staleColor = theme.colors.carbon.gray.medium;

  const desaturatedColor = new Color(CENTER_PIVOT_FRESH)
    .desaturate(isStale ? 100 : stalePercentage)
    .toString();

  return (
    <>
      {/* The center point */}
      <div
        onClick={onClick}
        onMouseEnter={() => {
          onMouseEnter?.();
        }}
        onMouseLeave={() => onMouseLeave?.()}
      >
        <Marker
          longitude={center.lng}
          latitude={center.lat}
          style={markerStyle}
          onClick={onClick}
        >
          <MarkerTooltip name={center.name} id={center.id?.id}>
            <div className={classes("h-5 w-5 rounded-lg flex bg-slate-900")}>
              <PivotIcon
                fill={desaturatedColor}
                className={classes(
                  "h-4 w-4 my-auto flex mx-auto mt-[4px] ml-[3px]"
                )}
              />
            </div>
          </MarkerTooltip>
        </Marker>
      </div>
      {endPoint?.point && (
        <>
          {/* the end point */}
          <div
            onMouseEnter={() => {
              onMouseEnter?.();
            }}
            onMouseLeave={() => onMouseLeave?.()}
          >
            <Marker
              longitude={endPoint.point.lng}
              latitude={endPoint.point.lat}
              style={markerStyle}
              onClick={onClick}
            >
              <MarkerTooltip id={endPoint.deviceId}>
                <CircleIcon className={"block"} fillColor={desaturatedColor} />
              </MarkerTooltip>
            </Marker>
          </div>
          {/* the connecting line */}
          {lineGeoJson && (
            <Source
              id={`pivot-irrigation-${center.id?.id}-endpoint-source`}
              type="geojson"
              data={lineGeoJson}
            >
              {warnOnMove && isMoving && (
                <Layer
                  id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-warn`}
                  beforeId={beforeId ?? undefined}
                  type="line"
                  paint={{
                    "line-color": theme.colors.carbon.yellow,
                    "line-width": 4,
                    "line-dasharray": [1, 1],
                  }}
                />
              )}
              <Layer
                id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer`}
                beforeId={
                  warnOnMove && isMoving
                    ? `pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-warn`
                    : beforeId ?? undefined
                }
                type="line"
                paint={{
                  "line-color": isStale ? staleColor : theme.colors.black,
                  "line-width": 4,
                }}
              />
              {isFocused && (
                <Layer
                  id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-outline`}
                  beforeId={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer`}
                  type="line"
                  paint={{
                    "line-color": theme.colors.blue[200],
                    "line-width": 8,
                  }}
                />
              )}
              {isSelected && (
                <Layer
                  id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-selectionBorder`}
                  beforeId={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer`}
                  type="line"
                  paint={{
                    "line-color": theme.colors.blue[200],
                    "line-width": 6,
                  }}
                />
              )}
              {isSelected && (
                <Layer
                  id={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-outline`}
                  beforeId={`pivot-irrigation-${center.id?.id}-${endpointDeviceId}-layer-selectionBorder`}
                  type="line"
                  paint={{
                    "line-color": theme.colors.carbon.blue,
                    "line-width": 10,
                  }}
                />
              )}
            </Source>
          )}
        </>
      )}
    </>
  );
};

export const useCenterPivotLayers = ({
  centerPivot,
  clickPivot,
  hoverPivot,
}: {
  centerPivot?: CenterPivotType;
  isFocused?: boolean;
  isSelected?: boolean;
  clickPivot?: (feature?: CenterPivotFeature) => void;
  hoverPivot?: (feature?: CenterPivotFeature) => void;
}): LayerConfig<CenterPivotLayersProps> | undefined => {
  if (!centerPivot) {
    return;
  }
  const interactionLayers: InteractionLayer[] = [
    {
      id: `pivot-irrigation-${centerPivot.center?.id?.id}-${centerPivot.endpointDeviceId}-layer`,
      cursor: "pointer",
      onClick: (e) => clickPivot?.(getCenterPivotFeature(e)),
      onMouseMove: (e) => hoverPivot?.(getCenterPivotFeature(e)),
      onLeave: () => hoverPivot?.(undefined),
    },
  ];
  return {
    interactionLayers,
    Layers: CenterPivotLayers,
    idsToLoad: [],
  };
};

interface CircleIconProps {
  className?: string;
  fillColor?: string;
}
const CircleIcon: FunctionComponent<CircleIconProps> = ({
  className,
  fillColor,
}) => (
  <svg viewBox="0 0 12 12" width={14} height={14} className={className}>
    <g fill="none">
      <circle
        cx={6}
        cy={6}
        r={6}
        fill={fillColor}
        stroke="black"
        strokeWidth={2}
      />
    </g>
  </svg>
);

const createLine = (
  centerPivot: CenterPivotType,
  pointA: Point,
  pointB: Point,
  lengthM?: number
): CenterPivotFeature => {
  const posA = [pointA.lng, pointA.lat];
  const posB = [pointB.lng, pointB.lat];
  const coordinates = lengthM
    ? destinationAlongLine(posA, posB, lengthM)
    : [posA, posB];
  return {
    type: "Feature",
    geometry: {
      type: "LineString",
      coordinates,
    },
    properties: {
      type: AutotractorPropsType.PIVOT,
      data: centerPivot,
      domain: Domain.AUTOTRACTOR,
      __portalFeatureProps: true,
    },
  };
};
