import {
  AutotractorPropsType,
  Domain,
  InteractionLayer,
  LayerConfig,
  MapImageId,
} from "./types";
import { CenterPivot } from "common/components/map/CenterPivot";
import { Expression } from "mapbox-gl";
import {
  FarmMapData,
  getPointFeature,
  getZoneFeature,
  PointFeature,
  ZoneFeature,
} from "common/utils/farm";
import { FeatureCollection } from "geojson";
import { FixType } from "protos/geo/geo";
import { Layer, Source } from "react-map-gl";
import { MarkerLayer } from "./MarkerLayer";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import React, { FC, useMemo } from "react";

export const useFarmLayers = ({
  clickPoint,
  hoverPoint,
  clickZone,
  hoverZone,
  leaveZone,
}: {
  clickPoint?: (feature?: PointFeature) => void;
  hoverPoint?: (feature?: PointFeature) => void;
  clickZone?: (feature?: ZoneFeature) => void;
  hoverZone?: (feature?: ZoneFeature) => void;
  leaveZone?: () => void;
}): LayerConfig<FarmLayersProps> => {
  const pointInteractionLayers: InteractionLayer[] = [
    {
      id: "rtk-fixed-points",
      cursor: "pointer",
      onClick: (e) => clickPoint?.(getPointFeature(e)),
      onMouseMove: (e) => hoverPoint?.(getPointFeature(e)),
    },
    {
      id: "rtk-float-points",
      cursor: "pointer",
      onClick: (e) => clickPoint?.(getPointFeature(e)),
      onMouseMove: (e) => hoverPoint?.(getPointFeature(e)),
    },
    {
      id: "gps-points",
      cursor: "pointer",
      onClick: (e) => clickPoint?.(getPointFeature(e)),
      onMouseMove: (e) => hoverPoint?.(getPointFeature(e)),
    },
  ];
  const zoneInteractionLayers: InteractionLayer[] = [
    {
      id: "interactable-zones",
      cursor: "pointer",
      onClick: (e) => clickZone?.(getZoneFeature(e)),
      onMouseMove: (e) => hoverZone?.(getZoneFeature(e)),
      onLeave: leaveZone,
    },
  ];
  const interactionLayers = [
    ...pointInteractionLayers,
    ...zoneInteractionLayers,
  ];

  return {
    interactionLayers,
    idsToLoad: [MapImageId.GpsPoint, MapImageId.RtkFixed, MapImageId.RtkFloat],
    Layers: FarmLayers,
  };
};

interface FarmLayersProps {
  farmMapData: FarmMapData;
  hidePivots?: boolean;
  shownPoints: string[];
  disabledZoneIds?: string[];
  enabledZoneIds?: string[];
  focusedZoneId?: string;
  selectedZoneId?: string;
  focusedPointId?: string;
  selectedPointId?: string;
  beforeId?: string;
}

export const FarmLayers: FC<FarmLayersProps> = ({
  farmMapData,
  focusedZoneId,
  selectedZoneId,
  shownPoints,
  disabledZoneIds,
  enabledZoneIds,
  hidePivots,
  focusedPointId,
  selectedPointId,
  beforeId,
}) => {
  const { rtkFixedPoints, rtkFloatPoints, gpsPoints } = useMemo(() => {
    const rtkFixedPoints: FeatureCollection = {
      type: "FeatureCollection",
      features: [],
    };
    const rtkFloatPoints: FeatureCollection = {
      type: "FeatureCollection",
      features: [],
    };
    const gpsPoints: FeatureCollection = {
      type: "FeatureCollection",
      features: [],
    };

    for (const p of Object.values(farmMapData.pointsById)) {
      const feature: PointFeature = {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [p.lng, p.lat],
        },
        properties: {
          ...p,
          pointId: p.id?.id,
          type: AutotractorPropsType.POINT,
          domain: Domain.AUTOTRACTOR,
          __portalFeatureProps: true,
        },
      };
      switch (p.captureInfo?.fixType) {
        case FixType.RTK_FIXED: {
          rtkFixedPoints.features.push(feature);
          break;
        }
        case FixType.RTK_FLOAT: {
          rtkFloatPoints.features.push(feature);
          break;
        }
        default: {
          gpsPoints.features.push(feature);
          break;
        }
      }
    }
    return { rtkFixedPoints, rtkFloatPoints, gpsPoints };
  }, [farmMapData]);

  const centerPivots = [...farmMapData.centerPivotsByFieldId.entries()];

  const focusedPointFilter = ["==", ["get", "pointId"], focusedPointId ?? ""];
  const selectedPointFilter = ["==", ["get", "pointId"], selectedPointId ?? ""];
  const focusedZoneFilter = ["==", ["get", "zoneId"], focusedZoneId ?? ""];
  const selectedZoneFilter = ["==", ["get", "zoneId"], selectedZoneId ?? ""];

  // interactable/disabled zones allows you to choose which zones provide interaction layers
  // (interactable zones have a border)
  // ======
  const usingInteractableZones =
    (enabledZoneIds && enabledZoneIds.length > 0) ||
    (disabledZoneIds && disabledZoneIds.length === 0) ||
    (!enabledZoneIds && !disabledZoneIds);

  const disabledZoneFilter: Expression = [
    "in",
    ["get", "zoneId"],
    ["literal", disabledZoneIds],
  ];

  const enabledZoneFilter: Expression = [
    "in",
    ["get", "zoneId"],
    ["literal", enabledZoneIds],
  ];

  let interactableZoneFilter: Expression = ["boolean", true];
  let nonInteractableZoneFilter: Expression = ["boolean", false];
  if (enabledZoneIds && !disabledZoneIds) {
    interactableZoneFilter = enabledZoneFilter;
    nonInteractableZoneFilter = ["!", interactableZoneFilter];
  } else if (disabledZoneIds && !enabledZoneIds) {
    nonInteractableZoneFilter = disabledZoneFilter;
    interactableZoneFilter = ["!", nonInteractableZoneFilter];
  } else if (enabledZoneIds && disabledZoneIds) {
    interactableZoneFilter = [
      "all",
      enabledZoneFilter,
      ["!", disabledZoneFilter],
    ];
    nonInteractableZoneFilter = [
      "any",
      disabledZoneFilter,
      ["!", enabledZoneFilter],
    ];
  }
  // ======

  const focusedMarkerFilter = [
    "==",
    ["get", "id", ["get", "id"]],
    focusedPointId ?? "",
  ];
  const selectedMarkerFilter = [
    "==",
    ["get", "id", ["get", "id"]],
    selectedPointId ?? "",
  ];
  const shownPointsFilter = [
    "in",
    ["get", "id", ["get", "id"]],
    ["literal", shownPoints],
  ];
  const focusedOrSelectedOrShownFilter = [
    "any",
    selectedMarkerFilter,
    focusedMarkerFilter,
    shownPointsFilter,
  ];

  return (
    <>
      <Source
        key="farm-areas"
        id="farm-areas"
        type="geojson"
        data={farmMapData.areasGeojson}
      >
        {focusedZoneId && (
          <Layer
            id="focused-zone"
            beforeId={beforeId ?? undefined}
            type="fill"
            paint={{
              "fill-color": theme.colors.carbon.map.farms.focused,
              "fill-opacity": 0.2,
            }}
            filter={focusedZoneFilter}
          />
        )}
        {selectedZoneId && (
          <Layer
            id="selected-zone"
            beforeId={beforeId ?? undefined}
            type="fill"
            paint={{
              "fill-color": theme.colors.carbon.map.farms.focused,
              "fill-opacity": 0.3,
            }}
            filter={selectedZoneFilter}
          />
        )}
        {selectedZoneId && (
          <Layer
            id={"farm-area-selected-zone-outline"}
            beforeId={beforeId ?? undefined}
            type={"line"}
            paint={{
              "line-color": theme.colors.carbon.map.farms.focused,
              "line-width": 2,
            }}
            filter={selectedZoneFilter}
          />
        )}
        {usingInteractableZones && (
          <Layer
            id={"interactable-zones-outline"}
            beforeId={
              selectedZoneId
                ? "farm-area-selected-zone-outline"
                : beforeId ?? undefined
            }
            type="line"
            paint={{
              "line-color": "black",
              "line-width": 2,
            }}
            filter={interactableZoneFilter}
          />
        )}
        {usingInteractableZones && (
          <Layer
            id={"interactable-zones"}
            beforeId={"interactable-zones-outline"}
            type={"fill"}
            paint={{
              "fill-color": ["get", "zoneColor"],
              "fill-opacity": 0.2,
            }}
            filter={interactableZoneFilter}
          />
        )}
        <Layer
          id={"non-interactable-zones"}
          beforeId={
            selectedZoneId
              ? "farm-area-selected-zone-outline"
              : beforeId ?? undefined
          }
          type={"fill"}
          paint={{
            "fill-color": ["get", "zoneColor"],
            "fill-opacity": 0.2,
          }}
          filter={nonInteractableZoneFilter}
        />
      </Source>
      {!hidePivots &&
        centerPivots.map(([zoneId, centerPivot]) => (
          <CenterPivot
            key={`${centerPivot.center?.id?.id}-${zoneId}`}
            centerPivot={centerPivot}
          />
        ))}
      <Source
        key="farm-points"
        id="farm-points"
        type="geojson"
        data={farmMapData.pointsGeojson}
      >
        {focusedPointId && (
          <Layer
            beforeId="gps-points"
            id={"farm-focused-point"}
            type={"circle"}
            paint={{
              "circle-radius": 16,
              "circle-color": theme.colors.carbon.map.farms.focused,
              "circle-opacity": 0.75,
            }}
            filter={focusedPointFilter}
          />
        )}
        {selectedPointId && (
          <Layer
            id={"farm-selected-point-outline"}
            beforeId="gps-points"
            type={"circle"}
            paint={{
              "circle-radius": 16,
              "circle-color": theme.colors.black,
              "circle-opacity": 1,
            }}
            filter={selectedPointFilter}
          />
        )}
        {selectedPointId && (
          <Layer
            id={"farm-selected-point"}
            beforeId="gps-points"
            type={"circle"}
            paint={{
              "circle-radius": 14,
              "circle-color": theme.colors.blue[600],
              "circle-opacity": 1,
            }}
            filter={selectedPointFilter}
          />
        )}
      </Source>
      <>
        <MarkerLayer
          id="rtk-fixed-points"
          iconName={MapImageId.RtkFixed}
          featureCollection={rtkFixedPoints}
          filter={focusedOrSelectedOrShownFilter}
        />
        <MarkerLayer
          beforeId="rtk-fixed-points"
          id="rtk-float-points"
          iconName={MapImageId.RtkFloat}
          featureCollection={rtkFloatPoints}
          filter={focusedOrSelectedOrShownFilter}
        />
        <MarkerLayer
          beforeId="rtk-float-points"
          id="gps-points"
          iconName={MapImageId.GpsPoint}
          featureCollection={gpsPoints}
          filter={focusedOrSelectedOrShownFilter}
        />
      </>
    </>
  );
};
