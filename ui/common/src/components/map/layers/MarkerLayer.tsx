import { FeatureCollection } from "geojson";
import { Layer, Source } from "react-map-gl";
import { MapImageId } from "./types";
import React, { FC } from "react";

interface MarkerLayerProps {
  featureCollection: FeatureCollection;
  id: string;
  beforeId?: string;
  iconName: MapImageId;
  filter?: any;
}

export const MarkerLayer: FC<MarkerLayerProps> = ({
  featureCollection,
  id,
  beforeId,
  iconName,
  filter,
}) => {
  return (
    <Source key={id} id={id} type="geojson" data={featureCollection}>
      <Layer
        beforeId={beforeId}
        id={id}
        type="symbol"
        layout={{
          "icon-image": iconName,
          "icon-size": 0.08,
          "icon-allow-overlap": true,
        }}
        filter={filter}
      />
    </Source>
  );
};
