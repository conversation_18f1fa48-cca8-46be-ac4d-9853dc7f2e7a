import { Expression } from "mapbox-gl";
import {
  getObjectiveFeature,
  getRowMapData,
  getTaskFeature,
  ObjectiveFeature,
  TaskFeature,
} from "common/utils/rtcJobs";
import {
  InteractionLayer,
  LayerConfig,
  MapImageId,
} from "common/components/map/layers/types";
import { Layer, Source } from "react-map-gl";
import { Objective, ObjectiveAssignment, State, Task } from "protos/rtc/jobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import React, { FC, useMemo } from "react";

interface ObjectiveLayersProps {
  objectives: Objective[];
  tasks: Task[];
  rowWidthMeters?: number;
  focusedObjective?: number;
  selectedObjective?: number;
  focusedTractor?: string;
  highlightedObjectives?: number[];
  hiddenTractors?: string[];
  assignmentsByTractorSerial: Record<string, ObjectiveAssignment[]>;
  colorsBySerial: Record<string, string>;
  beforeId?: string;
  showTasks?: boolean;
}

export const ObjectiveLayers: FC<ObjectiveLayersProps> = ({
  objectives,
  tasks,
  rowWidthMeters,
  focusedObjective,
  selectedObjective,
  highlightedObjectives,
  focusedTractor,
  hiddenTractors,
  assignmentsByTractorSerial,
  colorsBySerial,
  beforeId,
  showTasks,
}) => {
  const colorsByState: Record<State, string> = {
    [State.ACKNOWLEDGED]: theme.colors.gray[500],
    [State.CANCELLED]: theme.colors.orange[500],
    [State.COMPLETED]: theme.colors.green[500],
    [State.FAILED]: theme.colors.red[500],
    [State.IN_PROGRESS]: theme.colors.blue[500],
    [State.NEW]: theme.colors.gray[500],
    [State.PAUSED]: theme.colors.blue[500],
    [State.PENDING]: theme.colors.gray[400],
    [State.READY]: theme.colors.gray[500],
    [State.STATE_UNSPECIFIED]: theme.colors.gray[500],
    [State.UNRECOGNIZED]: theme.colors.gray[500],
  };
  const opacityByState: Record<State, number> = {
    [State.ACKNOWLEDGED]: 0.5,
    [State.CANCELLED]: 1,
    [State.COMPLETED]: 1,
    [State.FAILED]: 1,
    [State.IN_PROGRESS]: 1,
    [State.NEW]: 0.5,
    [State.PAUSED]: 1,
    [State.PENDING]: 0.5,
    [State.READY]: 0.5,
    [State.STATE_UNSPECIFIED]: 0.5,
    [State.UNRECOGNIZED]: 0.5,
  };

  const { aPoints, bPoints, rowLines, rowBoxes, taskBoxes } = useMemo(
    () =>
      getRowMapData({
        rowWidthMeters,
        objectives,
        tasks,
      }),
    [objectives, tasks, rowWidthMeters]
  );
  const colorsByObjectiveId: Record<string, string> = useMemo(() => {
    const colorsByObj: Record<string, string> = {};
    for (const [serial, assignments] of Object.entries(
      assignmentsByTractorSerial
    )) {
      const color = colorsBySerial[serial] ?? "#FF00DD";
      for (const assignment of assignments) {
        colorsByObj[assignment.objectiveId] = color;
      }
    }
    return colorsByObj;
  }, [assignmentsByTractorSerial, colorsBySerial]);

  const noColorObjectiveIds: (number | undefined)[] = useMemo(
    () =>
      hiddenTractors
        ? hiddenTractors
            .flatMap((t) => assignmentsByTractorSerial[t])
            .flatMap((a) => a?.objectiveId ?? [])
        : [],
    [hiddenTractors, assignmentsByTractorSerial]
  );

  const focusedTractorObjectiveIds: number[] = useMemo(() => {
    if (!focusedTractor) {
      return [];
    }
    const assignments = assignmentsByTractorSerial[focusedTractor];
    if (!assignments) {
      return [];
    }

    return assignments.map((a) => a.objectiveId);
  }, [focusedTractor, assignmentsByTractorSerial]);

  const mapBoxGetProps: Expression = ["get", "objective"];
  const mapBoxGetStateColor: Expression = [
    "get",
    ["to-string", ["get", "state", ["get", "task"]]],
    ["literal", colorsByState],
  ];
  const mapboxGetStateOpacity: Expression = [
    "get",
    ["to-string", ["get", "state", ["get", "task"]]],
    ["literal", opacityByState],
  ];
  const mapboxGetColor: Expression = [
    "get",
    ["to-string", ["get", "id", mapBoxGetProps]],
    ["literal", colorsByObjectiveId],
  ];
  const mapboxGetOpacity: Expression = [
    "case",
    [
      "to-boolean",
      ["in", ["get", "id", mapBoxGetProps], ["literal", noColorObjectiveIds]],
    ],
    0.01,
    ["to-boolean", mapboxGetColor],
    0.4,
    0.01,
  ];

  const firstHighlightedObjectiveFilter = [
    "==",
    ["get", "id", mapBoxGetProps],
    ["at", 0, ["literal", highlightedObjectives]],
  ];

  const mapBoxTractorFocusFilter = [
    "in",
    ["get", "id", mapBoxGetProps],
    ["literal", focusedTractorObjectiveIds],
  ];

  return (
    <>
      <Source data={bPoints} type="geojson">
        <Layer
          id="row-numbers-b"
          beforeId={beforeId ?? undefined}
          type="symbol"
          paint={{
            "text-color": ["coalesce", mapboxGetColor, "#FFF"],
            "text-halo-color": theme.colors.black,
            "text-halo-width": 2,
          }}
          layout={{ "text-field": ["get", "rowNum", mapBoxGetProps] }}
        />
      </Source>
      <Source data={aPoints} type="geojson">
        <Layer
          id="row-numbers-a"
          beforeId={"row-numbers-b"}
          type="symbol"
          paint={{
            "text-color": ["coalesce", mapboxGetColor, "#FFF"],
            "text-halo-color": theme.colors.black,
            "text-halo-width": 2,
          }}
          layout={{ "text-field": ["get", "rowNum", mapBoxGetProps] }}
        />
      </Source>
      <Source data={rowLines} type="geojson">
        <Layer
          id={"row-centerlines"}
          beforeId={"row-numbers-a"}
          type="line"
          minzoom={16}
          maxzoom={24}
          paint={{
            "line-color": theme.colors.black,
            "line-dasharray": [16, 8],
          }}
        />
        {highlightedObjectives && highlightedObjectives.length > 0 && (
          <Layer
            id={"row-centerlines-highlighted"}
            beforeId={"row-numbers-a"}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={[
              "in",
              ["get", "id", mapBoxGetProps],
              ["literal", highlightedObjectives],
            ]}
          />
        )}
        {highlightedObjectives && highlightedObjectives.length > 0 && (
          <Layer
            id={"row-centerlines-highlighted-first"}
            beforeId={"row-numbers-a"}
            type="line"
            paint={{
              "line-color": theme.colors.white,
              "line-width": 2,
            }}
            filter={firstHighlightedObjectiveFilter}
          />
        )}
      </Source>
      <Source data={rowBoxes} type="geojson">
        <Layer
          id="objectives-row-outline"
          beforeId="row-centerlines"
          minzoom={16}
          maxzoom={24}
          type="line"
          paint={{
            "line-color": theme.colors.black,
          }}
        />
        <Layer
          id="objectives-layer"
          beforeId={"row-centerlines"}
          type="fill"
          paint={{
            "fill-color": ["coalesce", mapboxGetColor, "#000"],
            "fill-opacity": mapboxGetOpacity,
          }}
        />
        {focusedObjective && (
          <Layer
            id={"focused-objective"}
            beforeId={"row-centerlines"}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[400],
              "fill-opacity": 0.6,
            }}
            filter={["==", ["get", "id", mapBoxGetProps], focusedObjective]}
          />
        )}
        {selectedObjective && (
          <Layer
            id={"selected-objective"}
            beforeId={"row-centerlines"}
            type="line"
            paint={{
              "line-color": theme.colors.blue[600],
              "line-width": 2,
            }}
            filter={["==", ["get", "id", mapBoxGetProps], selectedObjective]}
          />
        )}
        {selectedObjective && (
          <Layer
            id={"selected-objective-outline"}
            beforeId={"row-centerlines"}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[500],
              "fill-opacity": 0.2,
            }}
            filter={["==", ["get", "id", mapBoxGetProps], selectedObjective]}
          />
        )}
        {focusedTractor && (
          <Layer
            id={"tractor-hover-objectives"}
            beforeId={beforeId ?? undefined}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={mapBoxTractorFocusFilter}
          />
        )}
        {highlightedObjectives && highlightedObjectives.length > 0 && (
          <Layer
            id={"highlighted-objectives"}
            beforeId={beforeId ?? undefined}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={[
              "in",
              ["get", "id", mapBoxGetProps],
              ["literal", highlightedObjectives],
            ]}
          />
        )}
        {highlightedObjectives && highlightedObjectives.length > 0 && (
          <Layer
            id={"first-highlighted-objective"}
            beforeId={"objectives-layer"}
            type="line"
            paint={{
              "line-blur": 8,
              "line-color": theme.colors.white,
              "line-width": 16,
            }}
            filter={firstHighlightedObjectiveFilter}
          />
        )}
      </Source>
      {showTasks && (
        <Source data={taskBoxes} type="geojson">
          <Layer
            id="tasks-layer"
            beforeId={"row-centerlines"}
            type="fill"
            maxzoom={24}
            minzoom={16}
            paint={{
              "fill-color": ["coalesce", mapBoxGetStateColor, "#FFF"],
              "fill-opacity": mapboxGetStateOpacity,
            }}
          />
          <Layer
            id="tasks-outlines-layer"
            beforeId={"row-centerlines"}
            maxzoom={24}
            minzoom={16}
            type="line"
            paint={{
              "line-color": theme.colors.black,
            }}
          />
        </Source>
      )}
    </>
  );
};

export const useObjectiveLayers = ({
  onClick,
  onEnter,
  onLeave,
}: {
  onClick?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onEnter?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onLeave?: () => void;
}): LayerConfig<ObjectiveLayersProps> => {
  const interactionLayers: InteractionLayer[] = [
    {
      cursor: "pointer",
      id: "objectives-layer",
      onMouseMove: (f) => onEnter?.(getObjectiveFeature(f)),
      onLeave: () => onLeave?.(),
      onClick: (f) => onClick?.(getObjectiveFeature(f)),
    },
    {
      cursor: "pointer",
      id: "tasks-layer",
      onMouseMove: (f) => onEnter?.(getTaskFeature(f)),
      onLeave: () => onLeave?.(),
      onClick: (f) => onClick?.(getTaskFeature(f)),
    },
  ];
  const Layers = ObjectiveLayers;
  const imagesToLoad: MapImageId[] = [];

  return { interactionLayers, Layers, idsToLoad: imagesToLoad };
};
