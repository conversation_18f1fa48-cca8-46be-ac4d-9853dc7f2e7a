import {
  AutotractorPropsType,
  Domain,
  InteractionLayer,
  LayerConfig,
} from "common/components/map/layers/types";
import { createRobotOutline } from "common/components/map/RobotOutline";
import {
  getTractorFeature,
  TRACTOR_POLL_INTERVAL_S,
  TRACTOR_STALE_THRESHOLD_S,
  TractorFeature,
} from "common/utils/rtcJobs";
import { Layer, Marker, Source } from "react-map-gl";
import {
  LiveRtcTractorResponse,
  useStalePercentage,
} from "common/hooks/useLiveRtcLocation";
import { NumberedTractorIcon } from "common/components/map/NumberedTractorIcon";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import { Tooltip } from "@mui/material";
import Color from "color";
import NavigationIcon from "@mui/icons-material/Navigation";
import React, { FC } from "react";

export const useTractorLayers = ({
  serials,
  onClick,
  onEnter,
  onLeave,
}: {
  serials?: string[];
  onClick?: (feature?: TractorFeature) => void;
  onEnter?: (feature?: TractorFeature) => void;
  onLeave?: (feature?: TractorFeature) => void;
}): LayerConfig<TractorLayersProps> => {
  const interactionLayers: InteractionLayer[] = serials
    ? serials.map((serial) => ({
        id: `tractor-${serial}-layer`,
        cursor: "pointer",
        onClick: (e) => onClick?.(getTractorFeature(e)),
        onMouseMove: (e) => onEnter?.(getTractorFeature(e)),
        onLeave: () => onLeave?.(),
      }))
    : [];
  return { interactionLayers, Layers: TractorLayers, idsToLoad: [] };
};
interface TractorLayersProps {
  serial: string;
  liveData: LiveRtcTractorResponse;
  color?: string;
  isFocused?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  beforeId?: string;
}

export const TractorLayers: FC<TractorLayersProps> = ({
  serial,
  liveData,
  color,
  onClick,
  isFocused,
  isSelected,
  beforeId,
}) => {
  const { location, isMoving, isStale, lastCheckedTimestamp } = liveData;

  const stalePercentage = useStalePercentage(
    TRACTOR_POLL_INTERVAL_S,
    TRACTOR_STALE_THRESHOLD_S,
    lastCheckedTimestamp
  );

  const outlinePolygon =
    location?.point && location.headingDegrees
      ? createRobotOutline(
          location.point.lng,
          location.point.lat,
          location.headingDegrees
        )
      : undefined;

  const outlineFeature: TractorFeature | undefined = outlinePolygon
    ? {
        type: "Feature",
        geometry: outlinePolygon,
        properties: {
          __portalFeatureProps: true,
          domain: Domain.AUTOTRACTOR,
          type: AutotractorPropsType.TRACTOR,
          serial,
        },
      }
    : undefined;

  const desaturatedColor = new Color(color)
    .desaturate(isStale ? 1 : Math.min(stalePercentage, 1))
    .toString();

  return (
    <div>
      <Source data={outlineFeature} type="geojson">
        {location?.point && (
          <Marker
            style={{ cursor: "pointer" }}
            key={serial}
            latitude={location.point.lat}
            longitude={location.point.lng}
            onClick={onClick}
          >
            <Tooltip title={serial} arrow>
              <div className="relative">
                {isMoving && (
                  <NavigationIcon
                    fontSize="small"
                    className="absolute font-extrabold"
                    style={{
                      left: "50%",
                      top: "50%",
                      fill: color,
                      stroke: theme.colors.black,
                      strokeWidth: 2,
                      transform: `translate(-50%, -50%) rotate(${location.headingDegrees}deg) translateY(-24px)`,
                    }}
                  />
                )}
                <NumberedTractorIcon serial={serial} color={desaturatedColor} />
              </div>
            </Tooltip>
          </Marker>
        )}
        {isFocused && (
          <Layer
            type="fill"
            id={`tractor-${serial}-layer-hover`}
            beforeId={beforeId ?? undefined}
            paint={{
              "fill-opacity": 0.5,
              "fill-color": theme.colors.blue[400],
            }}
          />
        )}
        <Layer
          type="fill"
          id={`tractor-${serial}-layer`}
          beforeId={`tractor-${serial}-outline-top`}
          paint={{
            "fill-color": color ?? theme.colors.black,
          }}
        />
        <Layer
          id={`tractor-${serial}-outline`}
          type="line"
          beforeId={`tractor-${serial}-layer`}
          paint={{
            "line-color": theme.colors.black,
            "line-width": 4,
          }}
        />
        <Layer
          id={`tractor-${serial}-outline-top`}
          type="line"
          beforeId={beforeId ?? undefined}
          paint={{
            "line-color": theme.colors.black,
            "line-width": 2,
            "line-dasharray": [1, 1],
          }}
        />
        {(isFocused || isSelected) && (
          <Layer
            id={`tractor-${serial}-outline-2`}
            type="line"
            beforeId={`tractor-${serial}-outline`}
            paint={{
              "line-color": isSelected
                ? theme.colors.blue[400]
                : theme.colors.blue[600],
              "line-width": 8,
            }}
          />
        )}
      </Source>
    </div>
  );
};
