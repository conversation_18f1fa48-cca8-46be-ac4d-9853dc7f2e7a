import { FarmFeature, FarmFeatureProperties } from "common/utils/farm";
import { Feature } from "geojson";
import { JobFeature, JobFeatureProperties } from "common/utils/rtcJobs";
import React from "react";

export enum MapImageId {
  GpsPoint = "gps-point",
  RtkFixed = "rtk-fixed",
  RtkFloat = "rtk-float",
}

/**
 * a MapImageResolver is a function that takes a MapImageId and can produce
 * a URL that Mapbox can XHR to load it.
 */
export type MapImageResolver = (id: MapImageId) => string;

export interface LayerConfig<T> {
  interactionLayers: InteractionLayer[];
  idsToLoad: MapImageId[];
  Layers: React.FC<T>;
}

export interface InteractionLayer {
  id: string;
  cursor?: string;
  onClick?: (feature?: PortalFeature) => void;
  onEnter?: (feature?: PortalFeature) => void;
  onLeave?: (feature?: PortalFeature) => void;
  onMouseMove?: (feature?: PortalFeature) => void;
}

export type AutotractorProps = FarmFeatureProperties | JobFeatureProperties;

export enum AutotractorPropsType {
  POINT = "point",
  ZONE = "zone",
  PIVOT = "pivot",
  TRACTOR = "tractor",
  OBJECTIVE = "objective",
  TASK = "task",
}

export interface AutotractorBaseProps extends PortalFeatureBaseProps {
  domain: Domain.AUTOTRACTOR;
  type: AutotractorPropsType;
}

export enum Domain {
  AUTOTRACTOR = "at",
  LASERWEEDER = "lw",
}

export interface PortalFeatureBaseProps {
  __portalFeatureProps: true;
  domain: Domain;
}

export type AutotractorFeature = FarmFeature | JobFeature;

export type PortalFeature = AutotractorFeature;

export type PortalFeatureProps = AutotractorProps;

export const arePortalFeatureProps = (x?: unknown): x is PortalFeatureProps => {
  return (x as any)?.__portalFeatureProps === true;
};

export const isPortalFeature = (x?: Feature): x is PortalFeature => {
  const props = arePortalFeatureProps(x?.properties);
  return Boolean(x && props);
};
