import { OverridableComponent } from "@mui/material/OverridableComponent";
import { SvgIconTypeMap } from "@mui/material";
import React from "react";

/**
 * A React component the shape of (say) an SVGR import.  Used to create common
 * components which take per-consumer imported icons as props.
 */
export type SvgIconComponent = React.FC<
  React.SVGProps<SVGSVGElement> & { title?: string }
>;

/**
 * A React component of the same shape that is exported from MUI. This is distinct from SVGIconComponent.
 */
export type MuiIcon = OverridableComponent<SvgIconTypeMap> & {
  muiName: string;
};
