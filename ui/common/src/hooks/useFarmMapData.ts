import {
  AutotractorPropsType,
  Domain,
} from "common/components/map/layers/types";
import { CenterPivot, Farm, Zone } from "protos/portal/farm";
import {
  derefPoint,
  FarmMapData,
  getFarmBounds,
  NoSuchPoint,
  PointFeatureProperties,
  ZoneFeatureProperties,
} from "common/utils/farm";
import {
  Feature,
  FeatureCollection,
  Geometry,
  GeometryCollection,
  Point as GeoPoint,
  LineString,
  Position,
} from "geojson";
import { featureCollection } from "@turf/helpers";
import { Point } from "protos/geo/geo";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import { useMemo } from "react";

type LngLatAlt = [lng: number, lat: number, alt: number];

const derefCoordinates = (
  pointsById: Record<string, Point>,
  reference: Point
): LngLatAlt => {
  const referent = derefPoint(pointsById, reference);
  return [referent.lng, referent.lat, referent.alt];
};

export function useFarmMapData(farm: undefined): undefined;
export function useFarmMapData(farm: Farm): FarmMapData;
export function useFarmMapData(farm: Farm | undefined): FarmMapData | undefined;
export function useFarmMapData(
  farm: Farm | undefined
): FarmMapData | undefined {
  return useMemo(() => {
    if (!farm) {
      return;
    }

    const pointIdToZoneIds = new Map<string, string[]>();
    const pointsById: Record<string, Point> = {};
    for (const { point } of farm.pointDefs) {
      if (!point || !point.id) {
        continue;
      }
      pointsById[point.id.id] = point;
    }
    const zonesById = new Map<string, Zone>();

    const derefCoords = (reference: Point): LngLatAlt =>
      derefCoordinates(pointsById, reference);

    const centerPivotsByFieldId = new Map<string, CenterPivot>();
    const plantingHeadingsByFieldId = new Map<string, LineString>();
    const obstacles = featureCollection<Geometry, ZoneFeatureProperties>([]);
    const fields = featureCollection<Geometry, ZoneFeatureProperties>([]);
    const headlands = featureCollection<Geometry, ZoneFeatureProperties>([]);
    const roads = featureCollection<Geometry, ZoneFeatureProperties>([]);
    const farmBoundary = featureCollection<Geometry, ZoneFeatureProperties>([]);
    for (const zone of farm.zones) {
      const { contents, areas } = zone;
      const id = zone.id?.id;
      if (id) {
        zonesById.set(id, zone);
      }

      const geometries: Geometry[] = areas.flatMap(
        (area, index): Geometry[] => {
          const { point, lineString, polygon } = area;
          try {
            if (point) {
              const coordinates = derefCoords(point);
              if (point.id?.id && zone.id) {
                pointIdToZoneIds.set(point.id.id, [
                  ...(pointIdToZoneIds.get(point.id.id) ?? []),
                  zone.id.id,
                ]);
              }
              return [{ type: "Point", coordinates }];
            } else if (lineString) {
              const coordinates = lineString.points.map((p) => {
                if (p.id?.id && zone.id) {
                  pointIdToZoneIds.set(p.id.id, [
                    ...(pointIdToZoneIds.get(p.id.id) ?? []),
                    zone.id.id,
                  ]);
                }
                return derefCoords(p);
              });
              return [{ type: "LineString", coordinates }];
            } else if (polygon) {
              const { boundary, holes } = polygon;
              if (!boundary) {
                return [];
              }
              const pointToCoord = (p: Point): LngLatAlt => {
                if (p.id?.id && zone.id) {
                  pointIdToZoneIds.set(p.id.id, [
                    ...(pointIdToZoneIds.get(p.id.id) ?? []),
                    zone.id.id,
                  ]);
                }
                return derefCoords(p);
              };
              const coordinates: Position[][] = [];
              coordinates.push(boundary.points.map((p) => pointToCoord(p)));
              for (const hole of holes) {
                coordinates.push(hole.points.map((p) => pointToCoord(p)));
              }

              return [{ type: "Polygon", coordinates }];
            } else {
              return [];
            }
            // not called "error" because it's not an `Error`; it is used only
            // for control flow
            // eslint-disable-next-line unicorn/catch-error-name
          } catch (e) {
            if (!(e instanceof NoSuchPoint)) {
              throw e;
            }
            console.warn(
              `Invalid point reference in zone ${zone.id?.id} version ${zone.version?.ordinal}, areas[${index}]:`,
              e.point
            );
            return [];
          }
        }
      );

      const feature: Feature<GeometryCollection, ZoneFeatureProperties> = {
        type: "Feature",
        id,
        geometry: { type: "GeometryCollection", geometries },
        properties: {
          __portalFeatureProps: true,
          domain: Domain.AUTOTRACTOR,
          type: AutotractorPropsType.ZONE,
          zoneId: id,
        },
      };
      let color = theme.colors.carbon.map.farms.default;
      if (contents) {
        if (contents.field) {
          color = theme.colors.carbon.map.farms.field;
          // field with a center point
          if (id && contents.field.centerPivot?.center?.id?.id) {
            const center = derefPoint(
              pointsById,
              contents.field.centerPivot.center
            );
            const centerPivot = {
              ...contents.field.centerPivot,
              center,
            };
            centerPivotsByFieldId.set(id, centerPivot);
          }

          // Extract the planting heading line from the field
          if (id && contents.field.plantingHeading?.abLine) {
            const abLine = contents.field.plantingHeading.abLine;
            if (abLine.a && abLine.b) {
              const plantingHeading: LineString = {
                type: "LineString",
                coordinates: [derefCoords(abLine.a), derefCoords(abLine.b)],
              };
              plantingHeadingsByFieldId.set(id, plantingHeading);
            }
          }

          fields.features.push(feature);
        } else if (contents.obstacle) {
          color = theme.colors.carbon.map.farms.obstacle;
          obstacles.features.push(feature);
        } else if (contents.headland) {
          color = theme.colors.carbon.map.farms.headland;
          headlands.features.push(feature);
        } else if (contents.privateRoad) {
          color = theme.colors.carbon.map.farms.roads;
          roads.features.push(feature);
        } else if (contents.farmBoundary) {
          color = theme.colors.carbon.map.farms.boundary;
          farmBoundary.features.push(feature);
        }
      }
      feature.properties.zoneColor = color;
    }
    const areasGeojson = featureCollection<Geometry, ZoneFeatureProperties>([
      // as a side effect, this orders the layers
      // use individual collections for custom ordering.
      ...farmBoundary.features,
      ...headlands.features,
      ...fields.features,
      ...obstacles.features,
      ...roads.features,
    ]);

    const pointsGeojson: FeatureCollection<GeoPoint, PointFeatureProperties> =
      featureCollection(
        Array.from(Object.entries(pointsById), ([id, point]) => ({
          type: "Feature",
          id,
          geometry: {
            type: "Point",
            coordinates: [point.lng, point.lat, point.alt],
          },
          properties: {
            __portalFeatureProps: true,
            domain: Domain.AUTOTRACTOR,
            type: AutotractorPropsType.POINT,
            pointId: id,
          },
        }))
      );

    const bounds = getFarmBounds(farm, pointsById);

    return {
      id: farm.id,
      pointsById,
      centerPivotsByFieldId,
      plantingHeadingsByFieldId,
      areasGeojson,
      pointsGeojson,
      bounds,
      zonesById,
      pointIdToZoneIds,
      fields,
      headlands,
      roads,
      obstacles,
      farmBoundary,
    };
  }, [farm]);
}
