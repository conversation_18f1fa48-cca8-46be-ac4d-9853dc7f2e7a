import { useEffect, useRef } from "react";

// Exposes the latest version of a value as a ref so that it can be used
// from an event handler context.
//
// If you need to use this for a value that's stored in a state cell
// rather than a memo cell, consider instead wrapping the state cell's
// setter to update a ref, to avoid additional render cycles on change.
export const useLatest = <T>(value: T): React.MutableRefObject<T> => {
  const ref = useRef(value);
  useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref;
};
