import { DateTime } from "luxon";
import { DeviceLocationHistoryRecord } from "protos/rtc/device_location_history";
import { LocationHistoryRecord } from "protos/rtc/location_history";
import { setDeviceData, setTractorData } from "common/state/jobExplorer";
import {
  SubscriptionDeviceType,
  SubscriptionMessage,
} from "protos/rtc/web_socket";
import { useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { useLocatorWebSocket } from "./useLocatorWebsocket";

export interface LiveRtcTractorsResponse {
  serialToData: Record<string, LiveRtcTractorResponse>;
}
export interface LiveRtcTractorResponse extends LiveRtcResponse {
  location: LocationHistoryRecord | undefined;
}

export interface LiveRtcDeviceResponse extends LiveRtcResponse {
  location: DeviceLocationHistoryRecord | undefined;
}

export interface LiveRtcResponse {
  isStale: boolean;
  isMoving: boolean;
  lastCheckedTimestamp: string | null;
}

export const useDeviceLocations = (
  deviceIds: string[],
  onLocationUpdate?: (
    resp: DeviceLocationHistoryRecord & { id: string }
  ) => void
): void => {
  const dispatch = useDispatch();
  const { authenticated, subscribe } = useLocatorWebSocket({
    onLocationUpdate: (m: SubscriptionMessage) => {
      const deviceId = m.location?.device?.deviceId;
      const data = m.location?.deviceRec;
      if (deviceId && data) {
        dispatch(setDeviceData({ deviceId, data }));
        onLocationUpdate?.({
          id: deviceId,
          ...data,
        });
      }
    },
  });

  useEffect(() => {
    if (authenticated) {
      for (const deviceId of deviceIds) {
        subscribe(deviceId, SubscriptionDeviceType.DEVICE);
      }
    }
  }, [authenticated, subscribe, deviceIds]);
};

export const useTractorLocations = (
  serials: string[],
  onLocationUpdate?: (resp: LocationHistoryRecord & { serial: string }) => void
): void => {
  const dispatch = useDispatch();
  const { authenticated, subscribe } = useLocatorWebSocket({
    onLocationUpdate: (m: SubscriptionMessage) => {
      const serial = m.location?.device?.serial;
      const data = m.location?.robotRec;
      if (serial && data) {
        dispatch(setTractorData({ serial, data }));
        onLocationUpdate?.({
          serial,
          ...data,
        });
      }
    },
  });

  useEffect(() => {
    if (authenticated) {
      for (const serial of serials) {
        subscribe(serial, SubscriptionDeviceType.ROBOT);
      }
    }
  }, [authenticated, subscribe, serials]);
};

export const useStalePercentage = (
  pollIntervalSeconds: number,
  staleThresholdSeconds: number,
  lastCheckedTimestamp: string | null
): number => {
  const [now, setNow] = useState<DateTime>(DateTime.now());
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(DateTime.now());
    }, (pollIntervalSeconds / staleThresholdSeconds) * 1000);
    return () => {
      clearInterval(interval);
    };
  }, [pollIntervalSeconds, staleThresholdSeconds]);
  return lastCheckedTimestamp
    ? (now.toSeconds() - DateTime.fromISO(lastCheckedTimestamp).toSeconds()) /
        staleThresholdSeconds
    : 1;
};
