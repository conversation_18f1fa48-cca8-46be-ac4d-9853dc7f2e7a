import { QueryEnv } from "common/state/queryEnv";
import {
  SubscriptionDeviceType,
  SubscriptionMessage,
  SubscriptionRequest,
  SubscriptionRequest_Type as SubscriptionRequestType,
  WebSocketAuthenticateRequest,
  WebSocketResponse,
  WebSocketStatus,
} from "protos/rtc/web_socket";
import { useCallback, useEffect, useState } from "react";
import { useSharedWebsocket } from "common/hooks/useSharedWebsocket";
import { useStore } from "react-redux";

const isSubscriptionMessage = (m: any): m is SubscriptionMessage =>
  "location" in m && m.location !== undefined;

const isWebSocketResponse = (m: any): m is WebSocketResponse =>
  "status" in m && "message" in m;

interface UseLocatorWebSocketOptions {
  onLocationUpdate?: (message: SubscriptionMessage) => void;
}

interface UseLocatorWebSocketReturn {
  // true when our custom handshake process is complete
  authenticated: boolean;
  // subscribe to a device or tractor's location updates
  // locator should send the last known location and then any updates
  subscribe: (serial: string, deviceType: SubscriptionDeviceType) => void;
}

export const useLocatorWebSocket = ({
  onLocationUpdate,
}: UseLocatorWebSocketOptions): UseLocatorWebSocketReturn => {
  const [authenticated, setAuthenticated] = useState(false);
  const { getState } = useStore();

  const onMessage = useCallback(
    (m: SubscriptionMessage | WebSocketResponse) => {
      if (isWebSocketResponse(m)) {
        const message = WebSocketResponse.fromJSON(m);
        if (
          message.status === WebSocketStatus.OK &&
          message.message === "authenticated"
        ) {
          setAuthenticated(true);
        }
      } else if (isSubscriptionMessage(m) && onLocationUpdate) {
        const message = SubscriptionMessage.fromJSON(m);
        onLocationUpdate(message);
      }
    },
    [onLocationUpdate]
  );

  const { sendMessage, isConnected } = useSharedWebsocket<
    WebSocketAuthenticateRequest | SubscriptionRequest,
    SubscriptionMessage | WebSocketResponse
  >(`${QueryEnv.get().rtcLocatorBaseUrl}/ws/v1/event_subscription`, {
    onMessage,
  });

  useEffect(() => {
    if (isConnected && !authenticated) {
      QueryEnv.get()
        .accessTokenProvider({ getState })
        .then((token: string | undefined) => {
          if (token) {
            sendMessage({
              token,
              audience: QueryEnv.get().auth0Audience,
            });
          }
        });
    }
  }, [isConnected, authenticated, sendMessage, getState]);

  const subscribe = useCallback(
    (serial: string, deviceType: SubscriptionDeviceType) => {
      if (authenticated && isConnected) {
        const params = {
          serial:
            deviceType === SubscriptionDeviceType.ROBOT ? serial : undefined,
          deviceId:
            deviceType === SubscriptionDeviceType.DEVICE ? serial : undefined,
        };
        sendMessage({
          type: SubscriptionRequestType.SUBSCRIBE,
          device: { type: deviceType, ...params },
        });
      }
    },
    [authenticated, isConnected, sendMessage]
  );

  useEffect(() => {
    if (!isConnected) {
      setAuthenticated(false);
    }
  }, [isConnected]);

  return { authenticated, subscribe };
};
