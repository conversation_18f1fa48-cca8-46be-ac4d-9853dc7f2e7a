import { useCallback, useContext, useEffect, useRef, useState } from "react";
import { useLatest } from "./useLatest";
import { WebSocketContext } from "common/components/WebSocketProvider";
import ReconnectingWebSocket from "reconnecting-websocket";

export interface WebsocketState<TSend> {
  // maps to https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/readyState
  readyState: number;
  // shorthand for whether the websocket is connected
  isConnected: boolean;
  // send a message on the websocket connection
  sendMessage: (data: TSend) => void;
}

export function useSharedWebsocket<TSend = any, TRecv = any>(
  url: string,
  options?: { onMessage?: (data: TRecv) => void }
): WebsocketState<TSend> {
  const socketRef = useRef<ReconnectingWebSocket>();
  const sharedWebsocket = useContext(WebSocketContext);
  const [readyState, setReadyState] = useState<number>(WebSocket.CLOSED);
  const onMessageRef = useLatest(options?.onMessage);

  useEffect(() => {
    const { socket, unsubscribe } = sharedWebsocket.subscribe(url);
    socketRef.current = socket;

    const handleReadyState = (): void => setReadyState(socket.readyState);
    const handleMessage = (event: MessageEvent): void => {
      if (onMessageRef.current) {
        try {
          onMessageRef.current(JSON.parse(event.data));
        } catch {
          console.error(
            `Invalid JSON received from websocket - dropping message: ${event.data}`
          );
        }
      }
    };
    const handleErrorState = (error: any): void => {
      console.error(error);
      handleReadyState();
    };

    socket.addEventListener("open", handleReadyState);
    socket.addEventListener("close", handleReadyState);
    socket.addEventListener("error", handleErrorState);
    socket.addEventListener("message", handleMessage);

    // Set initial state
    handleReadyState();

    return () => {
      socket.removeEventListener("open", handleReadyState);
      socket.removeEventListener("close", handleReadyState);
      socket.removeEventListener("error", handleErrorState);
      socket.removeEventListener("message", handleMessage);
      unsubscribe();
    };
  }, [url, onMessageRef, sharedWebsocket]);

  const sendMessage = useCallback((data: TSend) => {
    const socket = socketRef.current;
    if (!socket) {
      return;
    }
    socket.send(JSON.stringify(data));
  }, []);

  return {
    readyState,
    isConnected: readyState === WebSocket.OPEN,
    sendMessage,
  };
}
