import {
  AVAILABLE_COLORS,
  PIVOT_GPS_JITTER_THRESHOLD_M,
  TRACTOR_GPS_JITTER_THRESHOLD_M,
} from "common/utils/rtcJobs";
import { CenterPivot, Farm, Zone } from "protos/portal/farm";
import { createSelector, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { DateTime } from "luxon";
import { DeviceLocationHistoryRecord } from "protos/rtc/device_location_history";
import { getSequence } from "common/utils/robots";
import { Job, Objective, ObjectiveAssignment, Task } from "protos/rtc/jobs";
import { LocationHistoryRecord } from "protos/rtc/location_history";
import distance from "@turf/distance";

export enum JobSelectionType {
  TASK = "task",
  ZONE = "zone",
  CENTER_PIVOT = "centerPivot",
  TRACTOR = "tractor",
  OBJECTIVE = "objective",
  JOB = "job",
}

interface ZoneSelection {
  type: JobSelectionType.ZONE;
  selection: Zone;
}
interface CenterPivotSelection {
  type: JobSelectionType.CENTER_PIVOT;
  selection: CenterPivot;
}
interface TractorSelection {
  type: JobSelectionType.TRACTOR;
  selection: { serial: string }; // we don't have a centralized autotractor entity, so we just store a serial
}
interface ObjectiveSelection {
  type: JobSelectionType.OBJECTIVE;
  selection: Objective;
}
interface TaskSelection {
  type: JobSelectionType.TASK;
  selection: Task;
}
interface JobSelection {
  type: JobSelectionType.JOB;
  selection: Job;
}

export type Selection =
  | ZoneSelection
  | CenterPivotSelection
  | TractorSelection
  | ObjectiveSelection
  | TaskSelection
  | JobSelection;

interface State {
  farm?: Farm;
  job?: Job;
  showDetails: boolean;
  hiddenTractors: string[];
  highlightedObjectives: number[];
  assignmentsByTractorSerial: Record<string, ObjectiveAssignment[]>;
  selection?: Selection;
  focus?: Selection;
  colorsBySerial: Record<string, string>;
  liveTractorData: Record<string, LocationHistoryRecord>;
  liveDeviceData: Record<string, DeviceLocationHistoryRecord>;
  tractorIsMoving: Record<string, boolean>;
  tractorIsStale: Record<string, boolean>;
  deviceIsMoving: Record<string, boolean>;
  deviceIsStale: Record<string, boolean>;
  lastPolledTimestamp: string | null;
}

const initialState: State = {
  hiddenTractors: [],
  farm: undefined,
  job: undefined,
  showDetails: false,
  assignmentsByTractorSerial: {},
  liveTractorData: {},
  liveDeviceData: {},
  lastPolledTimestamp: DateTime.now().toISO(),
  highlightedObjectives: [],
  selection: undefined,
  colorsBySerial: {},
  tractorIsMoving: {},
  tractorIsStale: {},
  deviceIsMoving: {},
  deviceIsStale: {},
};

export const jobExplorer = createSlice({
  name: "jobExplorer",
  initialState,
  reducers: {
    setFarm(state, action: PayloadAction<Farm | undefined>) {
      state.farm = action.payload;
    },
    setJob(state, action: PayloadAction<Job | undefined>) {
      state.job = action.payload;
    },
    setShowDetails(state, action: PayloadAction<boolean>) {
      state.showDetails = action.payload;
    },
    hideTractor(state, action: PayloadAction<string>) {
      state.hiddenTractors = [
        ...new Set([...state.hiddenTractors, action.payload]),
      ];
    },
    setHiddenTractors(state, action: PayloadAction<string[]>) {
      state.hiddenTractors = action.payload;
    },
    setSelection(state, action: PayloadAction<Selection | undefined>) {
      state.selection = action.payload;
      // toggles when something is (un)selected
      state.showDetails = action.payload !== undefined;

      // set highlighted objectives when a tractor is selected
      if (action.payload?.type === "tractor") {
        state.highlightedObjectives =
          state.assignmentsByTractorSerial[
            action.payload.selection.serial
          ]?.map((o) => o.objectiveId) ?? [];
      } else {
        state.highlightedObjectives = [];
      }
    },
    setAssignmentsByTractorSerial(
      state,
      action: PayloadAction<Record<string, ObjectiveAssignment[]>>
    ) {
      state.assignmentsByTractorSerial = action.payload;
    },
    setFocus(state, action: PayloadAction<Selection | undefined>) {
      state.focus = action.payload;
    },
    setTractorColor(
      state,
      action: PayloadAction<{ serial: string; color: string }>
    ) {
      state.colorsBySerial[action.payload.serial] = action.payload.color;
    },
    setHighlightedObjectives(
      state,
      action: PayloadAction<{ objectiveIds: number[] }>
    ) {
      state.highlightedObjectives = action.payload.objectiveIds;
    },
    setTractorStale(
      state,
      action: PayloadAction<{ serial: string; data: boolean }>
    ) {
      state.tractorIsStale[action.payload.serial] = action.payload.data;
    },
    setTractorMoving(
      state,
      action: PayloadAction<{ serial: string; data: boolean }>
    ) {
      state.tractorIsMoving[action.payload.serial] = action.payload.data;
    },
    setDeviceStale(
      state,
      action: PayloadAction<{ deviceId: string; data: boolean }>
    ) {
      state.deviceIsStale[action.payload.deviceId] = action.payload.data;
    },
    setDeviceMoving(
      state,
      action: PayloadAction<{ deviceId: string; data: boolean }>
    ) {
      state.deviceIsStale[action.payload.deviceId] = action.payload.data;
    },
    setTractorData(
      state,
      action: PayloadAction<{ serial: string; data: LocationHistoryRecord }>
    ) {
      const prevTractorData = state.liveTractorData[action.payload.serial];
      if (action.payload.data.point && prevTractorData?.point) {
        state.tractorIsMoving[action.payload.serial] =
          distance(
            [action.payload.data.point.lng, action.payload.data.point.lat],
            [prevTractorData.point.lng, prevTractorData.point.lat],
            { units: "meters" }
          ) > TRACTOR_GPS_JITTER_THRESHOLD_M;
      } else {
        state.tractorIsMoving[action.payload.serial] = true;
      }
      state.tractorIsStale[action.payload.serial] = false;
      state.liveTractorData[action.payload.serial] = action.payload.data;

      // if we don't have a color for this tractor, assign it a random one
      if (!(action.payload.serial in state.colorsBySerial)) {
        const i = getSequence(action.payload.serial) % AVAILABLE_COLORS.length;
        state.colorsBySerial[action.payload.serial] =
          AVAILABLE_COLORS[i] ?? "#FF0000";
      }
    },
    setDeviceData(
      state,
      action: PayloadAction<{
        deviceId: string;
        data: DeviceLocationHistoryRecord;
      }>
    ) {
      const prevDeviceData = state.liveDeviceData[action.payload.deviceId];
      if (action.payload.data.point && prevDeviceData?.point) {
        state.deviceIsMoving[action.payload.deviceId] =
          distance(
            [action.payload.data.point.lng, action.payload.data.point.lat],
            [prevDeviceData.point.lng, prevDeviceData.point.lat],
            { units: "meters" }
          ) > PIVOT_GPS_JITTER_THRESHOLD_M;
      } else {
        state.deviceIsMoving[action.payload.deviceId] = true;
      }
      state.deviceIsStale[action.payload.deviceId] = false;
      state.liveDeviceData[action.payload.deviceId] = action.payload.data;
    },
    setLastPolled(state, action: PayloadAction<DateTime>) {
      state.lastPolledTimestamp = action.payload.toISO();
    },
  },
  selectors: {
    selectFarmId: (state) => state.farm?.id?.id,
    selectLiveTractorData: (state) => state.liveTractorData,
  },
});

// Memoized selector that only updates when the specific tractor's data changes
export const selectTractorData = createSelector(
  [
    (state: { jobExplorer: State }) => state.jobExplorer.liveTractorData,
    (_state: any, serial: string) => serial,
  ],
  (liveTractorData, serial) => liveTractorData[serial]
);

export const {
  setFarm,
  setJob,
  setShowDetails,
  setSelection,
  setFocus,
  setHiddenTractors,
  hideTractor,
  setTractorColor,
  setHighlightedObjectives,
  setAssignmentsByTractorSerial,
  setTractorData,
  setDeviceData,
  setDeviceMoving,
  setDeviceStale,
  setLastPolled,
  setTractorMoving,
  setTractorStale,
} = jobExplorer.actions;

export const { selectFarmId, selectLiveTractorData } = jobExplorer.selectors;
