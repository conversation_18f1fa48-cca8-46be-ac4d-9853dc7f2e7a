import { Middleware } from "@reduxjs/toolkit";
import {
  setDeviceData,
  setDeviceMoving,
  setDeviceStale,
  setTractorData,
  setTractorMoving,
  setTractorStale,
} from "common/state/jobExplorer";
import {
  TRACTOR_IS_MOVING_THRESHOLD_S,
  TRACTOR_STALE_THRESHOLD_S,
} from "common/utils/rtcJobs";
type Timeout = ReturnType<typeof setTimeout>;

export const rtcLocatorTimeoutMiddleware: Middleware = (store) => {
  const tractorMovingTimeouts = new Map<string, Timeout>();
  const tractorStaleTimeouts = new Map<string, Timeout>();
  return (next) => (action) => {
    // eslint-disable-next-line unicorn/prefer-regexp-test
    if (setTractorData.match(action)) {
      const serial = action.payload.serial;
      const oldMovingTimeout = tractorMovingTimeouts.get(serial);
      const oldStaleTimeout = tractorStaleTimeouts.get(serial);
      clearTimeout(oldMovingTimeout);
      clearTimeout(oldStaleTimeout);

      const movingTimeout = setTimeout(() => {
        store.dispatch(setTractorMoving({ serial, data: false }));
      }, TRACTOR_IS_MOVING_THRESHOLD_S * 1000);
      const staleTimeout = setTimeout(() => {
        store.dispatch(setTractorStale({ serial, data: true }));
      }, TRACTOR_STALE_THRESHOLD_S * 1000);
      tractorMovingTimeouts.set(serial, movingTimeout);
      tractorStaleTimeouts.set(serial, staleTimeout);
    }

    const deviceMovingTimeouts = new Map<string, Timeout>();
    const deviceStaleTimeouts = new Map<string, Timeout>();
    // eslint-disable-next-line unicorn/prefer-regexp-test
    if (setDeviceData.match(action)) {
      const deviceId = action.payload.deviceId;
      const oldMovingTimeout = deviceMovingTimeouts.get(deviceId);
      const oldStaleTimeout = deviceStaleTimeouts.get(deviceId);
      clearTimeout(oldMovingTimeout);
      clearTimeout(oldStaleTimeout);

      const movingTimeout = setTimeout(() => {
        store.dispatch(setDeviceMoving({ deviceId, data: false }));
      }, TRACTOR_IS_MOVING_THRESHOLD_S * 1000);
      const staleTimeout = setTimeout(() => {
        store.dispatch(setDeviceStale({ deviceId, data: true }));
      }, TRACTOR_STALE_THRESHOLD_S * 1000);
      deviceMovingTimeouts.set(deviceId, movingTimeout);
      deviceStaleTimeouts.set(deviceId, staleTimeout);
    }
    return next(action);
  };
};
