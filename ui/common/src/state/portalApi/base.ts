import {
  BaseQueryFn,
  create<PERSON><PERSON>,
  Fetch<PERSON>rgs,
  fetchBaseQuery,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { DateTime } from "luxon";
import { makeTraceId } from "common/utils/http";
import { QueryEnv } from "../queryEnv";
import { toQuery } from "common/utils/browser";
import { values } from "common/utils/objects";

export enum Tag {
  ADMIN_ALARM = "Admin/Alarm",
  ADMIN_ALMANAC = "Admin/Almananc",
  ADMIN_CATEGORY_COLLECTION_PROFILE = "Admin/CategoryCollectionProfile",
  ADMIN_TARGET_VELOCITY_ESTIMATOR = "Admin/TargetVelocityEstimator",
  ALARM = "Alarm",
  ALMANAC = "Almananc",
  CATEGORY_COLLECTION_PROFILE = "CategoryCollectionProfile",
  CONFIG = "Config",
  CONFIG_SCHEMA = "ConfigSchema",
  CONFIG_TEMPLATE = "ConfigTemplate",
  CROP = "Crop",
  CUSTOMER = "Customer",
  DISCRIMINATOR = "Discriminator",
  FARM = "Farm",
  FIELD_DEFINITION = "FieldDefinition",
  GLOBAL = "Global",
  IMAGE = "Image",
  JOB_METRIC = "Job/Metric",
  METRIC = "Metric",
  MODEL = "Model",
  MODELINDATOR = "Modelinator",
  REPORT = "Report",
  REPORT_INSTANCE = "ReportInstance",
  ROBOT = "Robot",
  ROBOT_CROP = "Robot/Crop",
  ROBOT_HARDWARE = "Robot/Hardware",
  ROBOT_JOB = "Robot/Job",
  SPATIAL = "SPATIAL",
  TARGET_VELOCITY_ESTIMATOR = "TargetVelocityEstimator",
  USER = "User",
  FLEET_VIEW = "FleetView",
}

const baseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (arguments_, api, extraOptions = {}) => {
  return await fetchBaseQuery({
    baseUrl: `${QueryEnv.get().portalBaseUrl}/v1/`,
    prepareHeaders: async (headers) => {
      const accessToken = await QueryEnv.get().accessTokenProvider(api);

      if (accessToken) {
        headers.set("Authorization", `Bearer ${accessToken}`);
      }

      headers.set("X-AUTH0-audience", QueryEnv.get().auth0Audience);
      headers.set(
        "X-CARBON-Feature-Flags",
        JSON.stringify(["MetricValidCrops", "MetricOperatorEffectiveness"])
      );
      headers.set(
        "x-amzn-trace-id",
        makeTraceId(QueryEnv.get().traceFieldName, new URL(location.href))
      );

      const now = DateTime.local();
      if (now.zoneName) {
        headers.set("Timezone", now.zoneName);
      }
      return headers;
    },
    paramsSerializer: toQuery,
  })(arguments_, api, extraOptions);
};

export const _basePortalApi = createApi({
  reducerPath: "portalApi",
  keepUnusedDataFor: 60 * 5, // 5 minutes
  baseQuery,
  tagTypes: values(Tag),
  endpoints: () => ({}),
});
