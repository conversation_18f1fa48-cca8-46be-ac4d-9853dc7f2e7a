import { _basePortalApi, Tag } from "./base";
import { FieldDefinition } from "protos/portal/geo";

const fieldDefinitionApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    // FIELD DEFINITIONS
    listFieldDefinitions: builder.query<
      FieldDefinition[],
      {
        serial: string;
      }
    >({
      query: ({ serial }) => `fields/robots/${serial}/definitions`,
      transformResponse: (response: FieldDefinition[]) =>
        response.map((definition) => FieldDefinition.fromJSON(definition)),
      providesTags: (definitions) => [
        Tag.FIELD_DEFINITION,
        ...(definitions?.map((definition) => ({
          type: Tag.FIELD_DEFINITION,
          id: definition.db?.id,
        })) ?? []),
      ],
    }),
    createFieldDefinition: builder.mutation<
      FieldDefinition,
      {
        serial: string;
        fieldDefinition: Required<
          Omit<FieldDefinition, "db" | "fieldId" | "robotId">
        >;
      }
    >({
      query: ({ serial, fieldDefinition }) => ({
        url: `fields/robots/${serial}/definitions`,
        method: "POST",
        body: FieldDefinition.toJSON(
          FieldDefinition.fromPartial(fieldDefinition)
        ),
      }),
      transformResponse: (response: FieldDefinition) =>
        FieldDefinition.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newDefinition } = await queryFulfilled;
        if (!newDefinition.db?.id) {
          return;
        }
        dispatch(
          fieldDefinitionApi.util.updateQueryData(
            "listFieldDefinitions",
            { serial },
            (definitions) => {
              definitions.push(newDefinition);
              return definitions;
            }
          )
        );
      },
    }),
  }),
  overrideExisting: "throw",
});

export const {
  useListFieldDefinitionsQuery,
  useLazyListFieldDefinitionsQuery,
  useCreateFieldDefinitionMutation,
} = fieldDefinitionApi;
