import { _basePortalApi, Tag } from "./base";
import { camelTo<PERSON>nake, snakeToCamel } from "common/utils/strings";
import { CarbonUser } from "common/utils/auth";
import { FleetView, UserResponse } from "protos/portal/users";
import { mergeDeep, transformKeys } from "common/utils/objects";
import { Method } from "common/utils/api";
import { spliceIfExists } from "common/utils/arrays";

const userApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    // USERS
    listUsers: builder.query<CarbonUser[], void>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "users",
      transformResponse: (response: CarbonUser[]) =>
        transformKeys(response, snakeToCamel),
      providesTags: (users) => [
        Tag.USER,
        ...(users?.map((user) => ({
          type: Tag.USER,
          id: user.email,
        })) ?? []),
      ],
    }),
    getUser: builder.query<UserResponse, { userId: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ userId }) => {
        return `users/${userId}`;
      },
      transformResponse: (response: UserResponse) =>
        UserResponse.fromJSON(response),
      providesTags: (response) => {
        const tags = [{ type: Tag.USER, id: response?.user?.userId }];
        if (response?.customer) {
          tags.push({
            type: Tag.CUSTOMER,
            id: String(response.customer.db?.id),
          });
        }
        for (const fleetView of response?.fleetViews ?? []) {
          if (fleetView.db) {
            tags.push({
              type: Tag.FLEET_VIEW,
              id: String(fleetView.db.id),
            });
          }
        }
        return tags;
      },
    }),
    updateUser: builder.mutation<
      undefined,
      { userId: string; user: Partial<CarbonUser> }
    >({
      query: ({ userId, user }) => {
        const { appMetadata, userMetadata } = user;
        return {
          url: `users/${userId}`,
          method: Method.POST,
          body: {
            ...transformKeys(user, camelToSnake),
            app_metadata: appMetadata,
            user_metadata: userMetadata,
          },
        };
      },
      onQueryStarted: async (
        { userId, user },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          userApi.util.updateQueryData("listUsers", undefined, (users) => {
            const index = users.findIndex((user) => user.userId === userId);
            users[index] = mergeDeep(users[index] ?? {}, user);
            return users;
          })
        );
        dispatch(userApi.util.invalidateTags([{ type: Tag.USER, id: userId }]));
      },
    }),
    deleteUser: builder.mutation<undefined, { userId: string }>({
      query: ({ userId }) => ({
        url: `users/${userId}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async ({ userId }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        dispatch(
          userApi.util.updateQueryData("listUsers", undefined, (users) => {
            spliceIfExists(users, (user) => user.userId === userId, 1);
            return users;
          })
        );
        dispatch(userApi.util.invalidateTags([{ type: Tag.USER, id: userId }]));
      },
    }),
    inviteUser: builder.mutation<
      undefined,
      { email: string; customerId?: number }
    >({
      query: ({ email, customerId }) => ({
        url: "users/invite",
        method: Method.POST,
        body: { email, customerId },
      }),
      invalidatesTags: (result, error, { email }) => [
        Tag.USER,
        { type: Tag.USER, id: email },
      ],
    }),

    createFleetView: builder.mutation<
      FleetView,
      { userId: string; fleetView: FleetView }
    >({
      query: ({ userId, fleetView }) => {
        return {
          url: `users/${userId}/fleetviews/`,
          method: Method.POST,
          body: FleetView.toJSON(FleetView.fromPartial(fleetView)),
        };
      },
      transformResponse: (fleetView: FleetView) =>
        FleetView.fromJSON(fleetView),
      onQueryStarted: async ({ userId }, { dispatch, queryFulfilled }) => {
        const { data: newFleetView } = await queryFulfilled;
        if (!newFleetView.db?.id) {
          return;
        }
        dispatch(
          userApi.util.updateQueryData("getUser", { userId }, (user) => {
            user.fleetViews.push(newFleetView);
            return user;
          })
        );
      },
    }),
    updateFleetView: builder.mutation<
      undefined,
      { userId: string; fleetView: FleetView }
    >({
      query: ({ userId, fleetView }) => {
        const id = fleetView.db?.id;
        return {
          url:
            id === undefined
              ? `users/${userId}/fleetviews/`
              : `users/${userId}/fleetviews/${id}`,
          method: Method.POST,
          body: FleetView.toJSON(FleetView.fromPartial(fleetView)),
        };
      },
      onQueryStarted: async (
        { userId, fleetView },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          userApi.util.updateQueryData("getUser", { userId }, (user) => {
            const index = user.fleetViews.findIndex(
              (current) => current.db?.id === fleetView.db?.id
            );
            user.fleetViews[index] = fleetView;
            return user;
          })
        );
      },
    }),
    deleteFleetView: builder.mutation<
      undefined,
      { userId: string; fleetViewId: number }
    >({
      query: ({ userId, fleetViewId }) => ({
        url: `users/${userId}/fleetviews/${fleetViewId}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async (
        { userId, fleetViewId },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          userApi.util.updateQueryData("getUser", { userId }, (user) => {
            spliceIfExists(
              user.fleetViews,
              (fleetView) => fleetView.db?.id === fleetViewId,
              1
            );
            return user;
          })
        );
      },
    }),
  }),
  overrideExisting: "throw",
});

export const {
  // USERS
  useListUsersQuery,
  useLazyListUsersQuery,
  useGetUserQuery,
  useLazyGetUserQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useInviteUserMutation,
  useCreateFleetViewMutation,
  useUpdateFleetViewMutation,
  useDeleteFleetViewMutation,
} = userApi;
