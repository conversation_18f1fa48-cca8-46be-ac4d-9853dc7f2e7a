import { BaseQueryApi } from "@reduxjs/toolkit/query";

export type AccessTokenProviderApi = Pick<BaseQueryApi, "getState">;

export interface QueryEnvData {
  portalBaseUrl: string;
  rtcJobsBaseUrl: string;
  rtcLocatorBaseUrl: string;
  auth0Audience: string;
  accessTokenProvider: (
    api: AccessTokenProviderApi
  ) => Promise<string | undefined>;
  traceFieldName: string;
}

/**
 * this type allows common code to abstract over different Auth0 clients and
 * different systems for importing configuration from the environment, mostly
 * with an eye toward allowing us to use a common implementation across
 * multiple products for our RTK-based API clients.
 */
export class QueryEnv {
  public static get(): Readonly<QueryEnvData> {
    if (!QueryEnv.value) {
      throw new Error("query env accessed before initialization");
    }

    return QueryEnv.value;
  }

  public static initialize(env: QueryEnvData): void {
    if (QueryEnv.value) {
      throw new Error("query env already initialized");
    }

    QueryEnv.value = { ...env };
    Object.freeze(QueryEnv.value);
  }

  private static value: Readonly<QueryEnvData> | undefined;
}
