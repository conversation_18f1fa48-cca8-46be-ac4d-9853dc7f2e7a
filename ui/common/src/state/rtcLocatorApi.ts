import {
  type BaseQueryFn,
  createApi,
  type <PERSON>tch<PERSON>rgs,
  fetchBaseQuery,
  type FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { camelToSnake } from "common/utils/strings";
import { DateTime } from "luxon";
import { ListDeviceLocationHistoryResponse } from "protos/rtc/device_location_history";
import {
  ListLocationHistoryResponse,
  ListRobotsResponse,
  LocationHistoryRecord,
} from "protos/rtc/location_history";
import { QueryEnv } from "./queryEnv";
import { toQuery } from "common/utils/browser";
import { transformKeys } from "common/utils/objects";

const baseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (arguments_, api, extraOptions = {}) => {
  return await fetchBaseQuery({
    baseUrl: `${QueryEnv.get().rtcLocatorBaseUrl}/v1/`,
    prepareHeaders: async (headers) => {
      const accessToken = await QueryEnv.get().accessTokenProvider(api);

      if (accessToken) {
        headers.set("Authorization", `Bearer ${accessToken}`);
      }

      headers.set("X-AUTH0-audience", QueryEnv.get().auth0Audience);
      return headers;
    },
    paramsSerializer: toQuery,
  })(arguments_, api, extraOptions);
};

export const timeRangeParams = (range: {
  start?: DateTime;
  end?: DateTime;
}): {
  start: string;
  end: string;
} => {
  return {
    start: String(range.start?.toUnixInteger() ?? 0),
    end: String(range.end?.toUnixInteger() ?? 2 ** 31 - 1),
  };
};

export const rtcLocatorApi = createApi({
  reducerPath: "rtcLocatorApi",
  baseQuery,
  endpoints: (builder) => ({
    listRobots: builder.query<ListRobotsResponse, { serials?: string[] }>({
      query: ({ serials }) => ({
        url: "/robots/list",
        params: {
          robot_serials: serials ? serials.join(",") : undefined,
        },
      }),
      transformResponse: (response: unknown) =>
        ListRobotsResponse.fromJSON(response),
    }),
    getDeviceLocationHistory: builder.query<
      ListDeviceLocationHistoryResponse,
      {
        deviceId: string;
        pageToken?: string;
        pageSize: number;
        start?: string;
        end?: string;
        desc?: boolean;
        includeClosest?: boolean;
      }
    >({
      query: ({ deviceId, ...parameters }) => {
        return {
          url: `/devices/${deviceId}/location-history`,
          params: transformKeys(parameters, camelToSnake),
        };
      },
      transformResponse: (response: unknown) =>
        ListDeviceLocationHistoryResponse.fromJSON(response),
    }),
    getTractorLocation: builder.query<
      LocationHistoryRecord | undefined,
      { serial: string }
    >({
      query: ({ serial }) => {
        return {
          url: `/robots/${serial}/location-history`,
          params: {
            robot_serials: [serial],
            page_size: 1,
            desc: true,
            include_closest: true,
          },
        };
      },
      transformResponse: (resp) => {
        return ListLocationHistoryResponse.fromJSON(resp).history?.records[0];
      },
    }),
  }),
});

export const {
  useListRobotsQuery,
  useGetDeviceLocationHistoryQuery,
  useGetTractorLocationQuery,
} = rtcLocatorApi;
