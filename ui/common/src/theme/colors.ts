type Palette<T extends string | number> = Record<T, `#${Lowercase<string>}`>;

// infer precise domain while restricting codomain
const asPalette = <T extends string | number>(p: Palette<T>): Palette<T> => p;

// palettes can be generated here: https://m2.material.io/inline-tools/color/
export const CARBON_PALETTE = asPalette({
  50: "#f7e9e7",
  100: "#f7ccbe",
  200: "#f2ac94",
  300: "#ee8d6b",
  400: "#ec764b",
  500: "#ea612e",
  600: "#df5b2a",
  700: "#d15426",
  800: "#c34e23",
  900: "#a9431e",
  DEFAULT: "#a9431e",
});

export const GREEN_LIGHT_PALETTE = asPalette({
  50: "#e8f5e9",
  100: "#c8e6c9",
  200: "#a5d6a7",
  300: "#81c784",
  400: "#66bb69",
  DEFAULT: "#4caf4f",
  500: "#4caf4f",
  600: "#43a046",
  700: "#388e3b",
  800: "#2e7d31",
  900: "#1b5e1f",
});

export const YELLOW_LIGHT_PALETTE = asPalette({
  50: "#fcfce7",
  100: "#f8f7c4",
  200: "#f4f19e",
  300: "#f0eb78",
  400: "#ede75b",
  500: "#eae240",
  600: "#ead13b",
  700: "#e8ba31",
  DEFAULT: "#e8ba31",
  800: "#e6a327",
  900: "#e07d15",
});

export const RED_LIGHT_PALETTE = asPalette({
  50: "#ffebee",
  100: "#ffcdd2",
  200: "#ef9a9a",
  300: "#e57373",
  400: "#ef5350",
  DEFAULT: "#f44336",
  500: "#f44336",
  600: "#e53935",
  700: "#d32f2f",
  800: "#c62828",
  900: "#b71c1c",
});

export const MAP_COLORS = {
  field: asPalette({
    active: "#efb76b",
    inactive: "#fceedb",
  }),
};

export const OCEAN_PALETTE = asPalette({
  50: "#e7e9f9",
  100: "#c2c8f0",
  200: "#98a5e7",
  300: "#6b81dd",
  400: "#4565d6",
  DEFAULT: "#034ace",
  500: "#034ace",
  600: "#0042c3",
  700: "#0038b7",
  800: "#002dac",
  900: "#00199a",
});

export const GRAY_PALETTE = asPalette({
  50: "#ffffff",
  100: "#ecedef",
  200: "#ced3d9",
  300: "#afb6c0",
  400: "#8f99a7", // todo
  500: "#83909a",
  600: "#69757e",
  650: "#516573",
  700: "#333f48",
  800: "#24292c",
  900: "#111518",
  DEFAULT: "#606f83",
});

export { default as TAILWIND_COLORS } from "tailwindcss/colors";
