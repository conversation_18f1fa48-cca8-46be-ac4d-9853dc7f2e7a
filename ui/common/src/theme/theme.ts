import { twMerge } from "tailwind-merge";
import clsx, { ClassValue } from "clsx";

export const classes = (...inputs: ClassValue[]): string =>
  twMerge(clsx(inputs));

export const TRACTOR_COLORS = [
  "#FF0000", // Red
  "#00FF00", // Green
  "#0366fc", // Blue
  "#FFFF00", // Yellow
  "#00FFFF", // <PERSON>an
  "#F000F0", // Magenta
  "#F58231", // Orange
  "#FFA0BB", // Pink
  "#911EB4", // Purple
  "#9A6324", // <PERSON>
];

export const CENTER_PIVOT_FRESH = "#00AA00";
