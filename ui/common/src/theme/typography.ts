import type { TypographyStyleOptions } from "@mui/material/styles/createTypography";

export const LATO_FONT_FAMILY = [
  "Lato",
  "Open Sans",
  "Segoe UI",
  "sans-serif",
].join(", ");
export const POPPINS_FONT_FAMILY = ["Poppins", "sans-serif"].join(", ");
export const RUSSO_FONT_FAMILY = ["Russo One", "Poppins", "sans-serif"].join(
  ", "
);

export const MAIN_HEADINGS: TypographyStyleOptions = {
  fontFamily: RUSSO_FONT_FAMILY,
  fontWeight: "normal",
  textTransform: "uppercase",
};

export const SUB_HEADINGS: TypographyStyleOptions = {
  fontFamily: POPPINS_FONT_FAMILY,
  fontWeight: "bold",
};

export const FONT_SIZE = {
  sm: "0.8rem",
  base: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  "2xl": "1.5em",
  "3xl": "1.7rem",
  "4xl": "2rem",
  "5xl": "2.5rem",
};

export const MUI_FONT_SIZE = {
  h1: FONT_SIZE["4xl"],
  h2: FONT_SIZE["3xl"],
  h3: FONT_SIZE["2xl"],
  h4: FONT_SIZE.xl,
  h5: FONT_SIZE.lg,
  caption: FONT_SIZE.sm,
};
