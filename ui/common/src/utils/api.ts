import { entries, isEmpty } from "common/utils/objects";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export enum Method {
  DELETE = "DELETE",
  GET = "GET",
  PATCH = "PATCH",
  POST = "POST",
  PUT = "PUT",
}

export interface Pagination {
  limit?: number;
  page?: number;
  sort?: string;
}

// query string (?foo=1&bar=2)
export interface Query {
  [index: string]: string | string[] | boolean | number;
}

/**
 * Converts an Object of key/value pairs into a URL encoded query string
 */
export const buildQueryString = (query?: Query): string => {
  if (isEmpty(query)) {
    return "";
  }
  const result = entries(query).map(([key, value]) => {
    const encodedValue = Array.isArray(value) ? value.join(",") : String(value);
    return `${key}=${encodeURIComponent(encodedValue)}`;
  });

  return `?${result.join("&")}`;
};

export interface Download {
  file: Blob;
  name: string;
}

type ProgressCallback = (progress: number) => void;
export class ProgressPromise<T = unknown> extends Promise<T> {
  private progressCallbacks: ProgressCallback[];

  constructor(
    executor: (
      resolve: (result: T) => void,
      reject: (error: any) => void,
      progress: ProgressCallback
    ) => void
  ) {
    super((resolve, reject) =>
      executor(resolve, reject, (progress: number) => {
        try {
          for (const callback of this.progressCallbacks) {
            callback(progress);
          }
        } catch (error) {
          reject(error);
        }
      })
    );
    this.progressCallbacks = [];
  }

  public progress(callback: ProgressCallback): ProgressPromise<T> {
    this.progressCallbacks.push(callback);
    return this;
  }
}

export const isFetchBaseQueryError = (
  error: any
): error is FetchBaseQueryError => {
  return error && typeof error === "object" && "status" in error;
};
