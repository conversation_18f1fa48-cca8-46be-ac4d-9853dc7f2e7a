import { describe, expect, it, test } from "@jest/globals";
import {
  filterWhere,
  findWhere,
  isEmpty,
  keyBy,
  moveItem,
  Order,
  range,
  sortBy,
  spliceIfExists,
  without,
} from "./arrays";

const a1 = { foo: 1, bar: "a" };
const b2 = { foo: 2, bar: "b" };
const c3 = { foo: 3, bar: "c" };
const c1 = { foo: 1, bar: "c" };
const a4 = { foo: 4, bar: "a" };
const a3 = { foo: 3, bar: "a" };

const john = { id: 1, name: "<PERSON>", age: 30 };
const jane = { id: 2, name: "<PERSON>", age: 25 };
const bob = { id: 3, name: "<PERSON>", age: 35 };
const input = [john, jane, bob];

describe("without", () => {
  test("should exclude item from array", () => {
    const test = [1, 2, 3];
    expect(without(test, 1)).toEqual([2, 3]);
  });
  test("should not change array if item not present", () => {
    const test = [1, 2, 3];
    expect(without(test, 4)).toEqual(test);
  });
  test("should exclude item with known key", () => {
    const test = [a1, b2, c3];
    const match = c1;
    expect(without(test, match, "foo")).toEqual([b2, c3]);
  });
  test("should not change array if no item with key present", () => {
    const test = [a1, b2, c3];
    const match = a4;
    expect(without(test, match, "foo")).toEqual(test);
  });
});

describe("moveItem", () => {
  test("should move item to target position", () => {
    const test = [1, 2, 3, 4, 5];
    expect(moveItem(test, 1, 4)).toEqual([1, 3, 4, 5, 2]);
  });
  test("should not move items to invalid indexes", () => {
    const test = [1, 2, 3, 4, 5];
    expect(moveItem(test, -1, 4)).toEqual(test);
    expect(moveItem(test, 0, 99)).toEqual(test);
  });
});

describe("range", () => {
  test("should create zero-indexed range", () => {
    expect(range(5)).toEqual([0, 1, 2, 3, 4]);
  });
  test("should create one-indexed range", () => {
    expect(range(5, true)).toEqual([1, 2, 3, 4, 5]);
  });
  test("shouldn't create negative range", () => {
    expect(range(-2)).toEqual([]);
  });
});

describe("isEmpty", () => {
  test("should return `true` for empty array", () => {
    expect(isEmpty([])).toBe(true);
  });
  test("should return `false` for non-empty array", () => {
    expect(isEmpty([1, 2, 3])).toBe(false);
  });
  test("should return `true` for undefined", () => {
    expect(isEmpty(undefined)).toBe(true);
  });
});

describe("keyBy", () => {
  test("should create map with x keys for x items (all unique)", () => {
    const test = [a1, b2, a3];
    expect(keyBy(test, "foo")).toEqual({
      1: a1,
      2: b2,
      3: a3,
    });
  });
  test("should create map with x keys for x items (not all unique)", () => {
    const test = [a1, b2, a3];
    expect(keyBy(test, "bar")).toEqual({
      a: a3,
      b: b2,
    });
  });
  test("should accept callback function to generate keys", () => {
    const test = [a1, b2, a3];
    expect(keyBy(test, (t) => t.bar)).toEqual({
      a: a3,
      b: b2,
    });
  });
});

describe("sortBy", () => {
  test("should sort in ASCending direction", () => {
    const test = [b2, a3, a1];
    expect(sortBy(test, "foo")).toEqual([a1, b2, a3]);
  });
  test("should sort in DESCending direction", () => {
    const test = [b2, a3, a1];
    expect(sortBy(test, "foo", Order.DESC)).toEqual([a3, b2, a1]);
  });
  test("should accept callback function to generate sort value", () => {
    const test = [b2, a3, a1];
    expect(sortBy(test, (t) => t.foo)).toEqual([a1, b2, a3]);
  });
});

describe("findWhere", () => {
  test("should return existing item matching query", () => {
    const test = [b2, a3, a1];
    expect(
      findWhere(test, {
        foo: 2,
      })
    ).toBe(b2);
  });
  test("should return `undefined` if no items match query", () => {
    const test = [b2, a3, a1];
    expect(
      findWhere(test, {
        foo: 4,
      })
    ).toBeUndefined();
  });
});

describe("filterWhere", () => {
  test("should return undefined if the input is undefined", () => {
    expect(filterWhere(undefined, { id: 1 })).toBeUndefined();
  });

  test("should filter the input array based on the target object", () => {
    expect(filterWhere(input, { age: 30 })).toEqual([john]);
  });

  test("should return an empty array if no elements match the target object", () => {
    expect(filterWhere(input, { id: 4 })).toEqual([]);
  });

  test("should filter the input array based on multiple target properties", () => {
    expect(filterWhere(input, { name: "Jane", age: 25 })).toEqual([jane]);
  });

  test("should return all elements if the target object is empty", () => {
    expect(filterWhere(input, {})).toEqual(input);
  });
});

describe("spliceIfExists", () => {
  it("should return an empty array if start is -1", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, -1, 2);
    expect(result).toEqual([]);
    expect(array).toEqual([1, 2, 3, 4]);
  });

  it("should splice the array if start is not -1", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, 1, 2);
    expect(result).toEqual([2, 3]);
    expect(array).toEqual([1, 4]);
  });

  it("should add new elements if provided", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, 1, 2, 5, 6);
    expect(result).toEqual([2, 3]);
    expect(array).toEqual([1, 5, 6, 4]);
  });

  it("should handle deleteCount of 0", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, 1, 0, 5, 6);
    expect(result).toEqual([]);
    expect(array).toEqual([1, 5, 6, 2, 3, 4]);
  });

  it("should handle deleteCount greater than array length", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, 1, 10);
    expect(result).toEqual([2, 3, 4]);
    expect(array).toEqual([1]);
  });

  it("should find index using a function and splice the array", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, (item) => item === 2, 1);
    expect(result).toEqual([2]);
    expect(array).toEqual([1, 3, 4]);
  });

  it("should return an empty array if function does not find an index", () => {
    const array = [1, 2, 3, 4];
    const result = spliceIfExists(array, (item) => item === 5, 1);
    expect(result).toEqual([]);
    expect(array).toEqual([1, 2, 3, 4]);
  });
});
