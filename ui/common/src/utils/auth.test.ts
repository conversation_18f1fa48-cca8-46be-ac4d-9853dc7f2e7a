import { buildPermission, CarbonUser, isActivated } from "./auth";
import { describe, expect, test } from "@jest/globals";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";

const emptyUser: CarbonUser = {};
const deactivatedUser: CarbonUser = {
  ...emptyUser,
  appMetadata: { isActivated: false },
};
const activatedUser: CarbonUser = {
  ...emptyUser,
  appMetadata: { isActivated: true },
};
const internalUser: CarbonUser = {
  ...emptyUser,
  email: "<EMAIL>",
  userId: "google-oauth2|<EMAIL>",
  appMetadata: { isActivated: false },
};

describe("isActivated", () => {
  test("should return `false` for empty user", () => {
    expect(isActivated(emptyUser)).toBe(false);
  });
  test("should return `false` for deactivated user", () => {
    expect(isActivated(deactivatedUser)).toBe(false);
  });
  test("should return `true` for activated user", () => {
    expect(isActivated(activatedUser)).toBe(true);
  });
  test("should return `true` for internal user", () => {
    expect(isActivated(internalUser)).toBe(true);
  });
});

describe("buildPermission", () => {
  test("should build standard permission string", () => {
    const action = PermissionAction.update;
    const resource = PermissionResource.robots;
    const domain = PermissionDomain.all;
    const result = buildPermission(action, resource, domain);
    expect(result).toBe("update:robots:all");
  });

  test("should build permission string without domain", () => {
    const action = PermissionAction.read;
    const resource = PermissionResource.veselka;
    const result = buildPermission(action, resource);
    expect(result).toBe("read:veselka");
  });

  test("should build permission string with undefined domain", () => {
    const action = PermissionAction.read;
    const resource = PermissionResource.veselka;
    const domain = undefined;
    const result = buildPermission(action, resource, domain);
    expect(result).toBe("read:veselka");
  });
});
