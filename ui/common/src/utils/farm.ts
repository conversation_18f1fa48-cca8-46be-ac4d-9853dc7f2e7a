import { <PERSON><PERSON><PERSON><PERSON>, CustomLayerInterface } from "mapbox-gl";
import {
  AutotractorBaseProps,
  AutotractorFeature,
  AutotractorPropsType,
} from "common/components/map/layers/types";
import { Bounds } from "common/utils/geo";
import { CenterPivot, Farm, Zone } from "protos/portal/farm";
import {
  Feature,
  FeatureCollection,
  Geometry,
  Point as GeoPoint,
  LineString,
} from "geojson";
import { Id, Point } from "protos/geo/geo";

// simplify control flow with exceptions, for want of an option monad
export class NoSuchPoint {
  constructor(public point: Point) {}
}

// ===
// custom farm element properties are part of the autotractor purview, so they extend the AT base props.
export interface PointFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.POINT;
  pointId: string | undefined;
}

export interface ZoneFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.ZONE;
  zoneId: string | undefined;
  zoneColor?: any; // theme colors aren't typed right now
}

export interface CenterPivotFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.PIVOT;
  data: CenterPivot;
}

export type FarmFeatureProperties =
  | PointFeatureProperties
  | ZoneFeatureProperties;

export type PointFeature = Feature<GeoPoint, PointFeatureProperties>;
export type ZoneFeature = Feature<Geometry, ZoneFeatureProperties>;
export type CenterPivotFeature = Feature<
  Geometry,
  CenterPivotFeatureProperties
>;

export type FarmFeature = PointFeature | ZoneFeature | CenterPivotFeature;

export const getPointFeature = (
  f?: AutotractorFeature
): PointFeature | undefined => {
  if (
    !f ||
    f.geometry.type !== "Point" ||
    f.properties.type !== AutotractorPropsType.POINT
  ) {
    return;
  }
  return { ...f, geometry: f.geometry, properties: f.properties };
};

export const getZoneFeature = (
  f?: AutotractorFeature
): ZoneFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.ZONE) {
    return;
  }
  return { ...f, properties: f.properties };
};

export const getCenterPivotFeature = (
  f?: AutotractorFeature
): CenterPivotFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.PIVOT) {
    return;
  }
  return { ...f, properties: f.properties };
};

export const derefPoint = (
  pointsById: Record<string, Point>,
  reference: Point
): Point => {
  const id = reference.id?.id;
  if (!id) {
    throw new NoSuchPoint(reference);
  }
  const referent = pointsById[id];
  if (!referent) {
    throw new NoSuchPoint(reference);
  }
  return referent;
};

export interface FarmMapData {
  id?: Id;
  pointsById: Record<string, Point>;
  zonesById: Map<string, Zone>;
  pointIdToZoneIds: Map<string, string[]>;
  centerPivotsByFieldId: Map<string, CenterPivot>;
  plantingHeadingsByFieldId: Map<string, LineString>;
  farmBoundary: FeatureCollection<Geometry, ZoneFeatureProperties>;
  fields: FeatureCollection<Geometry, ZoneFeatureProperties>;
  headlands: FeatureCollection<Geometry, ZoneFeatureProperties>;
  obstacles: FeatureCollection<Geometry, ZoneFeatureProperties>;
  roads: FeatureCollection<Geometry, ZoneFeatureProperties>;
  areasGeojson: FeatureCollection<Geometry, ZoneFeatureProperties>;
  pointsGeojson: FeatureCollection<GeoPoint, PointFeatureProperties>;
  bounds?: Bounds;
}

export const getFarmBounds = (
  farm: Farm,
  pointsById: Record<string, Point>
): Bounds | undefined => {
  let bounds: Bounds | undefined;
  if (Object.entries(pointsById).length > 0) {
    bounds = {
      minX: Infinity,
      minY: Infinity,
      maxX: -Infinity,
      maxY: -Infinity,
    };
    for (const point of Object.values(pointsById)) {
      // XXX: These are intentionally extracted backward to match
      // corresponding errors in the Map component, which are hard to fix
      // because they pervade the stack all the way down to robots sending
      // incorrect health logs.
      const { lng: y, lat: x } = point;

      bounds.minX = Math.min(bounds.minX, x);
      bounds.minY = Math.min(bounds.minY, y);
      bounds.maxX = Math.max(bounds.maxX, x);
      bounds.maxY = Math.max(bounds.maxY, y);
    }
  }
  return bounds;
};

export const getFieldBounds = (
  field: Zone,
  pointsById: Record<string, Point>
): Bounds | undefined => {
  const fieldPoints = field.areas.flatMap((a) =>
    a.polygon?.boundary?.points
      ? a.polygon.boundary.points.flatMap((p) => [derefPoint(pointsById, p)])
      : []
  );

  let bounds: Bounds | undefined;
  if (fieldPoints.length > 0) {
    bounds = {
      minX: Infinity,
      minY: Infinity,
      maxX: -Infinity,
      maxY: -Infinity,
    };
    for (const point of fieldPoints) {
      // XXX: These are intentionally extracted backward to match
      // corresponding errors in the Map component, which are hard to fix
      // because they pervade the stack all the way down to robots sending
      // incorrect health logs.
      const { lng: y, lat: x } = point;

      bounds.minX = Math.min(bounds.minX, x);
      bounds.minY = Math.min(bounds.minY, y);
      bounds.maxX = Math.max(bounds.maxX, x);
      bounds.maxY = Math.max(bounds.maxY, y);
    }
  }
  return bounds;
};

// align Mapbox's generic layer type to `<Marker />`'s more restrictive props
export type AnyStandardLayer = Exclude<AnyLayer, CustomLayerInterface>;

export const polygonFillLayerStyle: AnyStandardLayer = {
  id: "farm-area-polygons-fill",
  type: "fill",
  filter: ["==", ["geometry-type"], "Polygon"],
  paint: {
    "fill-color": ["get", "zoneColor"],
    "fill-opacity": 0.6,
  },
};

export const pointLayerStyle: AnyStandardLayer = {
  id: "farm-area-points",
  type: "circle",
  paint: {
    "circle-radius": 5,
    "circle-color": ["get", "zoneColor"],
    "circle-stroke-color": "black",
    "circle-stroke-width": 2,
  },
  filter: ["==", ["geometry-type"], "Point"],
};

export const polygonOutlineLayerStyle: AnyStandardLayer = {
  id: "farm-area-polygons-outline",
  filter: ["==", ["geometry-type"], "Polygon"],
  type: "line",
  paint: {
    "line-color": "black",
    "line-width": 2,
  },
};
