import { Position } from "geojson";
import bearing from "@turf/bearing";
import destination from "@turf/destination";

export interface Bounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

/**
 * Given two points, calculate the line (LineAB)
 * and return a line segment that starts at pointA and ends at a distance of 'distanceMeters' such that
 * the returned line is colinear to LineAB
 */
export const destinationAlongLine = (
  pointA: Position,
  pointB: Position,
  lengthMeters: number
): [Position, Position] => {
  const lineBearing = bearing(pointA, pointB);
  const newEnd = destination(pointA, lengthMeters, lineBearing, {
    units: "meters",
  });
  return [pointA, newEnd.geometry.coordinates];
};
