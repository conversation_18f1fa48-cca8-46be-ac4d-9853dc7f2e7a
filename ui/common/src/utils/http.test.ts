import { describe, expect, it } from "@jest/globals";
import { makeTraceId } from "./http";

describe("http", () => {
  describe("makeTraceId", () => {
    it("works on a basic URL", () => {
      const url = new URL(
        "https://example.com/reports/scheduled/abc?def=ghi#jkl"
      );
      const traceId = makeTraceId("PortalPage", url);
      expect(traceId).toEqual("PortalPage=/reports/scheduled/abc?def=ghi#jkl");
    });

    it("escapes any semicolons", () => {
      const url = new URL("https://example.com/some/page;with/semicolons");
      const traceId = makeTraceId("PortalPage", url);
      // Semicolons separate fields in a trace ID, so should be escaped.
      expect(traceId).toEqual("PortalPage=/some/page%3Bwith/semicolons");
    });

    it("truncates long URLs", () => {
      const url = new URL(`https://example.com/a/${"b".repeat(9999)}`);
      const traceId = makeTraceId("PortalPage", url);
      // Keep header sizes within reasonable limits.
      expect(traceId).toEqual(
        `PortalPage=/a/${"b".repeat(512 - "/a/".length)}`
      );
    });

    it("handles trace field names other than portal", () => {
      const url = new URL("https://example.com");
      const traceId = makeTraceId("RtcPage", url);
      expect(traceId).toEqual("RtcPage=/");
    });
  });
});
