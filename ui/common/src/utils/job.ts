import { MuiIcon } from "common/components/types";
import { State, Task } from "protos/rtc/jobs";
import { TFunction } from "i18next";
import CancelledIcon from "@mui/icons-material/DisabledByDefault";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import FailedIcon from "@mui/icons-material/ReportGmailerrorred";
import InProgressIcon from "@mui/icons-material/Cached";
import PauseIcon from "@mui/icons-material/Pause";
import QuestionMarkIcon from "@mui/icons-material/QuestionMark";

export const jobStateIcon = (state: State): MuiIcon => {
  switch (state) {
    case State.ACKNOWLEDGED:
    case State.NEW:
    case State.PENDING:
    case State.READY: {
      return CheckBoxOutlineBlankIcon;
    }
    case State.PAUSED: {
      return PauseIcon;
    }
    case State.FAILED: {
      return FailedIcon;
    }
    case State.CANCELLED: {
      return CancelledIcon;
    }
    case State.COMPLETED: {
      return CheckBoxIcon;
    }
    case State.IN_PROGRESS: {
      return InProgressIcon;
    }
    default: {
      return QuestionMarkIcon;
    }
  }
};

export const getTaskTypeName = (t: TFunction, task: Task): string => {
  if (task.sequence) {
    return t("models.autotractor.taskTypes.sequence");
  }
  if (task.manual) {
    return t("models.autotractor.taskTypes.manual");
  }
  if (task.goToAndFace) {
    return t("models.autotractor.taskTypes.goToAndFace");
  }
  if (task.followPath) {
    return t("models.autotractor.taskTypes.followPath");
  }
  if (task.tractorState) {
    return t("models.autotractor.taskTypes.tractorState");
  }
  if (task.laserWeed) {
    return t("models.autotractor.taskTypes.laserWeed");
  }
  if (task.stopAutonomy) {
    return t("models.autotractor.taskTypes.stopAutonomy");
  }
  if (task.goToReversiblePath) {
    return t("models.autotractor.taskTypes.goToReversiblePath");
  }
  return t("models.autotractor.taskTypes.unknown");
};

export const jobStateIconColor = (
  state: State
): "success" | "error" | "inherit" => {
  let color: "success" | "error" | "inherit" = "inherit";
  if (state === State.COMPLETED) {
    color = "success";
  }
  if (state === State.FAILED) {
    color = "error";
  }
  return color;
};
