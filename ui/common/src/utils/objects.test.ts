import { describe, expect, test } from "@jest/globals";
import { pick, transformKeys } from "./objects";

const uppercase = (s: string): string => s.toUpperCase();

describe("transformKeys", () => {
  test("should work on deeply nested objects and arrays", () => {
    const input = { a: [{ b: [{ c: "d" }] }] };
    const result = transformKeys(input, uppercase);
    expect(result).toEqual({ A: [{ B: [{ C: "d" }] }] });
  });
});

describe("pick", () => {
  test("get subset", () => {
    const set = {
      a: true,
      b: true,
      c: true,
    };
    const subsetKeys: (keyof typeof set)[] = ["a"];
    const result = pick(set, subsetKeys);
    expect(result).toEqual({ a: true });
  });
  test("not in subset", () => {
    const set = {
      a: true,
      b: true,
      c: true,
    };
    // purposefully vague 'any' typing because pick's strong types would flag "d" as incorrect if properly typed
    const subsetKeys: any[] = ["d"];
    const result = pick(set, subsetKeys);
    expect(result).toEqual({});
  });
  test("more expansive than set", () => {
    const set = {
      a: true,
      b: true,
      c: true,
    };
    // purposefully vague 'any' typing because pick's strong types would flag "d" as incorrect if properly typed
    const subsetKeys: any[] = ["a", "b", "c", "d"];
    const result = pick(set, subsetKeys);
    expect(result).toEqual({ a: true, b: true, c: true });
  });
});
