import { describe, expect, test } from "@jest/globals";
import { getClass, getSequence, RobotClass } from "./robots";

describe("robots", () => {
  describe("getClass", () => {
    test("slayer", () => {
      expect(getClass("slayer1")).toBe(RobotClass.Slayers);
    });
    test("reaper", () => {
      expect(getClass("reaper1")).toBe(RobotClass.Reapers);
    });
    test("unknown user facing string that's not a valid serial", () => {
      expect(getClass("CR-LW-1")).toBe(RobotClass.Simulators);
    });
    test("bud", () => {
      expect(getClass("bud1")).toBe(RobotClass.Buds);
    });
    test("tractors", () => {
      expect(getClass("tractor1")).toBe(RobotClass.Rtcs);
    });
    test("dev", () => {
      expect(getClass("devbot")).toBe(RobotClass.Simulators);
      expect(getClass("botdev")).toBe(RobotClass.Simulators);
      expect(getClass("yeahdiagnosticyeah")).toBe(RobotClass.Simulators);
    });
    test("fallback", () => {
      expect(getClass("idk")).toBe(RobotClass.Simulators);
    });
  });

  describe("getSequence", () => {
    test("standard slayer", () => {
      expect(getSequence("slayer")).toBe(-1);
      expect(getSequence("slayer00")).toBe(0);
      expect(getSequence("slayer1")).toBe(1);
      expect(getSequence("slayer100")).toBe(100);
    });
    test("only take serials not customer facing", () => {
      expect(getSequence("CR-LW-123")).toBe(-1);
    });
    test("standard reaper", () => {
      expect(getSequence("reaper")).toBe(-1);
      expect(getSequence("reaper00")).toBe(0);
      expect(getSequence("reaper1")).toBe(1);
      expect(getSequence("reaper100")).toBe(100);
    });
    test("reaper resale", () => {
      // TODO: is this actually what we want?
      expect(getSequence("reaper114-1")).toBe(114);
    });
    test("standard bud", () => {
      expect(getSequence("bud")).toBe(-1);
      expect(getSequence("bud1")).toBe(1);
    });
    test("dev machines", () => {
      expect(getSequence("devserver1")).toBe(1);
      expect(getSequence("devserver11")).toBe(11);
      expect(getSequence("11")).toBe(-1);
    });
    test("other", () => {
      expect(getSequence("weedy")).toBe(-1);
      expect(getSequence("tb-123")).toBe(-1);
      expect(getSequence("abc-tb-def")).toBe(-1);
      expect(getSequence(undefined)).toBe(-1);
    });
  });
});
