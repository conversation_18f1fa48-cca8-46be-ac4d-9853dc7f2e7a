import { RobotResponse } from "protos/portal/robots";
import { TemplateSerial } from "common/utils/configs";

// NOTE: We depend on these enum values to specifically coincide with the
// values of crgo/carbon.Classification.
export enum RobotClass {
  Buds = "buds",
  Reapers = "reapers",
  Rtcs = "rtcs",
  Simulators = "simulators",
  Slayers = "slayers",
  Unknown = "unknown",
}

export const getClassStrict = (
  robot?: RobotResponse | string
): RobotClass | undefined => {
  const serial = typeof robot === "string" ? robot : robot?.serial ?? "";
  if (serial.startsWith("bud") || serial === TemplateSerial.BUD) {
    return RobotClass.Buds;
  } else if (serial.startsWith("slayer") || serial === TemplateSerial.SLAYER) {
    return RobotClass.Slayers;
  } else if (serial.startsWith("reaper") || serial === TemplateSerial.REAPER) {
    return RobotClass.Reapers;
  } else if (serial.startsWith("tractor") || serial.startsWith("rtc")) {
    return RobotClass.Rtcs;
  } else if (
    serial.startsWith("dev") ||
    serial.endsWith("dev") ||
    serial.includes("diagnostic") ||
    serial === TemplateSerial.SIMULATOR
  ) {
    return RobotClass.Simulators;
  }
};

export const getClass = (robot?: RobotResponse | string): RobotClass => {
  return getClassStrict(robot) ?? RobotClass.Simulators;
};

export const getSequence = (serial?: string): number => {
  if (
    !serial ||
    serial.includes("tb") ||
    serial.replaceAll(/[a-zA-Z]/g, "").length === serial.length // just a number
  ) {
    return -1;
  }
  const robotClass = getClass(serial);
  let cleanedNumber = "";
  if (
    [
      RobotClass.Slayers,
      RobotClass.Reapers,
      RobotClass.Rtcs,
      RobotClass.Simulators,
    ].includes(robotClass)
  ) {
    const cleaned = serial.replaceAll(
      /slayer|reaper|tractor|devserver|-D/g,
      ""
    );
    const match = cleaned.match(/^\d+/);
    return match ? Number(match[0]) : -1;
  } else if (robotClass === RobotClass.Buds) {
    cleanedNumber = serial.replace("bud", "");
  } else {
    return -1;
  }

  return /^\d+$/.test(cleanedNumber) ? Number(cleanedNumber) : -1;
};
