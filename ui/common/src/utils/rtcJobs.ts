import {
  AutotractorBaseProps,
  AutotractorFeature,
  AutotractorPropsType,
  Domain,
} from "common/components/map/layers/types";
import {
  Feature,
  FeatureCollection,
  Geometry,
  LineString,
  Point,
  Polygon,
} from "geojson";
import { getCoord } from "@turf/invariant";
import { lineString, polygon } from "@turf/helpers";
import { number, object } from "yup";
import { Objective, Task } from "protos/rtc/jobs";
import { Point as ProtoPoint } from "protos/geo/geo";
import bearing from "@turf/bearing";
import buffer from "@turf/buffer";
import destination from "@turf/destination";
import distance from "@turf/distance";

// poll frequency
// how frequently we want to poll for the device/tractor's position
// recommend less that 1/2 of movement threshold.
export const PIVOT_POLL_INTERVAL_S = 5;
export const TRACTOR_POLL_INTERVAL_S = 1;

// movement threshold
// if the device/tractor's position has not changed in this amount of time,
// it is considered stationary (not moving)
export const PIVOT_IS_MOVING_THRESHOLD_S = 15;
export const TRACTOR_IS_MOVING_THRESHOLD_S = 3;

// stale threshold
// if we have not received a position update in this amount of time,
// the device/tractor is considered stale.
export const PIVOT_STALE_THRESHOLD_S = 30;
export const TRACTOR_STALE_THRESHOLD_S = 10;

// about 1cm - any higher and low-speed movement won't show as moving
// (we could maybe get rid of this and accept that tractors will usually look like they are moving)
export const TRACTOR_GPS_JITTER_THRESHOLD_M = 0.01;
export const PIVOT_GPS_JITTER_THRESHOLD_M = 0.05; // this can be higher for pivots since they poll less frequently.

export type JobFeatureProperties = TractorFeatureProperties;
export type ObjectiveFeature = Feature<Geometry, ObjectiveFeatureProperties>;
export type TractorFeature = Feature<Geometry, TractorFeatureProperties>;
export type TaskFeature = Feature<Geometry, TaskFeatureProperties>;
export type JobFeature = TractorFeature | ObjectiveFeature | TaskFeature;

export const getTractorFeature = (
  f?: AutotractorFeature
): TractorFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.TRACTOR) {
    return;
  }
  return { ...f, properties: f.properties };
};
export const getObjectiveFeature = (
  f?: AutotractorFeature
): ObjectiveFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.OBJECTIVE) {
    return;
  }
  return {
    ...f,
    properties: {
      ...f.properties,
      // this object is getting stringified by mapbox but the types don't suggest it
      // https://github.com/mapbox/mapbox-gl-js/issues/2434
      objective: parseMapBoxFlattenedObject<Objective & LaserWeedRowData>(
        f.properties.objective
      ),
    },
  };
};
export const getTaskFeature = (
  f?: AutotractorFeature
): TaskFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.TASK) {
    return;
  }
  return {
    ...f,
    properties: {
      ...f.properties,
      // this object is getting stringified by mapbox but the types don't suggest it
      // https://github.com/mapbox/mapbox-gl-js/issues/2434
      task: parseMapBoxFlattenedObject<Task>(f.properties.task),
    },
  };
};

const parseMapBoxFlattenedObject = <T>(obj: any): T =>
  JSON.parse(obj as unknown as string);

export interface ObjectiveFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.OBJECTIVE;
  objective: Objective & LaserWeedRowData;
}

export interface TaskFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.TASK;
  task: Task;
}

export interface TractorFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.TRACTOR;
  serial: string;
}

interface LatLng {
  lat: number;
  lng: number;
}

interface LaserWeedRowData {
  rowNum: number;
  abLine: {
    a: LatLng;
    b: LatLng;
  };
}

const LaserWeedRowSchema = object({
  rowNum: number().required(),
  abLine: object({
    a: object({
      lat: number().required(),
      lng: number().required(),
    }).required(),
    b: object({
      lat: number().required(),
      lng: number().required(),
    }).required(),
  }).required(),
});

export const TASK_BASE_PROPS: Omit<TaskFeatureProperties, "task"> = {
  __portalFeatureProps: true,
  domain: Domain.AUTOTRACTOR,
  type: AutotractorPropsType.TASK,
};

export const OBJECTIVE_BASE_PROPS: Omit<
  ObjectiveFeatureProperties,
  "objective"
> = {
  __portalFeatureProps: true,
  domain: Domain.AUTOTRACTOR,
  type: AutotractorPropsType.OBJECTIVE,
};

// TODO: remove this and use the one from planting when it is available ==
const DEFAULT_ROW_WIDTH = 6.7056;
// ==
export const getRowMapData = ({
  rowWidthMeters = DEFAULT_ROW_WIDTH,
  objectives,
  tasks,
}: {
  rowWidthMeters?: number;
  objectives: Objective[];
  tasks: Task[];
}): {
  aPoints: FeatureCollection<Point, ObjectiveFeatureProperties>;
  bPoints: FeatureCollection<Point, ObjectiveFeatureProperties>;
  rowLines: FeatureCollection<LineString, ObjectiveFeatureProperties>;
  rowBoxes: FeatureCollection<Polygon, ObjectiveFeatureProperties>;
  taskBoxes: FeatureCollection<Polygon, TaskFeatureProperties>;
} => {
  const rowLines: FeatureCollection<LineString, ObjectiveFeatureProperties> = {
    type: "FeatureCollection",
    features: objectives.flatMap((od) => {
      const LWRD = LaserWeedRowSchema.validateSync(od.data);
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...od,
          ...LWRD,
        },
      };
      return {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [
            [LWRD.abLine.a.lng, LWRD.abLine.a.lat],
            [LWRD.abLine.b.lng, LWRD.abLine.b.lat],
          ],
        },
        properties,
      };
    }),
  };

  function createRectangle(
    point1: ProtoPoint,
    point2: ProtoPoint,
    width: number
  ): Feature<Polygon> {
    const p1 = [point1.lng, point1.lat];
    const p2 = [point2.lng, point2.lat];
    const lineBearing = bearing(p1, p2);

    const perpBearing1 = lineBearing + 90;
    const perpBearing2 = lineBearing - 90;

    const halfWidth = width / 2;

    const corner1 = destination(p1, halfWidth, perpBearing1, {
      units: "meters",
    });
    const corner2 = destination(p1, halfWidth, perpBearing2, {
      units: "meters",
    });
    const corner3 = destination(p2, halfWidth, perpBearing2, {
      units: "meters",
    });
    const corner4 = destination(p2, halfWidth, perpBearing1, {
      units: "meters",
    });

    return polygon([
      [
        getCoord(corner1),
        getCoord(corner2),
        getCoord(corner3),
        getCoord(corner4),
        getCoord(corner1),
      ],
    ]);
  }

  const taskBoxes: FeatureCollection<Polygon, TaskFeatureProperties> = {
    type: "FeatureCollection",
    features: tasks.flatMap((t) => {
      let start = t.startLocation;
      let end = t.endLocation;
      if (t.laserWeed && t.laserWeed.path?.points) {
        start = start ?? t.laserWeed.path.points[0];
        end = end ?? t.laserWeed.path.points[1];
      }
      if (!start || !end) {
        return [];
      }
      const properties = {
        ...TASK_BASE_PROPS,
        task: t,
      };
      if (
        t.laserWeed &&
        distance([start.lng, start.lat], [end.lng, end.lat], {
          units: "meters",
        }) > rowWidthMeters
      ) {
        return [
          { ...createRectangle(start, end, rowWidthMeters / 2), properties },
        ];
      }

      const bufferedLine = buffer<LineString>(
        lineString([
          [start.lng, start.lat],
          [end.lng, end.lat],
        ]),
        rowWidthMeters / 4,
        { units: "meters" }
      );

      return [{ ...bufferedLine, properties }];
    }),
  };

  const rowBoxes: FeatureCollection<Polygon, ObjectiveFeatureProperties> = {
    type: "FeatureCollection",
    features: objectives.flatMap((od) => {
      const isLaserWeedRowSchema = LaserWeedRowSchema.isValidSync(od.data);
      if (!isLaserWeedRowSchema) {
        // todo: support more types and change this when api returns protos and not any types
        return [];
      }
      const LWRD = LaserWeedRowSchema.validateSync(od.data);
      const lineGeoJson: LineString = {
        type: "LineString",
        coordinates: [
          [LWRD.abLine.a.lng, LWRD.abLine.a.lat],
          [LWRD.abLine.b.lng, LWRD.abLine.b.lat],
        ],
      };

      const bufferedLine = buffer(lineGeoJson, rowWidthMeters / 2, {
        units: "meters",
        steps: 1,
      });
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...od,
          ...LWRD,
        },
      };

      return {
        ...bufferedLine,
        properties,
      };
    }),
  };

  const aPoints: FeatureCollection<Point, ObjectiveFeatureProperties> = {
    type: "FeatureCollection",
    features: objectives.flatMap((od) => {
      const isLaserWeedRowSchema = LaserWeedRowSchema.isValidSync(od.data);
      if (!isLaserWeedRowSchema) {
        // todo: support more types and change this when api returns protos and not any types
        return [];
      }
      const LWRD = LaserWeedRowSchema.validateSync(od.data);
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...od,
          ...LWRD,
        },
      };
      return {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [LWRD.abLine.a.lng, LWRD.abLine.a.lat],
        },
        properties,
      };
    }),
  };

  const bPoints: FeatureCollection<Point, ObjectiveFeatureProperties> = {
    type: "FeatureCollection",
    features: objectives.flatMap((od) => {
      const isLaserWeedRowSchema = LaserWeedRowSchema.isValidSync(od.data);
      if (!isLaserWeedRowSchema) {
        // todo: support more types and change this when api returns protos and not any types
        return [];
      }
      const LWRD = LaserWeedRowSchema.validateSync(od.data);
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...od,
          ...LWRD,
        },
      };
      return {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [LWRD.abLine.b.lng, LWRD.abLine.b.lat],
        },
        properties,
      };
    }),
  };

  return { aPoints, bPoints, rowBoxes, rowLines, taskBoxes };
};

export { TRACTOR_COLORS as AVAILABLE_COLORS } from "common/theme/theme";
