import ReconnectingWebSocket from "reconnecting-websocket";

interface SocketState {
  socket: ReconnectingWebSocket;
  clients: number;
}

export class SharedWebsocket {
  private store: Map<string, SocketState> = new Map();

  subscribe(url: string): {
    socket: ReconnectingWebSocket;
    unsubscribe: () => void;
  } {
    let state = this.store.get(url);
    if (!state) {
      const socket = new ReconnectingWebSocket(url);
      state = { socket, clients: 0 };
      this.store.set(url, state);
    }
    state.clients++;
    let unsubscribed = false;
    return {
      socket: state.socket,
      unsubscribe: () => {
        if (unsubscribed) {
          return;
        }
        unsubscribed = true;
        state.clients--;
        if (state.clients === 0) {
          this.store.delete(url);
          state.socket.close();
        }
      },
    };
  }
}
