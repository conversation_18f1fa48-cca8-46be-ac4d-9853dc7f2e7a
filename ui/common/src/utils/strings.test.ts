import {
  camelToSnake,
  capitalize,
  isString,
  snakeToCamel,
  snakeToPascal,
  titleCase,
} from "./strings";
import { describe, expect, it } from "@jest/globals";

describe("Strings utility functions", () => {
  describe("isString", () => {
    it("should return true if the input is a string", () => {
      expect(isString("test")).toBe(true);
    });

    it("should return false if the input is not a string", () => {
      expect(isString(123)).toBe(false);
    });
  });

  describe("capitalize", () => {
    it("should capitalize the first letter of a string", () => {
      expect(capitalize("test")).toBe("Test");
    });

    it("should allow blank or undefined inputs", () => {
      expect(capitalize("")).toBe("");
      expect(capitalize()).toBe("");
    });
  });

  describe("titleCase", () => {
    it("should capitalize the first letter of each word in a string", () => {
      expect(titleCase("test case")).toBe("Test Case");
    });

    it("should allow blank or undefined inputs", () => {
      expect(capitalize("")).toBe("");
      expect(capitalize()).toBe("");
    });
  });

  describe("snakeToCamel", () => {
    it("should convert a snake_case string to camelCase", () => {
      expect(snakeToCamel("test_case")).toBe("testCase");
    });

    it("should handle weird snake_case strings", () => {
      expect(snakeToCamel("_test_case")).toBe("testCase");
      expect(snakeToCamel("test_case_")).toBe("testCase");
      expect(snakeToCamel("test__case")).toBe("testCase");
      expect(snakeToCamel("")).toBe("");
    });
  });

  describe("snakeToPascal", () => {
    it("should convert a snake_case string to PascalCase", () => {
      expect(snakeToPascal("test_case")).toBe("TestCase");
    });

    it("should handle weird snake_case strings", () => {
      expect(snakeToPascal("_test_case")).toBe("TestCase");
      expect(snakeToPascal("test_case_")).toBe("TestCase");
      expect(snakeToPascal("test__case")).toBe("TestCase");
      expect(snakeToPascal("")).toBe("");
    });
  });

  describe("camelToSnake", () => {
    it("should convert a camelCase string to snake_case", () => {
      expect(camelToSnake("testCase")).toBe("test_case");
    });

    it("should handle weird camelCase strings", () => {
      expect(camelToSnake("TestCase")).toBe("_test_case");
      expect(camelToSnake("testCASE")).toBe("test_c_a_s_e");
      expect(camelToSnake("")).toBe("");
    });
  });
});
