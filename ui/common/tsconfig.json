{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "declaration": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noUncheckedIndexedAccess": true, "module": "ES2022", "moduleResolution": "Node", "preserveSymlinks": false, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "outDir": "./dist", "jsx": "react", "lib": ["DOM", "DOM.Iterable", "ESNext", "WebWorker"], "noEmit": false, "noImplicitAny": true, "noImplicitThis": true, "target": "ES6", "baseUrl": "./", "paths": {"common/*": ["./src/*"], "localization-cloud/*": ["../../../../localization-cloud/*"], "protos/*": ["../../protos/golang/generated/ts2/*"]}, "sourceMap": true}, "include": ["jest.config.js", "webpack.config.ts", ".eslintrc.js", "src/**/*.ts", "src/**/*.tsx", "src/@types/*.d.ts", "../../golang/services/portal/ui/src/utils/i18n.ts"], "awesomeTypescriptLoaderOptions": {"useCache": true}, "ts-node": {"compilerOptions": {"module": "CommonJS"}}}