/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  // A preset that is used as a base for <PERSON><PERSON>'s configuration
  preset: "ts-jest",
  testEnvironment: "jsdom",
  // Automatically clear mock calls, instances, contexts and results before every test
  clearMocks: true,
  // A map from regular expressions to paths to transformers
  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        isolatedModules: true,
        diagnostics: {
          exclude: ["!**/*.(spec|test).ts?(x)"],
        },
      },
    ],
  },
  // A list of paths to directories that <PERSON><PERSON> should use to search for files in
  roots: ["<rootDir>"],
  // An array of regexp pattern strings that are matched against all modules before the module loader will automatically return a mock for them
  modulePaths: ["./"],
  moduleNameMapper: {
    "^.+\\.svg$": "<rootDir>/__mocks__/svgrMock.js",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
      "<rootDir>/__mocks__/fileMock.js",
    "^common/(.*)$": "<rootdir>/../common/src/$1",
    "^@main/(.*)$": "<rootDir>/src/$1",
    "^@protos/(.*)$": "<rootDir>/../../protos/golang/generated/$1",
  },
  coverageReporters: ["lcov"],
  coverageDirectory: "coverage",
};
