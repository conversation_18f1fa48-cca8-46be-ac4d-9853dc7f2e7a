{"name": "carbon-rtc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "NODE_ENV=development vite", "build": "tsc && vite build", "build:watch": "NODE_ENV=development vite build", "lint": "eslint . --ext ts,tsx --max-warnings 0 && tsc --noEmit", "preview": "vite preview", "test": "jest", "test:coverage": "jest --collectCoverage=true", "test:watch": "jest --watch", "vite:clear": "rm -rf ./node_modules/.vite && pnpm store prune"}, "engines": {"node": "20.13.*", "pnpm": "^10.12.2", "yarn": "please-use-pnpm", "npm": "please-use-pnpm"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@bufbuild/protobuf": "^2.2.2", "@emotion/react": "catalog:", "@emotion/styled": "^11.11.0", "@fontsource/lato": "^5.1.0", "@fontsource/poppins": "^5.0.12", "@fontsource/roboto": "^5.0.12", "@fontsource/russo-one": "^5.0.19", "@mapbox/mapbox-gl-draw": "^1.4.1", "@mui/icons-material": "catalog:", "@mui/material": "catalog:", "@mui/system": "catalog:", "@mui/x-license-pro": "catalog:", "@react-spring/web": "^9.7.3", "@reduxjs/toolkit": "catalog:", "@remix-run/router": "^1.11.0", "@turf/helpers": "catalog:", "@turf/turf": "^7.0.0", "@types/d3": "^7.4.3", "@types/geojson": "catalog:", "@types/luxon": "catalog:", "@types/mapbox-gl": "catalog:", "@types/mapbox__mapbox-gl-draw": "^1.4.0", "@types/slug": "^5.0.9", "@types/uuid": "^9.0.8", "@uidotdev/usehooks": "^2.4.1", "autoprefixer": "^10.4.18", "clsx": "catalog:", "d3": "^7.9.0", "formik": "^2.4.6", "luxon": "catalog:", "mapbox-gl": "catalog:", "postcss": "^8.4.35", "posthog-js": "^1.245.1", "react": "catalog:", "react-dom": "catalog:", "react-full-screen": "^1.1.1", "react-map-gl": "catalog:", "react-redux": "catalog:", "react-router": "catalog:", "react-router-dom": "catalog:", "react-use-websocket": "^4.8.1", "redux": "^5.0.1", "redux-persist": "^6.0.0", "ring-buffer-ts": "^1.2.0", "sass": "^1.71.1", "slug": "^10.0.0", "tailwind-merge": "catalog:", "tailwindcss": "catalog:", "tailwindcss-bg-patterns": "^0.3.0", "uuid": "^9.0.1", "vite-plugin-svgr": "^4.2.0", "webrtc-issue-detector": "^1.9.0", "yup": "catalog:"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.4.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.25", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-sort-imports-es6-autofix": "^0.6.0", "eslint-plugin-unused-imports": "^3.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "5.2.6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}