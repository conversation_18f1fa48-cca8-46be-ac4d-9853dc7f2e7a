import { carbonEnv } from "@main/env";
import {
  ChannelLabel,
  type DataChannelMessages,
  type DataChannels,
} from "@main/communication/rtc/dataChannelMessage.types";
import { entries } from "common/utils/objects";
import { RingBuffer } from "ring-buffer-ts";

type ReadyStateChangeListener = (
  readyState: RTCDataChannelState | undefined
) => void;

type MessageListener<M extends DataChannelMessages> = (message: M) => void;

export type RtcApiResult =
  | { data: DataChannelMessages }
  | { error: RtcApiError };

export type RtcApiError =
  | { type: RtcErrorType.CHANNEL_NOT_OPEN; channel: ChannelLabel }
  | { type: RtcErrorType.TIMEOUT; timeoutMs: number }
  | { type: RtcErrorType.ABORTED };

export enum RtcErrorType {
  CHANNEL_NOT_OPEN = "channelNotOpen",
  TIMEOUT = "timeout",
  ABORTED = "aborted",
}

export interface FetchOptions {
  signal?: AbortSignal;
  timeoutMs?: number;
}

interface ChannelListeners {
  messageListeners: Set<MessageListener<any>>;
  readyStateChangeListeners: Set<ReadyStateChangeListener>;
  handleReadyStateChange: () => void;
}

interface ActiveRequest {
  resolve: (value: DataChannelMessages) => void;
  reject: (reason: RtcApiError) => void;
  signal: AbortSignal | undefined;
}

const DEFAULT_TIMEOUT_MS = 5 * 1000;

const getInitialDataChannels = (): DataChannels => {
  const result: Partial<Record<ChannelLabel, RTCDataChannel | undefined>> =
    Object.fromEntries(Object.values(ChannelLabel).map((k) => [k, undefined]));
  return result as Required<typeof result>;
};

type MessageWithId = DataChannelMessages &
  Required<Pick<DataChannelMessages, "id">>;
type RequestId = MessageWithId["id"];

enum MessageDirection {
  SEND = "send",
  RECV = "recv",
}

interface MessageLog {
  channel: ChannelLabel;
  direction: MessageDirection;
  timestampMs: number;
  data: string;
}

export class DataChannelApi {
  private dataChannels: DataChannels = getInitialDataChannels();
  private activeRequests: Map<RequestId, ActiveRequest> = new Map();
  private listeningChannels: WeakSet<RTCDataChannel> = new WeakSet();
  private listeners: Record<ChannelLabel, ChannelListeners>;
  private lastMessageId: number = 0;
  private logs: RingBuffer<MessageLog> = new RingBuffer(
    carbonEnv.MESSAGE_LOG_SIZE
  );

  constructor() {
    const listeners: Partial<typeof this.listeners> = {};
    for (const label of Object.values(ChannelLabel)) {
      const listenersInfo: ChannelListeners = {
        messageListeners: new Set([this.handleResponse.bind(this)]),
        readyStateChangeListeners: new Set(),
        handleReadyStateChange: () => {
          const readyState = this.dataChannels[label]?.readyState;
          const listeners = [...listenersInfo.readyStateChangeListeners];
          for (const listener of listeners) {
            listener(readyState);
          }
        },
      };
      listeners[label] = listenersInfo;
    }
    this.listeners = listeners as Required<typeof listeners>;
  }

  setDataChannels(dataChannels: DataChannels): void {
    for (const [label, channel] of entries(this.dataChannels)) {
      if (channel) {
        this.stopListening(label as keyof typeof this.dataChannels, channel);
      }
    }
    this.dataChannels = dataChannels;
    for (const [label, channel] of entries(dataChannels)) {
      if (channel) {
        this.ensureListening(label as ChannelLabel, channel);
      }
    }

    for (const listeners of Object.values(this.listeners)) {
      listeners.handleReadyStateChange();
    }
  }

  resetDataChannels(): void {
    this.setDataChannels(getInitialDataChannels());
    this.clearLogs();
  }

  getDataChannels(): DataChannels {
    return { ...this.dataChannels };
  }

  getLogs(): MessageLog[] {
    return this.logs.toArray();
  }

  exportLogs(): Blob {
    return new Blob(
      this.getLogs().map((log) => `${JSON.stringify(log)}\n`),
      { type: "application/jsonlines+json" }
    );
  }

  clearLogs(): void {
    this.logs.clear();
  }

  private setMessageId(message: DataChannelMessages): MessageWithId {
    return { ...message, id: this.chooseMessageId() };
  }

  private sendOnChannel(
    label: ChannelLabel,
    channel: RTCDataChannel | undefined,
    message: MessageWithId
  ): boolean {
    if (channel && channel.readyState === "open") {
      console.log(`sending ${label} message`, message);
      const data = JSON.stringify(message);
      channel.send(data);
      this.logs.add({
        channel: label,
        direction: MessageDirection.SEND,
        timestampMs: Date.now(),
        data,
      });
      return true;
    } else {
      console.warn(
        `Failed to send ${label} message because channel is not open yet`,
        message
      );
      return false;
    }
  }

  // Tries to send a message, returning `true` on successful send or `false`
  // otherwise (generally because the channel is not yet open).
  send(label: ChannelLabel, message: DataChannelMessages): boolean {
    const channel = this.dataChannels[label];
    return this.sendOnChannel(label, channel, this.setMessageId(message));
  }

  // Sends a message and waits for the response.
  fetch(
    label: ChannelLabel,
    messageWithoutId: DataChannelMessages,
    options: Partial<FetchOptions> = {}
  ): Promise<RtcApiResult> {
    const { signal, timeoutMs = DEFAULT_TIMEOUT_MS } = options;

    const message = this.setMessageId(messageWithoutId);
    const { id } = message;

    const channel = this.dataChannels[label];
    const sent = this.sendOnChannel(label, channel, message);
    if (!channel || !sent) {
      const error: RtcApiError = {
        type: RtcErrorType.CHANNEL_NOT_OPEN,
        channel: label,
      };
      return Promise.resolve({ error });
    }

    const activeRequest: ActiveRequest = {
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      resolve: () => {},
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      reject: () => {},
      signal,
    };
    const responsePromise = new Promise<DataChannelMessages>(
      (resolve, reject) => {
        Object.assign(activeRequest, { resolve, reject });
        setTimeout(
          () => reject({ type: RtcErrorType.TIMEOUT, timeoutMs }),
          timeoutMs
        );
      }
    );
    this.activeRequests.set(id, activeRequest);
    if (signal) {
      signal.addEventListener("abort", () => {
        this.activeRequests.delete(id);
        activeRequest.reject({ type: RtcErrorType.ABORTED });
      });
    }
    return responsePromise
      .then((data) => ({ data }))
      .catch((error) => ({ error }));
  }

  // Adds a listener that will be invoked with the parsed content of every
  // message received along the given channel until `removeMessageListener` is
  // called with the same object. The listener will persist even if the
  // underlying `RTCDataChannel` is swapped out.
  //
  // Returns an unsubscribe helper, for convenience.
  addMessageListener(
    label: ChannelLabel,
    listener: MessageListener<any>
  ): () => void {
    this.listeners[label].messageListeners.add(listener);
    return () => this.removeMessageListener(label, listener);
  }

  removeMessageListener(
    label: ChannelLabel,
    listener: MessageListener<any>
  ): void {
    this.listeners[label].messageListeners.delete(listener);
  }

  // Adds a listener that will be called when any channel changes ready states.
  // Returns an unsubscribe helper, for convenience.
  addReadyStateChangeListener(
    label: ChannelLabel,
    listener: ReadyStateChangeListener
  ): () => void {
    this.listeners[label].readyStateChangeListeners.add(listener);
    return () => this.removeReadyStateChangeListener(label, listener);
  }

  removeReadyStateChangeListener(
    label: ChannelLabel,
    listener: ReadyStateChangeListener
  ): void {
    this.listeners[label].readyStateChangeListeners.delete(listener);
  }

  private ensureListening(label: ChannelLabel, channel: RTCDataChannel): void {
    if (this.listeningChannels.has(channel)) {
      return;
    }
    this.listeningChannels.add(channel);
    channel.addEventListener(
      "message",
      (message: MessageEvent<AllowSharedBufferSource>): void => {
        const decoder = new TextDecoder("utf-8");
        let data: string;
        let json: DataChannelMessages;
        try {
          data = decoder.decode(message.data);
          json = JSON.parse(data);
        } catch (e) {
          console.error(`Error parsing ${label} message`, message.data);
          return;
        }
        console.log(`receiving ${label} message`, json);
        this.logs.add({
          channel: label,
          direction: MessageDirection.RECV,
          timestampMs: Date.now(),
          data,
        });
        for (const listener of this.listeners[label].messageListeners) {
          listener(json);
        }
      }
    );

    const readyStateListener = this.listeners[label].handleReadyStateChange;
    channel.addEventListener("open", readyStateListener);
    channel.addEventListener("close", readyStateListener);
    channel.addEventListener("closing", readyStateListener);
  }

  private stopListening(label: ChannelLabel, channel: RTCDataChannel): void {
    // Keep message listeners on the old data channels so that we can handle
    // any belated responses (they'll be GCed eventually). Remove ready state
    // change listeners so that we don't clash.
    const readyStateListener = this.listeners[label].handleReadyStateChange;
    channel.removeEventListener("open", readyStateListener);
    channel.removeEventListener("close", readyStateListener);
    channel.removeEventListener("closing", readyStateListener);
  }

  private handleResponse(message: DataChannelMessages): void {
    const responseId = message.responseTo ?? message.id;
    if (responseId === undefined) {
      return;
    }
    const activeRequest = this.activeRequests.get(responseId);
    if (activeRequest && !activeRequest.signal?.aborted) {
      activeRequest.resolve(message);
      this.activeRequests.delete(responseId);
    }
  }

  // Generates a new message ID and updates the state to track it.
  // IDs must be monotonically increasing, ideally even across client
  // restarts (ignoring system time changes).
  private chooseMessageId(): RequestId {
    let id = this.lastMessageId + 1;
    const now = Date.now();
    if (Number.isSafeInteger(now) && now > id) {
      id = now;
    }
    this.lastMessageId = id;
    return id;
  }
}
