import {
  type Autonomy,
  Gear,
  HHState,
  HitchAction,
  PathPlanningInput,
} from "@main/components/controls/drive.types";
import {
  ControlRequest,
  MetaMessageType,
} from "@main/communication/rtc/metaMessages.types";
import {
  DeleteViewRequest,
  GetDefaultViewRequest,
  GetViewForTagRequest,
  GetViewportDataRequest,
  GetViewRequest,
  GetWindowRequest,
  ListCamerasRequest,
  ListViewsRequest,
  ListWindowsForCameraRequest,
  ListWindowsRequest,
  ResetViewRequest,
  SetViewActiveRequest,
  SetWheelPosContents,
  SetWheelPosRequest,
  StoreViewRequest,
  StoreWindowRequest,
  TagViewRequest,
  ViewConfigRequest,
  ViewControlMessageType,
  Window,
} from "@main/communication/rtc/viewControlMessages.types";
import {
  GetCurrentPathRequest,
  GetPathPlanningInputRequest,
  GetTaskCompleteRequest,
  GetTaskStartableRequest,
  HbRequest,
  SetATIMRequest,
  SetAutonomyRequest,
  SetBoundaryBypassRequest,
  SetBrakeMessage,
  SetBrakeRequest,
  SetCurrentPathRequest,
  SetGearRequest,
  SetHHStateRequest,
  SetHitchRequest,
  SetSafetyBypassRequest,
  SetSpeedRequest,
  SetSteeringRequest,
  SetSteeringZeroRequest,
  StreamInfo,
  TractorControlMessageType,
} from "@main/communication/rtc/tractorControlMessages.types";
import type { Feature, LineString } from "geojson";

/** META */
export const createControlRequest = (content: {
  inControl: boolean;
}): ControlRequest => ({
  type: MetaMessageType.CONTROL_REQUEST,
  content,
});

/** TRACTOR CONTROLS */
interface HeartbeatOptions {
  requestState?: boolean;
  streamInfo?: StreamInfo;
}
export const createControlsHeartbeat = (
  options: HeartbeatOptions = {}
): HbRequest => ({
  type: TractorControlMessageType.HB_REQUEST,
  content: {
    requestState: options.requestState ?? false,
    streamInfo: options.streamInfo,
  },
});

export const createSetState = (value: HHState): SetHHStateRequest => ({
  type: TractorControlMessageType.SET_HH_STATE_REQUEST,
  content: {
    value,
  },
});

export const createSetSteering = (steering: number): SetSteeringRequest => ({
  type: TractorControlMessageType.SET_STEERING_REQUEST,
  content: {
    value: steering,
  },
});

export const createSetSteeringZero = (): SetSteeringZeroRequest => ({
  type: TractorControlMessageType.SET_STEERING_ZERO_REQUEST,
  content: {},
});

export const createSetSpeed = (speedMph: number): SetSpeedRequest => {
  return {
    type: TractorControlMessageType.SET_SPEED_REQUEST,
    content: {
      speedMph,
    },
  };
};

export const createSetGear = (gear: Gear): SetGearRequest => {
  return {
    type: TractorControlMessageType.SET_GEAR_REQUEST,
    content: {
      value: gear,
    },
  };
};

export const createSetBrake = (content: SetBrakeMessage): SetBrakeRequest => {
  return {
    type: TractorControlMessageType.SET_BRAKE_REQUEST,
    content,
  };
};

export const createSetHitch = (action: HitchAction): SetHitchRequest => {
  const liftMap: Record<HitchAction, boolean> = {
    [HitchAction.RAISE]: true,
    [HitchAction.LOWER]: false,
  };
  const lift = liftMap[action];
  return {
    type: TractorControlMessageType.SET_HITCH_REQUEST,
    content: {
      lift,
      force: 0.0, // not used yet
    },
  };
};

/** VIEW CONTROLS */
export const createGetViewportDataRequest = (): GetViewportDataRequest => ({
  type: ViewControlMessageType.GET_VIEWPORT_DATA_REQUEST,
  content: {},
});

export const createListCompositeViews = (): ListViewsRequest => ({
  type: ViewControlMessageType.LIST_VIEWS_REQUEST,
  content: {},
});

export const createGetCompositeView = (viewId: string): GetViewRequest => ({
  type: ViewControlMessageType.GET_VIEW_REQUEST,
  content: { id: viewId },
});

export const createStoreCompositeView = (
  view: ViewConfigRequest,
  save: boolean
): StoreViewRequest => ({
  type: ViewControlMessageType.STORE_VIEW_REQUEST,
  content: {
    view,
    save,
  },
});

export const createDeleteView = (viewId: string): DeleteViewRequest => ({
  type: ViewControlMessageType.DELETE_VIEW_REQUEST,
  content: {
    id: viewId,
  },
});
export const createGetDefaultView = (
  cameraId: string
): GetDefaultViewRequest => ({
  type: ViewControlMessageType.GET_DEFAULT_VIEW_REQUEST,
  content: {
    id: cameraId,
  },
});

export const createSetActiveView = (viewId: string): SetViewActiveRequest => ({
  type: ViewControlMessageType.SET_VIEW_ACTIVE_REQUEST,
  content: {
    id: viewId,
  },
});

export enum ViewTag {
  DEFAULT_FORWARD = "DEFAULT_FORWARD",
  DEFAULT_BACKWARD = "DEFAULT_BACKWARD",
}

export const createTagView = (
  viewId: string,
  tag: ViewTag
): TagViewRequest => ({
  type: ViewControlMessageType.TAG_VIEW_REQUEST,
  content: {
    id: viewId,
    tag,
  },
});

export const createResetView = (viewId: string): ResetViewRequest => ({
  type: ViewControlMessageType.RESET_VIEW_REQUEST,
  content: {
    id: viewId,
  },
});

export const createGetCompositeViewForTag = (
  tag: ViewTag
): GetViewForTagRequest => ({
  type: ViewControlMessageType.GET_VIEW_FOR_TAG_REQUEST,
  content: {
    id: tag,
  },
});

export const createListCameras = (): ListCamerasRequest => ({
  type: ViewControlMessageType.LIST_CAMERAS_REQUEST,
  content: {},
});

export const createListCameraSlicesForCamera = (
  cameraId: string
): ListWindowsForCameraRequest => ({
  type: ViewControlMessageType.LIST_WINDOWS_FOR_CAMERA_REQUEST,
  content: {
    id: cameraId,
  },
});

export const createListCameraSlices = (): ListWindowsRequest => ({
  type: ViewControlMessageType.LIST_WINDOWS_REQUEST,
  content: {},
});

export const createGetCameraSlice = (windowId: string): GetWindowRequest => ({
  type: ViewControlMessageType.GET_WINDOW_REQUEST,
  content: {
    id: windowId,
  },
});

export const createStoreWindow = (
  window: Window,
  save: boolean
): StoreWindowRequest => ({
  type: ViewControlMessageType.STORE_WINDOW_REQUEST,
  content: {
    window,
    save,
  },
});

export const createGetCurrentPath = (): GetCurrentPathRequest => ({
  type: TractorControlMessageType.GET_CURRENT_PATH_REQUEST,
  content: {},
});

export const createGetPathPlanningInput = (): GetPathPlanningInputRequest => ({
  type: TractorControlMessageType.GET_PATH_PLANNING_INPUT_REQUEST,
  content: {},
});

export const createSetCurrentPath = (
  input: PathPlanningInput | null,
  path: Feature<LineString> | null
): SetCurrentPathRequest => ({
  type: TractorControlMessageType.SET_CURRENT_PATH_REQUEST,
  content: {
    pathPlanningInput: JSON.stringify(input),
    pathFeature: JSON.stringify(path),
  },
});

export const createSetAutonomy = (content: Autonomy): SetAutonomyRequest => ({
  type: TractorControlMessageType.SET_AUTONOMY_REQUEST,
  content,
});

export const createSetSafetyBypass = (
  bypass: boolean
): SetSafetyBypassRequest => ({
  type: TractorControlMessageType.SET_SAFETY_BYPASS_REQUEST,
  content: { value: bypass },
});

export const createSetBoundaryBypass = (
  bypass: boolean
): SetBoundaryBypassRequest => ({
  type: TractorControlMessageType.SET_BOUNDARY_BYPASS_REQUEST,
  content: { enabled: bypass },
});

export const createSetWheelPos = (
  content: SetWheelPosContents
): SetWheelPosRequest => ({
  type: ViewControlMessageType.SET_WHEEL_POS_REQUEST,
  content,
});

export const createSetATIM = (enabled: boolean): SetATIMRequest => ({
  type: TractorControlMessageType.SET_ATIM_REQUEST,
  content: { enabled },
});

export const createGetTaskComplete = (
  taskId: number
): GetTaskCompleteRequest => ({
  type: TractorControlMessageType.GET_TASK_COMPLETION_STATE_REQ,
  content: { value: taskId },
});

export const createCanStartTask = (
  taskId: number
): GetTaskStartableRequest => ({
  type: TractorControlMessageType.GET_TASK_START_STATE_REQ,
  content: { value: taskId },
});
