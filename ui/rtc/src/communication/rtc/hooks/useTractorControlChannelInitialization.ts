import {
  AutonomyMode,
  type PathPlanningInput,
} from "@main/components/controls/drive.types";
import { carbonEnv } from "@main/env";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import {
  ControlMessages,
  TractorControlMessageType,
} from "@main/communication/rtc/tractorControlMessages.types";
import {
  createControlsHeartbeat,
  createGetCurrentPath,
  createGetPathPlanningInput,
} from "@main/communication/rtc/dataChannelMessages";
import { DateTime } from "luxon";
import { point } from "@turf/helpers";
import {
  selectInDriverSeat,
  selectReportedControlState,
  setClientSteeringWheelPosition,
  setIsCalibratingSteering,
  updateReportedAutonomyState,
  updateReportedControlState,
} from "@main/store/driving.slice";
import {
  setTaskEndCriteria,
  setTaskStartCriteria,
} from "@main/store/job.slice";
import { setVideoHeartbeatTimestamp } from "@main/store/metrics.slice";
import { store } from "@main/store/store";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useCallback, useEffect } from "react";
import { useDataChannelApi } from "@main/communication/rtc/dataChannelApiContext";
import { usePoll } from "@main/hooks/usePoll";
import { useTractorControlChannelMessaging } from "./useTractorControlChannelMessaging";
import type { Feature, LineString } from "geojson";

const { CONTROLS_HEARTBEAT_S } = carbonEnv;

/** Should be called on the datachannel only once */
export const useTractorControlChannelInitialization = (): void => {
  const dispatch = useAppDispatch();
  const dataChannelApi = useDataChannelApi();

  const { autonomousState } = useAppSelector(selectReportedControlState);
  const { sendControlsMessage } = useTractorControlChannelMessaging();
  const inDriverSeat = useAppSelector(selectInDriverSeat);

  // ensure we send a controls heartbeat at a regular interval to prove we're still alive and in control
  const sendControlsHeartbeat = useCallback((): void => {
    // always HB when operating autonomously (watcher)
    if (autonomousState?.mode !== AutonomyMode.TASK_AUTONOMY) {
      // in any other state, heartbeat only when in driver seat.
      if (!inDriverSeat) {
        return;
      }
    }

    const streamInfo = store.getState().metrics.videoStreamMetrics;
    const newTimestamp = DateTime.now();
    const milliDiff =
      newTimestamp.toMillis() -
      DateTime.fromISO(streamInfo.heartbeatIsoTimestamp).toMillis();
    sendControlsMessage(
      createControlsHeartbeat({
        streamInfo: {
          timePeriodMs: milliDiff,
          frames: streamInfo.framesSinceHeartbeat,
        },
      }),
      {
        warn: false,
      }
    );
    dispatch(setVideoHeartbeatTimestamp(newTimestamp.toISO()));
  }, [sendControlsMessage, dispatch, inDriverSeat, autonomousState]);

  // ====
  // this code is required for path autonomy to work,
  // when we deprecate field defintions in favor of job-centric gps-navigation, this should go away.
  const requestAutonomyState = useCallback((): void => {
    sendControlsMessage(createGetCurrentPath(), { warn: false });
    sendControlsMessage(createGetPathPlanningInput(), { warn: false });
  }, [sendControlsMessage]);
  // get the state of the tractor on connection
  useEffect(() => {
    requestAutonomyState();
  }, [requestAutonomyState]);
  // ====

  usePoll(sendControlsHeartbeat, CONTROLS_HEARTBEAT_S * 1000, true);

  const tractorControlsSubscriptions = useCallback(
    (data: ControlMessages): void => {
      if (!data.content) {
        return;
      }
      switch (data?.type) {
        case TractorControlMessageType.CONTROL_STATE: {
          const controlState = data.content;
          dispatch(updateReportedControlState(controlState));
          break;
        }
        case TractorControlMessageType.GET_CURRENT_PATH_RESPONSE: {
          const { pathFeature } = data.content;
          // We're assuming this is well-formed without validating it.
          const path: null | Feature<LineString> = JSON.parse(pathFeature);
          dispatch(updateReportedAutonomyState({ path }));
          break;
        }
        case TractorControlMessageType.GET_PATH_PLANNING_INPUT_RESPONSE: {
          const { pathPlanningInput: raw } = data.content;
          // We're assuming this is well-formed without validating it.
          const pathPlanningInput: null | PathPlanningInput = JSON.parse(raw);
          if (pathPlanningInput) {
            /* eslint-disable no-fallthrough */ // successive version upgrades
            switch (pathPlanningInput.version as `v${number}`) {
              case "v1": {
                // Backfill sidestep origin as start of target line.
                const { targetLine } = pathPlanningInput;
                const start = targetLine.geometry.coordinates[0];
                pathPlanningInput.sidestepOrigin = point(start);
              }
              case "v2":
                break;
              default:
                console.warn(
                  "Unknown path planning input version:",
                  pathPlanningInput
                );
            }
          }
          dispatch(updateReportedAutonomyState({ pathPlanningInput }));
          break;
        }
        case TractorControlMessageType.SET_STEERING_ZERO_RESPONSE: {
          dispatch(setClientSteeringWheelPosition(0));
          dispatch(setIsCalibratingSteering(false));
          break;
        }
        case TractorControlMessageType.REMOTE_ASSIST_REQUIRED: {
          dispatch(
            updateReportedAutonomyState({
              remoteAssistanceRequired: { reason: data.content.msg },
            })
          );
          break;
        }
        case TractorControlMessageType.GET_TASK_START_STATE_RESP: {
          dispatch(setTaskStartCriteria(data.content));
          break;
        }
        case TractorControlMessageType.GET_TASK_COMPLETION_STATE_RESP: {
          dispatch(setTaskEndCriteria(data.content));
          break;
        }
      }
    },
    [dispatch]
  );

  // hook up ongoing subscriptions
  useEffect(
    () =>
      dataChannelApi.addMessageListener(
        ChannelLabel.TRACTOR_CONTROLS,
        tractorControlsSubscriptions
      ),
    [dataChannelApi, tractorControlsSubscriptions]
  );
};
