import {
  addAlertsIfNew,
  disengageQuickstopLockout,
  dismissAlertsOfType,
  engageQuickstopLockout,
  selectClientSpeed,
  selectDataChannelStates,
  selectIsCalibratingSteering,
  selectQuickstopLockout,
  selectReportedControlState,
  selectServiceStatuses,
  setClientBrakes,
  setClientGear,
  setClientSpeed,
  setClientSteeringWheelPosition,
  setDraftPlan,
  setIsCalibratingSteering,
  setLastControlsSentTimestamp,
  updateReportedAutonomyState,
} from "@main/store/driving.slice";
import { AlertType } from "@main/components/alerts/alert.types";
import {
  type Autonomy,
  Gear,
  GEAR_INFO,
  HHState,
  HitchAction,
  PathPlanningInput,
} from "@main/components/controls/drive.types";
import { carbonEnv } from "@main/env";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import {
  ControlMessages,
  type SetBrakeMessage,
  TractorControlMessageType,
} from "@main/communication/rtc/tractorControlMessages.types";
import {
  createCanStartTask,
  createGetTaskComplete,
  createSetATIM,
  createSetAutonomy,
  createSetBoundaryBypass,
  createSetBrake,
  createSetCurrentPath,
  createSetGear,
  createSetHitch,
  createSetSafetyBypass,
  createSetSpeed,
  createSetState,
  createSetSteering,
  createSetSteeringZero,
} from "@main/communication/rtc/dataChannelMessages";
import {
  setTaskEndCriteria,
  setTaskStartCriteria,
} from "@main/store/job.slice";
import { useAppDispatch, useAppSelector, useAppStore } from "@main/store/hooks";
import { useCallback } from "react";
import { useDataChannelApi } from "@main/communication/rtc/dataChannelApiContext";
import { useDataChannelFetch } from "@main/communication/rtc/hooks/useDataChannelFetch";
import type { Feature, LineString } from "geojson";

const { GEAR_MAX_SHIFTING_SPEED_MPH, QUICKSTOP_LOCKOUT_DURATION_MS } =
  carbonEnv;

interface CommandOptions {
  clientOnly?: boolean;
}
export interface TractorControlCommands {
  setSteering(value: number, options?: CommandOptions): void;
  setSpeed(value: number, options?: CommandOptions): void;
  setGear(value: Gear): void;
  setHitch(action: HitchAction): void;
  setBrakes(action: SetBrakeMessage): void;
  quickstop(): void;
}

interface GetControlsChannelOptions {
  warn?: boolean;
}

export interface TaskStateData<T = any> {
  data: T;
  state: boolean;
}

export interface TaskStateDetails {
  unsupported?: TaskStateData<boolean>;
  posDistValid?: TaskStateData<number>;
  posXteValid?: TaskStateData<number>;
  headingValid?: TaskStateData<number>;
  gearStateValid?: TaskStateData<number>;
  hitchStateValid?: TaskStateData<number>;
}

interface UseTractorControlChannelMessagingResult {
  controlsReady: boolean;
  sendSetSteeringWheelCalibrate: () => void;
  clearRemoteAssistanceRequired: () => void;
  sendSetCurrentPath: (
    input: PathPlanningInput | null,
    path: Feature<LineString> | null
  ) => Promise<boolean>;
  sendSetAutonomy: (autonomy: Autonomy) => Promise<boolean>;
  sendSetBoundaryBypass: (bypass: boolean) => Promise<boolean>;
  sendSetSafetyBypass: (bypass: boolean) => Promise<boolean>;
  sendEnableControl: () => Promise<boolean>;
  sendEnableATIM: (enabled: boolean) => Promise<boolean>;
  sendControlsMessage: (
    message: ControlMessages,
    options?: GetControlsChannelOptions
  ) => void;
  sendGetTaskComplete: (
    taskId: number
  ) => Promise<TaskStateDetails | undefined>;
  sendCanStartTask: (taskId: number) => Promise<TaskStateDetails | undefined>;
  commands: TractorControlCommands;
}

/** Handles sending and receiving data on the Tractor Control Data Channel */
export const useTractorControlChannelMessaging =
  (): UseTractorControlChannelMessagingResult => {
    const store = useAppStore();
    const dispatch = useAppDispatch();
    const dataChannelApi = useDataChannelApi();

    const isCalibratingSteering = useAppSelector(selectIsCalibratingSteering);

    const channel = ChannelLabel.TRACTOR_CONTROLS;
    const controlsChannelOpen: boolean = useAppSelector(
      (state) => selectDataChannelStates(state)[channel] === "open"
    );
    const serviceOnline = useAppSelector((state) =>
      Boolean(selectServiceStatuses(state)[channel]?.online)
    );
    const { dataChannelFetch } = useDataChannelFetch(channel);

    // Wait until tractor reports its state before we send any controls.
    const gotControlState = Boolean(
      useAppSelector(selectReportedControlState).steeringPos
    );

    const sendControlsMessage = useCallback(
      (
        message: ControlMessages,
        options?: GetControlsChannelOptions
      ): boolean => {
        const { warn = true } = options ?? {};

        if (!controlsChannelOpen) {
          if (warn) {
            dispatch(
              addAlertsIfNew([
                {
                  title: AlertType.DATA_CHANNEL_CONNECTION,
                  message: "Data channel disconnected. Message cannot be sent.",
                },
              ])
            );
          }
          return false;
        }

        if (!serviceOnline) {
          if (warn) {
            const statusText =
              serviceOnline === undefined ? "loading" : "offline";
            dispatch(
              addAlertsIfNew([
                {
                  title: AlertType.TRACTOR_CONTROLS_CONNECTION,
                  message: `Tractor controls ${statusText}. Message cannot be sent.`,
                },
              ])
            );
          }
          return false;
        }

        const didMessageSend = dataChannelApi.send(channel, message);
        if (didMessageSend) {
          const now = Date.now();
          dispatch(setLastControlsSentTimestamp(now));
        }
        return didMessageSend;
      },
      [channel, controlsChannelOpen, dataChannelApi, dispatch, serviceOnline]
    );

    interface GetControlsChannelOptions {
      warn?: boolean;
    }

    const checkGotControlState = useCallback(
      (...args: unknown[]): boolean => {
        if (!gotControlState) {
          console.warn(
            "Haven't received backend controls yet; dropping command:",
            ...args
          );
          return false;
        }
        return true;
      },
      [gotControlState]
    );

    const checkQuickstopLockout = useCallback(
      (actionGerund: string) => {
        const lockedOut = selectQuickstopLockout(store.getState());
        if (lockedOut) {
          dispatch(
            addAlertsIfNew([
              {
                title: AlertType.QUICKSTOP_LOCKOUT,
                message: `Tractor is stopping; wait before ${actionGerund}`,
              },
            ])
          );
          return false;
        }
        return true;
      },
      [dispatch, store]
    );

    const setGear = useCallback(
      (gear: Gear): void => {
        if (!checkGotControlState("setGear", gear)) {
          return;
        }

        const info = GEAR_INFO[gear];
        if (!info.mayEnterDuringQuickstop) {
          if (!checkQuickstopLockout("shifting gears")) {
            return;
          }
        }
        if (info?.mustBeStoppedToEnter ?? true) {
          const speedMph = selectClientSpeed(store.getState());
          if (speedMph > GEAR_MAX_SHIFTING_SPEED_MPH) {
            dispatch(
              addAlertsIfNew([
                {
                  title: AlertType.STOP_BEFORE_SHIFTING_GEARS,
                  message: `Slow down before shifting into ${info?.name ?? "that gear"}`,
                },
              ])
            );
            return;
          }
        }

        dispatch(setClientGear(gear));
        dispatch(setClientSpeed(0));
        dispatch(dismissAlertsOfType(AlertType.STOP_BEFORE_SHIFTING_GEARS));
        sendControlsMessage(createSetGear(gear));
        sendControlsMessage(createSetSpeed(0));
      },
      [
        checkGotControlState,
        checkQuickstopLockout,
        dispatch,
        sendControlsMessage,
        store,
      ]
    );

    const setHitch = useCallback(
      (action: HitchAction): void => {
        if (!checkGotControlState("setHitch", action)) {
          return;
        }
        sendControlsMessage(createSetHitch(action));
      },
      [checkGotControlState, sendControlsMessage]
    );

    const setBrakes = useCallback(
      (message: SetBrakeMessage): void => {
        if (!checkGotControlState("setBrakes", message)) {
          return;
        }
        dispatch(setClientBrakes(message));
        sendControlsMessage(createSetBrake(message));
      },
      [checkGotControlState, dispatch, sendControlsMessage]
    );

    const quickstop = useCallback((): void => {
      if (!checkGotControlState("quickstop")) {
        return;
      }
      const zeroSpeed = (): boolean => {
        dispatch(setClientSpeed(0));
        return sendControlsMessage(createSetSpeed(0));
      };
      const zeroGear = (): boolean => {
        dispatch(setClientGear(Gear.POWERZERO));
        return sendControlsMessage(createSetGear(Gear.POWERZERO));
      };

      const lockedOut = selectQuickstopLockout(store.getState());
      if (lockedOut) {
        // If the user is hammering, bypass lockout and do everything
        // we can.
        zeroSpeed();
        zeroGear();
        return;
      }

      const sentSpeed = zeroSpeed();
      if (!sentSpeed) {
        return;
      }
      const timeoutId = setTimeout(() => {
        try {
          zeroGear();
        } finally {
          dispatch(disengageQuickstopLockout(timeoutId));
          dispatch(dismissAlertsOfType(AlertType.QUICKSTOP_LOCKOUT));
        }
      }, QUICKSTOP_LOCKOUT_DURATION_MS);
      dispatch(engageQuickstopLockout(timeoutId));
    }, [checkGotControlState, dispatch, sendControlsMessage, store]);

    const setSteering = useCallback(
      (steering: number, { clientOnly = false } = {}): void => {
        if (!checkGotControlState("setSteering", steering)) {
          return;
        }
        if (isCalibratingSteering) {
          console.warn("Cannot steer while calibrating zero; dropping command");
          return;
        }
        dispatch(setClientSteeringWheelPosition(steering));
        if (!clientOnly) {
          sendControlsMessage(createSetSteering(steering));
        }
      },
      [
        isCalibratingSteering,
        checkGotControlState,
        dispatch,
        sendControlsMessage,
      ]
    );

    const setSpeed = useCallback(
      (speedMph: number, { clientOnly = false } = {}): void => {
        if (!checkGotControlState("setSpeed", speedMph)) {
          return;
        }
        if (!checkQuickstopLockout("adjusting speed")) {
          return;
        }
        speedMph = Math.abs(speedMph);
        dispatch(setClientSpeed(speedMph));
        if (speedMph < GEAR_MAX_SHIFTING_SPEED_MPH) {
          dispatch(dismissAlertsOfType(AlertType.STOP_BEFORE_SHIFTING_GEARS));
        }
        if (!clientOnly) {
          sendControlsMessage(createSetSpeed(speedMph));
        }
      },
      [
        checkGotControlState,
        checkQuickstopLockout,
        dispatch,
        sendControlsMessage,
      ]
    );

    /**
     * Steering offset to sync the digital wheel with the real wheel
     */
    const sendSetSteeringWheelCalibrate = useCallback((): void => {
      if (!checkGotControlState("setSteeringZero")) {
        return;
      }
      // After we've sent a request to set the steering zero point, wait for the
      // response before sending any additional steering commands to avoid races.
      if (isCalibratingSteering) {
        console.warn("Already zeroing steering; nothing to do");
        return;
      }
      dispatch(setIsCalibratingSteering(true));
      sendControlsMessage(createSetSteeringZero());
    }, [
      checkGotControlState,
      dispatch,
      isCalibratingSteering,
      sendControlsMessage,
    ]);

    const clearRemoteAssistanceRequired = useCallback((): void => {
      dispatch(
        updateReportedAutonomyState({ remoteAssistanceRequired: undefined })
      );
    }, [dispatch]);

    const sendSetCurrentPath = useCallback(
      async (
        input: PathPlanningInput | null,
        path: Feature<LineString> | null
      ): Promise<boolean> => {
        const response = await dataChannelFetch(
          createSetCurrentPath(input, path)
        );
        if (
          response.type !==
            TractorControlMessageType.SET_CURRENT_PATH_RESPONSE ||
          !response.content
        ) {
          return false;
        }
        dispatch(setDraftPlan(undefined));
        dispatch(
          updateReportedAutonomyState({
            path,
            pathPlanningInput: input,
          })
        );
        return true;
      },
      [dispatch, dataChannelFetch]
    );

    const sendSetAutonomy = useCallback(
      async (autonomy: Autonomy): Promise<boolean> => {
        const response = await dataChannelFetch(createSetAutonomy(autonomy));
        return (
          response.type === TractorControlMessageType.SET_AUTONOMY_RESPONSE &&
          !response.error
        );
      },
      [dataChannelFetch]
    );

    const sendSetSafetyBypass = useCallback(
      async (bypass: boolean): Promise<boolean> => {
        const response = await dataChannelFetch(createSetSafetyBypass(bypass));
        return (
          response.type ===
            TractorControlMessageType.SET_SAFETY_BYPASS_RESPONSE &&
          !response.error
        );
      },
      [dataChannelFetch]
    );

    const sendSetBoundaryBypass = useCallback(
      async (bypass: boolean): Promise<boolean> => {
        const response = await dataChannelFetch(
          createSetBoundaryBypass(bypass)
        );
        return (
          response.type ===
            TractorControlMessageType.SET_BOUNDARY_BYPASS_RESPONSE &&
          !response.error
        );
      },
      [dataChannelFetch]
    );

    const sendEnableControl = useCallback(async (): Promise<boolean> => {
      dispatch(setClientSpeed(0));
      const response = await dataChannelFetch(
        createSetState(HHState.HH_OPERATIONAL)
      );
      if (
        response.type !== TractorControlMessageType.SET_HH_STATE_RESPONSE ||
        response.error
      ) {
        return false;
      }
      sendControlsMessage(createSetSpeed(0));
      return true;
    }, [dataChannelFetch, dispatch, sendControlsMessage]);

    const sendEnableATIM = useCallback(
      async (enabled: boolean): Promise<boolean> => {
        const response = await dataChannelFetch(createSetATIM(enabled));
        return (
          response.type === TractorControlMessageType.SET_ATIM_RESPONSE &&
          !response.error
        );
      },
      [dataChannelFetch]
    );

    const sendGetTaskComplete = useCallback(
      async (taskId: number): Promise<TaskStateDetails | undefined> => {
        const response = await dataChannelFetch(createGetTaskComplete(taskId));
        const endCriteria =
          response.type ===
          TractorControlMessageType.GET_TASK_COMPLETION_STATE_RESP
            ? response.content
            : undefined;
        dispatch(setTaskEndCriteria(endCriteria));
        return endCriteria;
      },
      [dataChannelFetch, dispatch]
    );

    const sendCanStartTask = useCallback(
      async (taskId: number): Promise<TaskStateDetails | undefined> => {
        const response = await dataChannelFetch(createCanStartTask(taskId));
        const startCriteria =
          response.type === TractorControlMessageType.GET_TASK_START_STATE_RESP
            ? response.content
            : undefined;
        dispatch(setTaskStartCriteria(startCriteria));
        return startCriteria;
      },
      [dataChannelFetch, dispatch]
    );

    return {
      controlsReady: gotControlState,
      sendControlsMessage,
      sendSetSteeringWheelCalibrate,
      clearRemoteAssistanceRequired,
      sendSetCurrentPath,
      sendSetAutonomy,
      sendSetBoundaryBypass,
      sendSetSafetyBypass,
      sendEnableControl,
      sendEnableATIM,
      sendGetTaskComplete,
      sendCanStartTask,
      commands: {
        setSteering,
        setSpeed,
        setGear,
        setHitch,
        setBrakes,
        quickstop,
      },
    };
  };
