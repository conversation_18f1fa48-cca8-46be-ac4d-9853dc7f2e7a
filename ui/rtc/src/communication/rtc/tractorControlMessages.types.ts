import { DataChannelMessage } from "./dataChannelMessage.types";
import { EmptyObject } from "../message.types";
import { TaskStateDetails } from "./hooks/useTractorControlChannelMessaging";
import type {
  Autonomy,
  ControlState as ControlStateContents,
  HHState,
  PathPlanningInput,
} from "@main/components/controls/drive.types";
import type { Feature, LineString } from "geojson";

export enum TractorControlMessageType {
  CONTROL_STATE = "CONTROL_STATE",
  REMOTE_ASSIST_REQUIRED = "REMOTE_ASSIST_REQUIRED",
  // Heartbeat ("HB") requests and responses.
  HB_REQUEST = "HB_REQUEST",
  HB_RESPONSE = "HB_RESPONSE",
  SET_STEERING_REQUEST = "SET_STEERING_REQUEST",
  SET_STEERING_RESPONSE = "SET_STEERING_RESPONSE",
  SET_STEERING_ZERO_REQUEST = "SET_STEERING_ZERO_REQUEST",
  SET_STEERING_ZERO_RESPONSE = "SET_STEERING_ZERO_RESPONSE",
  SET_SPEED_REQUEST = "SET_SPEED_REQUEST",
  SET_SPEED_RESPONSE = "SET_SPEED_RESPONSE",
  SET_GEAR_REQUEST = "SET_GEAR_REQUEST",
  SET_GEAR_RESPONSE = "SET_GEAR_RESPONSE",
  SET_BRAKE_REQUEST = "SET_BRAKE_REQUEST",
  SET_BRAKE_RESPONSE = "SET_BRAKE_RESPONSE",
  SET_HITCH_REQUEST = "SET_HITCH_REQUEST",
  SET_HITCH_RESPONSE = "SET_HITCH_RESPONSE",
  CLEAR_STEERING_FAULT_REQUEST = "CLEAR_STEERING_FAULT_REQUEST",
  CLEAR_STEERING_FAULT_RESPONSE = "CLEAR_STEERING_FAULT_RESPONSE",
  GET_CURRENT_PATH_REQUEST = "GET_CURRENT_PATH_REQUEST",
  GET_CURRENT_PATH_RESPONSE = "GET_CURRENT_PATH_RESPONSE",
  GET_PATH_PLANNING_INPUT_REQUEST = "GET_PATH_PLANNING_INPUT_REQUEST",
  GET_PATH_PLANNING_INPUT_RESPONSE = "GET_PATH_PLANNING_INPUT_RESPONSE",
  SET_CURRENT_PATH_REQUEST = "SET_CURRENT_PATH_REQUEST",
  SET_CURRENT_PATH_RESPONSE = "SET_CURRENT_PATH_RESPONSE",
  SET_AUTONOMY_REQUEST = "SET_AUTONOMY_REQUEST",
  SET_AUTONOMY_RESPONSE = "SET_AUTONOMY_RESPONSE",
  SET_HH_STATE_REQUEST = "SET_HH_STATE_REQUEST",
  SET_HH_STATE_RESPONSE = "SET_HH_STATE_RESPONSE",
  SET_SAFETY_BYPASS_REQUEST = "SET_SAFETY_BYPASS_REQUEST",
  SET_SAFETY_BYPASS_RESPONSE = "SET_SAFETY_BYPASS_RESPONSE",
  SET_ATIM_REQUEST = "SET_ATIM_REQUEST",
  SET_ATIM_RESPONSE = "SET_ATIM_RESPONSE",
  SET_BOUNDARY_BYPASS_REQUEST = "SET_BOUNDARY_BYPASS_REQUEST",
  SET_BOUNDARY_BYPASS_RESPONSE = "SET_BOUNDARY_BYPASS_RESPONSE",
  GET_TASK_COMPLETION_STATE_REQ = "GET_TASK_COMPLETION_STATE_REQUEST",
  GET_TASK_COMPLETION_STATE_RESP = "GET_TASK_COMPLETION_STATE_RESPONSE",
  GET_TASK_START_STATE_REQ = "GET_TASK_START_STATE_REQUEST",
  GET_TASK_START_STATE_RESP = "GET_TASK_START_STATE_RESPONSE",
}

type SetIntContents = {
  value: number;
};
type SetSpeedContents = {
  speedMph: number;
};
type SetFloatContents = {
  value: number;
};
type SetBoolContents = {
  value: boolean;
};
type SetEnabledContents = {
  enabled: boolean;
};

export type ControlState = DataChannelMessage<
  TractorControlMessageType.CONTROL_STATE,
  Partial<ControlStateContents>
>;

export type RemoteAssistRequired = DataChannelMessage<
  TractorControlMessageType.REMOTE_ASSIST_REQUIRED,
  { msg: string }
>;

export interface StreamInfo {
  frames: number;
  timePeriodMs: number;
}

export type SetHHStateRequest = DataChannelMessage<
  TractorControlMessageType.SET_HH_STATE_REQUEST,
  { value: HHState }
>;

export type SetHHStateResponse = DataChannelMessage<
  TractorControlMessageType.SET_HH_STATE_RESPONSE,
  EmptyObject
>;

export type HbRequest = DataChannelMessage<
  TractorControlMessageType.HB_REQUEST,
  { requestState?: boolean; streamInfo?: StreamInfo }
>;
export type HbResponse = DataChannelMessage<
  TractorControlMessageType.HB_RESPONSE,
  EmptyObject
>;

export type SetSteeringRequest = DataChannelMessage<
  TractorControlMessageType.SET_STEERING_REQUEST,
  SetFloatContents
>;
export type SetSteeringResponse = DataChannelMessage<
  TractorControlMessageType.SET_STEERING_RESPONSE,
  EmptyObject
>;

export type SetSteeringZeroRequest = DataChannelMessage<
  TractorControlMessageType.SET_STEERING_ZERO_REQUEST,
  EmptyObject
>;
export type SetSteeringZeroResponse = DataChannelMessage<
  TractorControlMessageType.SET_STEERING_ZERO_RESPONSE,
  EmptyObject
>;

export type SetSpeedRequest = DataChannelMessage<
  TractorControlMessageType.SET_SPEED_REQUEST,
  SetSpeedContents
>;
export type SetSpeedResponse = DataChannelMessage<
  TractorControlMessageType.SET_SPEED_RESPONSE,
  EmptyObject
>;

export type SetGearRequest = DataChannelMessage<
  TractorControlMessageType.SET_GEAR_REQUEST,
  SetIntContents
>;
export type SetGearResponse = DataChannelMessage<
  TractorControlMessageType.SET_GEAR_RESPONSE,
  EmptyObject
>;

export type GetTaskCompleteRequest = DataChannelMessage<
  TractorControlMessageType.GET_TASK_COMPLETION_STATE_REQ,
  SetIntContents
>;

export type GetTaskCompleteResponse = DataChannelMessage<
  TractorControlMessageType.GET_TASK_COMPLETION_STATE_RESP,
  TaskStateDetails
>;

export type GetTaskStartableRequest = DataChannelMessage<
  TractorControlMessageType.GET_TASK_START_STATE_REQ,
  SetIntContents
>;

export type GetTaskStartableResponse = DataChannelMessage<
  TractorControlMessageType.GET_TASK_START_STATE_RESP,
  TaskStateDetails
>;

export interface SetBrakeMessage {
  forceLeft: number; // 0 to 100
  forceRight: number; // 0 to 100
}

export type SetBrakeRequest = DataChannelMessage<
  TractorControlMessageType.SET_BRAKE_REQUEST,
  SetBrakeMessage
>;
export type SetBrakeResponse = DataChannelMessage<
  TractorControlMessageType.SET_BRAKE_RESPONSE,
  EmptyObject
>;

export interface SetHitchMessage {
  lift: boolean; // true = raise hitch to max, false = lower hitch to min
  force: number; // not yet used; will control the hydraulic force
}

export type SetHitchRequest = DataChannelMessage<
  TractorControlMessageType.SET_HITCH_REQUEST,
  SetHitchMessage
>;
export type SetHitchResponse = DataChannelMessage<
  TractorControlMessageType.SET_HITCH_RESPONSE,
  EmptyObject
>;

// A string holding valid JSON that, when decoded, yields a `_T`. (There's no
// real type-checking here; this is just for documentation value.)
type EncodedJson<_T> = string;

export type GetCurrentPathRequest = DataChannelMessage<
  TractorControlMessageType.GET_CURRENT_PATH_REQUEST,
  EmptyObject
>;
export type GetCurrentPathResponse = DataChannelMessage<
  TractorControlMessageType.GET_CURRENT_PATH_RESPONSE,
  { pathFeature: EncodedJson<Feature<LineString> | null> }
>;

export type GetPathPlanningInputRequest = DataChannelMessage<
  TractorControlMessageType.GET_PATH_PLANNING_INPUT_REQUEST,
  EmptyObject
>;
export type GetPathPlanningInputResponse = DataChannelMessage<
  TractorControlMessageType.GET_PATH_PLANNING_INPUT_RESPONSE,
  { pathPlanningInput: EncodedJson<PathPlanningInput | null> }
>;

export type SetCurrentPathRequest = DataChannelMessage<
  TractorControlMessageType.SET_CURRENT_PATH_REQUEST,
  {
    pathFeature: EncodedJson<Feature<LineString> | null>;
    pathPlanningInput: EncodedJson<PathPlanningInput | null>;
  }
>;
export type SetCurrentPathResponse = DataChannelMessage<
  TractorControlMessageType.SET_CURRENT_PATH_RESPONSE,
  EmptyObject
>;

export type SetAutonomyRequest = DataChannelMessage<
  TractorControlMessageType.SET_AUTONOMY_REQUEST,
  Autonomy
>;
export type SetAutonomyResponse = DataChannelMessage<
  TractorControlMessageType.SET_AUTONOMY_RESPONSE,
  EmptyObject
>;

export type SetBoundaryBypassRequest = DataChannelMessage<
  TractorControlMessageType.SET_BOUNDARY_BYPASS_REQUEST,
  SetEnabledContents
>;
export type SetBoundaryBypassResponse = DataChannelMessage<
  TractorControlMessageType.SET_BOUNDARY_BYPASS_RESPONSE,
  EmptyObject
>;

export type SetSafetyBypassRequest = DataChannelMessage<
  TractorControlMessageType.SET_SAFETY_BYPASS_REQUEST,
  SetBoolContents
>;
export type SetSafetyBypassResponse = DataChannelMessage<
  TractorControlMessageType.SET_SAFETY_BYPASS_RESPONSE,
  EmptyObject
>;

export type SetATIMRequest = DataChannelMessage<
  TractorControlMessageType.SET_ATIM_REQUEST,
  SetEnabledContents
>;
export type SetATIMResponse = DataChannelMessage<
  TractorControlMessageType.SET_ATIM_RESPONSE,
  EmptyObject
>;

export type ControlMessages =
  | ControlState
  | RemoteAssistRequired
  | HbRequest
  | HbResponse
  | GetTaskCompleteRequest
  | GetTaskCompleteResponse
  | GetTaskStartableRequest
  | GetTaskStartableResponse
  | SetSteeringRequest
  | SetSteeringResponse
  | SetSteeringZeroRequest
  | SetSteeringZeroResponse
  | SetSpeedRequest
  | SetSpeedResponse
  | SetGearRequest
  | SetGearResponse
  | SetBrakeRequest
  | SetBrakeResponse
  | SetHitchRequest
  | SetHitchResponse
  | GetCurrentPathRequest
  | GetCurrentPathResponse
  | GetPathPlanningInputRequest
  | GetPathPlanningInputResponse
  | SetCurrentPathRequest
  | SetCurrentPathResponse
  | SetAutonomyRequest
  | SetAutonomyResponse
  | SetBoundaryBypassRequest
  | SetBoundaryBypassResponse
  | SetSafetyBypassRequest
  | SetSafetyBypassResponse
  | SetATIMRequest
  | SetATIMResponse
  | SetHHStateRequest
  | SetHHStateResponse;
