import {
  type Breakpoint,
  Button,
  DialogActions,
  DialogContent,
  DialogTitle,
  Dialog as MuiDialog,
} from "@mui/material";
import React, { FC, PropsWithChildren } from "react";

interface DialogProps extends PropsWithChildren {
  isOpen: boolean;
  title?: string;
  okText?: string;
  okDisabled?: boolean;
  onOk?: () => void;
  cancelText?: string;
  onCancel?: () => void;
  onCloseModal: () => void;
  maxDialogWidth?: Breakpoint;
  dialogContentClassName?: string;
}
export const Dialog: FC<DialogProps> = ({
  isOpen,
  title,
  okText = "Ok",
  okDisabled = false,
  onOk,
  cancelText = "Cancel",
  onCancel,
  onCloseModal,
  maxDialogWidth = "sm",
  dialogContentClassName,
  children,
}) => {
  return (
    <MuiDialog
      open={isOpen}
      onClose={() => {
        if (onCancel !== undefined) {
          onCancel();
        }
        onCloseModal();
      }}
      fullWidth
      maxWidth={maxDialogWidth}
    >
      {title && (
        <DialogTitle className="font-bold border-solid border-0 border-b border-rtc-gray-300 mb-2 leading-6">
          {title}
        </DialogTitle>
      )}
      <DialogContent className={dialogContentClassName}>
        {children}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            if (onCancel !== undefined) {
              onCancel();
            }
            onCloseModal();
          }}
        >
          {cancelText}
        </Button>
        <Button
          variant="contained"
          disabled={okDisabled}
          onClick={() => {
            if (onOk !== undefined) {
              onOk();
            }
            onCloseModal();
          }}
        >
          {okText}
        </Button>
      </DialogActions>
    </MuiDialog>
  );
};
