import { Link } from "@mui/material";
import clsx from "clsx";
import ErrorIcon from "@mui/icons-material/Warning";
import React, {
  Component,
  ComponentType,
  FunctionComponent,
  PropsWithChildren,
  ReactNode,
} from "react";
import ReloadIcon from "@mui/icons-material/Autorenew";

const ICON_SIZE_LARGE = "text-6xl";
const ICON_SIZE_SMALL = "text-2xl";

interface ErrorBoundaryProps extends PropsWithChildren {
  message?: ReactNode;
  small?: boolean;
}
/**
 * Catches unhandled UI errors and displays fallback text
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps> {
  state: { hasError: boolean; isReloading: boolean };

  constructor(properties: PropsWithChildren) {
    super(properties);
    this.state = { hasError: false, isReloading: false };
  }

  static getDerivedStateFromError(): any {
    return { hasError: true };
  }

  componentDidCatch(error: Error, ...arguments_: any[]): void {
    console.error(error, ...arguments_);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="w-full h-full flex-1 flex items-center justify-center">
          <div
            className={clsx("text-rtc-gray-500 flex gap-4 items-center", {
              "flex-col": !this.props.small,
            })}
          >
            {this.state.isReloading ? (
              <ReloadIcon
                className={clsx("animate-spin", {
                  [ICON_SIZE_LARGE]: !this.props.small,
                  [ICON_SIZE_SMALL]: this.props.small,
                })}
              />
            ) : (
              <ErrorIcon
                className={clsx({
                  [ICON_SIZE_LARGE]: !this.props.small,
                  [ICON_SIZE_SMALL]: this.props.small,
                })}
              />
            )}
            <span>
              {this.props.message ??
                (this.props.small ? "Error." : "Sorry, unexpected error.")}{" "}
              {this.state.isReloading ? (
                "Reloading..."
              ) : (
                <Link
                  className="cursor-pointer"
                  onClick={() => {
                    this.setState((state) => ({ ...state, isReloading: true }));
                    window.location.reload();
                  }}
                >
                  Reload?
                </Link>
              )}
            </span>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * HOC version of ErrorBoundary
 */
export const withErrorBoundary =
  <P extends object>(
    WrappedComponent: ComponentType<P>,
    errorBoundaryProperties?: ErrorBoundaryProps
  ): FunctionComponent<P> =>
  (properties) => (
    <ErrorBoundary {...errorBoundaryProperties}>
      <WrappedComponent {...properties} />
    </ErrorBoundary>
  );
