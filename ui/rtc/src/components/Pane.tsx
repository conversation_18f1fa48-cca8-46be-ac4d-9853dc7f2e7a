import { classes } from "@main/utils/theme";
import { Divider, IconButton } from "@mui/material";
import clsx from "clsx";
import React, { FC, PropsWithChildren, ReactNode, useState } from "react";
import UnfoldLessIcon from "@mui/icons-material/UnfoldLess";
import UnfoldMoreIcon from "@mui/icons-material/UnfoldMore";
interface PaneProps extends PropsWithChildren {
  className?: string;
  headerClassName?: string;
  header: ReactNode;
}
export const Pane: FC<PaneProps> = ({
  className,
  header,
  headerClassName,
  children,
}) => {
  const [expanded, setExpanded] = useState<boolean>(true);
  return (
    <div className={className}>
      <div
        className={clsx(
          "px-3 py-1 bg-rtc-gray-700 text-white uppercase font-bold min-h-[22px] flex",
          headerClassName
        )}
      >
        <IconButton
          size="small"
          className="flex my-auto"
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? (
            <UnfoldLessIcon fontSize="small" />
          ) : (
            <UnfoldMoreIcon fontSize="small" />
          )}
        </IconButton>

        <div className={classes("w-full my-auto", headerClassName)}>
          {header}
        </div>
      </div>
      {expanded && <div>{children}</div>}
      <Divider />
    </div>
  );
};
