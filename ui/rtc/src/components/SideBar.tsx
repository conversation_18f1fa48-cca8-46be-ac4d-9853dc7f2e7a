import { IconButton, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import clsx from "clsx";
import React, { FC, PropsWithChildren, ReactNode } from "react";

export interface SideBarProps extends PropsWithChildren {
  className?: string;
  style?: React.CSSProperties;
  header: {
    className?: string;
    center?: boolean;
    backLink?: string;
    backOnClick?: () => void;
    left?: ReactNode;
    title?: ReactNode;
    right?: ReactNode;
  };
  bottom?: ReactNode;
}
/** Standardizes the side bar with a three column header and a scrollable content area */
export const SideBar: FC<SideBarProps> = ({
  className,
  style,
  header: {
    className: headerClassName,
    backLink,
    backOnClick,
    center,
    left,
    title,
    right,
  },
  children,
  bottom,
}) => {
  return (
    <div
      className={clsx("h-screen bg-rtc-gray-900 flex flex-col", className)}
      style={style}
    >
      <div
        className={clsx(
          "bg-carbon-orange p-2 flex justify-between items-center shrink-0",
          headerClassName
        )}
      >
        <div>
          {backLink && (
            <IconButton component={Link} to={backLink}>
              <ArrowBackIcon />
            </IconButton>
          )}
          {!backLink && backOnClick ? (
            <IconButton onClick={backOnClick}>
              <ArrowBackIcon />
            </IconButton>
          ) : null}
          {left}
        </div>
        <div className="grow">
          <Typography
            className={clsx("uppercase text-[1rem]", center && "text-center")}
            variant="h2"
          >
            {title}
          </Typography>
        </div>
        <div>{right}</div>
      </div>
      <div className="grow overflow-y-auto overflow-x-hidden break-words">
        {children}
      </div>
      {bottom}
    </div>
  );
};
