import { SvgIcon } from "@mui/material";
import clsx from "clsx";
import GoodIcon from "@mui/icons-material/Circle";
import HollowCircleSvg from "@main/assets/hollowCircle.svg?react";
import React, { FC } from "react";

export enum StatusOption {
  INFO = "Info",
  GOOD = "Good",
  OK = "OK",
  BAD = "Bad",
  UNKNOWN = "Unknown",
  UNINITIALIZED = "Uninitialized",
}
interface StatusCircleProps {
  status: StatusOption;
}
export const StatusCircle: FC<StatusCircleProps> = ({ status }) => {
  const statusIconClasses = "w-3 h-3";
  const data = {
    [StatusOption.INFO]: [BadIcon, "text-blue-500"],
    [StatusOption.GOOD]: [GoodIcon, "text-success-400"],
    [StatusOption.OK]: [GoodIcon, "text-yellow-500"],
    [StatusOption.BAD]: [BadIcon, "text-red-500"],
    [StatusOption.UNKNOWN]: [BadIcon, "text-rtc-gray-500"],
    [StatusOption.UNINITIALIZED]: [BadIcon, "text-rtc-gray-600"],
  };
  const [Icon, color] = data[status];
  return <Icon className={clsx(statusIconClasses, color)} />;
};

const BadIcon: FC<{ className?: string }> = ({ className }) => {
  return (
    <SvgIcon className={className} component={HollowCircleSvg} inheritViewBox />
  );
};
