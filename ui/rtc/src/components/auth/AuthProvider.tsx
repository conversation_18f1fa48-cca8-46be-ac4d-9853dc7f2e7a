import { Auth0Provider, useAuth0 } from "@auth0/auth0-react";
import { carbonEnv } from "@main/env";
import { PermissionSet } from "./WithAuthorizationRequired";
import {
  selectAccessToken,
  setAccessToken,
  setAuthFailed,
  setPermissions,
} from "@main/store/self.slice";
import { skipToken } from "@reduxjs/toolkit/query";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useAuth } from "@main/components/auth/useAuth";
import { useGetUserQuery } from "common/state/portalApi/user";
import { useNavigate } from "react-router-dom";
import React, { FC, PropsWithChildren, useEffect, useMemo } from "react";

const {
  AUTH0_AUDIENCE,
  AUTH0_AUTH_DOMAIN,
  AUTH0_CALLBACK_URL,
  AUTH0_CLIENT_ID,
  DISABLE_AUTH,
} = carbonEnv;

export const AuthProvider: FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();

  return DISABLE_AUTH ? (
    <>{children}</>
  ) : (
    <Auth0Provider
      domain={AUTH0_AUTH_DOMAIN}
      clientId={AUTH0_CLIENT_ID}
      useRefreshTokens
      useRefreshTokensFallback
      onRedirectCallback={(appState, ...args) => {
        console.debug(
          "AuthProvider.tsx:onRedirectCallback: appState, args:",
          appState,
          ...args
        );
        const to = appState?.returnTo || "/";
        navigate(to, { replace: true });
      }}
      authorizationParams={{
        redirect_uri: AUTH0_CALLBACK_URL,
        audience: AUTH0_AUDIENCE,
      }}
    >
      <AccessTokenFetcher>
        <PermissionFetcher>{children}</PermissionFetcher>
      </AccessTokenFetcher>
    </Auth0Provider>
  );
};

const AccessTokenFetcher: FC<PropsWithChildren> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { isLoading, getAccessTokenSilently } = useAuth();

  useEffect(() => {
    if (isLoading) {
      // Don't request an access token before Auth0 has read the login
      // credentials from the callback. Skipping this check can put the
      // application into a login loop.
      return;
    }
    const ac = new AbortController();
    const fetchToken = async (): Promise<void> => {
      const accessToken = await getAccessTokenSilently();
      if (ac.signal.aborted) {
        dispatch(setAuthFailed());
        return;
      }
      dispatch(setAccessToken(accessToken));
    };
    fetchToken();
    return () => ac.abort();
  }, [dispatch, getAccessTokenSilently, isLoading]);

  return children;
};

const PermissionFetcher: FC<PropsWithChildren> = ({ children }) => {
  const dispatch = useAppDispatch();
  const accessToken = useAppSelector(selectAccessToken);
  const { user: auth0User } = useAuth0();
  const { data: userResponse } = useGetUserQuery(
    auth0User?.sub ? { userId: auth0User.sub } : skipToken,
    { skip: !accessToken }
  );
  const permissionSet: PermissionSet = useMemo(() => {
    const permissionSet: PermissionSet = {};
    if (userResponse?.permissions?.names) {
      for (const perm of userResponse.permissions.names) {
        permissionSet[perm] = true;
      }
    }
    return permissionSet;
  }, [userResponse]);

  useEffect(() => {
    dispatch(setPermissions(permissionSet));
  }, [dispatch, permissionSet]);

  return children;
};
