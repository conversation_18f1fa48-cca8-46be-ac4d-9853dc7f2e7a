import { Button, Skeleton, Typography } from "@mui/material";
import { Pane } from "@main/components/Pane";
import { SideBarBesideVideoLayout } from "@main/pages/stream/layouts/SideBarBesideVideoLayout";
import React, { FC } from "react";

interface CompositeViewSuspenseProps {
  title: string;
  isLoading: boolean;
  error: string | undefined;
  onBack: () => void;
}
export const CompositeViewSuspense: FC<CompositeViewSuspenseProps> = ({
  title,
  isLoading,
  error,
  onBack,
}) => {
  return (
    <SideBarBesideVideoLayout
      sideBarProps={{
        header: { title, backOnClick: onBack },
        bottom: (
          <div className="flex justify-end gap-4 p-6">
            <Button variant="outlined" size="small" onClick={onBack}>
              Cancel
            </Button>
          </div>
        ),
      }}
      videoOverlay={
        <div className="flex justify-center gap-2 text-rtc-gray-200">
          {isLoading && !error ? (
            <Typography>Loading View...</Typography>
          ) : null}
          {error ? (
            <Typography className="bg-error-100 rounded px-2 py-1 m-2 text-error-800">
              {error}
            </Typography>
          ) : null}
        </div>
      }
      videoWrapperClassName={
        "pattern-cross pattern-rtc-gray-900 pattern-bg-rtc-gray-800 pattern-size-4 pattern-opacity-100"
      }
      disableVideo={true}
    >
      <Pane
        className="min-h-16"
        header={<Typography variant="caption">Info</Typography>}
      >
        {isLoading && <PaneSkeleton />}
      </Pane>
      <Pane
        className="min-h-36"
        header={<Typography variant="caption">Camera Slices</Typography>}
      >
        {isLoading && <PaneSkeleton />}
      </Pane>
    </SideBarBesideVideoLayout>
  );
};

const PaneSkeleton: FC = () => (
  <Skeleton
    variant="rectangular"
    className="w-full bg-rtc-gray-800"
    height={100}
  />
);
