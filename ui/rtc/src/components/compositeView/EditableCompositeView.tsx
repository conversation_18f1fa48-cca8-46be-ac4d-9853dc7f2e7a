import { addAlerts } from "@main/store/driving.slice";
import { AlertType } from "@main/components/alerts/alert.types";
import { Button, Typography } from "@mui/material";
import { CompositeViewCameraSliceSelectionPane } from "@main/components/panes/CompositeViewCameraSliceSelectionPane";
import {
  CompositeViewInfoForm,
  CompositeViewInfoFormData,
} from "@main/components/forms/CompositeViewInfoForm";
import { CompositeViewWindows } from "../videoOverlays/CompositeViewWindows";
import {
  Dimensions,
  ViewConfigRequest,
} from "@main/communication/rtc/viewControlMessages.types";
import {
  type Layer,
  syncReceivedSlicesWithIntentionLayers,
  windowToLayer,
} from "@main/logic/compositeView/layers";
import { LoadingButton } from "../LoadingButton";
import { Pane } from "@main/components/Pane";
import { ROUTES } from "@main/pages/routes";
import { SideBarBesideVideoLayout } from "@main/pages/stream/layouts/SideBarBesideVideoLayout";
import { useAppDispatch } from "@main/store/hooks";
import { useDebouncedCallback } from "@main/hooks/useDebouncedCallback";
import { useNavigate } from "react-router";
import { useViewChannelMessaging } from "@main/communication/rtc/hooks/useViewChannelMessaging";
import { ViewTag } from "@main/communication/rtc/dataChannelMessages";
import React, { FC, useCallback, useEffect, useState } from "react";

export interface EditableViewData extends ViewConfigRequest {
  tags: string[];
}
interface EditableCompositeViewProps {
  initialValues: EditableViewData;
  sideBarTitle: string;
  onBack: () => void;
  isBackLoading?: boolean;
  allowReset?: boolean;
}
export const EditableCompositeView: FC<EditableCompositeViewProps> = ({
  initialValues,
  sideBarTitle,
  onBack,
  isBackLoading = false,
  allowReset = false,
}) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [viewportDims, setViewportDims] = useState<Dimensions>();
  const {
    sendGetViewportDimensions,
    sendGetCameras,
    sendGetCompositeView,
    sendStoreCompositeView,
    sendTagView,
    sendResetCompositeView,
  } = useViewChannelMessaging();
  const [isResetting, setIsResetting] = useState(false);
  const [selectedLayerId, setSelectedLayerId] = useState<string | undefined>();
  const initialInfoFormValues = {
    name: initialValues.metadata.data.name ?? "",
    isDefaultForward: initialValues.tags.some(
      (t) => t === ViewTag.DEFAULT_FORWARD
    ),
    isDefaultBackward: initialValues.tags.some(
      (t) => t === ViewTag.DEFAULT_BACKWARD
    ),
  };
  const [infoFormValues, setInfoFormValues] = useState<
    CompositeViewInfoFormData | undefined
  >(initialInfoFormValues);
  const [infoFormHasErrors, setInfoFormHasErrors] = useState<boolean>(true);

  const [newCompositeView, setNewCompositeView] =
    useState<ViewConfigRequest>(initialValues);
  // layers ordered by z index. Top most layer has the highest index. Bottom layer is at index 0
  const [intentionLayers, setIntentionLayers] = useState<Layer[]>([]);
  const [reportedLayers, setReportedLayers] = useState<Layer[]>([]);

  const populateLayersFromData = useCallback(
    async (viewData: ViewConfigRequest): Promise<void> => {
      const camerasToSlices = await sendGetCameras();
      if (camerasToSlices) {
        const sliceIdToName: { [sliceId: string]: string } = {};
        for (const value of Object.values(camerasToSlices)) {
          for (const slice of value.slices) {
            sliceIdToName[slice.id] = slice.name;
          }
        }
        const initialLayers = viewData.windows.map((w) => {
          const name = sliceIdToName[w.id];
          return windowToLayer(name, w);
        });
        setIntentionLayers(initialLayers);
        setReportedLayers(initialLayers);
      }
    },
    [sendGetCameras]
  );

  useEffect(() => {
    const getViewportData = async (): Promise<void> => {
      setViewportDims(await sendGetViewportDimensions());
    };
    getViewportData();
  }, [sendGetViewportDimensions]);

  useEffect(() => {
    populateLayersFromData(initialValues);
  }, [initialValues, populateLayersFromData]);

  const updateCompositeView = useCallback(
    (layers: Layer[]): void => {
      const windows = layers
        .filter((layer) => layer.visible)
        .map(({ sliceId, x, y }) => ({
          id: sliceId,
          x,
          y,
        }));
      const view = { ...newCompositeView, windows };
      sendStoreCompositeView(view, false).then(async () => {
        const data = await sendGetCompositeView(view.id);
        if (data) {
          setNewCompositeView(view);
          const syncedAndUpdatedLayers = syncReceivedSlicesWithIntentionLayers(
            layers,
            data.view.windows
          );
          // update intention layers that have no width / height values;
          const uninitializedIntentionLayers = layers.filter(
            (originalLayer) => {
              const updatedMatch = syncedAndUpdatedLayers.find(
                (l) => l.layerId === originalLayer.layerId
              );
              const hasDimensions = originalLayer.width || originalLayer.height;
              return (
                !hasDimensions &&
                updatedMatch &&
                (updatedMatch.height || updatedMatch.width)
              );
            }
          );
          if (
            uninitializedIntentionLayers.length ||
            layers.length !== syncedAndUpdatedLayers.length
          ) {
            setIntentionLayers(syncedAndUpdatedLayers);
          }
          setReportedLayers(syncedAndUpdatedLayers);
        }
      });
    },
    [newCompositeView, sendGetCompositeView, sendStoreCompositeView]
  );

  const DEBOUNCE_MS = 100;
  const debouncedUpdateCompositeView = useDebouncedCallback(
    (updates: Layer[]) => {
      updateCompositeView(updates);
    },
    DEBOUNCE_MS
  );

  const onReset = useCallback(async (): Promise<void> => {
    setIsResetting(true);
    const viewId = initialValues.id;
    setIntentionLayers([]);
    await sendResetCompositeView(viewId);
    const data = await sendGetCompositeView(viewId);
    if (data?.view) {
      await populateLayersFromData(data.view);
    }
    setIsResetting(false);
  }, [
    initialValues.id,
    populateLayersFromData,
    sendGetCompositeView,
    sendResetCompositeView,
  ]);

  return (
    <SideBarBesideVideoLayout
      sideBarProps={{
        header: {
          title: sideBarTitle,
          backOnClick: onBack,
        },
        bottom: (
          <div className="flex justify-end gap-4 p-6">
            <LoadingButton
              variant="outlined"
              size="small"
              onClick={onBack}
              isLoading={isBackLoading}
              disabled={isBackLoading}
              circularProgressClassName="text-rtc-gray-200"
            >
              Cancel
            </LoadingButton>
            {allowReset && (
              <LoadingButton
                variant="outlined"
                className="py-0"
                onClick={onReset}
                isLoading={isResetting}
                disabled={isResetting}
              >
                Reset
              </LoadingButton>
            )}
            <Button
              variant="contained"
              size="small"
              disabled={
                !infoFormValues ||
                infoFormHasErrors ||
                !intentionLayers.length ||
                !intentionLayers.some((layer) => layer.visible)
              }
              onClick={async () => {
                const error = {
                  title: AlertType.EDITING,
                  message: "Error Saving Composite View",
                };
                if (
                  !infoFormValues ||
                  infoFormHasErrors ||
                  !intentionLayers.length
                ) {
                  dispatch(addAlerts([error]));
                  return;
                }

                const success = await sendStoreCompositeView(
                  {
                    ...newCompositeView,
                    metadata: {
                      ...newCompositeView.metadata,
                      data: {
                        ...newCompositeView.metadata.data,
                        name: infoFormValues.name,
                      },
                    },
                  },
                  true
                );
                if (!success) {
                  dispatch(dispatch(addAlerts([error])));
                  return;
                }
                let taggingSuccess = true;
                if (infoFormValues.isDefaultForward) {
                  const tagSuccess = await sendTagView(
                    newCompositeView.id,
                    ViewTag.DEFAULT_FORWARD
                  );
                  taggingSuccess = taggingSuccess && tagSuccess;
                }
                if (infoFormValues.isDefaultBackward) {
                  const tagSuccess = await sendTagView(
                    newCompositeView.id,
                    ViewTag.DEFAULT_BACKWARD
                  );
                  taggingSuccess = taggingSuccess && tagSuccess;
                }
                if (!taggingSuccess) {
                  return;
                }
                navigate(`../${ROUTES.ListViewsAndSlices.path}`);
              }}
            >
              Save
            </Button>
          </div>
        ),
      }}
      videoOverlay={
        viewportDims && (
          <CompositeViewWindows
            intentionLayers={intentionLayers.filter((l) => l.visible)}
            reportedLayers={reportedLayers}
            videoWidth={viewportDims.width}
            videoHeight={viewportDims.height}
            onLayerChange={(layerId, newFrame) => {
              const update = intentionLayers.map((layer) =>
                layer.layerId === layerId
                  ? {
                      ...layer,
                      x: newFrame.x,
                      y: newFrame.y,
                      width: newFrame.width,
                      height: newFrame.height,
                    }
                  : layer
              );
              setIntentionLayers(update);
              debouncedUpdateCompositeView(update);
            }}
            selectedLayerId={selectedLayerId}
            setSelectedLayerId={setSelectedLayerId}
          />
        )
      }
      videoWrapperClassName={
        "pattern-cross pattern-rtc-gray-900 pattern-bg-rtc-gray-800 pattern-size-4 pattern-opacity-100"
      }
      disableVideo={false}
    >
      <Pane
        className="min-h-16"
        header={<Typography variant="caption">Info</Typography>}
      >
        <div className="pt-4 pb-6 px-6 flex flex-col gap-2">
          <CompositeViewInfoForm
            initialValues={initialInfoFormValues}
            onChange={setInfoFormValues}
            onError={setInfoFormHasErrors}
            isSubmitting={false}
          />
        </div>
      </Pane>
      <CompositeViewCameraSliceSelectionPane
        layers={intentionLayers}
        onLayerChange={(update: Layer[]) => {
          setIntentionLayers(update);
          updateCompositeView(update);
        }}
        selectedLayerId={selectedLayerId}
        setSelectedLayerId={setSelectedLayerId}
      />
    </SideBarBesideVideoLayout>
  );
};
