import * as turf from "@turf/turf";
import { Button, IconButton, type SvgIcon, Tooltip } from "@mui/material";
import { classes } from "@main/utils/theme";
import { INCH_PER_KM, KM_PER_INCH } from "@main/utils/units";
import {
  MAX_DISTANCE_METERS,
  useSteeringAutonomy,
} from "@main/hooks/useSteeringAutonomy";
import {
  type PathPlan,
  selectInControl,
  selectReportedControlState,
} from "@main/store/driving.slice";
import {
  pointToLineDistanceKm,
  rotateAbLine,
  sidestepOffsetKmRightward,
  translateAbLine,
} from "@main/logic/utils/geo";
import { ROUTES } from "@main/pages/routes";
import { selectTractorWidthM } from "@main/store/preferences.slice";
import { SteeringControlAlgo } from "@main/components/controls/drive.types";
import { useAppSelector } from "@main/store/hooks";
import { useNavigate } from "react-router";
import { useTractorAnalytics } from "@main/hooks/useTractorAnalytics";
import ChevronDoubleLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import ChevronDoubleRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import ChevronLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import ChevronRightIcon from "@mui/icons-material/KeyboardArrowRight";
import PauseIcon from "@mui/icons-material/PauseCircleOutline";
import PlayIcon from "@mui/icons-material/PlayCircleOutline";
import React, {
  type FC,
  type PropsWithChildren,
  type ReactNode,
  useMemo,
  useState,
} from "react";
import SettingsIcon from "@mui/icons-material/Settings";
import UTurnIcon from "@mui/icons-material/UTurnLeft";
import type { Point as GeojsonPoint } from "geojson";

interface AutonomyControlsBarProps {
  style?: React.CSSProperties;
  controlsLoading: boolean;
}

const signedOneDecimal = new Intl.NumberFormat(undefined, {
  minimumFractionDigits: 1,
  maximumFractionDigits: 1,
  signDisplay: "exceptZero",
});

export const AutonomyControlsBar: FC<AutonomyControlsBarProps> = ({
  style,
  controlsLoading,
}) => {
  const navigate = useNavigate();
  const { trackEvent } = useTractorAnalytics();

  const inControl = useAppSelector(selectInControl);
  const reportedAutonomy = useAppSelector(
    selectReportedControlState
  ).autonomousState;

  const {
    pathPlan,
    pathPlanningInput,
    currentPoint,
    enableGpsPathAutonomy,
    enableFurrowFollowingAutonomy,
    disableAutonomy,
    submitTargetLine,
  } = useSteeringAutonomy();
  const autonomyEnabled =
    reportedAutonomy?.steeringAssistAlgo ===
      SteeringControlAlgo.CV_FURROW_FOLLOW ||
    reportedAutonomy?.steeringAssistAlgo === SteeringControlAlgo.GPS_PATH;

  // ===== todo: remove all this when job-based autonomy is available.
  const implementWidthInches =
    Math.round(useAppSelector(selectTractorWidthM) * INCH_PER_KM) / 1000;

  const currentOffsetInchesRightward = useMemo<number | undefined>(() => {
    if (!pathPlanningInput) {
      return;
    }
    const { targetLine, sidestepOrigin } = pathPlanningInput;
    const kmRight = sidestepOffsetKmRightward(
      targetLine.geometry,
      sidestepOrigin.geometry
    );
    return kmRight * INCH_PER_KM;
  }, [pathPlanningInput]);

  const distanceMeters = useMemo((): number | undefined => {
    if (!currentPoint || !pathPlan) {
      return undefined;
    }
    const km = pointToLineDistanceKm(currentPoint, pathPlan.path);
    return km * 1e3;
  }, [pathPlan, currentPoint]);

  const heading = ((): string => {
    const geometry = pathPlanningInput?.targetLine?.geometry;
    if (!geometry) {
      return "";
    }
    const [a, b] = geometry.coordinates;
    let heading = turf.bearing(a, b);
    heading = Math.round(heading * 10) / 10;
    heading = (heading + 360) % 360; // [-180, 180] -> [0, 360]
    const headingString = heading.toLocaleString("en-US", {
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    });
    return `${headingString}°`;
  })();
  // =====

  const structuralClasses = "w-full h-8";

  if (controlsLoading) {
    return (
      <div
        className={classes("w-full h-7 mt-1 bg-rtc-gray-800")}
        style={style}
      />
    );
  }

  if (!pathPlan && !pathPlanningInput) {
    const enabled =
      autonomyEnabled &&
      reportedAutonomy?.steeringAssistAlgo ===
        SteeringControlAlgo.CV_FURROW_FOLLOW;
    const onToggle = enabled ? disableAutonomy : enableFurrowFollowingAutonomy;

    return (
      <div
        className={classes(
          structuralClasses,
          "text-sm grid justify-center items-center gap-6 uppercase font-lato",
          "transition-colors duration-500",
          enabled && "bg-green-700 text-white"
        )}
        style={{
          ...style,
          gridTemplateColumns: "minmax(0, 1fr) auto minmax(0, 1fr)",
        }}
      >
        <Tooltip
          arrow
          title="To enable GPS path following, use the side panel to plot a path"
          className="justify-self-end"
        >
          <span>No GPS path</span>
        </Tooltip>
        <span className="w-px h-6 bg-white"></span>
        <div className="flex justify-self-start justify-start items-center gap-2">
          <Button
            variant="outlined"
            className="py-0"
            onClick={() => {
              trackEvent("follow_furrow_clicked", { method: "button" });
              onToggle();
            }}
            disabled={!inControl}
          >
            {enabled ? "Stop Following Furrow" : "Follow Furrow"}
          </Button>
          <Tooltip arrow title="Calibrate wheel position">
            <div>
              <IconButton
                disabled={!inControl}
                onClick={() =>
                  navigate(`../${ROUTES.CalibrateWheelPosition.path}`)
                }
                className="p-0"
              >
                <SettingsIcon aria-label="calibrate wheel position" />
              </IconButton>
            </div>
          </Tooltip>
        </div>
      </div>
    );
  }

  const { onSteer, onSidestep } = (() => {
    if (!pathPlan || !pathPlanningInput || !currentPoint) {
      return {};
    }
    const oldLine = pathPlanningInput.targetLine.geometry;

    const onSteer = (
      degreesRight: number,
      sidestepOrigin: GeojsonPoint
    ): void => {
      const newLine = rotateAbLine(oldLine, currentPoint, degreesRight);
      submitTargetLine({
        fieldId: pathPlanningInput.fieldId,
        currentPoint,
        targetLine: newLine,
        sidestepOrigin,
      });
    };

    const onSidestep = (inchesRight: number): void => {
      const kmRight = inchesRight * KM_PER_INCH;
      const newLine = translateAbLine(oldLine, kmRight);
      submitTargetLine({
        fieldId: pathPlanningInput.fieldId,
        currentPoint,
        targetLine: newLine,
        sidestepOrigin: pathPlanningInput.sidestepOrigin.geometry,
      });
    };

    return { onSteer, onSidestep };
  })();

  const uTurnHandler = (): Action | undefined => {
    const label = "turn around 180°";
    const handler =
      onSteer && pathPlanningInput
        ? () => onSteer(180, pathPlanningInput.sidestepOrigin.geometry)
        : undefined;
    return { handler, label };
  };

  const steerHandler = (degreesRight: number): Action | undefined => {
    const direction = degreesRight < 0 ? "left" : "right";
    const label = `steer ${Math.abs(degreesRight)}° ${direction}`;
    const handler =
      onSteer && currentPoint
        ? () => onSteer(degreesRight, currentPoint)
        : undefined;
    return { handler, label };
  };

  const sidestepHandler = (inchesRight: number): Action | undefined => {
    const direction = inchesRight < 0 ? "left" : "right";
    const label = `sidestep ${Math.abs(inchesRight)}in ${direction}`;
    const handler = onSidestep ? () => onSidestep(inchesRight) : undefined;
    return { handler, label };
  };

  const inchesDisplay = (inches: number | undefined): ReactNode =>
    inches !== undefined && (
      <span>
        <span>
          {withMinusSign(signedOneDecimal.format(inches))}
          &thinsp;
        </span>
        <span className="text-xs">in</span>
      </span>
    );

  return (
    <div
      style={{
        ...style,
        gridTemplateColumns: "minmax(0, 1fr) auto minmax(0, 1fr)",
      }}
      className={classes(
        structuralClasses,
        "transition-colors duration-500",
        autonomyEnabled
          ? "bg-green-700 text-white"
          : "bg-rtc-gray-900 text-white",
        "grid items-center gap-6",
        "text-sm uppercase font-lato"
      )}
    >
      <AdjustableDisplay
        disabled={!inControl}
        className="justify-self-end"
        title="Heading"
        onLeft={steerHandler(-0.5)}
        onRight={steerHandler(0.5)}
        startAdornment={
          <ActionButton
            action={uTurnHandler()}
            icon={UTurnIcon}
            disabled={!inControl}
          />
        }
      >
        <AtLeastAsWide as="360.0°" align="center">
          {heading}
        </AtLeastAsWide>
      </AdjustableDisplay>
      <AutonomySwitch
        autonomyEnabled={autonomyEnabled}
        onEnable={enableGpsPathAutonomy}
        onDisable={disableAutonomy}
        pathPlan={pathPlan}
        currentPoint={currentPoint}
        distanceMeters={distanceMeters}
      />
      <AdjustableDisplay
        disabled={!inControl}
        className="justify-self-start"
        title="Sidestep"
        onLeftBig={sidestepHandler(-implementWidthInches)}
        onLeft={sidestepHandler(-0.5)}
        onRight={sidestepHandler(0.5)}
        onRightBig={sidestepHandler(implementWidthInches)}
      >
        <AtLeastAsWide as={inchesDisplay(-999.9)} align="center">
          {inchesDisplay(currentOffsetInchesRightward)}
        </AtLeastAsWide>
      </AdjustableDisplay>
    </div>
  );
};

interface Action {
  handler: (() => void) | undefined;
  label: string;
}
interface ActionButtonProps {
  action: Action | undefined;
  icon: typeof SvgIcon;
  disabled?: boolean;
}
const ActionButton: FC<ActionButtonProps> = ({
  action,
  icon: Icon,
  disabled,
}) => {
  if (!action) {
    return;
  }
  const { handler, label } = action;
  return (
    <Tooltip arrow title={label}>
      <span>
        <IconButton
          disabled={disabled}
          className={classes("p-0", !handler && "invisible")}
          color="inherit"
          onClick={action?.handler}
        >
          <Icon fontSize="small" aria-label={label} />
        </IconButton>
      </span>
    </Tooltip>
  );
};

interface AdjustableDisplayProps {
  title: string;
  onLeft?: Action | undefined;
  onRight?: Action | undefined;
  onLeftBig?: Action | undefined;
  onRightBig?: Action | undefined;
  startAdornment?: ReactNode;
  className?: string;
  disabled?: boolean;
}
const AdjustableDisplay: FC<PropsWithChildren<AdjustableDisplayProps>> = ({
  title,
  onLeft,
  onRight,
  onLeftBig,
  onRightBig,
  startAdornment,
  className,
  children,
  disabled,
}) => {
  return (
    <div className={classes("flex gap-2 items-center", className)}>
      {startAdornment}
      <span>{title}</span>
      <div className="flex gap-1 items-center normal-case">
        <ActionButton
          action={onLeftBig}
          icon={ChevronDoubleLeftIcon}
          disabled={disabled}
        />
        <ActionButton
          action={onLeft}
          icon={ChevronLeftIcon}
          disabled={disabled}
        />
        <strong className="flex" style={{ alignItems: "last baseline" }}>
          {children}
        </strong>
        <ActionButton
          action={onRight}
          icon={ChevronRightIcon}
          disabled={disabled}
        />
        <ActionButton
          action={onRightBig}
          icon={ChevronDoubleRightIcon}
          disabled={disabled}
        />
      </div>
    </div>
  );
};

interface AutonomySwitchProps {
  autonomyEnabled: boolean | undefined;
  onEnable: (value: PathPlan) => Promise<boolean>;
  onDisable: () => Promise<boolean>;
  pathPlan: PathPlan | undefined;
  currentPoint: GeojsonPoint | undefined;
  distanceMeters: number | undefined;
}
const AutonomySwitch: FC<AutonomySwitchProps> = ({
  autonomyEnabled,
  onEnable,
  onDisable,
  pathPlan,
  currentPoint,
  distanceMeters,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const inControl = useAppSelector(selectInControl);

  const onToggle = (): void => {
    setLoading(true);
    const promise =
      autonomyEnabled === false && pathPlan ? onEnable(pathPlan) : onDisable();
    promise.finally(() => {
      setLoading(false);
    });
  };

  const tooFar =
    distanceMeters === undefined || distanceMeters > MAX_DISTANCE_METERS;

  let tooltipOverride;
  if (!currentPoint) {
    tooltipOverride = "GPS position loading…";
  } else if (tooFar && distanceMeters !== undefined) {
    const distanceString = formatMeters(distanceMeters, {
      roundingMode: "ceil",
    });
    const targetString = formatMeters(MAX_DISTANCE_METERS);
    tooltipOverride = `You are ${distanceString} from the path start. Move to within ${targetString} to enable follow an autonomous path.`;
  }

  return (
    <Tooltip
      arrow
      placement="top"
      title={
        tooltipOverride ?? (
          <div className="flex flex-col items-center">
            {autonomyEnabled ? (
              <>
                <span>Following path</span>
                <span>Click to disengage autonomous steering</span>
              </>
            ) : (
              <>
                <span>Ready to follow path</span>
                <span>Click to engage autonomous steering</span>
              </>
            )}
          </div>
        )
      }
    >
      <span>
        <IconButton
          disabled={loading || (tooFar && !autonomyEnabled) || !inControl}
          className="p-0 disabled:opacity-40"
          classes={{ disabled: "text-inherit" }}
          color="inherit"
          onClick={onToggle}
        >
          {autonomyEnabled ? (
            <PauseIcon className="h-full aspect-square" />
          ) : (
            <PlayIcon className="h-full aspect-square" />
          )}
        </IconButton>
      </span>
    </Tooltip>
  );
};

interface AtLeastAsWideProps {
  as: ReactNode;
  align: "left" | "right" | "center";
}
const AtLeastAsWide: FC<PropsWithChildren<AtLeastAsWideProps>> = ({
  as: lowerBound,
  align,
  children,
}) => {
  const className: string = {
    left: "items-start",
    right: "items-end",
    center: "items-center",
  }[align];
  return (
    <div className={classes("inline-flex flex-col", className)}>
      <span className="h-0 max-h-0 overflow-hidden">{lowerBound}</span>
      {children}
    </div>
  );
};

const MINUS_SIGN = "\u2212";
const withMinusSign = (s: string): string => s.replace("-", MINUS_SIGN);

const formatMeters = (
  m: number,
  options?: Partial<Intl.NumberFormatOptions>
): string => {
  const metersString = m.toLocaleString("en-US", {
    maximumFractionDigits: 1,
    ...options,
  });
  const nbsp = "\u00a0";
  return `${metersString}${nbsp}m`;
};
