import { capitalize, Tooltip } from "@mui/material";
import {
  mosScoreToStatus,
  mosScoreToText,
} from "@main/components/debuggingInfo/NetworkInfo";
import { selectNetworkStats } from "@main/store/driving.slice";
import { StatusOption } from "@main/components/StatusCircle";
import { useAppSelector } from "@main/store/hooks";
import clsx from "clsx";
import LoadingIcon from "@mui/icons-material/Autorenew";
import React, { FC } from "react";
import SignalCellularAlt1BarIcon from "@mui/icons-material/SignalCellularAlt1Bar";
import SignalCellularAlt2BarIcon from "@mui/icons-material/SignalCellularAlt2Bar";
import SignalCellularAltIcon from "@mui/icons-material/SignalCellularAlt";
import SignalCellularConnectedNoInternet0BarIcon from "@mui/icons-material/SignalCellularConnectedNoInternet0Bar";
import SignalCellularConnectedNoInternet4BarIcon from "@mui/icons-material/SignalCellularConnectedNoInternet4Bar";

export const ConnectionStatus: FC = () => {
  const networkStats = useAppSelector(selectNetworkStats);
  const isConnected = networkStats.rtcStatus === "connected";
  const tooltipText = isConnected
    ? `Video Network Status: ${networkStats.mosScore ? mosScoreToText(networkStats.mosScore) : "Unknown"}`
    : capitalize(networkStats.rtcStatus ?? "Unknown");

  return (
    <Tooltip title={tooltipText}>
      <div className="leading-none">
        {networkStats.rtcStatus ? (
          <>
            {isConnected && (
              <MosScoreIcon
                className="h-full"
                mosScore={networkStats.mosScore}
              />
            )}
            {["connecting", "new"].includes(networkStats.rtcStatus) && (
              <LoadingIcon
                className="h-full animate-spin text-primary-400"
                fontSize="small"
              />
            )}
            {["closed", "disconnected", "failed"].includes(
              networkStats.rtcStatus
            ) && (
              <SignalCellularConnectedNoInternet4BarIcon
                className="h-full text-error-400"
                fontSize="small"
              />
            )}
          </>
        ) : null}
      </div>
    </Tooltip>
  );
};

interface MosScoreIconProps {
  className?: string;
  mosScore?: number;
}
const MosScoreIcon: FC<MosScoreIconProps> = ({ className, mosScore }) => {
  if (!mosScore) {
    return (
      <SignalCellularConnectedNoInternet0BarIcon
        className={clsx(className, "text-rtc-gray-400")}
        fontSize="small"
      />
    );
  }
  const mosScoreStatus = mosScoreToStatus(mosScore);
  switch (mosScoreStatus) {
    case StatusOption.GOOD:
      return (
        <SignalCellularAltIcon
          className={clsx(className, "text-success-400")}
          fontSize="small"
        />
      );
    case StatusOption.OK:
      return (
        <SignalCellularAlt2BarIcon
          className={clsx(className, "text-yellow-400")}
          fontSize="small"
        />
      );
    case StatusOption.BAD:
      return (
        <SignalCellularAlt1BarIcon
          className={clsx(className, "text-error-400")}
          fontSize="small"
        />
      );
  }
};
