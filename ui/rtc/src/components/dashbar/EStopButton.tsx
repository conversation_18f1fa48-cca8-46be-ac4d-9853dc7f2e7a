import {
  Button,
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  IconButton,
  SvgIcon,
  Tooltip,
  Typography,
} from "@mui/material";
import { classes } from "@main/utils/theme";
import { createSetState } from "@main/communication/rtc/dataChannelMessages";

import { HHState } from "../controls/drive.types";
import { selectInDriverSeat } from "@main/store/driving.slice";
import { useAppSelector } from "@main/store/hooks";
import { useTractorAnalytics } from "@main/hooks/useTractorAnalytics";
import { useTractorControlChannelMessaging } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import EstopIcon from "@main/assets/estop.svg?react";
import React, { FC, useState } from "react";

interface EStopButtonProps {}

export const EStopButton: FC<EStopButtonProps> = () => {
  const inControl = useAppSelector(selectInDriverSeat);
  const { sendControlsMessage } = useTractorControlChannelMessaging();
  const { trackEvent } = useTractorAnalytics();

  const estop = (): void => {
    trackEvent("e_stop_clicked", { method: "button" });
    sendControlsMessage(createSetState(HHState.HH_ESTOP));
    setModalOpen(false);
  };

  const [modalOpen, setModalOpen] = useState<boolean>(false);

  return (
    <>
      <Tooltip
        placement="top"
        arrow
        title={inControl ? "E-Stop" : "Take control to use E-Stop"}
      >
        <div
          className={classes(
            inControl ? "bg-yellow-600" : "bg-rtc-gray-800",
            "rounded-sm p-1"
          )}
        >
          <IconButton
            disabled={!inControl}
            className={classes(inControl ? "bg-white" : "bg-black", "p-0")}
            onClick={() => setModalOpen(true)}
          >
            <SvgIcon
              className={classes(
                inControl ? "fill-red-600" : "fill-rtc-gray-600"
              )}
              component={EstopIcon}
              scale={"small"}
            />
          </IconButton>
        </div>
      </Tooltip>
      <Dialog title="Are you sure?" open={modalOpen}>
        <div className="flex flex-col px-4 pb-4 max-w-96 gap-4 text-center">
          <DialogTitle>Are you sure?</DialogTitle>
          <Typography className="flex-wrap">
            Tractor will require in-person intervention to regain control.
          </Typography>
          <div className="flex gap-2">
            <Button
              onClick={estop}
              className="w-full"
              variant="contained"
              color="error"
            >
              E-Stop
            </Button>
            <Button
              onClick={() => setModalOpen(false)}
              className="w-full"
              variant="contained"
              color="info"
            >
              Cancel
            </Button>
          </div>
        </div>
      </Dialog>
    </>
  );
};
