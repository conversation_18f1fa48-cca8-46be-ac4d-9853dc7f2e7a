import { classes } from "@main/utils/theme";
import { selectFuelLevel } from "@main/store/driving.slice";
import { SvgIcon, Tooltip } from "@mui/material";
import { useAppSelector } from "@main/store/hooks";
import clsx from "clsx";
import FuelHighIcon from "@main/assets/fuelHigh.svg?react";
import FuelLowIcon from "@main/assets/fuelLow.svg?react";
import FuelMedIcon from "@main/assets/fuelMed.svg?react";
import FuelNeedleIcon from "@main/assets/fuelNeedle.svg?react";
import React, { FC, useMemo } from "react";

// constants represent dependencies between this component and the
// gauge assets; if you change them, you might need to change the gauge
// assets to match in order for the component to look and feel right (and
// vice versa.)

// cutoffs for which fuel levels are considered "high" and "low"
const GAUGE_HIGH_CUTOFF = 0.67;
const GAUGE_LOW_CUTOFF = 0.33;
// center of rotation of the gauge needle in CSS transform space
const GAUGE_NEEDLE_CENTER_X = 0;
const GAUGE_NEEDLE_CENTER_Y = 7;

export const FuelGauge: FC = () => {
  const fuelLevel = useAppSelector(selectFuelLevel);
  // fuel level ranges from 0 to 1, with -1 meaning invalid/unknown; old
  // firmware may not expose the fuel level in control state messages
  const fuelLevelValid = fuelLevel !== undefined && fuelLevel >= 0;
  let fuelLevelCategory: "unknown" | "low" | "medium" | "high" = "unknown";

  if (fuelLevelValid) {
    if (fuelLevel > GAUGE_HIGH_CUTOFF) {
      fuelLevelCategory = "high";
    } else if (fuelLevel > GAUGE_LOW_CUTOFF) {
      fuelLevelCategory = "medium";
    } else {
      fuelLevelCategory = "low";
    }
  }

  const gaugeLowClasses = useMemo(
    () =>
      classes("w-8 fill-rtc-gray", {
        "fill-error": fuelLevelCategory === "low",
        "fill-warning": fuelLevelCategory === "medium",
        "fill-success": fuelLevelCategory === "high",
      }),
    [fuelLevelCategory]
  );

  const gaugeMiddleClasses = useMemo(
    () =>
      classes("w-8 absolute left-0 top-0 fill-rtc-gray", {
        "fill-warning": fuelLevelCategory === "medium",
        "fill-success": fuelLevelCategory === "high",
      }),
    [fuelLevelCategory]
  );

  const gaugeHighClasses = useMemo(
    () =>
      clsx("w-8 absolute left-0 top-0 fill-rtc-gray", {
        "fill-success": fuelLevelCategory === "high",
      }),
    [fuelLevelCategory]
  );

  // CSS transform for the gauge needle; undefined if the needle shouldn't be
  // shown at all
  const needleTransform = useMemo(() => {
    if (!fuelLevelValid) {
      return undefined;
    }

    // map [0, 1] onto [-90, 90]
    const angle = -90 + fuelLevel * 180;

    return `rotate(${angle} ${GAUGE_NEEDLE_CENTER_X} ${GAUGE_NEEDLE_CENTER_Y})`;
  }, [fuelLevel, fuelLevelValid]);

  const tooltipText = useMemo(() => {
    if (fuelLevelValid) {
      const fuelLevelPct = Math.round(fuelLevel * 100);
      return `Fuel level: ${fuelLevelPct}%`;
    } else {
      return "Fuel level unknown";
    }
  }, [fuelLevel, fuelLevelValid]);

  return (
    <>
      <Tooltip arrow placement="top" title={tooltipText}>
        <div className="relative">
          <SvgIcon
            fontSize="small"
            component={FuelLowIcon}
            inheritViewBox={true}
            className={gaugeLowClasses}
          />
          <SvgIcon
            fontSize="small"
            component={FuelMedIcon}
            inheritViewBox={true}
            className={gaugeMiddleClasses}
          />
          <SvgIcon
            fontSize="small"
            component={FuelHighIcon}
            inheritViewBox={true}
            className={gaugeHighClasses}
          />
          {needleTransform === undefined ? null : (
            <SvgIcon
              fontSize="small"
              component={FuelNeedleIcon}
              inheritViewBox={true}
              className="w-8 absolute top-0 left-0"
              transform={needleTransform}
            />
          )}
        </div>
      </Tooltip>
    </>
  );
};
