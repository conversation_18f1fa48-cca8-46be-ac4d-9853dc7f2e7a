import { Badge, <PERSON>con<PERSON>utton, Too<PERSON>ip, Typography } from "@mui/material";
import { classes } from "@main/utils/theme";
import { GEAR_INFO } from "../controls/drive.types";
import {
  overrideQuickstopLockout,
  selectClientGear,
  selectInControl,
  selectQuickstopLockout,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import LockIcon from "@mui/icons-material/Lock";
import React, { FC, useEffect, useState } from "react";

interface GearDisplayProps {
  controlsReady: boolean;
}

export const GearDisplay: FC<GearDisplayProps> = ({ controlsReady }) => {
  const dispatch = useAppDispatch();
  const clientGear = useAppSelector(selectClientGear);
  const reportedGear = useAppSelector(
    (state) => selectReportedControlState(state).gear?.gear
  );
  const lockedOut = useAppSelector(selectQuickstopLockout);
  const inControl = useAppSelector(selectInControl);

  const synced = !inControl || reportedGear === clientGear;
  // Show a badge highlighting desync if we've desynced for an extended period.
  const DESYNC_TIMEOUT_MS = 500;
  const [showDesyncBadge, setShowDesyncBadge] = useState(false);

  useEffect(() => {
    if (!controlsReady) {
      return;
    }
    if (synced) {
      setShowDesyncBadge(false);
    } else {
      // show desync badge after a brief delay
      const timeout = setTimeout(() => {
        setShowDesyncBadge(true);
      }, DESYNC_TIMEOUT_MS);
      return () => clearTimeout(timeout);
    }
  }, [DESYNC_TIMEOUT_MS, controlsReady, synced]);

  const reportedGearAbbreviation =
    controlsReady && reportedGear !== undefined
      ? GEAR_INFO[reportedGear]?.abbreviation
      : undefined;

  let title = "Waiting to connect";
  if (controlsReady && reportedGear !== undefined) {
    title = `${GEAR_INFO[reportedGear]?.name} gear`;
    if (!synced) {
      title += `, trying to shift into ${GEAR_INFO[clientGear]?.name}`;
    }
  }

  return (
    <Tooltip arrow title={lockedOut ? undefined : title}>
      <div className="w-6 text-center leading-[0] relative">
        <Badge
          anchorOrigin={{
            horizontal: "right",
            vertical: "top",
          }}
          variant="dot"
          overlap="rectangular"
          color="primary"
          invisible={!showDesyncBadge}
        >
          <Typography
            className={classes(
              "w-4 text-center font-bold",
              !synced && "text-rtc-gray-400",
              lockedOut && "text-yellow-400"
            )}
          >
            {reportedGearAbbreviation ?? "—"}
          </Typography>
        </Badge>
        {lockedOut && (
          <Tooltip
            arrow
            title="Gear locked while tractor stops. Click to override."
          >
            <IconButton
              className="absolute top-0 left-0 right-0 bottom-0 flex items-center align-center"
              onClick={() => dispatch(overrideQuickstopLockout())}
            >
              <LockIcon
                aria-label="speed locked while stopping; click to override"
                className="absolute bottom-0 right-0 w-3 h-3 text-yellow-400"
              />
            </IconButton>
          </Tooltip>
        )}
      </div>
    </Tooltip>
  );
};
