import { SafetyToggle } from "./SafetyToggle";
import { selectReportedControlState } from "@main/store/driving.slice";
import { Sensors, SensorsOff } from "@mui/icons-material";

import { useAppSelector } from "@main/store/hooks";
import { useTractorAnalytics } from "@main/hooks/useTractorAnalytics";
import { useTractorControlChannelMessaging } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import React, { FC } from "react";

export const SafetySensorsControls: FC = () => {
  const isTriggered = Boolean(
    useAppSelector(selectReportedControlState).safety?.safetyTripped
  );

  const isBypassed = Boolean(
    useAppSelector(selectReportedControlState).safety?.bypassEnabled
  );

  const { sendSetSafetyBypass, sendEnableControl } =
    useTractorControlChannelMessaging();
  const { trackEvent } = useTractorAnalytics();

  return (
    <SafetyToggle
      onClick={(v) => {
        trackEvent("safety_radar_toggled", { enabled: v, method: "button" });
        sendSetSafetyBypass(v);
        sendEnableControl();
      }}
      isBypassed={isBypassed}
      isTriggered={isTriggered}
      icon={(a) => <Sensors className={a} />}
      bypassedIcon={(a) => <SensorsOff className={a} />}
      messageTriggered="Safety radar or bumper triggered"
      messageBypassed="Safety radar and bumper bypassed"
      messageNominal="Safety radar and bumpers"
    />
  );
};
