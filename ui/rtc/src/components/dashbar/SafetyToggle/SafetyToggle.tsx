import { classes } from "@main/utils/theme";
import { I<PERSON><PERSON><PERSON>on, Tooltip } from "@mui/material";
import { selectInDriverSeat } from "@main/store/driving.slice";
import { useAppSelector } from "@main/store/hooks";
import React, { FC, ReactNode } from "react";

export interface SafetyToggleProps {
  isBypassed: boolean;
  isTriggered: boolean;
  messageBypassed: string;
  messageTriggered: string;
  messageNominal: string;
  altActionText?: string;
  icon: (className: string) => ReactNode;
  bypassedIcon?: (className: string) => ReactNode;
  hideToggle?: boolean;
  onClick?: (value: boolean) => void;
}

export const SafetyToggle: FC<SafetyToggleProps> = ({
  isBypassed,
  isTriggered,
  messageBypassed,
  messageTriggered,
  messageNominal,
  altActionText,
  bypassedIcon,
  icon,
  hideToggle,
  onClick,
}: SafetyToggleProps) => {
  const inControl = useAppSelector(selectInDriverSeat);

  const iconColor = (): string => {
    if (isBypassed) {
      return "fill-yellow-600";
    }
    if (isTriggered) {
      return "fill-red-600";
    }
    return "fill-rtc-gray-600";
  };

  const actionText = "Click to toggle bypass";

  const tooltipTitle = (): ReactNode => {
    return (
      <div className="flex flex-col items-center">
        {isTriggered && messageTriggered && <span>{messageTriggered}</span>}
        {isBypassed && messageBypassed && <span>{messageBypassed}</span>}
        {(isTriggered || isBypassed) && inControl && (
          <span>{altActionText ?? actionText}</span>
        )}
        {!isTriggered && !isBypassed && messageNominal && (
          <span>{messageNominal}</span>
        )}
      </div>
    );
  };
  return (
    <Tooltip placement="top" arrow title={tooltipTitle()}>
      <span>
        <IconButton
          size="small"
          disabled={!inControl}
          disableRipple={hideToggle}
          className={`flex my-auto gap-1 ${classes(hideToggle && "cursor-auto")}`}
          onClick={() => !hideToggle && onClick?.(!isBypassed)}
        >
          {isTriggered && (
            <div className="fixed my-auto size-5 rounded-full bg-red-600 animate-ping" />
          )}
          {isBypassed && bypassedIcon
            ? bypassedIcon(`size-5 my-auto flex ${iconColor()}`)
            : icon(`size-5 my-auto flex ${iconColor()}`)}
        </IconButton>
      </span>
    </Tooltip>
  );
};
