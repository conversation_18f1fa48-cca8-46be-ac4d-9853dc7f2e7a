import { Bad<PERSON>, Tooltip } from "@mui/material";
import { classes } from "@main/utils/theme";
import {
  GRAY_PALETTE,
  GREEN_LIGHT_PALETTE,
  OCEAN_PALETTE,
} from "@main/theme/colors";
import { guessSerial } from "@main/logic/utils/robots";
import {
  overrideQuickstopLockout,
  selectClientSpeed,
  selectInControl,
  selectQuickstopLockout,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useMaxSpeedMph } from "@main/hooks/useMaxSpeedMph";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import LockIcon from "@mui/icons-material/Lock";
import React, { FC } from "react";

interface SpeedometerProps {
  style?: React.CSSProperties;
  controlsLoading: boolean;
}

export const Speedometer: FC<SpeedometerProps> = ({
  style,
  controlsLoading,
}) => {
  const dispatch = useAppDispatch();
  const clientSpeedMph = useAppSelector(selectClientSpeed);
  const lockedOut = useAppSelector(selectQuickstopLockout);
  const controlState = useAppSelector(selectReportedControlState);
  const tractorState = controlState?.tractorBoardState;
  const usingSpeedControl = controlState.atim?.enabled === true;

  const inControl = useAppSelector(selectInControl);

  const reportedSpeedMph = controlState?.speed?.speedMph;

  const showControls = !controlsLoading;
  const rtcEnabled = inControl && !tractorState?.rtcLockout;
  const displaySpeedMph = rtcEnabled ? clientSpeedMph : reportedSpeedMph;

  return (
    <div className="relative">
      <div
        className={
          showControls
            ? "hidden"
            : classes(
                "absolute inset-0 flex items-center justify-center",
                "text-white/75 uppercase text-sm",
                "bg-rtc-gray-800 rounded-t mt-1"
              )
        }
      >
        <span className={classes("animate-pulse")}>
          Connecting to controls…
        </span>
      </div>
      <div
        className={classes("grid gap-1 py-1", showControls || "invisible")}
        style={{
          ...style,
          gridTemplateAreas: rtcEnabled
            ? "'client-left text client-right' 'reported-left text reported-right'"
            : "'reported-left text reported-right' 'reported-left text reported-right'",
          gridTemplateRows: "1.5rem 0.5rem",
        }}
      >
        <div
          style={{ gridArea: "text" }}
          className={classes(
            "w-12 h-full relative flex flex-col justify-center items-center font-lato",
            lockedOut && "text-yellow-400"
          )}
        >
          <Tooltip
            title={usingSpeedControl ? "Speed Control Active" : undefined}
          >
            <Badge invisible={!usingSpeedControl} variant="dot" color="success">
              <span className="tabular-nums font-bold text-lg/none">
                {displaySpeedMph?.toFixed(2) ?? "—"}
              </span>
            </Badge>
          </Tooltip>
          <span className="text-xs/none">mph</span>
          {lockedOut && (
            <Tooltip
              arrow
              title="Speed locked while tractor stops. Click to override."
            >
              <button
                className="absolute w-full h-full bg-transparent border-none cursor-pointer"
                onClick={() => dispatch(overrideQuickstopLockout())}
              >
                <LockIcon
                  aria-label="speed locked while stopping; click to override"
                  className="absolute bottom-0 right-0 w-3 h-3 aspect-square text-yellow-400 z-10"
                />
              </button>
            </Tooltip>
          )}
        </div>
        {rtcEnabled && (
          <>
            <SpeedBars
              speedMph={clientSpeedMph}
              palette={usingSpeedControl ? GREEN_LIGHT_PALETTE : OCEAN_PALETTE}
              className="-scale-x-100"
              style={{ gridArea: "client-left" }}
            />
            <SpeedBars
              speedMph={clientSpeedMph}
              palette={usingSpeedControl ? GREEN_LIGHT_PALETTE : OCEAN_PALETTE}
              style={{ gridArea: "client-right" }}
            />
          </>
        )}
        <SpeedBars
          speedMph={reportedSpeedMph}
          palette={GRAY_PALETTE}
          className="-scale-x-100"
          style={{ gridArea: "reported-left" }}
        />
        <SpeedBars
          speedMph={reportedSpeedMph}
          palette={GRAY_PALETTE}
          style={{ gridArea: "reported-right" }}
        />
      </div>
    </div>
  );
};

type Palette = {
  [Weight in 100 | 200 | 300 | 400 | 500]: string;
};

interface SpeedBarSpec {
  maxMph: number;
  colorWeight: keyof Palette;
}
const SPEED_BARS: ReadonlyArray<Readonly<SpeedBarSpec>> = [
  { maxMph: 0.5, colorWeight: 500 },
  { maxMph: 1, colorWeight: 400 },
  { maxMph: 1.5, colorWeight: 300 },
  { maxMph: 2.25, colorWeight: 200 },
  { maxMph: 3.5, colorWeight: 100 },
  { maxMph: 5, colorWeight: 100 },
  { maxMph: 10, colorWeight: 100 },
];

interface SpeedBarsProps {
  speedMph: number | undefined;
  palette: Palette;
  className?: string;
  style?: React.CSSProperties;
}

const SpeedBars: FC<SpeedBarsProps> = ({
  className,
  style,
  speedMph,
  palette,
}) => {
  if (speedMph === undefined) {
    speedMph = 0;
  }

  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);
  const tractorMaxMph = useMaxSpeedMph(serial);

  return (
    <div className={classes(className, "flex gap-1 h-full")} style={style}>
      {SPEED_BARS.map(({ maxMph, colorWeight }, i, bars) => {
        const minMph = bars[i - 1]?.maxMph ?? 0;
        if (i === bars.length - 1 && maxMph < tractorMaxMph) {
          maxMph = tractorMaxMph;
        }
        const progressUnclamped = (speedMph - minMph) / (maxMph - minMph);
        const progress = Math.max(0, Math.min(1, progressUnclamped));
        return (
          <div
            key={i}
            className="h-full w-4 inline-block bg-rtc-gray-800 relative"
          >
            <div
              className="absolute top-0 bottom-0 left-0"
              style={{
                width: `${progress * 100}%`,
                background: palette[colorWeight],
              }}
            />
          </div>
        );
      })}
    </div>
  );
};
