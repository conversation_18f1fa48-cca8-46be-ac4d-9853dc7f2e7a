import { AutonomyMode, SteeringControlAlgo } from "../controls/drive.types";
import { classes } from "@main/utils/theme";
import { getConfigNode } from "@main/logic/utils/portal";
import { guessSerial } from "@main/logic/utils/robots";
import {
  selectClientSteeringWheelPosition,
  selectInControl,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { Tooltip } from "@mui/material";
import { useAppSelector } from "@main/store/hooks";
import { useGetConfigQuery } from "@main/store/portalApi/configs";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import React, { FC, useMemo, useRef, useState } from "react";

interface SteerometerProps {
  style?: React.CSSProperties;
  onCenterSteering?: () => void;
  controlsLoading: boolean;
}

export const Steerometer: FC<SteerometerProps> = ({
  style,
  onCenterSteering,
  controlsLoading,
}) => {
  const clientSteering = useAppSelector(selectClientSteeringWheelPosition);
  const controlState = useAppSelector(selectReportedControlState);
  const reportedSteering = controlState?.steeringPos?.pos;
  const tractorState = controlState?.tractorBoardState;
  const autonomyEnabled = Boolean(
    controlState.autonomousState?.steeringAssistAlgo !==
      SteeringControlAlgo.NONE
  );

  const inControl = useAppSelector(selectInControl);

  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);
  const { data: configResponse } = useGetConfigQuery(serial);

  const steeringExtentDegrees = useMemo<number | undefined>(() => {
    const config = configResponse?.config;
    if (!config) {
      return;
    }
    const steeringNode = getConfigNode(config, [
      "rtc",
      "tractor_ctl",
      "steering_conf",
    ]);
    if (!steeringNode) {
      return;
    }
    const leftNode = getConfigNode(steeringNode, ["left_hard_stop"]);
    const rightNode = getConfigNode(steeringNode, ["right_hard_stop"]);
    const leftExtent = leftNode?.value?.floatVal;
    const rightExtent = rightNode?.value?.floatVal;
    if (!leftExtent || !rightExtent) {
      // reject undefined; also reject zero
      return;
    }
    return (Math.abs(leftExtent) + Math.abs(rightExtent)) / 2;
  }, [configResponse]);
  const tractorInControl =
    controlState.autonomousState?.mode === AutonomyMode.TASK_AUTONOMY;

  const rtcEnabled =
    inControl && !tractorState?.rtcLockout && !tractorInControl;
  const displaySteering = (rtcEnabled ? clientSteering : reportedSteering) ?? 0;
  const displayQuantity =
    steeringExtentDegrees === undefined
      ? { scalar: displaySteering * 100, units: "%" }
      : { scalar: displaySteering * steeringExtentDegrees, units: "°" };

  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const staticContainerClasses =
    "w-full max-w-[600px] h-6 rounded-b-full bg-rtc-gray-800 relative flex justify-center items-end border-none text-white";
  if (controlsLoading) {
    return <div className={staticContainerClasses} style={style} />;
  }

  if (!rtcEnabled && tooltipOpen) {
    setTooltipOpen(false);
    return;
  }

  return (
    <Tooltip
      arrow
      placement="top"
      title={onCenterSteering ? "Click to center steering" : ""}
      open={rtcEnabled && tooltipOpen}
      onOpen={() => setTooltipOpen(true)}
      onClose={() => setTooltipOpen(false)}
    >
      <button
        ref={buttonRef}
        className={classes(
          staticContainerClasses,
          onCenterSteering &&
            rtcEnabled &&
            "cursor-pointer hover:bg-rtc-gray-700 active:bg-rtc-gray-900"
        )}
        style={{ ...style, containerType: "size" }}
        disabled={!onCenterSteering || !rtcEnabled}
        onClick={() => {
          onCenterSteering?.();
          // Close tooltip on click, as a visible signal that the click went
          // through---but let the button remain clickable.
          setTooltipOpen(false);
          // Blur so that further keypresses (steering, speed, gear, etc.)
          // don't make this :focus-visible again. It's not the *most*
          // accessible, but it's better than tabIndex={-1}.
          buttonRef.current?.blur();
        }}
      >
        {/* bar showing reported steering angle */}
        <div
          className="bg-rtc-gray-650 h-1 absolute top-0 left-1/2 w-1/2 origin-left"
          style={{
            transform: "scaleX(var(--reported-steering))",
            ["--reported-steering" as any]: reportedSteering ?? 0,
          }}
        />

        {/* marker triangle showing client steering angle */}
        {rtcEnabled && (
          <MarkerTriangle
            steering={clientSteering}
            className={classes(
              autonomyEnabled ? "text-green-600" : "text-primary-400",
              "transition-colors"
            )}
          />
        )}

        {/* digital readout */}
        <StableDecimal
          value={displayQuantity}
          className="absolute text-sm font-bold select-none"
        />
      </button>
    </Tooltip>
  );
};

interface MarkerTriangleProps {
  steering: number;
  className?: string;
}
const MarkerTriangle: FC<MarkerTriangleProps> = ({ steering, className }) => {
  // triangle dimensions
  const width = 22;
  const triangleHeight = 12;
  const inset = 3;

  return (
    <svg
      className={classes(className, "absolute top-0 left-1/2")}
      width={width}
      height="100%"
      style={{
        transform: "translateX(calc(var(--steering) * 50cqw - 50%))",
        ["--steering" as any]: steering,
      }}
    >
      <g>
        <path
          className="fill-rtc-gray-800"
          d={`M0 0L${width / 2} ${triangleHeight}L${width} 0z`}
        />
        <line
          className="stroke-rtc-gray-800"
          x1={width / 2}
          y1={0}
          x2={width / 2}
          y2="100%"
          strokeWidth={3 + inset}
        />
      </g>
      <g>
        <line
          stroke="currentColor"
          x1={width / 2}
          y1={0}
          x2={width / 2}
          y2="100%"
          strokeWidth={2}
        />
        <path
          fill="currentColor"
          d={`M${inset} 0L${width / 2} ${triangleHeight - inset}L${width - inset} 0z`}
        />
      </g>
    </svg>
  );
};

interface Quantity {
  scalar: number;
  units: string;
}

// A decimal where each column of the number stays in the same place even as
// the number changes magnitudes. Designed for scalars between -999 and 999
// where changes smaller than 0.1 are negligible.
interface StableDecimalProps {
  value: Quantity;
  className?: string;
}
const StableDecimal: FC<StableDecimalProps> = ({
  value: { scalar, units },
  className,
}) => {
  const MINUS_SIGN = "\u2212";

  const isNegative = scalar < 0;
  const absValue = Math.abs(scalar);
  const absText = absValue.toFixed(absValue < 100 ? 1 : 0);
  const match = absText.match(/^([0-9]+)(\.[0-9]+)?$/);
  let intPart = "";
  let fracPart = "";
  if (match) {
    [, intPart, fracPart = ""] = match;
  } else {
    // avoid crashing; can happen on e.g. NaN
    console.warn(`Unexpected number format: ${JSON.stringify(absText)}`);
    intPart = absText;
  }

  // empirical width of one digit plus half a decimal point, in Lato
  const dx = "0.68em";
  return (
    <svg
      width="100%" // arbitrary as long as it's wide enough, so just slam it
      height="1em"
      className={classes(className, "tabular-figures font-lato")}
      fill="currentcolor"
    >
      <text x="50%" dx={dx} dominantBaseline="hanging" textAnchor="end">
        {isNegative ? MINUS_SIGN : ""}
        {intPart}
        {fracPart || <tspan fill="transparent">.</tspan>}
      </text>
      <text x="50%" dx={dx} dominantBaseline="hanging">
        {units}
      </text>
    </svg>
  );
};
