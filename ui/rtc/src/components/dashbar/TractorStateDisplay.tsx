import { Button, SvgIcon, Tooltip } from "@mui/material";
import { HHState } from "@main/components/controls/drive.types";
import {
  selectInDriverSeat,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { useAppSelector } from "@main/store/hooks";
import { useTractorControlChannelMessaging } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import EstopIcon from "@main/assets/estop.svg?react";
import LockIcon from "@mui/icons-material/Lock";
import QuestionMarkIcon from "@mui/icons-material/QuestionMark";
import React, { FC, useRef, useState } from "react";
import RemoteDriveIcon from "@main/assets/remoteDrive.svg?react";
import SensorsIcon from "@mui/icons-material/Sensors";
import SteeringWheelIcon from "@main/assets/steeringWheel.svg?react";
import StoppedIcon from "@mui/icons-material/Stop";

export const TractorStateDisplay: FC = () => {
  const { sendEnableControl } = useTractorControlChannelMessaging();
  const inDriverSeat = useAppSelector(selectInDriverSeat);

  const boardState = useAppSelector(
    selectReportedControlState
  ).tractorBoardState;

  const [takingControl, setTakingControl] = useState<boolean>(false);
  const takingControlTimeoutRef = useRef<
    ReturnType<typeof setTimeout> | undefined
  >(undefined);

  // Sets the state to "taking control..." until the tractor state
  // changes or a timeout occurs, whichever happens first.
  const markAsTakingControl = (): void => {
    setTakingControl(true);
    const lastTimeoutId = takingControlTimeoutRef.current;
    if (lastTimeoutId !== undefined) {
      clearTimeout(lastTimeoutId);
    }
    takingControlTimeoutRef.current = setTimeout(() => {
      setTakingControl(false);
    }, 5000);
  };

  const [lastTractorState, setLastTractorState] = useState(boardState);
  if (lastTractorState !== boardState) {
    setLastTractorState(boardState);
    setTakingControl(false);
    const timeoutId = takingControlTimeoutRef.current;
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId);
      takingControlTimeoutRef.current = undefined;
    }
    return;
  }

  interface StateDisplayParams {
    label: string[];
    icon: React.ElementType;
    iconClasses?: string;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
    disabled?: boolean;
  }

  const getStateDisplay = (): StateDisplayParams => {
    if (boardState?.rtcLockout) {
      return {
        label: [
          "In-cab operator has locked out remote driving",
          "Talk to in-cab operator",
        ],
        icon: LockIcon,
        iconClasses: "text-red-500",
      };
    }

    switch (boardState?.hhState) {
      case HHState.HH_ESTOP:
        return {
          label: ["E-Stopped"],
          icon: EstopIcon,
          iconClasses: "text-red-500",
        };
      case HHState.HH_STOPPED:
        return {
          label: [
            "Stopped",
            takingControl ? "Taking control…" : "Click to take control",
          ],
          icon: StoppedIcon,
          disabled: takingControl,
          onClick: async () => {
            markAsTakingControl();
            await sendEnableControl();
          },
        };
      case HHState.HH_OPERATIONAL:
        return { label: ["Remotely driving"], icon: RemoteDriveIcon };
      case HHState.HH_SAFE:
        return {
          label: ["Safe State", "Resolve issue before driving"],
          icon: SensorsIcon,
          onClick: async () => {
            markAsTakingControl();
            await sendEnableControl();
          },
        };
      case HHState.HH_DISABLED:
        return {
          label: [
            "In-cab operator driving",
            takingControl ? "Taking control…" : "Click to take control",
          ],
          icon: SteeringWheelIcon,
          disabled: takingControl,
          onClick: () => {
            markAsTakingControl();
            sendEnableControl();
          },
        };
      case HHState.HH_UNKNOWN:
      default:
        return {
          label: ["Unknown tractor state"],
          icon: QuestionMarkIcon,
          onClick: async () => {
            markAsTakingControl();
            await sendEnableControl();
          },
        };
    }
  };
  const { label, disabled, icon, iconClasses, onClick } = getStateDisplay();

  const tooltipTitle = (
    <div className="flex flex-col items-center">
      {label.map((line, i) => (
        <span key={i}>{line}</span>
      ))}
    </div>
  );
  const ariaLabel = `tractor state: ${label.join("; ").toLowerCase()}`;

  return (
    <>
      <Tooltip
        arrow
        placement="top"
        title={tooltipTitle}
        slotProps={
          onClick
            ? undefined
            : {
                popper: {
                  modifiers: [{ name: "offset", options: { offset: [0, -4] } }],
                },
              }
        }
      >
        <div>
          <Button
            variant={onClick ? "contained" : undefined}
            disabled={!onClick || disabled || !inDriverSeat}
            className="p-[3px] min-w-0 text-white"
            onClick={onClick}
          >
            <SvgIcon
              aria-label={ariaLabel}
              fontSize="small"
              component={icon}
              className={iconClasses}
            />
          </Button>
        </div>
      </Tooltip>
    </>
  );
};
