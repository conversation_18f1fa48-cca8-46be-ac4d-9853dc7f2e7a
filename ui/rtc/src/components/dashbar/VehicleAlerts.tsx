import {
  <PERSON><PERSON>,
  Click<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@mui/material";
import { selectReportedAutonomyState } from "@main/store/driving.slice";
import { useAppSelector } from "@main/store/hooks";
import { useFullScreenContext } from "@main/components/fullScreenContext";
import AlertIcon from "@mui/icons-material/PriorityHigh";
import AlertTriangleIcon from "@mui/icons-material/Warning";
import CloseIcon from "@mui/icons-material/Close";
import clsx from "clsx";
import React, { FC, Fragment, type ReactNode, useState } from "react";

interface VehicleAlertsProps {
  onClearRemoteAssistance: () => void;
}
export const VehicleAlerts: FC<VehicleAlertsProps> = ({
  onClearRemoteAssistance,
}) => {
  const { fullScreenNode } = useFullScreenContext();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | undefined>();
  const isOpen = Boolean(anchorEl);
  const open = setAnchorEl;
  const close = (): void => setAnchorEl(undefined);

  const remoteAssistanceRequired = useAppSelector(
    (state) => selectReportedAutonomyState(state).remoteAssistanceRequired
  );

  const alerts = [];

  if (remoteAssistanceRequired) {
    const { reason } = remoteAssistanceRequired;
    alerts.push(
      <Alert
        key="assistance-required"
        title="Remote assistance required"
        description={reason}
        severity="critical"
        action={
          <Button
            color="error"
            variant="contained"
            size="small"
            onClick={onClearRemoteAssistance}
          >
            Dismiss
          </Button>
        }
      />
    );
  }

  const hasAlerts = alerts.length > 0;
  if (!hasAlerts && anchorEl) {
    setAnchorEl(undefined);
    return; // re-render
  }

  return (
    <ClickAwayListener onClickAway={close}>
      <div className={clsx(!hasAlerts && "invisible")}>
        <Tooltip title={isOpen ? "" : "Vehicle alerts active"}>
          <IconButton
            size="small"
            className="relative"
            aria-label={
              hasAlerts ? "vehicle status: alerts active" : "vehicle status: OK"
            }
            onClick={(e) => (isOpen ? close() : open(e.currentTarget))}
          >
            {/* draw visual attention with a pulse */}
            {hasAlerts && (
              <div className="fixed w-8 h-8 rounded-full bg-red-700 animate-ping pointer-events-none" />
            )}
            <AlertTriangleIcon className="text-red-500 z-10" />
          </IconButton>
        </Tooltip>
        <Popper
          anchorEl={anchorEl}
          open={isOpen}
          className="bg-rtc-gray-900/80 rounded-md w-80 flex flex-col gap-3 py-3 text-sm"
          container={fullScreenNode ?? document.body}
        >
          <IconButton
            size="small"
            aria-label="close"
            onClick={close}
            className="absolute p-1 top-0 right-0 translate-x-1/4 -translate-y-1/4 bg-rtc-gray-900/80"
          >
            <CloseIcon />
          </IconButton>
          {alerts.map((item, i) => (
            <Fragment key={item.key}>
              {i > 0 && (
                <hr className="w-full my-0 border-none h-px bg-white/50" />
              )}
              {<div className="pl-2 pr-3">{item}</div>}
            </Fragment>
          ))}
        </Popper>
      </div>
    </ClickAwayListener>
  );
};

interface AlertProps {
  title: string;
  description: string;
  action: ReactNode;
  severity: "critical";
}
const Alert: FC<AlertProps> = ({ title, description, action, severity }) => {
  let color: string;
  let Icon;
  switch (severity) {
    case "critical": {
      color = "text-red-500";
      Icon = AlertIcon;
      break;
    }
  }
  return (
    <div
      className="grid gap-1"
      style={{
        gridTemplateAreas: "'icon title' '. description' '. action'",
        gridTemplateColumns: "24px 1fr",
      }}
    >
      <div className="relative" style={{ gridArea: "icon" }}>
        <Icon className={clsx(color, "absolute top-0")} />
      </div>
      <div className={clsx(color, "font-bold")} style={{ gridArea: "title" }}>
        {title}
      </div>
      <div style={{ gridArea: "description" }}>{description}</div>
      <div style={{ gridArea: "action" }} className="text-end">
        {action}
      </div>
    </div>
  );
};
