import {
  <PERSON><PERSON>,
  Checkbox,
  FormControlLabel,
  MenuItem,
  <PERSON>lt<PERSON>,
  Typography,
} from "@mui/material";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import { classes } from "@main/utils/theme";
import {
  type CompositeViews,
  selectActiveViewId,
  selectNetworkStats,
  selectServiceStatuses,
} from "@main/store/driving.slice";
import { Menu } from "@main/components/Menu";
import { Link as RouterLink } from "react-router-dom";
import { ROUTES } from "@main/pages/routes";
import { useAppSelector } from "@main/store/hooks";
import { useTractorAnalytics } from "@main/hooks/useTractorAnalytics";
import { useViewChannelMessaging } from "@main/communication/rtc/hooks/useViewChannelMessaging";
import PreviewIcon from "@mui/icons-material/Preview";
import React, { FC, useEffect, useState } from "react";
import SettingsIcon from "@mui/icons-material/Settings";

interface ViewSelectorProps {
  parked: boolean;
}

export const ViewSelector: FC<ViewSelectorProps> = ({ parked }) => {
  const { sendGetCompositeViews, sendSetActiveCompositeView } =
    useViewChannelMessaging();
  const { trackEvent } = useTractorAnalytics();

  const networkStats = useAppSelector(selectNetworkStats);
  const isDataStreamConnected = networkStats.rtcStatus === "connected";

  const [compositeViews, setCompositeViews] = useState<CompositeViews>({});
  const [areCompositeViewsLoading, setAreCompositeViewsLoading] =
    useState(false);
  const [compositeViewLoadingError, setCompositeViewLoadingError] =
    useState<boolean>(false);
  const activeViewId = useAppSelector(selectActiveViewId);

  const servicesOnline = useAppSelector((state) =>
    Boolean(selectServiceStatuses(state)[ChannelLabel.VIEW_CONTROLS]?.online)
  );

  useEffect(() => {
    const getInitialData = async (): Promise<void> => {
      setAreCompositeViewsLoading(true);
      setCompositeViewLoadingError(false);

      // Wait for the `view_controls` channel to open
      if (!servicesOnline) {
        return;
      }

      try {
        const data = await sendGetCompositeViews();
        setCompositeViews(data ?? {});
      } catch {
        setCompositeViewLoadingError(true);
      } finally {
        setAreCompositeViewsLoading(false);
      }
    };
    getInitialData();
  }, [servicesOnline, sendGetCompositeViews]);

  return (
    <Menu
      icon={
        <Tooltip
          title={areCompositeViewsLoading ? "Views Loading..." : "Views"}
        >
          <PreviewIcon
            className={classes({
              "text-white": !areCompositeViewsLoading,
              "text-rtc-gray-300 animate-pulse": areCompositeViewsLoading,
            })}
          />
        </Tooltip>
      }
      iconButtonProps={{
        size: "small",
        className: "p-0",
      }}
      menuItems={Object.entries(compositeViews)
        .filter(([viewId, { isSaved }]) => isSaved || viewId === activeViewId)
        .map(([id, view], i) => (
          <MenuItem
            key={id}
            onClick={() => {
              trackEvent("view_switched", {
                from_view: activeViewId,
                to_view: id,
                method: "button",
              });
              sendSetActiveCompositeView(id);
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  checked={id === activeViewId}
                  className="text-primary-300"
                />
              }
              label={
                <>
                  <span className="font-bold text-rtc-gray-400 pr-2">
                    {i + 1 < 10 ? i + 1 : ""}
                  </span>
                  <span>{view.name}</span>
                </>
              }
              className="uppercase"
            />
          </MenuItem>
        ))}
      bottomElement={
        <div className="flex justify-end px-4 py-2">
          <Tooltip
            title={
              parked ? "" : "Vehicle must be parked before views can be edited"
            }
          >
            <div>
              {areCompositeViewsLoading && (
                <div className="flex justify-center pb-2">
                  <Typography className="text-xs italic text-rtc-gray-300">
                    Loading...
                  </Typography>
                </div>
              )}
              {compositeViewLoadingError && (
                <div className="flex justify-center pb-2">
                  <Typography className="text-xs italic text-carbon-300">
                    Error Loading Views
                  </Typography>
                </div>
              )}
              <Button
                disabled={!parked || !isDataStreamConnected}
                component={RouterLink}
                to={`../${ROUTES.ListViewsAndSlices.path}`}
                className={"flex gap-2"}
                size="small"
                variant="outlined"
              >
                <SettingsIcon /> Configure
              </Button>
            </div>
          </Tooltip>
        </div>
      }
    ></Menu>
  );
};
