import { But<PERSON>, FormControlLabel, SvgIcon } from "@mui/material";
import { classes } from "@main/utils/theme";
import React, { type FC } from "react";

export type IconToggleButtonProps = {
  value: boolean;
  onChange: (value: boolean) => void;
  disabled?: boolean;
  label: string;
  icon: React.ElementType;
  iconClassName?: string;
};

// A checkbox-like component controlling a boolean property, rendered as an
// icon button next to a label. The icon button is lit when the value is true.
export const IconToggleButton: FC<IconToggleButtonProps> = ({
  value,
  onChange,
  disabled,
  label,
  icon,
  iconClassName,
}) => {
  return (
    <FormControlLabel
      control={
        <Button
          disabled={disabled}
          aria-pressed={value}
          onClick={() => onChange(!value)}
          size="small"
          variant="outlined"
          className={classes(
            "mr-2 min-w-0 min-h-0 w-7 h-7 text-white gap-2",
            value && "bg-white text-rtc-gray-900"
          )}
        >
          <SvgIcon
            className={classes("text-base", iconClassName)}
            fontSize="inherit"
            component={icon}
            inheritViewBox
          />
        </Button>
      }
      classes={{
        root: "ml-0", // MUI default is -11px for some reason
        label: "text-sm",
      }}
      label={label}
    />
  );
};
