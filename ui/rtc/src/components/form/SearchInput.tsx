import { IconButton, InputBase } from "@mui/material";
import { twMerge } from "tailwind-merge";
import ClearIcon from "@mui/icons-material/Clear";
import clsx from "clsx";
import React, { FC } from "react";
import SearchIcon from "@mui/icons-material/Search";

interface SearchInputProps {
  className?: string;
  placeholder?: string;
  value?: string;
  onChange: (newValue: string) => void;
}
/**
 * Provides a search bar that can pair with a list of data
 */
export const SearchInput: FC<SearchInputProps> = ({
  className,
  placeholder,
  value,
  onChange,
}) => {
  const iconClasses = "text-xl w-[20px] h-[20px]";
  return (
    <InputBase
      className={twMerge(
        clsx(
          "border-solid border-[0.5px] border-rtc-gray-300 px-2 py-0 rounded text-sm w-full",
          className
        )
      )}
      placeholder={placeholder}
      startAdornment={
        <SearchIcon className={clsx(iconClasses, "text-white mr-2")} />
      }
      value={value}
      onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
        onChange(event.target.value);
      }}
      endAdornment={
        <>
          {value && (
            <IconButton
              className={clsx("text-rtc-gray-300 text-inherit", iconClasses)}
              onClick={() => onChange("")}
            >
              <ClearIcon fontSize="inherit" />
            </IconButton>
          )}
        </>
      }
    />
  );
};
