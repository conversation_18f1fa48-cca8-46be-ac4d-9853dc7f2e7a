import { Dialog, DialogContent, DialogTitle } from "@mui/material";
import { KeyMapGroup } from "./hotKeys.types";
import React, { FC, Fragment } from "react";

export interface HotKey {
  name?: string;
  action: string;
  sequence?: string;
  sequences?: string[];
  group: KeyMapGroup | string;
  hidden?: boolean;
}
interface GroupedHotKeyMap {
  [group: string]: HotKey[];
}

interface HotKeysReferenceDialogProps {
  keyMaps: HotKey[];
  open: boolean;
  onClose: () => void;
}

export const HotKeysReferenceDialog: FC<HotKeysReferenceDialogProps> = ({
  keyMaps,
  open,
  onClose,
}) => {
  const groupedKeyMap: GroupedHotKeyMap = {};
  for (const item of keyMaps) {
    if (groupedKeyMap[item.group]) {
      groupedKeyMap[item.group].push(item);
    } else {
      groupedKeyMap[item.group] = [item];
    }
  }

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Keyboard shortcuts</DialogTitle>
      <DialogContent dividers>
        <p>Focus on the driving window in order to use hotkeys to drive</p>
        <ul className="p-2 flex flex-col gap-4">
          {Object.entries(groupedKeyMap).map(([groupName, keyMappings]) => (
            <Fragment key={groupName}>
              {groupName === KeyMapGroup.BASE ? null : (
                <p className="m-0 font-bold">{groupName}</p>
              )}
              {keyMappings
                .filter(({ hidden }) => !hidden)
                .map(({ name, sequence, sequences }) => {
                  return (
                    <li
                      key={name}
                      className="flex justify-between items-center"
                    >
                      <div className="text-white rounded bg-black border-0.5 border-solid border-rtc-gray-600 p-2 text-xs font-mono">
                        {sequences ? sequences.join(" | ") : sequence}
                      </div>
                      <div className="ml-4">{name}</div>
                    </li>
                  );
                })}
            </Fragment>
          ))}
        </ul>
      </DialogContent>
    </Dialog>
  );
};
