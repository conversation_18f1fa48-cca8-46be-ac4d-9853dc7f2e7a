import * as turf from "@turf/turf";
import * as yup from "yup";
import { Button, ButtonGroup } from "@mui/material";
import { Field, Form, Formik } from "formik";
import { guessSerial } from "@main/logic/utils/robots";
import { normalizeFieldDefinitions } from "@main/utils/fieldDefinition";
import { NumberField } from "../form/formik/NumberField";
import { Pane } from "@main/components/Pane";
import { selectInControl, setFocusedFieldId } from "@main/store/driving.slice";
import { SelectInput } from "../form/SelectInput";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useListFieldDefinitionsQuery } from "common/state/portalApi/fieldDefinition";
import { usePlanPathMutation } from "@main/store/portalApi/planPath";
import { useSteeringAutonomy } from "@main/hooks/useSteeringAutonomy";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import ClearIcon from "@mui/icons-material/Clear";
import FlipIcon from "@mui/icons-material/Autorenew";
import React, { FC, useEffect, useMemo, useRef } from "react";
import type { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import type {
  FieldDefinition,
  PathPlanningInput,
} from "@main/components/controls/drive.types";
import type { LineString as GeojsonLineString } from "geojson";
import type { SerializedError } from "@reduxjs/toolkit";

interface FormValues {
  fieldId: string;
  heading: number;
}
const validationSchema = yup.object().shape({
  fieldId: yup.string().required(),
  heading: yup.number().min(0).lessThan(360).required(),
});

export const AutonomyControlsPane: FC = () => (
  <Pane header="GPS Path Autonomy">
    <div className="pt-1 pb-3 px-3">
      <AutonomyControlsPaneContent />
    </div>
  </Pane>
);
export const AutonomyControlsPaneContent: FC = () => {
  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);

  const {
    pathPlanningInput,
    currentPoint,
    submitTargetLine,
    clearPath,
    planPathQuery,
  } = useSteeringAutonomy();

  const fieldDefinitionsQuery = useListFieldDefinitionsQuery({ serial });
  const fieldDefinitions = useMemo(
    () =>
      fieldDefinitionsQuery.currentData
        ? indexFieldDefinitions(
            normalizeFieldDefinitions(fieldDefinitionsQuery.currentData)
          )
        : undefined,
    [fieldDefinitionsQuery.currentData]
  );

  const initialFormValues = useMemo(
    () =>
      fieldDefinitions && pathPlanningInput !== undefined
        ? getInitialFormValues(pathPlanningInput, fieldDefinitions)
        : undefined,
    [pathPlanningInput, fieldDefinitions]
  );

  if (fieldDefinitionsQuery.isLoading) {
    return <span className="opacity-75">Loading…</span>;
  }
  if (fieldDefinitionsQuery.isError) {
    return (
      <span className="opacity-75">
        Field definitions not available:{" "}
        {describeError(fieldDefinitionsQuery.error)}
      </span>
    );
  }
  if (!fieldDefinitions || fieldDefinitions.byFieldId.size === 0) {
    return <span className="opacity-75">No field definitions</span>;
  }

  if (!initialFormValues) {
    return <span className="opacity-75">Loading…</span>;
  }

  let onSubmit;
  let loadingStatus;
  if (currentPoint) {
    onSubmit = (values: FormValues) => {
      const { fieldId, heading } = values;

      // Move `currentPoint` in the direction of `heading` for some
      // distance to find the other end of the target line.
      const targetPoint = turf.destination(
        currentPoint.coordinates,
        0.01,
        ((heading + 180) % 360) - 180,
        { units: "kilometers" }
      ).geometry;
      const targetLine = turf.lineString([
        currentPoint.coordinates,
        targetPoint.coordinates,
      ]).geometry;
      return submitTargetLine({
        fieldId,
        currentPoint,
        targetLine,
        sidestepOrigin: currentPoint,
      });
    };
  } else {
    loadingStatus = "Waiting for GPS position…";
  }

  return (
    <div className="flex flex-col gap-3">
      <AutonomyForm
        initialValues={initialFormValues}
        onSubmit={onSubmit}
        onClear={clearPath}
        loadingStatus={loadingStatus}
        fieldDefinitions={fieldDefinitions}
      />
      <PlanPathDescription planPathResult={planPathQuery} />
    </div>
  );
};

interface PlanPathDescriptionProps {
  planPathResult: ReturnType<typeof usePlanPathMutation>[1];
}
const PlanPathDescription: FC<PlanPathDescriptionProps> = ({
  planPathResult,
}) => {
  if (planPathResult.isUninitialized) {
    return null;
  }
  if (planPathResult.isLoading) {
    return <span className="opacity-75">Planning path…</span>;
  }
  if (planPathResult.isError) {
    return (
      <span className="opacity-75">
        <strong>Failed:</strong> {describeError(planPathResult.error)}
      </span>
    );
  }
};
const describeError = (
  error: FetchBaseQueryError | SerializedError
): string => {
  if ("status" in error) {
    const msg = "error" in error ? error.error : JSON.stringify(error.data);
    const { status } = error;
    return `${msg} (status ${status})`;
  } else {
    const { message, code } = error;
    return `${message} (code ${code})`;
  }
};

interface IndexedFieldDefinitions {
  byName: Map<string, FieldDefinition>;
  byFieldId: Map<string, FieldDefinition>;
  raw: Readonly<Array<FieldDefinition>>;
}

const indexFieldDefinitions = (
  fieldDefinitions: FieldDefinition[]
): IndexedFieldDefinitions => {
  const collator = new Intl.Collator("en-US", { numeric: true });
  fieldDefinitions = fieldDefinitions.toSorted((a, b) =>
    collator.compare(a.name, b.name)
  );
  const byName: Map<string, FieldDefinition> = new Map();
  const byFieldId: Map<string, FieldDefinition> = new Map();
  for (const field of fieldDefinitions) {
    const { fieldId, name, db: { updatedAt = "-1" } = {} } = field;
    byFieldId.set(fieldId, field);
    const existing = byName.get(name);
    if (
      !existing ||
      Number(updatedAt) > Number(existing?.db?.updatedAt ?? "-1")
    ) {
      byName.set(name, field);
    }
  }
  return { byName, byFieldId, raw: fieldDefinitions };
};

// work around bad TypeScript types for Map.get (should be covariant)
const mapGet = <T, K, V>(map: Map<K, V>, t: T): V | undefined => {
  const readonlyMap: ReadonlyMap<T | K, V> = map;
  return readonlyMap.get(t);
};

const getInitialFormValues = (
  pathPlanningInput: PathPlanningInput | null,
  fieldDefinitions: IndexedFieldDefinitions
): FormValues | undefined => {
  const fieldDefinition =
    mapGet(fieldDefinitions.byFieldId, pathPlanningInput?.fieldId) ??
    fieldDefinitions.raw[0];
  if (!fieldDefinition) {
    return;
  }
  const { fieldId } = fieldDefinition;
  let heading;
  if (pathPlanningInput?.targetLine?.geometry) {
    heading = roundHeading(getAbHeading(pathPlanningInput.targetLine.geometry));
  } else {
    heading = getFieldDefinitionHeading(fieldDefinition) ?? 0;
  }
  return { fieldId, heading };
};

const getFieldDefinitionHeading = (
  fieldDefinition: FieldDefinition
): number | undefined => {
  const geometry = fieldDefinition.plantingHeading?.geometry;
  return geometry ? roundHeading(getAbHeading(geometry)) : undefined;
};
const getAbHeading = (ab: GeojsonLineString): number => {
  const [a, b] = ab.coordinates;
  return (turf.bearing(a, b) + 360) % 360; // [-180, 180] -> [0, 360]
};
const roundHeading = (heading: number): number => {
  const roundingScale = 1e4;
  return Math.round(heading * roundingScale) / roundingScale;
};

interface AutonomyFormProps {
  initialValues: FormValues;
  onSubmit: ((values: FormValues) => Promise<void>) | undefined;
  onClear: (() => Promise<void>) | undefined;
  loadingStatus: string | undefined;
  fieldDefinitions: IndexedFieldDefinitions;
}

const AutonomyForm: FC<AutonomyFormProps> = ({
  initialValues,
  onSubmit,
  onClear,
  loadingStatus,
  fieldDefinitions,
}) => {
  const flipIconRef = useRef<SVGSVGElement | null>(null);
  const inControl = useAppSelector(selectInControl);

  return (
    <Formik
      enableReinitialize
      validationSchema={validationSchema}
      initialValues={initialValues}
      onSubmit={(values) => onSubmit?.(values)}
    >
      {({ isSubmitting, isValid, setFieldTouched, setFieldValue, values }) => (
        <Form className="flex flex-col gap-3">
          <SelectInput
            label="Field"
            disabled={!inControl}
            options={fieldDefinitions.raw.map((f) => f.fieldId)}
            autocompleteProps={{
              disableClearable: true,
              disabled: isSubmitting,
              value: values.fieldId,
              getOptionLabel: (fieldId: string) =>
                fieldDefinitions.byFieldId.get(fieldId)?.name ?? fieldId,
              onChange: (_, fieldId: string) => {
                // When switching field definition, also update the heading to
                // match the field definition's default.
                setFieldValue("fieldId", fieldId);
                setFieldTouched("fieldId", true);
                const field = fieldDefinitions.byFieldId.get(fieldId);
                setFieldValue(
                  "heading",
                  field ? getFieldDefinitionHeading(field) : 0
                );
                setFieldTouched("heading", false);
              },
            }}
          />
          <ExfiltrateFocusedFieldId fieldId={values.fieldId} />
          <Field
            disabled={!inControl}
            component={NumberField}
            name="heading"
            label="Heading (degrees CW from north)"
            labelProps={{ classes: { root: "w-full" } }}
            InputProps={{
              classes: { root: "rounded-r-none" },
              inputProps: { step: "any" },
            }}
            className="basis-0 grow"
            endAdornment={
              <Button
                disabled={!inControl}
                variant="outlined"
                className="min-w-0 p-0 w-10 h-10 rounded-l-none border-l-0 border-rtc-gray-300 hover:border-white focus-within:border-red-500"
                onClick={() => {
                  setFieldValue(
                    "heading",
                    roundHeading((values.heading + 180) % 360)
                  );
                  setFieldTouched("heading", true);
                  const flipIcon = flipIconRef.current;
                  if (flipIcon) {
                    flipIcon.getAnimations()[0]?.play();
                  }
                }}
              >
                <FlipIcon
                  fontSize="small"
                  className="animate-[semispin_400ms_ease-out_0s_1_both_paused]"
                  ref={flipIconRef}
                />
              </Button>
            }
          />
          <ButtonGroup variant="contained">
            <Button
              type="submit"
              disabled={!onSubmit || !isValid || isSubmitting || !inControl}
              title={onSubmit ? undefined : loadingStatus}
              className="w-full"
            >
              Plot Path
            </Button>
            <Button
              size="small"
              disabled={!onClear || !inControl}
              onClick={onClear}
            >
              <ClearIcon aria-label="clear path" fontSize="small" />
            </Button>
          </ButtonGroup>
          {loadingStatus !== undefined && (
            <p className="m-0 opacity-75">{loadingStatus}</p>
          )}
        </Form>
      )}
    </Formik>
  );
};

// Formik effect hack to propagate state to other parts of the app.
interface ExfiltrateFocusedFieldIdProps {
  fieldId: string | undefined;
}
const ExfiltrateFocusedFieldId: FC<ExfiltrateFocusedFieldIdProps> = ({
  fieldId,
}) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFocusedFieldId(fieldId));
  }, [dispatch, fieldId]);

  return null;
};
