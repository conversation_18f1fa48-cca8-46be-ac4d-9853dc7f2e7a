import {
  addLayer,
  changeIndex,
  deleteLayer,
  duplicateLayer,
  Layer,
  toggleLayerVisibility,
} from "@main/logic/compositeView/layers";
import { Button, IconButton, MenuItem, Typography } from "@mui/material";
import {
  CamerasToSlices,
  selectCamerasToSlices,
} from "@main/store/driving.slice";
import { CollapsibleListButtons } from "@main/components/CollapsibleListButtons";
import { Menu } from "@main/components/Menu";
import { Pane } from "@main/components/Pane";
import { SearchInput } from "@main/components/form/SearchInput";
import { useAppSelector } from "@main/store/hooks";
import AddIcon from "@mui/icons-material/Add";
import clsx from "clsx";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import React, { FC, useMemo, useState } from "react";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

interface CompositeViewCameraSliceSelectionPaneProps {
  layers: Layer[];
  onLayerChange: (update: Layer[]) => void;
  selectedLayerId: string | undefined;
  setSelectedLayerId: (layerId: string | undefined) => void;
}

export const CompositeViewCameraSliceSelectionPane: FC<
  CompositeViewCameraSliceSelectionPaneProps
> = ({ layers, onLayerChange, selectedLayerId, setSelectedLayerId }) => {
  return (
    <Pane
      className="min-h-36"
      header={<Typography variant="caption">Camera Slices</Typography>}
    >
      <CameraSlicePalette
        onAdd={(id: string, name: string) => {
          const updatedLayers = addLayer(layers, id, name);
          onLayerChange(updatedLayers);
          setSelectedLayerId(updatedLayers[updatedLayers.length - 1].layerId);
        }}
      />
      <CameraSliceLayers
        layers={layers}
        selectedLayerId={selectedLayerId}
        setSelectedLayerId={setSelectedLayerId}
        onVisibilityToggle={(layerId: string) => {
          const layer = layers.find((l) => l.layerId === layerId);
          const isNowHidden = layer && layer.visible;
          onLayerChange(toggleLayerVisibility(layers, layerId));
          if (isNowHidden && selectedLayerId === layerId) {
            setSelectedLayerId(undefined);
          }
        }}
        onDelete={(layerId: string) => {
          onLayerChange(deleteLayer(layers, layerId));
          if (selectedLayerId === layerId) {
            setSelectedLayerId(undefined);
          }
        }}
        onDuplicate={(layerId: string) => {
          const duplicated = duplicateLayer(layers, layerId);
          onLayerChange(duplicated);
          setSelectedLayerId(duplicated[duplicated.length - 1].layerId);
        }}
        changeZIndex={(fromIndex: number, toIndex: number) => {
          onLayerChange(changeIndex(layers, fromIndex, toIndex));
        }}
      />
    </Pane>
  );
};

interface CameraSlicePaletteProps {
  onAdd: (sliceId: string, sliceName: string) => void;
}
const CameraSlicePalette: FC<CameraSlicePaletteProps> = ({ onAdd }) => {
  const camerasToSlices = useAppSelector(selectCamerasToSlices);
  const [searchText, setSearchText] = useState("");

  const filteredCameraSlices: CamerasToSlices = useMemo(() => {
    const filtered: CamerasToSlices = {};
    for (const [cameraId, sliceDetails] of Object.entries(camerasToSlices)) {
      if (cameraId.toLowerCase().includes(searchText.toLowerCase())) {
        filtered[cameraId] = sliceDetails;
      }
      const matchingSlices = sliceDetails.slices.filter((slice) =>
        slice.name.toLowerCase().includes(searchText.toLowerCase())
      );
      if (matchingSlices.length) {
        filtered[cameraId] = {
          ...sliceDetails,
          slices: matchingSlices,
        };
      }
    }
    return filtered;
  }, [camerasToSlices, searchText]);

  return (
    <div>
      <div className="px-6 pt-2 pb-4">
        <div className="mb-2">
          <Typography variant="caption" className="uppercase text-xs font-bold">
            Options
          </Typography>
        </div>
        <SearchInput onChange={setSearchText} value={searchText} />
      </div>
      <div className="max-h-44 overflow-y-scroll">
        <CollapsibleListButtons
          expandAllByDefault={true}
          listItemClassName="border-solid border-rtc-gray-700 border-0 border-t-[0.5px] pl-5"
          list={Object.entries(filteredCameraSlices).map(
            ([cameraId, { slices }]) => ({
              id: cameraId,
              name: cameraId,
              selectable: false,
              items: slices.map(({ id, name }) => ({
                id,
                name,
                selectable: true,
                childItemEndAdornment: <AddIcon className="text-lg mr-4" />,
                onClick: () => {
                  onAdd(id, name);
                },
              })),
            })
          )}
        />
      </div>
    </div>
  );
};

interface CameraSliceLayersProps {
  layers: Layer[];
  selectedLayerId: string | undefined;
  setSelectedLayerId: (layerId: string | undefined) => void;
  onVisibilityToggle: (layerId: string) => void;
  onDelete: (layerId: string) => void;
  onDuplicate: (layerId: string) => void;
  changeZIndex: (fromIndex: number, toIndex: number) => void;
}
const CameraSliceLayers: FC<CameraSliceLayersProps> = ({
  layers,
  selectedLayerId,
  setSelectedLayerId,
  onVisibilityToggle,
  onDelete,
  onDuplicate,
  changeZIndex,
}) => {
  const getIndex = (layerId: string): number =>
    layers.findIndex((layer) => layer.layerId === layerId);
  const menuItems = [
    {
      title: "Delete",
      onClick: onDelete,
    },
    {
      title: "Duplicate",
      onClick: onDuplicate,
    },
    {
      title: "Move Up",
      onClick: (layerId: string) => {
        const layerIndex = getIndex(layerId);
        if (layerIndex >= 0 && layerIndex !== layers.length - 1) {
          changeZIndex(layerIndex, layerIndex + 1);
        }
      },
    },
    {
      title: "Move Down",
      onClick: (layerId: string) => {
        const layerIndex = getIndex(layerId);
        if (layerIndex >= 0 && layerIndex !== 0) {
          changeZIndex(layerIndex, layerIndex - 1);
        }
      },
    },
    {
      title: "Bring To Front",
      onClick: (layerId: string) => {
        const layerIndex = getIndex(layerId);
        if (layerIndex >= 0 && layerIndex !== layers.length - 1) {
          changeZIndex(layerIndex, layers.length - 1);
        }
      },
    },
    {
      title: "Send to Back",
      onClick: (layerId: string) => {
        const layerIndex = getIndex(layerId);
        if (layerIndex >= 0 && layerIndex !== 0) {
          changeZIndex(layerIndex, 0);
        }
      },
    },
    {
      title: "Toggle Visibility",
      onClick: onVisibilityToggle,
    },
  ];
  return (
    <div className="border-solid border-0 border-t border-rtc-gray-600">
      <div className="px-6 py-2">
        <Typography variant="caption" className="uppercase text-xs font-bold">
          Layers
        </Typography>
      </div>
      <div className="max-h-60 overflow-y-scroll">
        {[...layers].reverse().map(({ layerId, name, visible }) => {
          return (
            <Button
              key={layerId}
              fullWidth
              onClick={() => {
                if (visible) {
                  setSelectedLayerId(layerId);
                }
              }}
              className={clsx(
                "text-white pl-4 pr-5 py-0 normal-case justify-start group border-solid border-rtc-gray-700 border-t-[0.5px",
                {
                  "hover:bg-blue-600/20":
                    visible && selectedLayerId !== layerId,
                  "hover:bg-rtc-gray-600/20": !visible,
                  "bg-primary-600/50": selectedLayerId === layerId,
                }
              )}
              startIcon={
                <IconButton
                  size="small"
                  disableRipple
                  className={clsx({
                    "text-white invisible group-hover:visible": visible,
                  })}
                  onClick={() => onVisibilityToggle(layerId)}
                >
                  <VisibilityOffIcon className="text-sm" />
                </IconButton>
              }
              classes={{
                startIcon: "m-0 mr-1",
              }}
              disableRipple
            >
              <div className="flex w-full justify-between items-center">
                <Typography variant="body2">{name}</Typography>
                <Menu
                  icon={<MoreVertIcon className="text-base text-white" />}
                  menuItems={menuItems.map(({ title, onClick }) => (
                    <MenuItem key={title} onClick={() => onClick(layerId)}>
                      <Typography textAlign="center">{title}</Typography>
                    </MenuItem>
                  ))}
                ></Menu>
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
};
