import { AutonomyMode, SteeringControlAlgo } from "../controls/drive.types";
import { Badge, Button, LinearProgress, Paper, Tooltip } from "@mui/material";
import { guessSerial } from "@main/logic/utils/robots";
import { Pane } from "../Pane";
import {
  selectInControl,
  selectLocation,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { State, stateToJSON } from "@protos/ts2/rtc/jobs";
import {
  TaskStateDetails,
  useTractorControlChannelMessaging,
} from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import { useAppSelector } from "@main/store/hooks";
import {
  useGetActiveObjectiveQuery,
  useGetActiveTaskQuery,
  useStartManualTaskMutation,
  useStopManualTaskMutation,
  useUpdateTaskMutation,
} from "common/state/rtcJobsApi";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";

import {
  canPressAutonomyButton,
  canPressManualButton,
  getCriteriaInfo,
} from "./jobPaneHelpers";
import { classes } from "@main/utils/theme";
import { getDisplayValue, getExpectedCriteria } from "@main/utils/task";
import { isFetchBaseQueryError } from "common/utils/api";
import {
  selectTaskEndCriteria,
  selectTaskStartCriteria,
} from "@main/store/job.slice";
import AutonomyIcon from "@mui/icons-material/AutoAwesome";
import ManualWorkIcon from "@mui/icons-material/Engineering";
import React, { FC, useEffect, useMemo } from "react";
import StopIcon from "@mui/icons-material/Stop";

const translateCriteriaTemp = (name: string): string => {
  // these are copypasted from localization-cloud
  // adding i18n to rtc is non-trivial, so i'm going with this solution until i get around to it
  const options: { [key: string]: string } = {
    gearStateValid: "Gear",
    headingValid: "Heading",
    hitchStateValid: "Hitch Position",
    posDistValid: "Distance",
    posXteValid: "Cross Track",
    unsupported: "Autonomy not supported",
  };
  return options[name] ?? name;
};
const t = translateCriteriaTemp;

export const JobPane: FC = () => {
  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);
  const { data: activeObjectiveResult, error: activeObjectiveError } =
    useGetActiveObjectiveQuery(
      { serial },
      { refetchOnMountOrArgChange: 1, pollingInterval: 1000 }
    );

  let activeObjective = activeObjectiveResult;
  if (
    isFetchBaseQueryError(activeObjectiveError) &&
    activeObjectiveError.status === 404
  ) {
    activeObjective = undefined;
  }

  const { data: activeTaskResult, error: activeTaskError } =
    useGetActiveTaskQuery(
      { serial },
      { refetchOnMountOrArgChange: 1, pollingInterval: 1000 }
    );

  let activeTask = activeTaskResult;
  if (
    isFetchBaseQueryError(activeTaskError) &&
    activeTaskError.status === 404
  ) {
    activeTask = undefined;
  }
  const [startManualTask] = useStartManualTaskMutation();
  const [stopManualTask] = useStopManualTaskMutation();
  const [updateTask] = useUpdateTaskMutation();
  const inControl = useAppSelector(selectInControl);
  const { autonomousState } = useAppSelector(selectReportedControlState);
  const { current: tractorLocation } = useAppSelector(selectLocation);
  const { sendSetAutonomy, sendGetTaskComplete, sendCanStartTask } =
    useTractorControlChannelMessaging();
  const tractorInControl = autonomousState?.mode === AutonomyMode.TASK_AUTONOMY;

  const taskInProgress = activeTask?.state === State.IN_PROGRESS;
  const workingManually = activeTask?.manuallyAssisted;

  const startCriteria = useAppSelector(selectTaskStartCriteria);
  const endCriteria = useAppSelector(selectTaskEndCriteria);
  const expectedCriteria = useMemo(() => {
    return getExpectedCriteria(activeTask);
  }, [activeTask]);

  const { criteria, totalCriteria, completedCriteria } = getCriteriaInfo(
    activeTask,
    startCriteria,
    endCriteria
  );

  const autoButtonEnabled = canPressAutonomyButton({
    tractorInControl,
    inControl,
    task: activeTask,
    startCriteria,
    endCriteria,
  });

  const manualButtonEnabled = canPressManualButton({
    tractorInControl,
    inControl,
    task: activeTask,
    startCriteria,
    endCriteria,
  });

  useEffect(() => {
    const refetchInterval = setInterval(() => {
      if (!inControl) {
        return;
      }
      if (activeTask) {
        if (activeTask.state === State.IN_PROGRESS) {
          sendGetTaskComplete(activeTask.id);
        } else {
          sendCanStartTask(activeTask.id);
        }
      }
    }, 1000);
    return () => {
      clearInterval(refetchInterval);
    };
  }, [inControl, activeTask, sendGetTaskComplete, sendCanStartTask]);

  return (
    <Pane header={"Job"}>
      <div className="p-2 flex flex-col gap-2">
        {!activeObjective && <div>No Active Objective</div>}
        {activeObjective && (
          <div className="px-2">
            <div className="flex justify-between">
              <div>Objective:</div>
              <div>{activeObjective?.name}</div>
            </div>
          </div>
        )}
        {!activeTask && <div>No Active Task</div>}
        {activeTask && (
          <Paper className="flex flex-col p-2 gap-1">
            <div className="flex justify-between pb-4 gap-2">
              <div>Task:</div>
              <div className="flex break-all">{activeTask.name}</div>
            </div>
            <div className="flex justify-between pb-4">
              <div>State:</div>
              <div>{stateToJSON(activeTask.state)}</div>
            </div>
            <div className="flex gap-4 w-full">
              <div className="w-full">
                <Tooltip title="Press to work task autonomously.">
                  <Badge
                    showZero={false}
                    badgeContent={
                      totalCriteria - completedCriteria === 0 ? (
                        0
                      ) : (
                        <div className="flex my-auto gap-1">
                          <div className="flex my-auto">
                            {`${completedCriteria}/${totalCriteria}`}
                          </div>
                        </div>
                      )
                    }
                    color={"primary"}
                    className="w-full"
                  >
                    <Button
                      variant="contained"
                      disabled={!autoButtonEnabled}
                      className="flex my-auto"
                      fullWidth
                      color={tractorInControl ? "success" : "primary"}
                      onClick={() =>
                        sendSetAutonomy({
                          mode: tractorInControl
                            ? AutonomyMode.REMOTE_DRIVER
                            : AutonomyMode.TASK_AUTONOMY,
                          steeringAssistAlgo: SteeringControlAlgo.NONE,
                        })
                      }
                    >
                      <AutonomyIcon />
                      {taskInProgress && tractorInControl && <StopIcon />}
                    </Button>
                  </Badge>
                </Tooltip>
              </div>
              <div className="flex w-full">
                <Tooltip title="Press to work task manually.">
                  <Button
                    variant="contained"
                    disabled={!manualButtonEnabled}
                    className="flex my-auto"
                    fullWidth
                    color={"primary"}
                    onClick={() =>
                      workingManually
                        ? stopManualTask({
                            id: activeTask.id,
                            endLocation: tractorLocation,
                            endHeading: tractorLocation?.heading ?? 0,
                          })
                        : startManualTask({
                            id: activeTask.id,
                            startLocation: tractorLocation,
                            startHeading: tractorLocation?.heading ?? 0,
                          })
                    }
                  >
                    <ManualWorkIcon />
                    {taskInProgress && workingManually && <StopIcon />}
                  </Button>
                </Tooltip>
              </div>
            </div>
            {criteria && (
              <div className="p-2">
                {Object.entries(criteria).map(
                  ([name, val]) =>
                    val !== null && (
                      <div key={name} className="flex justify-between">
                        <div className="flex my-auto">
                          {val.state ? (
                            <CheckIcon color="success" />
                          ) : (
                            <CloseIcon color="error" />
                          )}
                        </div>
                        <div className="flex flex-col">
                          <div
                            className={classes(
                              "ml-auto font-bold my-auto",
                              val.state ? "text-success-500" : "text-error-500"
                            )}
                          >
                            {t(name)}
                          </div>
                        </div>
                        <div className="flex flex-col">
                          <div
                            className={classes(
                              "ml-auto font-bold my-auto",
                              val.state ? "text-success-500" : "text-error-500"
                            )}
                          >
                            {getDisplayValue(
                              val,
                              name as keyof TaskStateDetails,
                              expectedCriteria
                            )}
                          </div>
                        </div>
                      </div>
                    )
                )}
              </div>
            )}
            <LinearProgress
              variant={taskInProgress ? "indeterminate" : "determinate"}
              className="h-5"
              value={activeTask.state === State.COMPLETED ? 100 : 0}
              color={
                // eslint-disable-next-line no-nested-ternary
                taskInProgress
                  ? tractorInControl
                    ? "success" // autonomy color
                    : "primary" // manual color
                  : "inherit" // greyed out
              }
            />
            <div className="m-2">
              <Button
                fullWidth
                disabled={!inControl || tractorInControl || workingManually}
                onClick={() =>
                  activeTask &&
                  updateTask({
                    ...activeTask,
                    state: State.COMPLETED,
                  })
                }
              >
                Mark Complete
              </Button>
            </div>
          </Paper>
        )}
      </div>
    </Pane>
  );
};
