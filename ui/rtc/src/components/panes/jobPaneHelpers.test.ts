import { canPressAutonomyButton, canPressManualButton } from "./jobPaneHelpers";
import { describe, expect, it } from "@jest/globals";
import { State, Task } from "@protos/ts2/rtc/jobs";
import { TaskStateDetails } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";

describe("Task Button Functions", () => {
  const mockTask: Task = {
    id: 1,
    name: "Test Task",
    state: State.PENDING,
    manuallyAssisted: false,
  } as Task;

  const mockStartCriteria: TaskStateDetails = {
    unsupported: undefined,
    posDistValid: { data: 10, state: true },
    posXteValid: { data: 5, state: true },
    headingValid: { data: 90, state: false },
    gearStateValid: undefined,
    hitchStateValid: undefined,
  };

  describe("canPressAutonomyButton", () => {
    it("should return false when not in control", () => {
      const result = canPressAutonomyButton({
        tractorInControl: true,
        inControl: false,
        task: mockTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(false);
    });

    it("should return false when task is undefined", () => {
      const result = canPressAutonomyButton({
        tractorInControl: true,
        inControl: false,
        task: undefined,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(false);
    });

    it("should return false for manual tasks", () => {
      const manualTask = { ...mockTask, manual: {} } as Task;
      const result = canPressAutonomyButton({
        tractorInControl: true,
        inControl: true,
        task: manualTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(false);
    });

    it("should return true when in control, tractor in control, and task in progress", () => {
      const inProgressTask = { ...mockTask, state: State.IN_PROGRESS };
      const result = canPressAutonomyButton({
        tractorInControl: true,
        inControl: true,
        task: inProgressTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(true);
    });

    it("should return true when all criteria are completed", () => {
      const completedCriteria: TaskStateDetails = {
        unsupported: undefined,
        posDistValid: { data: 10, state: true },
        posXteValid: { data: 5, state: true },
        headingValid: undefined,
        gearStateValid: undefined,
        hitchStateValid: undefined,
      };
      const result = canPressAutonomyButton({
        tractorInControl: false,
        inControl: true,
        task: mockTask,
        startCriteria: completedCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(true);
    });
  });

  describe("canPressManualButton", () => {
    it("should return false when no task provided", () => {
      const result = canPressManualButton({
        tractorInControl: false,
        inControl: true,
        task: undefined,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(false);
    });

    it("should return false when tractor is in control", () => {
      const result = canPressManualButton({
        tractorInControl: true,
        inControl: true,
        task: mockTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(false);
    });

    it("should return true for manual tasks", () => {
      const manualTask = { ...mockTask, manual: {} } as Task;
      const result = canPressManualButton({
        tractorInControl: false,
        inControl: true,
        task: manualTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(true);
    });

    it("should return true when working manually and task in progress", () => {
      const manuallyAssistedTask = {
        ...mockTask,
        state: State.IN_PROGRESS,
        manuallyAssisted: true,
      };
      const result = canPressManualButton({
        tractorInControl: false,
        inControl: true,
        task: manuallyAssistedTask,
        startCriteria: mockStartCriteria,
        endCriteria: undefined,
      });
      expect(result).toBe(true);
    });
  });
});
