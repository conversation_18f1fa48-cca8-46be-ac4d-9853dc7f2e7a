import { State, Task } from "@protos/ts2/rtc/jobs";
import {
  TaskStateData,
  TaskStateDetails,
} from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";

interface TaskButtonProps {
  tractorInControl: boolean;
  inControl: boolean;
  task: Task | undefined;
  startCriteria: TaskStateDetails | undefined;
  endCriteria: TaskStateDetails | undefined;
}

export const canPressAutonomyButton = ({
  tractorInControl,
  inControl,
  task,
  startCriteria,
  endCriteria,
}: TaskButtonProps): boolean => {
  const taskInProgress = task?.state === State.IN_PROGRESS;
  const workingManually = task?.manuallyAssisted;
  const { criteria, totalCriteria, completedCriteria } = getCriteriaInfo(
    task,
    startCriteria,
    endCriteria
  );
  const unsupported = Boolean(criteria?.unsupported);

  if (!inControl) {
    return false;
  }
  if (workingManually) {
    return false;
  }
  if (task?.manual) {
    return false;
  }
  if (unsupported) {
    return false;
  }
  if (inControl && tractorInControl && taskInProgress) {
    return true;
  }
  if (completedCriteria === totalCriteria) {
    return true;
  }

  return false;
};

export const canPressManualButton = ({
  tractorInControl,
  inControl,
  task,
  startCriteria,
  endCriteria,
}: TaskButtonProps): boolean => {
  if (!task) {
    return false;
  }
  const taskInProgress = task?.state === State.IN_PROGRESS;
  const { criteria, totalCriteria, completedCriteria } = getCriteriaInfo(
    task,
    startCriteria,
    endCriteria
  );
  const unsupported = Boolean(criteria?.unsupported);
  const workingManually = task?.manuallyAssisted;

  if (!inControl) {
    return false;
  }
  if (tractorInControl) {
    return false;
  }
  if (task?.manual) {
    return true;
  }
  if (unsupported) {
    return true;
  }
  if (completedCriteria === totalCriteria) {
    return true;
  }
  if (inControl && workingManually && taskInProgress) {
    return true;
  }
  return false;
};

export const getCriteriaInfo = (
  task?: Task,
  startCriteria?: TaskStateDetails,
  endCriteria?: TaskStateDetails
): {
  criteria?: TaskStateDetails;
  completedCriteria: number;
  totalCriteria: number;
} => {
  const taskInProgress = task?.state === State.IN_PROGRESS;
  let criteria = taskInProgress ? endCriteria : startCriteria;
  criteria = taskInProgress && task?.laserWeed ? undefined : criteria;

  const completedCriteria = criteria
    ? Object.values(criteria).reduce(
        (p: number, c: TaskStateData) => (c && c.state ? p + 1 : p),
        0
      )
    : 0;
  const totalCriteria = criteria
    ? Object.values(criteria).reduce((p, c) => (c ? p + 1 : p), 0)
    : 0;

  return { totalCriteria, completedCriteria, criteria };
};
