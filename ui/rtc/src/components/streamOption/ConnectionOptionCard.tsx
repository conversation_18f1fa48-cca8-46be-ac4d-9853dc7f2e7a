import { Available } from "@main/communication/websocket/webSocketMessage.types";
import { Card, Link, Typography } from "@mui/material";
import { DateTime } from "luxon";
import { Link as RouterLink } from "react-router-dom";
import { ROUTES } from "@main/pages/routes";
import { SearchInput } from "../form/SearchInput";
import { TrackedStream } from "@main/pages/tractors/utils";
import { twMerge } from "tailwind-merge";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import CameraIcon from "@mui/icons-material/Videocam";
import clsx from "clsx";
import React, { FC, useMemo, useState } from "react";
import slugify from "slug";

interface ConnectionOptionCardProps {
  className?: string;
  host: Available;
  streams: TrackedStream[];
  disabled: boolean;
}
export const ConnectionOptionCard: FC<ConnectionOptionCardProps> = ({
  className,
  host,
  streams,
  disabled,
}) => {
  const [streamSearchText, setStreamSearchText] = useState("");
  const filteredStreams = streams.filter((stream) =>
    stream.name
      .toLocaleLowerCase()
      .includes(streamSearchText.toLocaleLowerCase())
  );

  const slugCounts = useMemo(() => {
    const result = new Map<string, number>();
    for (const { ID: id, name } of streams) {
      const slug = slugify(name);
      result.set(slug, 1 + (result.get(slug) ?? 0));
      result.set(id, 1 + (result.get(id) ?? 0));
    }
    return result;
  }, [streams]);

  const uniqueName = (stream: TrackedStream): string => {
    const { ID: id, name } = stream;
    const slug = slugify(name);
    const count = slugCounts.get(slug) ?? 0;
    return count > 1 ? id : slug;
  };

  const makeLink = (stream: TrackedStream): string => {
    const hostId = encodeURIComponent(host.hostID);
    const streamName = encodeURIComponent(uniqueName(stream));
    return `/${ROUTES.Stream.path}/${hostId}/${streamName}/${ROUTES.Drive.path}`;
  };

  const makeGenericLink = (stream: TrackedStream): string => {
    const hostId = encodeURIComponent(host.hostID);
    const streamName = encodeURIComponent(uniqueName(stream));
    return `/${ROUTES.Stream.path}/${hostId}/${streamName}/${ROUTES.View.path}`;
  };

  return (
    <Card className={clsx("h-full bg-rtc-gray-700 rounded-sm", className)}>
      <div className="h-full w-full p-4">
        <Typography variant="h4" className="pb-2">
          {host.hostName}
        </Typography>
        <SearchInput
          placeholder="Filter streams..."
          className="my-1"
          value={streamSearchText}
          onChange={setStreamSearchText}
        />
        <div className="pt-2 flex items-center mb-1">
          <CameraIcon className="text-sm mr-2" />
          <Typography className="font-bold text-xs uppercase">
            Streams
          </Typography>
          <Typography className="ml-2 text-xs">
            (
            {streamSearchText
              ? `${filteredStreams.length} of ${streams.length}`
              : streams.length}
            )
          </Typography>
        </div>
        <div className="h-[160px] overflow-y-scroll pb-2 pt-0">
          {filteredStreams.map((stream) => (
            <div key={stream.ID}>
              <Link
                component={RouterLink}
                to={
                  // TODO: use controlSupported once non-tractor robots use it properly
                  stream.name === "Deck"
                    ? makeLink(stream)
                    : makeGenericLink(stream)
                }
                className={twMerge(
                  clsx(
                    "px-3 py-1 flex gap-1 items-center text-white border-solid border-0 border-b-[0.5px] border-b-rtc-gray-600 no-underline hover:no-underline hover:bg-rtc-gray-800",
                    {
                      "text-rtc-gray-400 pointer-events-none":
                        disabled || !stream.available,
                    }
                  )
                )}
              >
                <Typography className="font-bold text-sm">
                  {stream.name}
                </Typography>
                {disabled || (!stream.available && stream.lastSeen) ? (
                  <Typography className="text-xs" variant="h6">
                    {`last seen: ${DateTime.fromJSDate(stream.lastSeen).toRelative()}`}
                  </Typography>
                ) : null}
                <ArrowForwardIosIcon className="text-xs" />
              </Link>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};
