import { Link } from "react-router-dom";
import { Menu } from "@main/components/Menu";
import { MenuItem, SvgIcon, Tooltip, Typography } from "@mui/material";
import { ROUTES } from "../../pages/routes";
import { useAuth } from "../auth/useAuth";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import React, { FC } from "react";

export const AccountButton: FC = () => {
  const { user } = useAuth();

  const renderCurrentUser =
    user && (user.name || user.email) ? (
      <div
        key="current-user"
        className="px-4 border-solid border-0 border-b border-rtc-gray-300 pb-2"
      >
        <Typography key="caption" variant="caption" className="uppercase">
          Logged In
        </Typography>
        <Typography key="user-name" className="font-bold">
          {user?.name || user?.email || ""}
        </Typography>
      </div>
    ) : undefined;

  const menuItems = [
    <MenuItem key="logout" component={Link} to={ROUTES.Logout.path}>
      <Typography textAlign="center">Log Out</Typography>
    </MenuItem>,
  ];

  if (renderCurrentUser) {
    menuItems.unshift(renderCurrentUser);
  }
  return (
    <>
      <Menu
        icon={
          <Tooltip title="Account">
            <SvgIcon
              fontSize="inherit"
              className="fill-white"
              component={AccountCircleIcon}
              inheritViewBox
            />
          </Tooltip>
        }
        menuItems={menuItems}
      ></Menu>
    </>
  );
};
