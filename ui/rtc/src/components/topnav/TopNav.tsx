import { AccountButton } from "./AccountButton";
import { AppBar, Link, Toolbar, Typography } from "@mui/material";
import { ReadyState } from "react-use-websocket";
import { ROUTES } from "@main/pages/routes";
import { WebSocketStatus } from "@main/components/WebSocketStatus";
import CarbonLogo from "@main/assets/logo.svg?react";
import React, { FC } from "react";

interface TopNavProps {
  wsReadyState?: ReadyState;
}
export const TopNav: FC<TopNavProps> = ({ wsReadyState }) => {
  return (
    <AppBar position="fixed" className="bg-carbon-orange">
      <Toolbar className="w-full flex">
        <Link href={ROUTES.Tractors.path}>
          <CarbonLogo className="h-8 mr-4" />
        </Link>
        <Link href={ROUTES.Interventions.path}>
          <Typography variant="h5" className="ml-4">
            Interventions
          </Typography>
        </Link>
        <div className="flex-1">&nbsp;</div>

        <div className="flex items-center">
          {wsReadyState && <WebSocketStatus readyState={wsReadyState} />}
          <AccountButton />
        </div>
      </Toolbar>
    </AppBar>
  );
};
