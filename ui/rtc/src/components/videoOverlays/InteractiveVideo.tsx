import { Typography } from "@mui/material";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import { Video } from "@main/components/Video";
import CameraIcon from "@mui/icons-material/Videocam";
import clsx from "clsx";
import React, {
  FC,
  MouseEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";

type Range = { min: number; max: number };

/** Provides a way to layer drag and zoom interactivity over a video */
export interface InteractiveVideoProps {
  disableVideo?: boolean;
  translation?:
    | undefined
    | {
        current: { x: number; y: number };
        debounceDistance: number;
        range: { x: Range; y: Range };
        scalar: number;
        onChange: (diff: { x: number; y: number }) => void;
      };
  zoom?:
    | undefined
    | {
        current: number;
        debounceDistance: number;
        range: Range;
        scalar: number;
        onChange: (change: number) => void;
      };
}
export const InteractiveVideo: FC<InteractiveVideoProps> = ({
  disableVideo = false,
  translation,
  zoom,
}) => {
  const divRef = useRef<HTMLDivElement>(null);
  const { stream } = useVideoStreamContext();
  const [isDragging, setIsDragging] = useState(false);
  const [lastReportedPos, setLastReportedPos] = useState({ x: 0, y: 0 });
  const [lastReportedZoom, setLastReportedZoom] = useState(0);

  const translationInteractions = (() => {
    if (!translation) {
      return;
    }

    const handleMouseDown = (e: MouseEvent<HTMLVideoElement>): void => {
      setIsDragging(true);
      setLastReportedPos({ x: e.clientX, y: e.clientY });
    };

    const handleMouseMove = (e: MouseEvent<HTMLVideoElement>): void => {
      const currentX = e.clientX;
      const currentY = e.clientY;

      if (!isDragging) {
        return;
      }
      const xDiff = currentX - lastReportedPos.x;
      const yDiff = currentY - lastReportedPos.y;

      if (
        Math.abs(xDiff) >= translation.debounceDistance ||
        Math.abs(yDiff) >= translation.debounceDistance
      ) {
        // Emit deltas corresponding to the direction of motion, which is
        // opposite of the way that the mouse moves.
        const scaledDiff = {
          x: -xDiff * translation.scalar,
          y: -yDiff * translation.scalar,
        };
        const newValue = {
          x: translation.current.x + scaledDiff.x,
          y: translation.current.y + scaledDiff.y,
        };
        // making sure we don't go out of range
        if (newValue.x > translation.range.x.max) {
          newValue.x = translation.range.x.max;
        }
        if (newValue.x < translation.range.x.min) {
          newValue.x = translation.range.x.min;
        }
        if (newValue.y > translation.range.y.max) {
          newValue.y = translation.range.y.max;
        }
        if (newValue.y < translation.range.y.min) {
          newValue.y = translation.range.y.min;
        }

        translation.onChange(newValue);
        setLastReportedPos({ x: currentX, y: currentY });
      }
    };

    const handleMouseUp = (): void => {
      setIsDragging(false);
    };

    const pointerClass = isDragging ? "cursor-move" : "cursor-pointer";

    return { handleMouseDown, handleMouseMove, handleMouseUp, pointerClass };
  })();

  const handleWheel = useCallback(
    (e: WheelEvent): void => {
      if (!divRef.current) {
        return;
      }
      if (!zoom) {
        return; // zoom disabled
      }
      // reversing sign because it feels better
      const scrollZoom = -e.deltaY;
      if (Math.abs(lastReportedZoom - scrollZoom) >= zoom.debounceDistance) {
        let newZoom = zoom.current + scrollZoom * zoom.scalar;

        if (newZoom > zoom.range.max) {
          newZoom = zoom.range.max;
        }
        if (newZoom < zoom.range.min) {
          newZoom = zoom.range.min;
        }

        zoom.onChange(newZoom);
        setLastReportedZoom(scrollZoom);
      }
      e.preventDefault();
    },
    [lastReportedZoom, zoom]
  );

  useEffect(() => {
    const videoWrapper = divRef.current;
    if (!videoWrapper) {
      return;
    }
    videoWrapper.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      videoWrapper.removeEventListener("wheel", handleWheel);
    };
  }, [handleWheel]);

  return disableVideo ? (
    <div className="flex h-full justify-center items-center gap-2 text-rtc-gray-200">
      <CameraIcon />
      <Typography>Select a Camera</Typography>
    </div>
  ) : (
    <div ref={divRef}>
      <Video
        onMouseDown={translationInteractions?.handleMouseDown}
        onMouseMove={translationInteractions?.handleMouseMove}
        onMouseUp={translationInteractions?.handleMouseUp}
        onMouseLeave={translationInteractions?.handleMouseUp}
        className={clsx(
          "absolute h-full w-full",
          translationInteractions?.pointerClass
        )}
        srcObject={stream}
      />
    </div>
  );
};
