const REQUIRED = Symbol("carbonEnvRequired");
type EnvRequired = typeof REQUIRED;

type EnvParser<T> = (envKey: string, envValue: string | undefined) => T;

const env = {
  string: (defaultValue: string | EnvRequired) =>
    makeEnvParser(parseString, defaultValue),
  number: (defaultValue: number | EnvRequired) =>
    makeEnvParser(parseNumber, defaultValue),
  boolean: (defaultValue: boolean | EnvRequired) =>
    makeEnvParser(parseBoolean, defaultValue),
};

// In Docker builds, these environment variables can be set in the container,
// prefixed with `VITE_`: e.g., `VITE_MAX_SPEED_MPH=10`.
const envSpec = {
  // auth0
  AUTH0_AUDIENCE: env.string("https://customer-test.cloud.carbonrobotics.com"),
  AUTH0_AUTH_DOMAIN: env.string("carbonrobotics-dev.us.auth0.com"),
  AUTH0_CALLBACK_URL: env.string("http://localhost:5000/callback/"),
  AUTH0_CLIENT_ID: env.string("m33rZMXs6Ij4cZFYjeECX5b0ptnANYD9"),
  // map
  MAPBOX_ACCESS_TOKEN: env.string(
    "pk.************************************************************************.NXsMkVCcCwHaVnWsYR_ZXg"
  ),
  // portal
  PORTAL_ORIGIN: env.string("https://customer-test.cloud.carbonrobotics.com"),
  // jobs
  JOBS_ORIGIN: env.string("https://rtc-jobs-test.cloud.carbonrobotics.com"),
  // locator
  LOCATOR_ORIGIN: env.string(
    "https://rtc-locator-test.cloud.carbonrobotics.com"
  ),
  // websocket
  WS_ORIGIN: env.string("wss://signal-rtc-test.cloud.carbonrobotics.com"),
  WS_TIMEOUT_MS: env.number(1000),
  DISABLE_AUTH: env.boolean(false),
  DISABLE_LATENCY_METRICS: env.boolean(false),
  CLIENT_HEARTBEAT_INTERVAL_S: env.number(10),
  // posthog
  POSTHOG_KEY: env.string("phc_6ZxABRGXekYNLYHktGZA1ZXzwyUhWSOfc870zJzPxCl"),
  POSTHOG_HOST: env.string("https://us.i.posthog.com"),
  // monitoring
  MESSAGE_LOG_SIZE: env.number(50000),
  // controls
  CONTROLS_HEARTBEAT_S: env.number(1),
  CONTROLS_MESSAGE_INTERVAL_MS: env.number(100),
  //// steering control
  MAX_STEERING: env.number(1), // left hard-stop, about 30° of steering angle
  MIN_STEERING: env.number(-1), // right hard-stop, about 30° of steering angle
  STEERING_SPEED_SLOW_UNITS_PER_S: env.number(0.25),
  STEERING_SPEED_FAST_UNITS_PER_S: env.number(1),
  STEERING_TRANSITION_TIME_S: env.number(0.5),
  STEERING_SHARPNESS: env.number(10),
  STEERING_WHEEL_POLL_MS: env.number(10),
  //// speed control
  SPEED_DOUBLING_PERIOD_S: env.number(0.12),
  SPEED_INITIAL_DELTA_MPH: env.number(0.01),
  SPEED_INITIAL_ACCELERATION_MPH_PER_S: env.number(0.1),
  SPEED_MAX_ACCELERATION_MPH_PER_S: env.number(2),
  SPEED_DECELERATION_SCALAR: env.number(2),
  SPEED_REVERSE_SCALAR: env.number(0.5),
  MAX_SPEED_MPH: env.number(10),
  QUICKSTOP_LOCKOUT_DURATION_MS: env.number(1000),
  //// gear control
  GEAR_MAX_SHIFTING_SPEED_MPH: env.number(2.5),
  /// brakes
  BRAKES_SHIFT_NEUTRAL_THRESHOLD_PERCENT: env.number(5),
  /// implement
  IMPLEMENT_STATUS_POLL_S: env.number(4),
} as const;

export type CarbonEnv = Readonly<{
  [K in keyof typeof envSpec]: ReturnType<(typeof envSpec)[K]>;
}>;

function makeEnvParser<T>(
  parser: (raw: string) => T,
  defaultValue: T | EnvRequired
): EnvParser<T> {
  return (envKey, envValue) => {
    if (envValue !== undefined) {
      try {
        return parser(envValue);
      } catch (e) {
        const message = e instanceof Error ? e.message : String(e);
        if (defaultValue === REQUIRED) {
          throw new Error(`${envKey}: ${message}`);
        } else {
          const defaultValueString = JSON.stringify(defaultValue);
          console.error(
            `${envKey}: ${message}; using default value: ${defaultValueString}`
          );
        }
      }
    }
    if (defaultValue === REQUIRED) {
      throw new Error(`${envKey}: missing required environment variable`);
    }
    return defaultValue;
  };
}

function parseString(raw: string): string {
  return raw;
}

function parseNumber(raw: string): number {
  return Number(raw);
}

function parseBoolean(raw: string): boolean {
  switch (raw) {
    case "true":
      return true;
    case "false":
      return false;
    default:
      throw new Error(`invalid boolean: ${JSON.stringify(raw)}`);
  }
}

declare global {
  interface Window {
    _carbonRuntimeEnv?: Record<string, string>;
    _carbonEnv: CarbonEnv;
  }
}

const parseEnv = (): CarbonEnv => {
  const result: Partial<CarbonEnv> = {};
  const carbonRuntimeEnv = window._carbonRuntimeEnv ?? {};
  for (const [key, parser] of Object.entries(envSpec)) {
    const envKey = `VITE_${key}`;
    const runtimeValue = carbonRuntimeEnv[envKey];
    (result as any)[key] = parser(envKey, runtimeValue);
  }
  return Object.freeze(result as Required<typeof result>);
};

export const carbonEnv = parseEnv();
window._carbonEnv = carbonEnv; // for debugging
