import { carbonEnv } from "@main/env";
import { getConfigNode } from "@main/logic/utils/portal";
import { useGetConfigQuery } from "@main/store/portalApi/configs";
import { useMemo } from "react";

export const useMaxSpeedMph = (serial: string): number => {
  const { data: configResponse } = useGetConfigQuery(serial);

  return useMemo<number>(() => {
    const defaultValue = carbonEnv.MAX_SPEED_MPH;
    const config = configResponse?.config;
    if (!config) {
      return defaultValue;
    }
    const node = getConfigNode(config, [
      "rtc",
      "tractor_ctl",
      "speed",
      "max_speed_mph",
    ]);
    return node?.value?.floatVal ?? defaultValue;
  }, [configResponse]);
};
