import {
  AutonomyMode,
  type PathPlanningInput,
  SteeringControlAlgo,
} from "@main/components/controls/drive.types";
import { feature } from "@turf/helpers";
import {
  geojsonToCarbonLineString,
  geojsonToCarbonPoint,
  portalToGeojsonLineString,
  portalToGeojsonPoint,
} from "@main/logic/utils/geo";
import { guessSerial } from "@main/logic/utils/robots";
import {
  type PathPlan,
  selectDraftPlan,
  selectLocation,
  selectReportedAutonomyState,
  setDraftPlan,
} from "@main/store/driving.slice";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useMemo, useState } from "react";
import { usePlanPathMutation } from "@main/store/portalApi/planPath";
import { useTractorControlChannelMessaging } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import { useVideoStreamContext } from "@main/pages/stream/videoStreamContext";
import type {
  LineString as GeojsonLineString,
  Point as GeojsonPoint,
} from "geojson";
import type { PlanPathRequest } from "@protos/ts2/portal/geo";

// After planning a path, we only send it to the robot if you are within this
// distance of the path start.
export const MAX_DISTANCE_METERS = 5;

export interface UseSteeringAutonomyResult {
  pathPlan: PathPlan | undefined;
  pathPlanningInput: PathPlanningInput | null | undefined;
  currentPoint: GeojsonPoint | undefined;
  autonomyLoading: boolean;

  enableGpsPathAutonomy: (path: PathPlan) => Promise<boolean>;
  enableFurrowFollowingAutonomy: () => Promise<boolean>;
  disableAutonomy: () => Promise<boolean>;

  submitTargetLine: (args: SubmitTargetLineArgs) => Promise<void>;
  clearPath: () => Promise<void>;

  planPathQuery: ReturnType<typeof usePlanPathMutation>[1];
  setPathState: SetPathState;
}

export interface SubmitTargetLineArgs {
  fieldId: string;
  currentPoint: GeojsonPoint;
  targetLine: GeojsonLineString;
  sidestepOrigin: GeojsonPoint;
}

export enum SetPathStateType {
  UNINITIALIZED = "uninitialized",
  PENDING = "pending",
  FULFILLED = "fulfilled",
  REJECTED = "rejected",
}

export type SetPathState =
  | { type: SetPathStateType.UNINITIALIZED }
  | { type: SetPathStateType.PENDING }
  | { type: SetPathStateType.FULFILLED }
  | { type: SetPathStateType.REJECTED; reason: string };

export const useSteeringAutonomy = (): UseSteeringAutonomyResult => {
  const dispatch = useAppDispatch();
  const { targetHostId } = useVideoStreamContext();
  const serial = guessSerial(targetHostId);

  const { sendSetCurrentPath, sendSetAutonomy } =
    useTractorControlChannelMessaging();
  const [stateOfSetCurrentPath, setStateOfSetCurrentPath] =
    useState<SetPathState>({ type: SetPathStateType.UNINITIALIZED });

  const [planPath, planPathQuery] = usePlanPathMutation();

  const draftPlan = useAppSelector(selectDraftPlan);
  const reportedAutonomyState = useAppSelector(selectReportedAutonomyState);
  let pathPlan: PathPlan | undefined = draftPlan;
  if (!pathPlan && reportedAutonomyState) {
    const { path, pathPlanningInput: input } = reportedAutonomyState;
    if (path && input) {
      pathPlan = { path, input };
    }
  }
  const pathPlanningInput = pathPlan
    ? pathPlan.input
    : reportedAutonomyState.pathPlanningInput;

  const autonomyLoading = !reportedAutonomyState;
  const robotLocation = useAppSelector(
    (state) => selectLocation(state).current
  );
  const currentPoint = useMemo(
    () => (robotLocation ? portalToGeojsonPoint(robotLocation) : undefined),
    [robotLocation]
  );

  const setCurrentPath = async (
    plan: PathPlan | undefined
  ): Promise<boolean> => {
    const { input, path } = plan ?? { input: null, path: null };
    const promise = sendSetCurrentPath(input, path);
    setStateOfSetCurrentPath({ type: SetPathStateType.PENDING });
    let success;
    try {
      success = await promise;
    } catch (e) {
      setStateOfSetCurrentPath({
        type: SetPathStateType.REJECTED,
        reason: String(e),
      });
      return false;
    }
    if (success) {
      setStateOfSetCurrentPath({ type: SetPathStateType.FULFILLED });
      return true;
    } else {
      setStateOfSetCurrentPath({
        type: SetPathStateType.REJECTED,
        reason: "robot rejected path",
      });
      return false;
    }
  };

  const submitTargetLine = async ({
    fieldId,
    currentPoint,
    targetLine,
    sidestepOrigin,
  }: SubmitTargetLineArgs): Promise<void> => {
    const request: PlanPathRequest = {
      serial,
      fieldId,
      currentPoint: geojsonToCarbonPoint(currentPoint),
      targetLine: geojsonToCarbonLineString(targetLine),
      bufferMeters: 0,
    };
    const result = await planPath(request);
    if (result.data?.track) {
      setStateOfSetCurrentPath({ type: SetPathStateType.UNINITIALIZED });
      const { distanceMeters, track } = result.data;
      const path = feature(portalToGeojsonLineString(track));
      const input = {
        version: "v2" as const,
        fieldId,
        targetLine: feature(targetLine),
        sidestepOrigin: feature(sidestepOrigin),
      };
      const plan = { path, input };
      dispatch(setDraftPlan(plan));
      if (distanceMeters < MAX_DISTANCE_METERS) {
        await setCurrentPath(plan);
      }
    }
  };

  const enableGpsPathAutonomy = async (plan: PathPlan): Promise<boolean> => {
    if (!(await setCurrentPath(plan))) {
      return false;
    }
    return await sendSetAutonomy({
      steeringAssistAlgo: SteeringControlAlgo.GPS_PATH,
      mode: AutonomyMode.REMOTE_DRIVER,
    });
  };

  const enableFurrowFollowingAutonomy = async (): Promise<boolean> => {
    return await sendSetAutonomy({
      steeringAssistAlgo: SteeringControlAlgo.CV_FURROW_FOLLOW,
      mode: AutonomyMode.REMOTE_DRIVER,
    });
  };

  const disableAutonomy = async (): Promise<boolean> => {
    return await sendSetAutonomy({
      steeringAssistAlgo: SteeringControlAlgo.NONE,
      mode: AutonomyMode.REMOTE_DRIVER,
    });
  };

  const clearPath = async (): Promise<void> => {
    await Promise.all([setCurrentPath(undefined), disableAutonomy()]);
  };

  return {
    pathPlan,
    pathPlanningInput,
    currentPoint,
    autonomyLoading,
    enableGpsPathAutonomy,
    enableFurrowFollowingAutonomy,
    disableAutonomy,
    submitTargetLine,
    clearPath,
    planPathQuery,
    setPathState: stateOfSetCurrentPath,
  };
};
