import { carbonEnv } from "./env";
import { PostHogProvider } from "./components/PostHogProvider";
import { QueryEnv } from "common/state/queryEnv";
import { RootState } from "./store/store";
import { selectAccessToken } from "./store/self.slice";
import App from "./App";
import React from "react";
import ReactDOM from "react-dom/client";

import "@fontsource/lato";
import "@fontsource/poppins";
import "@fontsource/russo-one";
import "mapbox-gl/dist/mapbox-gl.css";

QueryEnv.initialize({
  portalBaseUrl: carbonEnv.PORTAL_ORIGIN,
  rtcJobsBaseUrl: carbonEnv.JOBS_ORIGIN,
  rtcLocatorBaseUrl: carbonEnv.LOCATOR_ORIGIN,
  auth0Audience: carbonEnv.AUTH0_AUDIENCE,
  accessTokenProvider: (api) => {
    const state = api.getState() as RootState;
    const accessToken = selectAccessToken(state);

    return Promise.resolve(accessToken);
  },
  traceFieldName: "RtcPage",
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <PostHogProvider>
    <App />
  </PostHogProvider>
);
