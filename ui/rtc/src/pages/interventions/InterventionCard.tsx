import { <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import { getDrivePath } from "@main/utils/routing";
import {
  Intervention,
  intervention_InterventionCauseToJSON as interventionCauseToJSON,
  State,
  stateToJSON,
} from "@protos/ts2/rtc/jobs";
import { jobStateIcon } from "common/utils/job";
import { ROUTES } from "../routes";
import {
  useGetJobQuery,
  useUpdateInterventionMutation,
} from "common/state/rtcJobsApi";
import DescriptionIcon from "@mui/icons-material/FormatQuote";
import FlagIcon from "@mui/icons-material/Flag";
import JobIcon from "@mui/icons-material/Assignment";
import React, { FC } from "react";

interface InterventionCardProps {
  intervention: Intervention;
}

export const InterventionCard: FC<InterventionCardProps> = ({
  intervention,
}) => {
  const [updateIntervention] = useUpdateInterventionMutation();
  const { isLoading, data: job } = useGetJobQuery({
    id: intervention.jobId.toString(),
  });

  const StateIcon = jobStateIcon(intervention.state);

  return (
    <Card className="bg-rtc-gray-700 p-4 flex flex-col gap-2">
      <div className="flex justify-between">
        <Typography variant="h3">{intervention.id}</Typography>
        <Typography variant="h3">{intervention.robotSerial}</Typography>
      </div>
      <div className=" flex flex-col p-2 gap-2">
        <div className="flex justify-between">
          <div className="flex gap-2">
            <JobIcon />
            <Typography className="font-semibold">Job:</Typography>
          </div>
          {isLoading && <Typography>...loading</Typography>}
          {!isLoading && (
            <Typography>
              {job ? `${job.name}` : `unknown job (id ${intervention.jobId})`}
            </Typography>
          )}
        </div>
        <div className="flex justify-between">
          <div className="flex gap-2">
            <FlagIcon />
            <Typography className="font-semibold">Cause:</Typography>
          </div>
          <Typography>{interventionCauseToJSON(intervention.cause)}</Typography>
        </div>
        <div className="flex justify-between">
          <div className="flex gap-2">
            <StateIcon />
            <Typography className="font-semibold">State:</Typography>
          </div>
          <Typography>{stateToJSON(intervention.state)}</Typography>
        </div>
        <div>
          <div className="flex gap-2">
            <DescriptionIcon />
            <Typography className="font-semibold">Description:</Typography>
          </div>
          <div className="p-2 max-h-24 overflow-y-auto">
            {intervention.description}
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2 mt-auto">
        {intervention.state === State.IN_PROGRESS && (
          <Button
            color="primary"
            variant="contained"
            disabled={intervention.state !== State.IN_PROGRESS}
            size="medium"
            href={getDrivePath(intervention.robotSerial, {
              returnTo: ROUTES.Interventions.path,
            })}
          >
            drive {intervention.robotSerial}
          </Button>
        )}
        {intervention.state !== State.IN_PROGRESS &&
          intervention.state !== State.COMPLETED && (
            <Button
              variant="contained"
              onClick={() =>
                updateIntervention({
                  ...intervention,
                  state: State.IN_PROGRESS,
                })
              }
            >
              Start Intervention
            </Button>
          )}
        {intervention.state === State.IN_PROGRESS && (
          <Button
            variant="contained"
            onClick={() =>
              updateIntervention({ ...intervention, state: State.COMPLETED })
            }
          >
            Complete Intervention
          </Button>
        )}
      </div>
    </Card>
  );
};
