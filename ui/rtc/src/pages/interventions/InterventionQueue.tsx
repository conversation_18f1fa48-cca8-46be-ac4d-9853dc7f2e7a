import { Button, CircularProgress, Typography } from "@mui/material";
import { InterventionCard } from "./InterventionCard";
import { Page } from "@main/components/Page";
import { State } from "@protos/ts2/rtc/jobs";
import { TopNav } from "@main/components/topnav/TopNav";
import { useListInterventionsQuery } from "common/state/rtcJobsApi";
import React, { FC, useState } from "react";

export const InterventionQueue: FC = () => {
  const { isLoading, data: interventionsResp } = useListInterventionsQuery(
    {},
    { pollingInterval: 5000 }
  );
  const interventions = interventionsResp?.interventions;

  const [hideCompleted, setHideCompleted] = useState(true);

  return (
    <div className="h-screen">
      <TopNav />
      <Page className="flex flex-col gap-4">
        <div className="flex gap-8">
          <Typography className="flex my-auto" variant="h3">
            interventions
            {isLoading && (
              <CircularProgress
                variant="indeterminate"
                className="ml-4 size-6"
              />
            )}
          </Typography>
          <Button
            variant="contained"
            onClick={() => setHideCompleted(!hideCompleted)}
          >
            {hideCompleted ? "Show Completed" : "Hide Completed"}
          </Button>
        </div>
        <div className="flex flex-col md:grid md:grid-cols-3 md:grid-flow-row gap-4 md:gap-2">
          {interventions &&
            interventions
              .filter((i) =>
                hideCompleted ? i.state !== State.COMPLETED : true
              )
              .map((intervention) => (
                <InterventionCard
                  key={intervention.id}
                  intervention={intervention}
                />
              ))}
        </div>
      </Page>
    </div>
  );
};
