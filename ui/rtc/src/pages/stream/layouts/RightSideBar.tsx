import { animated, useSpring } from "@react-spring/web";
import { AutonomyControlsPane } from "@main/components/panes/AutonomyControlsPane";
import { ButtonBase, IconButton, Tooltip } from "@mui/material";
import { DebugInfoPane } from "@main/components/panes/DebugInfoPane";
import {
  type HotKey,
  HotKeysReferenceDialog,
} from "@main/components/hotKeys/HotKeysReferenceDialog";
import { ImplementPane } from "@main/components/panes/implementPane/ImplementPane";
import { JobPane } from "@main/components/panes/JobPane";
import { MapPane } from "@main/components/panes/MapPane";
import { SideBar } from "@main/components/SideBar";
import clsx from "clsx";
import CollapseIcon from "@mui/icons-material/ChevronRight";
import GamepadIcon from "@mui/icons-material/Gamepad";
import React, { FC, useState } from "react";

interface RightSideBarProps {
  keyMaps?: HotKey[];
  style?: React.CSSProperties;
}
export const RightSideBar: FC<RightSideBarProps> = ({ keyMaps, style }) => {
  const [isKeyboardHotkeyDialogOpen, setIsKeyboardHotkeyDialogOpen] =
    useState(false);
  const [collapsed, setCollapsed] = useState<boolean>(false);

  // NOTE(bret): This is related to `collapsed`, but is set at the tail end of
  // the useSpring animation. `collapsed` and `expandedContentVisible` will be
  // opposite values during the animation
  // Using `collapsed` for both values causes a scrollbar to flash on animation
  const [expandedContentVisible, setExpandedContentVisible] =
    useState<boolean>(true);

  const collapseExpandContentAnimationProps = useSpring({
    opacity: collapsed ? 0 : 1,
    config: { duration: 50 },
    delay: collapsed ? 0 : 100,
    onRest: () => {
      setExpandedContentVisible(!collapsed);
    },
  });

  const widthExpandedClassName = "w-64 min-w-64 max-w-64";
  const widthCollapsedClassName = "w-12 min-w-12 max-w-12";

  return (
    <>
      {keyMaps && keyMaps.length > 0 && (
        <HotKeysReferenceDialog
          keyMaps={keyMaps}
          open={isKeyboardHotkeyDialogOpen}
          onClose={() => {
            setIsKeyboardHotkeyDialogOpen(false);
          }}
        />
      )}
      <SideBar
        className={clsx("transition-all duration-200 ease-in-out text-sm", {
          [widthExpandedClassName]: !collapsed,
          [widthCollapsedClassName]: collapsed,
        })}
        style={style}
        header={{
          className: "bg-transparent",
          left: keyMaps && !collapsed && expandedContentVisible && (
            <Tooltip title="Show driving keyboard commands">
              <IconButton
                size="small"
                className="text-rtc-gray-200"
                onClick={() => {
                  setIsKeyboardHotkeyDialogOpen(true);
                }}
              >
                <GamepadIcon />
              </IconButton>
            </Tooltip>
          ),
          right: (
            <div>
              <IconButton size="small" onClick={() => setCollapsed((c) => !c)}>
                <CollapseIcon
                  aria-label={collapsed ? "expand" : "collapse"}
                  className={clsx("transition", {
                    "rotate-180": collapsed,
                    "rotate-0": !collapsed,
                  })}
                />
              </IconButton>
            </div>
          ),
        }}
      >
        <animated.div
          style={{
            ...collapseExpandContentAnimationProps,
            display: expandedContentVisible ? "block" : "none",
          }}
          // NOTE(bret): this is set to the sidebar's expanded width
          // to keep from shrinking during the animation
          className={widthExpandedClassName}
        >
          <DebugInfoPane />
          <JobPane />
          <MapPane />
          <ImplementPane />
          <AutonomyControlsPane />
        </animated.div>
        <ButtonBase
          className={clsx(
            expandedContentVisible && "hidden",
            "p-0 w-full h-full hover:bg-white/20 transition"
          )}
          onClick={() => setCollapsed(false)}
          disableRipple
        />
      </SideBar>
    </>
  );
};
