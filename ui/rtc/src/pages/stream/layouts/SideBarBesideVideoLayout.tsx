import { Alerts } from "@main/components/alerts/Alerts";
import { ConnectionStatus } from "@main/components/dashbar/ConnectionStatus";
import {
  InteractiveVideo,
  InteractiveVideoProps,
} from "../../../components/videoOverlays/InteractiveVideo";
import { selectNetworkStats } from "@main/store/driving.slice";
import { SideBar, SideBarProps } from "@main/components/SideBar";
import { Typography } from "@mui/material";
import { useAppSelector } from "@main/store/hooks";
import { useVideoStreamContext } from "../videoStreamContext";
import { Video } from "@main/components/Video";
import CameraIcon from "@mui/icons-material/Videocam";
import clsx from "clsx";
import React, { FC, PropsWithChildren, ReactNode } from "react";

/**
 * Component that provides a base layout with the side bar next to a fully
 * visible video
 */
interface SideBarBesideVideoLayoutProps extends PropsWithChildren {
  disableVideo?: boolean;
  sideBarProps: SideBarProps;
  videoInteractionProps?: InteractiveVideoProps;
  videoOverlay?: ReactNode;
  videoWrapperClassName?: string;
}
export const SideBarBesideVideoLayout: FC<SideBarBesideVideoLayoutProps> = ({
  sideBarProps,
  disableVideo = false,
  videoInteractionProps,
  videoOverlay,
  videoWrapperClassName,
  children,
}) => {
  const networkStats = useAppSelector(selectNetworkStats);
  const isDataStreamConnected = networkStats.rtcStatus === "connected";
  const { stream } = useVideoStreamContext();

  const renderNonInteractiveVideo = disableVideo ? (
    <div className="flex h-full justify-center items-center gap-2 text-rtc-gray-200">
      <CameraIcon />
      <Typography>Disabled</Typography>
    </div>
  ) : (
    <Video className="absolute h-full w-full" srcObject={stream} />
  );

  return (
    <>
      <Alerts />
      <div
        className={clsx(videoWrapperClassName, "grid")}
        style={{ gridTemplateColumns: "300px minmax(0, 1fr)" }}
      >
        <SideBar
          {...sideBarProps}
          header={{
            className: clsx({ "opacity-75": !isDataStreamConnected }),
            right: (
              <div className="px-2">
                <ConnectionStatus />
              </div>
            ),
            ...sideBarProps.header,
          }}
        >
          {children}
        </SideBar>
        <div className="self-center overflow-y-visible relative h-full">
          {videoInteractionProps ? (
            <InteractiveVideo
              {...videoInteractionProps}
              disableVideo={disableVideo}
            />
          ) : (
            renderNonInteractiveVideo
          )}
          {videoOverlay}
        </div>
      </div>
    </>
  );
};
