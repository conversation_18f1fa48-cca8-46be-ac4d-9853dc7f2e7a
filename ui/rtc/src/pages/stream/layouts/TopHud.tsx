import { AutonomyMode, HHState } from "@main/components/controls/drive.types";
import {
  buildPermission,
  WithAuthorizationRequired,
} from "@main/components/auth/WithAuthorizationRequired";
import {
  Button,
  IconButton,
  LinearProgress,
  Paper,
  SvgIcon,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  ClientInfoResponse,
  MessageType,
} from "@main/communication/websocket/webSocketMessage.types";
import { createClientInfoRequest } from "@main/communication/websocket/webSocketMessages";
import { createControlRequest } from "@main/communication/rtc/dataChannelMessages";
import { DateTime } from "luxon";
import { FullScreenButton } from "@main/components/FullScreenButton";
import { Link, useParams, useSearchParams } from "react-router-dom";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "@protos/ts2/portal/auth";
import { ROUTES } from "@main/pages/routes";
import {
  selectControllingClientId,
  selectInDriverSeat,
  selectReportedControlState,
} from "@main/store/driving.slice";
import { useAppSelector } from "@main/store/hooks";
import { useAuth } from "@main/components/auth/useAuth";
import { useMetaChannelMessaging } from "@main/communication/rtc/hooks/useMetaChannelMessaging";
import { useTractorAnalytics } from "@main/hooks/useTractorAnalytics";
import { useVideoStreamContext } from "../videoStreamContext";
import { useWebSocketRequest } from "@main/communication/websocket/hooks/useWebSocketRequest";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AutonomyIcon from "@mui/icons-material/AutoAwesome";
import ManualIcon from "@mui/icons-material/Engineering";
import React, { FC, useEffect, useState } from "react";
import SteeringWheelIcon from "@main/assets/steeringWheel.svg?react";

interface TopHudProps {
  toggleFullscreen: () => void;
  isFullscreen: boolean;
}
export const TopHud: FC<TopHudProps> = ({ toggleFullscreen, isFullscreen }) => {
  const { stream } = useVideoStreamContext();
  const { targetHostId } = useParams();
  const controllerId = useAppSelector(selectControllingClientId);
  const inDriverSeat = useAppSelector(selectInDriverSeat);
  const [searchParams] = useSearchParams();
  const returnTo = searchParams.get("returnTo") ?? ROUTES.Tractors.path;
  const { tractorBoardState, autonomousState } = useAppSelector(
    selectReportedControlState
  );
  const tractorInControl = autonomousState?.mode === AutonomyMode.TASK_AUTONOMY;

  const { user } = useAuth();

  const [clientInfo, setClientInfo] = useState<
    ClientInfoResponse["content"] | undefined
  >();
  const { fetch: fetchClientInfo } = useWebSocketRequest({
    responseMessageType: MessageType.CLIENT_INFO_RESPONSE,
    onResponse: (r: ClientInfoResponse["content"]) => {
      setClientInfo(r);
    },
  });

  const { trackEvent } = useTractorAnalytics();

  useEffect(() => {
    if (controllerId && targetHostId) {
      fetchClientInfo(createClientInfoRequest(controllerId, targetHostId));
    }
  }, [controllerId, fetchClientInfo, targetHostId]);

  const buttonMessage = inDriverSeat ? "Stop Controlling" : "Take Control";

  const controllerMessage = (): string => {
    let message = "";
    if (
      tractorBoardState?.rtcLockout ||
      tractorBoardState?.hhState === HHState.HH_DISABLED
    ) {
      message = "In-cab operator";
      return message;
    }
    if (controllerId === undefined) {
      return message;
    }
    if (clientInfo?.displayName) {
      message = clientInfo.displayName;
    }
    if (clientInfo?.displayName === user?.name && !inDriverSeat) {
      message = `${message} (another window)`;
    }

    return message;
  };

  const { sendMetaMessage } = useMetaChannelMessaging();
  const toggleControl = (): void => {
    if (inDriverSeat) {
      trackEvent("stop_controlling_clicked");
    } else {
      trackEvent("take_control_clicked");
    }

    sendMetaMessage(createControlRequest({ inControl: !inDriverSeat }));
  };

  const progressValue =
    (inDriverSeat && Boolean(stream)) || tractorInControl ? 100 : 0;

  const [displayTime, setDisplayTime] = useState(DateTime.now());
  useEffect(() => {
    const timer = setInterval(() => {
      setDisplayTime(DateTime.now());
    }, 30000); // every 30 seconds

    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <>
      <div>
        <Paper className="flex p-1 w-full">
          <div className="flex w-1/3 justify-between">
            <IconButton component={Link} to={returnTo}>
              <ArrowBackIcon aria-label="back" />
            </IconButton>
            <div className="flex gap-2">
              <Typography className="my-auto">{targetHostId}</Typography>
              <Typography className="my-auto">{`${displayTime.toFormat("HH:mm")}`}</Typography>
            </div>
          </div>
          <div className="flex flex-grow justify-center gap-1">
            <WithAuthorizationRequired
              permissionGroups={[
                buildPermission(
                  PermissionAction.update,
                  PermissionResource.autotractor_drive,
                  PermissionDomain.all
                ),
                buildPermission(
                  PermissionAction.update,
                  PermissionResource.autotractor_drive,
                  PermissionDomain.customer
                ),
              ]}
            >
              <Button
                color="primary"
                variant={inDriverSeat ? "text" : "contained"}
                size="small"
                onClick={toggleControl}
              >
                {buttonMessage}
              </Button>
            </WithAuthorizationRequired>
          </div>
          <div className="flex w-1/3 justify-between">
            <div className="flex">
              <Tooltip title="Who currently has control of the tractor">
                <div className="flex my-auto gap-2">
                  {tractorInControl && <AutonomyIcon color="success" />}
                  {!tractorInControl && <ManualIcon />}
                  <SvgIcon fontSize="small" className="flex my-auto">
                    <SteeringWheelIcon />
                  </SvgIcon>
                  <Typography className="flex my-auto" fontSize={"small"}>
                    {controllerMessage()}
                  </Typography>
                </div>
              </Tooltip>
            </div>
            <FullScreenButton
              className="flex ml-auto"
              onClick={toggleFullscreen}
              isFullscreen={isFullscreen}
            />
          </div>
        </Paper>
        <LinearProgress
          variant="determinate"
          color={tractorInControl ? "success" : "primary"}
          value={progressValue}
        />
      </div>
    </>
  );
};
