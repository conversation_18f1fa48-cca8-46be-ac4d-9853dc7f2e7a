import { <PERSON><PERSON>, LinearProgress, MenuItem, Typography } from "@mui/material";
import {
  CamerasToSlices,
  CompositeViews,
  selectActiveViewId,
  selectInControl,
  selectNetworkStats,
  selectServiceStatuses,
} from "@main/store/driving.slice";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import { CollapsibleListButtons } from "@main/components/CollapsibleListButtons";
import { createControlRequest } from "@main/communication/rtc/dataChannelMessages";
import { DeleteCompositeViewDialog } from "@main/components/dialogs/DeleteCompositeViewDialog";
import {
  getCameraSlicePreviewViewId,
  useViewChannelMessaging,
} from "@main/communication/rtc/hooks/useViewChannelMessaging";
import { Menu } from "@main/components/Menu";
import { Pane } from "@main/components/Pane";
import { Link as RouterLink } from "react-router-dom";
import { ROUTES } from "@main/pages/routes";
import { SideBarBesideVideoLayout } from "../layouts/SideBarBesideVideoLayout";
import { useAppSelector } from "@main/store/hooks";
import { useMetaChannelMessaging } from "@main/communication/rtc/hooks/useMetaChannelMessaging";
import { useNavigate } from "react-router";
import { ViewTagChip } from "@main/components/tags/ViewTagChip";
import AddIcon from "@mui/icons-material/Add";
import clsx from "clsx";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import React, { FC, useEffect, useState } from "react";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";

export const ListViewsAndSlices: FC = () => {
  const navigate = useNavigate();
  const networkStats = useAppSelector(selectNetworkStats);
  const isDataStreamConnected = networkStats.rtcStatus === "connected";

  const { sendMetaMessage } = useMetaChannelMessaging();
  const inControl = useAppSelector(selectInControl);

  const activeCompositeViewId = useAppSelector(selectActiveViewId);
  const [compositeViews, setCompositeViews] = useState<CompositeViews>({});
  const [cameraSlices, setCameraSlices] = useState<CamerasToSlices>({});
  const [areCompositeViewsLoading, setAreCompositeViewsLoading] =
    useState(false);
  const [compositeViewsLoadingError, setCompositeViewsError] = useState(false);
  const [areCameraSlicesLoading, setAreCameraSlicesLoading] = useState(false);
  const [cameraSlicesLoadingError, setCameraSlicesError] = useState(false);
  const {
    sendGetCompositeViews,
    sendGetCameras,
    sendViewCameraSlice,
    sendSetActiveCompositeView,
    sendDeleteView,
  } = useViewChannelMessaging();
  const [isConfirmDeletionDialogOpen, setIsConfirmDeletionDialogOpen] =
    useState(false);
  const [deletionItemId, setDeletionItemId] = useState<string | undefined>();
  const deletionItem = deletionItemId
    ? compositeViews[deletionItemId]
    : undefined;

  const isViewServiceOnline = useAppSelector((state) =>
    Boolean(selectServiceStatuses(state)[ChannelLabel.VIEW_CONTROLS]?.online)
  );

  useEffect(() => {
    if (!inControl) {
      sendMetaMessage(createControlRequest({ inControl: true }));
    }
  }, [inControl, sendMetaMessage]);

  useEffect(() => {
    // get initial data on page load
    setAreCompositeViewsLoading(true);
    setCompositeViewsError(false);

    setAreCameraSlicesLoading(true);
    setCameraSlicesError(false);

    if (!isViewServiceOnline) {
      return;
    }

    sendGetCompositeViews()
      .then((data) => {
        setCompositeViews(data ?? {});
        setAreCompositeViewsLoading(false);
        setCompositeViewsError(false);
      })
      .catch(() => {
        setAreCompositeViewsLoading(false);
        setCompositeViewsError(true);
      });

    sendGetCameras()
      .then((data) => {
        setCameraSlices(data ?? {});
        setAreCameraSlicesLoading(false);
        setCameraSlicesError(false);
      })
      .catch(() => {
        setAreCameraSlicesLoading(false);
        setCameraSlicesError(true);
      });
  }, [isViewServiceOnline, sendGetCameras, sendGetCompositeViews]);

  const onSliceClicked = (sliceId: string): void => {
    const windowConfig = {
      id: sliceId,
      x: 0,
      y: 0,
      width: 1,
      height: 1,
    };
    sendViewCameraSlice(windowConfig);
  };

  const paneHeaderClassNames = clsx({
    "text-rtc-gray-400": !isDataStreamConnected,
  });

  return (
    <>
      {deletionItemId && deletionItem && (
        <DeleteCompositeViewDialog
          isOpen={isConfirmDeletionDialogOpen}
          onOk={async () => {
            const success = await sendDeleteView(deletionItemId);
            if (success) {
              const updated = { ...compositeViews };
              delete updated[deletionItemId];
              setCompositeViews(updated);
            }
          }}
          deletionItem={{
            name: deletionItem.name,
            tags: deletionItem.tags,
          }}
          onCloseModal={() => {
            setDeletionItemId(undefined);
            setIsConfirmDeletionDialogOpen(false);
          }}
        />
      )}
      <SideBarBesideVideoLayout
        sideBarProps={{
          header: { backOnClick: () => navigate(`../${ROUTES.Drive.path}`) },
        }}
      >
        <Pane
          className="min-h-16"
          headerClassName={paneHeaderClassNames}
          header={
            <div className="flex justify-between items-center">
              <Typography variant="caption">Composite Views</Typography>
              <NewButton
                disabled={!isDataStreamConnected}
                to={`../${ROUTES.NewCompositeView.path}`}
              />
            </div>
          }
        >
          {areCompositeViewsLoading && (
            <LinearProgress variant="indeterminate" />
          )}
          {compositeViewsLoadingError && <LoadingError />}
          <div className="max-h-[40vh] overflow-y-scroll">
            <CollapsibleListButtons
              list={Object.entries(compositeViews)
                .filter(([_viewId, { isSaved }]) => isSaved)
                .map(([viewId, { name, cameraSlices, tags }]) => ({
                  id: viewId,
                  name: (
                    <div className="flex gap-1">
                      <Typography variant="body2" className="font-bold">
                        {name}
                      </Typography>
                      {tags.map((tag) => (
                        <ViewTagChip key={tag} tag={tag} />
                      ))}
                    </div>
                  ),
                  endParentAction: (
                    <ItemMore
                      menuItems={[
                        {
                          title: "Edit",
                          onClick: () => {
                            navigate(
                              `../${ROUTES.EditCompositeView.path}/${viewId}`
                            );
                          },
                        },
                        {
                          title: "Delete",
                          disabled: activeCompositeViewId === viewId,
                          onClick: () => {
                            setDeletionItemId(viewId);
                            setIsConfirmDeletionDialogOpen(true);
                          },
                        },
                      ]}
                    />
                  ),
                  selectable: true,
                  selected: viewId === activeCompositeViewId,
                  onClick: () => {
                    sendSetActiveCompositeView(viewId);
                  },
                  items: cameraSlices.map(({ id, name }) => ({
                    id,
                    name,
                    selectable: true,
                    selected: id === activeCompositeViewId,
                    onClick: onSliceClicked,
                  })),
                }))}
            />
          </div>
        </Pane>
        <Pane
          className="min-h-16"
          headerClassName={paneHeaderClassNames}
          header={
            <div className="flex justify-between items-center">
              <Typography variant="caption">Camera Slices</Typography>
              <NewButton
                disabled={!isDataStreamConnected}
                to={`../${ROUTES.NewCameraSlice.path}`}
              />
            </div>
          }
        >
          {areCameraSlicesLoading && <LinearProgress variant="indeterminate" />}
          {cameraSlicesLoadingError && <LoadingError />}
          <div className="max-h-[45vh] overflow-y-scroll">
            <CollapsibleListButtons
              list={Object.entries(cameraSlices).map(
                ([cameraId, { slices }]) => ({
                  id: cameraId,
                  name: cameraId,
                  selectable: false,
                  items: slices.map(({ id, name }) => ({
                    id,
                    name,
                    selectable: true,
                    selected:
                      getCameraSlicePreviewViewId(id) === activeCompositeViewId,
                    onClick: onSliceClicked,
                  })),
                })
              )}
            />
          </div>
        </Pane>
      </SideBarBesideVideoLayout>
    </>
  );
};

interface NewButtonProps {
  to: string;
  disabled?: boolean;
}
const NewButton: FC<NewButtonProps> = ({ to, disabled }) => {
  return (
    <Button
      component={RouterLink}
      to={to}
      size="small"
      className="text-inherit p-0 hover:bg-white/5"
      startIcon={<AddIcon />}
      disabled={disabled}
    >
      New
    </Button>
  );
};

const LoadingError: FC = () => (
  <div className="flex justify-center gap-2 text-red-300 p-2">
    <WarningAmberIcon className="text-xs" />
    <Typography className="text-xs">Loading Error</Typography>
  </div>
);

interface ItemMoreProps {
  menuItems: {
    title: string;
    disabled?: boolean;
    onClick: () => void;
  }[];
}
const ItemMore: FC<ItemMoreProps> = ({ menuItems }) => {
  return (
    <Menu
      icon={<MoreVertIcon className="text-base text-white" />}
      iconButtonClassName="p-1"
      menuItems={menuItems.map(({ title, disabled, onClick }) => (
        <MenuItem key={title} onClick={onClick} disabled={disabled}>
          <Typography textAlign="center">{title}</Typography>
        </MenuItem>
      ))}
    />
  );
};
