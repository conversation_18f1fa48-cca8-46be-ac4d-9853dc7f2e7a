import {
  add<PERSON><PERSON>ts,
  CamerasToSlices,
  selectActiveViewId,
  selectDataChannelStates,
  selectInControl,
  selectNetworkStats,
} from "@main/store/driving.slice";
import { AlertType } from "@main/components/alerts/alert.types";
import {
  applyViewportTranslation,
  buildFinalTransform as buildFinalFisheyeTransform,
  buildPreviewTransform as buildPreviewFisheyeTransform,
  FISHEYE_ZOOM_RANGE,
  getInitialFisheyeState,
} from "@main/logic/cameraSlice/fisheye";
import { assertNever } from "@main/logic/utils/identity";
import {
  buildFinalTransforms as buildFinalFlatTransforms,
  buildPreviewTransforms as buildPreviewFlatTransforms,
  FLAT_ZOOM_RANGE,
  getInitialFlatState,
  getOffsetBounds,
} from "@main/logic/cameraSlice/flat";
import { Button, Collapse, Typography } from "@mui/material";
import { CameraSliceInfoForm } from "@main/components/forms/CameraSliceInfoForm";
import { CameraSubPane } from "@main/components/panes/subPanes/CameraSubPane";
import {
  CameraType,
  CURRENT_METADATA_VERSION,
  type Transform,
  type Window,
} from "@main/communication/rtc/viewControlMessages.types";
import { ChannelLabel } from "@main/communication/rtc/dataChannelMessage.types";
import { createControlRequest } from "@main/communication/rtc/dataChannelMessages";
import {
  type FisheyeState,
  type FlatState,
  type FrameState,
  type TransformState,
  TransformStateType,
  type Viewport,
} from "@main/logic/cameraSlice/state";
import { FisheyeStateForm } from "@main/components/forms/FisheyeStateForm";
import { FlatStateForm } from "@main/components/forms/FlatStateForm";
import { FrameOverlay } from "@main/components/videoOverlays/FrameOverlay";
import { FrameSubPane } from "@main/components/panes/subPanes/FrameSubPane";
import { Pane } from "@main/components/Pane";
import { roundTwoDecimal } from "@main/logic/numbers";
import { ROUTES } from "@main/pages/routes";
import { SideBarBesideVideoLayout } from "../layouts/SideBarBesideVideoLayout";
import { useAppDispatch, useAppSelector } from "@main/store/hooks";
import { useMetaChannelMessaging } from "@main/communication/rtc/hooks/useMetaChannelMessaging";
import { useNavigate } from "react-router";
import { useViewChannelMessaging } from "@main/communication/rtc/hooks/useViewChannelMessaging";
import React, { FC, useCallback, useEffect, useRef, useState } from "react";

export const NewCameraSlice: FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [viewport, setViewport] = useState<Viewport>({
    width: 1920,
    height: 1080,
  });
  const activeViewId = useAppSelector(selectActiveViewId);
  const previousActiveViewId = useRef<string | undefined>(activeViewId);
  const networkStats = useAppSelector(selectNetworkStats);

  const { sendMetaMessage } = useMetaChannelMessaging();
  const inControl = useAppSelector(selectInControl);

  const {
    sendGetCameras,
    sendGetDefaultViewForCamera,
    sendStoreCameraSlice,
    sendSetActiveCompositeView,
    sendGetViewportDimensions,
  } = useViewChannelMessaging();
  const rtcConnected = networkStats.rtcStatus === "connected";
  const dataChannelOpen = useAppSelector(
    (state) =>
      selectDataChannelStates(state)[ChannelLabel.VIEW_CONTROLS] === "open"
  );
  const isConnected = rtcConnected && dataChannelOpen;

  const [areCameraOptionsLoading, setAreCameraOptionsLoading] = useState(true);
  const [availableCameras, setAvailableCameras] = useState<CamerasToSlices>();

  // Form data being edited.
  const [infoForm, setInfoForm] = useState<{
    name: string;
    cameraId: string | null;
  }>({
    name: "",
    cameraId: null,
  });
  const [frame, setFrame] = useState<FrameState>({
    ...viewport,
    x: 0,
    y: 0,
  });
  const [transformState, setTransformState] = useState<
    TransformState | undefined
  >();

  const [infoFormHasErrors, setInfoFormHasErrors] = useState(false);
  const [frameFormHasErrors, setFrameFormHasErrors] = useState(false);
  const [transformsFormHasErrors, setTransformsFormHasErrors] = useState(false);

  // Camera slice returned by GET_DEFAULT_VIEW, used as a base when we need to
  // store the window.
  const [cameraSlice, setCameraSlice] = useState<Window | undefined>();

  useEffect(() => {
    if (!inControl) {
      sendMetaMessage(createControlRequest({ inControl: true }));
    }
  }, [inControl, sendMetaMessage]);

  useEffect(() => {
    const getInitialData = async (): Promise<void> => {
      const viewportDims = await sendGetViewportDimensions();
      if (viewportDims) {
        setViewport(viewportDims);
      }
      const cameras = await sendGetCameras();
      setAvailableCameras(cameras);
      setAreCameraOptionsLoading(false);
    };
    if (isConnected) {
      getInitialData();
    }
  }, [isConnected, sendGetCameras, sendGetViewportDimensions]);

  const onBack = useCallback(() => {
    if (previousActiveViewId.current) {
      sendSetActiveCompositeView(previousActiveViewId.current);
    }
    navigate(`../${ROUTES.ListViewsAndSlices.path}`);
  }, [navigate, sendSetActiveCompositeView]);

  const onFisheyeInputChange = (
    cameraSlice: Window,
    state: FisheyeState
  ): void => {
    setTransformState(state);
    const transforms = [buildPreviewFisheyeTransform(state, viewport)];
    const transformedCameraSlice = { ...cameraSlice, transforms };
    sendStoreCameraSlice(transformedCameraSlice, false);
  };

  const onFlatInputChange = (cameraSlice: Window, state: FlatState): void => {
    setTransformState(state);
    const transforms = buildPreviewFlatTransforms(state, viewport);
    const transformedCameraSlice = { ...cameraSlice, transforms };
    sendStoreCameraSlice(transformedCameraSlice, false);
  };

  return (
    <SideBarBesideVideoLayout
      sideBarProps={{
        header: {
          title: "New Camera Slice",
          backOnClick: onBack,
        },
        bottom: (
          <div className="flex justify-end gap-4 p-6">
            <Button variant="outlined" size="small" onClick={onBack}>
              Cancel
            </Button>
            <Button
              variant="contained"
              size="small"
              disabled={
                !cameraSlice ||
                infoFormHasErrors ||
                frameFormHasErrors ||
                transformsFormHasErrors
              }
              onClick={() => {
                if (!cameraSlice || !transformState) {
                  return;
                }
                let transforms: Transform[];
                switch (transformState.type) {
                  case TransformStateType.FISHEYE: {
                    const fisheyeTransform = buildFinalFisheyeTransform(
                      transformState,
                      frame,
                      viewport
                    );
                    transforms = [fisheyeTransform];
                    break;
                  }
                  case TransformStateType.FLAT: {
                    transforms = buildFinalFlatTransforms(
                      transformState,
                      frame,
                      viewport
                    );
                    break;
                  }
                }
                const toBeSaved = structuredClone(cameraSlice);
                toBeSaved.transforms = transforms;
                toBeSaved.metadata.data.name = infoForm.name;
                sendStoreCameraSlice(toBeSaved, true /* mark as "saved" */);
                navigate(`../${ROUTES.ListViewsAndSlices.path}`);
              }}
            >
              Save
            </Button>
          </div>
        ),
      }}
      disableVideo={!infoForm.cameraId}
      videoOverlay={
        infoForm.cameraId && cameraSlice ? (
          <FrameOverlay
            videoWidth={viewport.width}
            videoHeight={viewport.height}
            initialFrame={frame}
            onFrameChange={(frame) =>
              setFrame({
                x: roundTwoDecimal(frame.x),
                y: roundTwoDecimal(frame.y),
                width: roundTwoDecimal(frame.width),
                height: roundTwoDecimal(frame.height),
              })
            }
          />
        ) : null
      }
      videoInteractionProps={(() => {
        if (!transformState || !cameraSlice) {
          return {};
        }
        switch (transformState.type) {
          case TransformStateType.FISHEYE:
            return {
              translation: {
                current: { x: 0, y: 0 },
                debounceDistance: 0.1,
                range: {
                  // these are not really hit since we only get one-frame deltas
                  x: { min: -90, max: 90 },
                  y: { min: -90, max: 90 },
                },
                scalar: 0.05,
                onChange: ({ x, y }) => {
                  onFisheyeInputChange(
                    cameraSlice,
                    applyViewportTranslation(transformState, { x, y: -y })
                  );
                },
              },
              zoom: {
                current: transformState.zoom,
                debounceDistance: 5,
                range: FISHEYE_ZOOM_RANGE,
                scalar: 0.0025,
                onChange: (zoom: number) => {
                  zoom = roundTwoDecimal(zoom);
                  onFisheyeInputChange(cameraSlice, {
                    ...transformState,
                    zoom,
                  });
                },
              },
            };
          case TransformStateType.FLAT: {
            const bounds = getOffsetBounds(transformState);
            return {
              translation: {
                current: {
                  x: transformState.offsetX,
                  y: transformState.offsetY,
                },
                debounceDistance: 0.1,
                range: {
                  x: { min: 0, max: bounds.offsetX },
                  y: { min: 0, max: bounds.offsetY },
                },
                scalar: 1 / transformState.zoom,
                onChange: ({ x: offsetX, y: offsetY }) => {
                  onFlatInputChange(cameraSlice, {
                    ...transformState,
                    offsetX: Math.round(offsetX),
                    offsetY: Math.round(offsetY),
                  });
                },
              },
              zoom: {
                current: transformState.zoom,
                debounceDistance: 5,
                range: FLAT_ZOOM_RANGE,
                scalar: 0.0025,
                onChange: (zoom: number) => {
                  zoom = roundTwoDecimal(zoom);
                  onFlatInputChange(cameraSlice, { ...transformState, zoom });
                },
              },
            };
          }
        }
      })()}
    >
      <Pane
        className="min-h-16"
        header={
          <div>
            <Typography variant="caption">Info</Typography>
          </div>
        }
      >
        <div className="pt-4 pb-6 px-6 flex flex-col gap-2">
          <CameraSliceInfoForm
            initialValues={infoForm}
            onNameChange={(name: string) => setInfoForm({ ...infoForm, name })}
            areCameraOptionsLoading={areCameraOptionsLoading}
            cameraOptions={
              availableCameras ? Object.keys(availableCameras) : []
            }
            onError={setInfoFormHasErrors}
            onCameraChange={async (cameraId: string): Promise<void> => {
              setInfoForm({ ...infoForm, cameraId });
              if (!cameraId) {
                return;
              }

              const cameraSlice = await sendGetDefaultViewForCamera(cameraId);
              if (!cameraSlice) {
                dispatch(
                  addAlerts([
                    {
                      title: AlertType.EDITING,
                      message: `Can't get the default view for camera ${cameraId}. Try a different camera.`,
                    },
                  ])
                );
                return;
              }

              setCameraSlice({
                ...cameraSlice,
                metadata: {
                  version: CURRENT_METADATA_VERSION,
                  data: {
                    name:
                      infoForm.name ??
                      `${infoForm.cameraId} - Unsaved Camera Slice`,
                  },
                },
              });

              setFrame({ ...viewport, x: 0, y: 0 });
              const cameraDetails = availableCameras?.[cameraId];
              if (!cameraDetails) {
                throw new Error(`Missing camera ${cameraId}`);
              }
              const { cameraType } = cameraDetails;
              switch (cameraType) {
                case CameraType.FISHEYE: {
                  const state = getInitialFisheyeState(cameraSlice);
                  onFisheyeInputChange(cameraSlice, state);
                  break;
                }
                case CameraType.STANDARD:
                case CameraType.RGBD:
                case CameraType.LIDAR: {
                  const state = getInitialFlatState(cameraDetails, viewport);
                  onFlatInputChange(cameraSlice, state);
                  break;
                }
                default: {
                  assertNever("No such CameraType", cameraType);
                }
              }
            }}
            isSubmitting={false}
          />
        </div>
      </Pane>
      <Pane
        className="min-h-36"
        header={
          <div>
            <Typography variant="caption">Transform</Typography>
          </div>
        }
      >
        <Collapse in={Boolean(infoForm.cameraId)}>
          <div className="p-6 pr-8 border-0 border-b border-solid border-rtc-gray-600">
            {availableCameras && infoForm.cameraId && cameraSlice ? (
              <FrameSubPane
                formProps={{
                  initialValues: frame,
                  isSubmitting: false,
                  videoDimensions: viewport,
                  onChange: setFrame,
                  onError: setFrameFormHasErrors,
                  onReset: () => {
                    if (!cameraSlice) {
                      return;
                    }
                    setFrame({ ...viewport, x: 0, y: 0 });
                  },
                }}
              />
            ) : null}
          </div>
          {(() => {
            if (!cameraSlice || !transformState) {
              return;
            }
            const { cameraId } = infoForm;
            const cameraDetails = cameraId
              ? availableCameras?.[cameraId]
              : undefined;
            const cameraType = cameraDetails?.cameraType;

            const resetFisheye = (): void => {
              const state = getInitialFisheyeState(cameraSlice);
              onFisheyeInputChange(cameraSlice, state);
            };

            const resetFlat = (): void => {
              if (!cameraDetails) {
                throw new Error(`Missing camera details for ${cameraId}`);
              }
              const state = getInitialFlatState(cameraDetails, viewport);
              onFlatInputChange(cameraSlice, state);
            };

            let form;
            switch (transformState.type) {
              case TransformStateType.FISHEYE: {
                form = (
                  <FisheyeStateForm
                    value={transformState}
                    onChange={(state) =>
                      onFisheyeInputChange(cameraSlice, state)
                    }
                    setHasErrors={setTransformsFormHasErrors}
                    onReset={resetFisheye}
                    onSelectFlat={resetFlat}
                  />
                );
                break;
              }
              case TransformStateType.FLAT: {
                form = (
                  <FlatStateForm
                    value={transformState}
                    onChange={(state) => onFlatInputChange(cameraSlice, state)}
                    setHasErrors={setTransformsFormHasErrors}
                    onReset={resetFlat}
                    onSelectFisheye={
                      cameraType === CameraType.FISHEYE
                        ? resetFisheye
                        : undefined
                    }
                  />
                );
                break;
              }
            }
            return (
              <div className="p-6 pr-8">
                <CameraSubPane>{form}</CameraSubPane>
              </div>
            );
          })()}
        </Collapse>
      </Pane>
    </SideBarBesideVideoLayout>
  );
};
