import { Alerts } from "@main/components/alerts/Alerts";
import { carbonEnv } from "@main/env";
import { ConnectionOptionCard } from "@main/components/streamOption/ConnectionOptionCard";
import { createListRequest } from "@main/communication/websocket/webSocketMessages";
import { <PERSON>rid, IconButton, Tooltip, Typography } from "@mui/material";
import { HostsAndStreams, updateHostAndStreamsList } from "./utils";
import {
  ListResponse,
  MessageType,
} from "@main/communication/websocket/webSocketMessage.types";
import { Page } from "@main/components/Page";
import { ReadyState } from "react-use-websocket";
import { SearchInput } from "@main/components/form/SearchInput";
import { selectAuthenticated } from "@main/store/signal.slice";
import { selectClientId } from "@main/store/driving.slice";
import { TopNav } from "@main/components/topnav/TopNav";
import { useAppSelector } from "@main/store/hooks";
import { usePoll } from "@main/hooks/usePoll";
import { useWebSocketContext } from "@main/communication/websocket/webSocketContext";
import { useWebSocketRequest } from "@main/communication/websocket/hooks/useWebSocketRequest";
import React, { FC, useCallback, useEffect, useState } from "react";
import SyncIcon from "@mui/icons-material/Sync";

const { CLIENT_HEARTBEAT_INTERVAL_S } = carbonEnv;

const Tractors: FC = () => {
  const clientId = useAppSelector(selectClientId);
  const authenticated = useAppSelector(selectAuthenticated);
  const [hostsAndStreams, setHostsAndStreams] = useState<HostsAndStreams[]>([]);
  const [hostSearchText, setHostSearchText] = useState("");

  const { readyState } = useWebSocketContext();
  const isWebsocketOpen = readyState === ReadyState.OPEN;

  const isConnecting = !isWebsocketOpen || !authenticated;
  const getLoadingStatus = (): string => {
    if (!isWebsocketOpen) {
      return "Connecting ...";
    }
    if (!authenticated) {
      return "Authenticating ...";
    }
    return "Loading ...";
  };

  const onSourceListing = (content: ListResponse["content"]): void => {
    if (content?.available?.length) {
      setHostsAndStreams((previousHostsAndStreams) =>
        updateHostAndStreamsList(previousHostsAndStreams, content.available)
      );
    }
  };

  const { fetch } = useWebSocketRequest({
    responseMessageType: MessageType.LIST_RESPONSE,
    onResponse: onSourceListing,
  });

  const fetchList = useCallback((): void => {
    if (isConnecting) {
      return;
    }
    fetch(createListRequest(clientId));
  }, [clientId, fetch, isConnecting]);

  useEffect(() => {
    if (!isConnecting) {
      // trigger the initial fetch on the first connection
      fetchList();
    }
  }, [fetchList, isConnecting]);

  // poll periodically to get up to date info
  usePoll(fetchList, CLIENT_HEARTBEAT_INTERVAL_S * 1000);

  const filteredHostsAndStreams = hostsAndStreams.filter(
    (host) =>
      !hostSearchText ||
      host.hostName
        .toLocaleLowerCase()
        .includes(hostSearchText.toLocaleLowerCase())
  );
  return (
    <div className="h-screen">
      <TopNav wsReadyState={readyState} />
      <Page>
        <Alerts />
        <div className="flex justify-end mb-4">
          <SearchInput
            placeholder="Filter hosts..."
            className="text-md p-1 mb-3 bg-rtc-gray-900"
            value={hostSearchText}
            onChange={setHostSearchText}
          />
          <Tooltip title="Refetch Available Streams">
            <div className="flex items-middle">
              <IconButton
                color="primary"
                onClick={() => {
                  fetch(createListRequest(clientId));
                }}
                disabled={!isWebsocketOpen}
              >
                <SyncIcon />
              </IconButton>
            </div>
          </Tooltip>
        </div>
        <Typography variant="caption" className="mb-4 uppercase">
          {hostSearchText ? `${filteredHostsAndStreams.length} of ` : ""}
          {hostsAndStreams.length} Results
        </Typography>
        {hostsAndStreams.length &&
        hostsAndStreams.some((host) => host.videoStreams.length) ? (
          <Grid container spacing={2}>
            {filteredHostsAndStreams.map((host) => (
              <Grid
                item
                xs={12}
                sm={6}
                lg={4}
                key={`${host.hostID}-${host.hostName}`}
                className="h-[300px]"
              >
                <ConnectionOptionCard
                  host={host}
                  streams={host.videoStreams}
                  disabled={!isWebsocketOpen}
                />
              </Grid>
            ))}
          </Grid>
        ) : (
          <div className="px-4">
            <Typography>
              {isConnecting ? getLoadingStatus() : "No Tractors Available"}
            </Typography>
          </div>
        )}
      </Page>
    </div>
  );
};
export default Tractors;
