import { createSlice } from "@reduxjs/toolkit";
import { Objective, Task } from "@protos/ts2/rtc/jobs";
import { TaskStateDetails } from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";
import type { PayloadAction } from "@reduxjs/toolkit";

interface JobState {
  activeTask?: Task;
  activeObjective?: Objective;
  taskStartCriteria?: TaskStateDetails | undefined;
  taskEndCriteria?: TaskStateDetails | undefined;
}

const initialState: JobState = {};

export const jobSlice = createSlice({
  name: "job",
  initialState,
  reducers: {
    setActiveTask: (
      state,
      { payload: task }: PayloadAction<Task | undefined>
    ) => {
      state.activeTask = task;
    },
    setActiveObjective: (
      state,
      { payload: task }: PayloadAction<Objective | undefined>
    ) => {
      state.activeObjective = task;
    },
    setTaskStartCriteria: (
      state,
      { payload: task }: PayloadAction<TaskStateDetails | undefined>
    ) => {
      state.taskStartCriteria = task;
    },
    setTaskEndCriteria: (
      state,
      { payload: task }: PayloadAction<TaskStateDetails | undefined>
    ) => {
      state.taskEndCriteria = task;
    },
  },
  selectors: {
    selectActiveTask: (state): Task | undefined => state.activeTask,
    selectActiveObjective: (state): Objective | undefined =>
      state.activeObjective,
    selectTaskStartCriteria: (state): TaskStateDetails | undefined =>
      state.taskStartCriteria,
    selectTaskEndCriteria: (state): TaskStateDetails | undefined =>
      state.taskEndCriteria,
  },
});

export const {
  selectActiveTask,
  selectActiveObjective,
  selectTaskEndCriteria,
  selectTaskStartCriteria,
} = jobSlice.selectors;
export const {
  setActiveTask,
  setActiveObjective,
  setTaskStartCriteria,
  setTaskEndCriteria,
} = jobSlice.actions;
