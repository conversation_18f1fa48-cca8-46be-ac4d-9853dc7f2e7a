import { _basePortalApi, Tag } from "common/state/portalApi/base";
import type { ConfigResponse } from "@protos/ts2/portal/configs";

/*
 * RTC uses the getConfig API for a variety of small bits, mostly around
 * steering and speed limits.  work has been done to expose tractor configs
 * over the RTC data channel, so this should be obsoletable, and it's not worth
 * adding to the common partition of the portal API.
 *
 * https://carbonrobotics.atlassian.net/browse/SOFTWARE-1851
 */
const configsApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    getConfig: builder.query<ConfigResponse, string>({
      query: (serial: string) => ({ url: `configs/${serial}` }),
      providesTags: (result, error, [serial]) => [
        { type: Tag.CONFIG, id: serial },
      ],
    }),
  }),
});

export const { useGetConfigQuery } = configsApi;
