import { _basePortal<PERSON>pi } from "common/state/portalApi/base";
import { camelToSnake } from "common/utils/strings";
import { PlanPathRequest, PlanPathResponse } from "@protos/ts2/portal/geo";
import { transformKeys } from "common/utils/objects";

/*
 * The planPath API is unique to the RTC implementation of the Portal API client,
 * and it should become obsolete soon anyway.  If this changes, it may be worth
 * moving to ui/common.
 */
const planPathApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    planPath: builder.mutation<PlanPathResponse, PlanPathRequest>({
      query: (request) => ({
        url: `fields/robots/${request.serial}/definitions/${request.fieldId}/path`,
        method: "POST",
        body: transformKeys<any>(PlanPathRequest.toJSON(request), camelToSnake),
      }),
      transformResponse: (response: unknown) =>
        PlanPathResponse.fromJSON(response),
    }),
  }),
});

export const { usePlanPathMutation } = planPathApi;
