import { _basePortal<PERSON><PERSON> } from "common/state/portalApi/base";
import { combineReducers, configureStore, Tuple } from "@reduxjs/toolkit";
import { drivingSlice } from "./driving.slice";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";
import { jobExplorer } from "common/state/jobExplorer";
import { jobSlice } from "./job.slice";
import { metricsSlice } from "./metrics.slice";
import { preferencesSlice } from "./preferences.slice";
import { rtcJobsApi } from "common/state/rtcJobsApi";
import { rtcLocatorApi } from "common/state/rtcLocatorApi";
import { rtcLocatorTimeoutMiddleware } from "common/state/middleware/rtcLocatorTimeoutMiddleware";
import { selfSlice } from "./self.slice";
import { signalSlice } from "./signal.slice";
import { useSelector } from "react-redux";
import storage from "redux-persist/lib/storage";

const reducers = combineReducers({
  [_basePortalApi.reducerPath]: _basePortalApi.reducer,
  [rtcJobsApi.reducerPath]: rtcJobsApi.reducer,
  [rtcLocatorApi.reducerPath]: rtcLocatorApi.reducer,
  [drivingSlice.name]: drivingSlice.reducer,
  [jobSlice.name]: jobSlice.reducer,
  [preferencesSlice.name]: preferencesSlice.reducer,
  [selfSlice.name]: selfSlice.reducer,
  [signalSlice.name]: signalSlice.reducer,
  [metricsSlice.name]: metricsSlice.reducer,
  [jobExplorer.name]: jobExplorer.reducer,
});

export const useJobExplorer: () => RootState["jobExplorer"] =
  (): RootState["jobExplorer"] =>
    useSelector((state: RootState) => state.jobExplorer);

const persistedReducers = persistReducer(
  {
    key: "rtc-redux",
    version: 1,
    storage,
    whitelist: [preferencesSlice.name],
  },
  reducers
);

export const store = configureStore({
  reducer: persistedReducers,
  middleware: (getDefaultMiddleware) =>
    [
      ...getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }),
      _basePortalApi.middleware,
      rtcJobsApi.middleware,
      rtcLocatorApi.middleware,
      rtcLocatorTimeoutMiddleware,
    ] as Tuple,
  enhancers: (getDefaultEnhancers) => [...getDefaultEnhancers()] as Tuple,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppStore = typeof store;
export type AppDispatch = typeof store.dispatch;
