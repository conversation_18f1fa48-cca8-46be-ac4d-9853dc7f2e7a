import {
  CARBON_PALETTE,
  GRAY_PALETTE,
  GREEN_LIGHT_PALETTE,
  OCEAN_PALETTE,
} from "common/theme/colors";
import { createTheme } from "@mui/material/styles";
import {
  MAIN_HEADINGS,
  MUI_FONT_SIZE,
  POPPINS_FONT_FAMILY,
  SUB_HEADINGS,
} from "common/theme/typography";

const typographyBase = {
  lineHeight: 1,
  padding: 0,
  margin: 0,
  fontWeight: "normal",
};

export const theme = createTheme({
  palette: {
    mode: "dark",
    primary: {
      light: CARBON_PALETTE[400],
      main: CARBON_PALETTE[700],
      dark: CARBON_PALETTE[900],
    },
    secondary: {
      light: OCEAN_PALETTE[100],
      main: OCEAN_PALETTE[200],
      dark: OCEAN_PALETTE[400],
    },
    success: {
      light: GREEN_LIGHT_PALETTE[400],
      main: GREEN_LIGHT_PALETTE[700],
      dark: GRE<PERSON>_LIGHT_PALETTE[900],
    },
    background: {
      default: "black",
      paper: "black",
    },
  },
  typography: {
    fontFamily: POPPINS_FONT_FAMILY,
    h1: {
      ...typographyBase,
      ...MAIN_HEADINGS,
      fontSize: MUI_FONT_SIZE.h1,
    },
    h2: {
      ...typographyBase,
      ...MAIN_HEADINGS,
      fontSize: MUI_FONT_SIZE.h2,
    },
    h3: {
      ...typographyBase,
      ...MAIN_HEADINGS,
      fontSize: MUI_FONT_SIZE.h3,
    },
    h4: {
      ...typographyBase,
      ...MAIN_HEADINGS,
      fontSize: MUI_FONT_SIZE.h4,
    },
    h5: { ...typographyBase, ...SUB_HEADINGS, fontSize: MUI_FONT_SIZE.h5 },
    h6: { ...typographyBase, ...SUB_HEADINGS },
    caption: {
      ...typographyBase,
      fontWeight: "bold",
      fontSize: MUI_FONT_SIZE.caption,
      lineHeight: MUI_FONT_SIZE.caption,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        outlined: {
          borderColor: "white",
          color: "white",
          width: "fit-content",
          "&:hover": {
            borderColor: "white",
            backgroundColor: "rgba(255, 255, 255, 0.2)", // White with 20% opacity
          },
          "&:disabled": {
            color: GRAY_PALETTE[500],
            borderColor: GRAY_PALETTE[500],
            backgroundColor: "rgba(255, 255, 255, 0.1)", // White with 20% opacity
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          "& fieldset": {
            borderColor: GRAY_PALETTE[300],
          },
          "&:hover fieldset": {
            borderColor: GRAY_PALETTE[300],
          },
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          "& fieldset": {
            borderColor: GRAY_PALETTE[300],
          },
          "&:hover fieldset": {
            borderColor: GRAY_PALETTE[300],
          },
        },
      },
    },
  },
});
