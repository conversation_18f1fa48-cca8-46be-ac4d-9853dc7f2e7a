import type { FieldDefinition } from "@main/components/controls/drive.types";
import type { FieldDefinition as FieldDefinitionRaw } from "@protos/ts2/portal/geo";

/**
 * RTC's old implementation of the Portal API performed some additional data
 * normalization on the returned field definitions that the Portal implementation
 * does not; that normalization has been extracted into this method.  since field
 * definitions are hopefully on their way out anyway, it's not really worth
 * doing something more elegant than this.
 *
 * reference: https://github.com/carbonrobotics/cloud/blob/3f9c40ebed5b2c44445e086cb4db096b26fc0c3d/ui/rtc/src/store/portalApi.ts
 *
 * @param rawDefs the raw field definitions from the Portal API
 * @returns the normalized field definitions
 */
export function normalizeFieldDefinitions(
  rawDefs: FieldDefinitionRaw[]
): FieldDefinition[] {
  // Deserialize nested GeoJSON values.
  const tryParseJson = <T>(
    text: string | undefined,
    ...errorArgs: unknown[]
  ): T | undefined => {
    if (text === undefined) {
      return;
    }
    try {
      return JSON.parse(text);
    } catch (e) {
      console.error("Failed to parse JSON on response field", ...errorArgs);
    }
  };
  return rawDefs.map(
    (field: FieldDefinitionRaw): FieldDefinition => ({
      ...field,
      boundary: tryParseJson(
        field.boundary?.feature,
        field.fieldId,
        "boundary"
      ),
      plantingHeading: tryParseJson(
        field.plantingHeading?.feature,
        field.fieldId,
        "plantingHeading"
      ),
    })
  );
}
