import { ROUTES } from "@main/pages/routes";

export const getDrivePath = (
  serial: string,
  params: { returnTo?: string }
): string => {
  const searchParams = new URLSearchParams(params);
  let paramsString = "";

  if (searchParams.toString().length > 0) {
    paramsString = `?${searchParams.toString()}`;
  }

  // todo: move getClass from portal to common and switch off of that
  // or make it so robot streams don't have an extension in the name, just their serial.
  const extension = serial.includes("devserver") ? "-simulator" : "-rtc";

  return `/${ROUTES.Stream.path}/${serial}${extension}/deck/${ROUTES.Drive.path}${paramsString}`;
};
