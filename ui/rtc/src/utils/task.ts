import { Gear, GEAR_INFO } from "@main/components/controls/drive.types";
import {
  GoToReversiblePathTask,
  // eslint-disable-next-line camelcase
  hitchState_HitchCommandToJSON,
  LaserWeedTask,
  Task,
  // eslint-disable-next-line camelcase
  TractorState_Gear,
} from "@protos/ts2/rtc/jobs";
import {
  TaskStateData,
  TaskStateDetails,
} from "@main/communication/rtc/hooks/useTractorControlChannelMessaging";

/*
turns
  GEAR_UNSPECIFIED = 0,
  PARK = 1,
  REVERSE = 2,
  NEUTRAL = 3,
  FORWARD = 4,
  POWERZERO = 5,
  UNRECOGNIZED = -1,

into
    GEAR_PARK = 0
    GEAR_REVERSE = 1
    GEAR_NEUTRAL = 2
    GEAR_FORWARD = 3
    GEAR_POWERZERO = 4

*/
const translateProtoGearToRTCGear = (
  // eslint-disable-next-line camelcase
  gear: TractorState_Gear
): number => {
  switch (gear) {
    case 0:
      return -1;
    case -1:
      return -1;
    case 1:
      return 0;
    case 2:
      return 1;
    case 3:
      return 2;
    case 4:
      return 3;
    case 5:
      return 4;
    default:
      return -1;
  }
};

// This workflow introduces a BIG gap in consistency with our system.
// The robot is the source of truth for whether it can start a task or complete a task.
// Any calculations here are no better than a guess as to whether the robot will accept them.
//
// This is an ongoing discussion, but the source of truth should either be entirely contained on the task
// Or both the expected value and the resultant value should be returned from the robot.
//
// Furthermore, we need to standardize the data model for task criteria.
export const getExpectedCriteria = (
  taskInput?: Task
): TaskStateDetails | undefined => {
  if (!taskInput) {
    return undefined;
  }
  const task = { ...taskInput };

  // TODO: Protoize RTC :(
  task.expectedTractorState = task.expectedTractorState.map((state) => {
    if (state.gear) {
      return { ...state, gear: translateProtoGearToRTCGear(state.gear) };
    }
    return state;
  });

  if (task.laserWeed) {
    return getLaserWeedCriteria({ ...task, laserWeed: task.laserWeed });
  }
  if (task.goToReversiblePath) {
    return getGoToReversiblePathCriteria({
      ...task,
      goToReversiblePath: task.goToReversiblePath,
    });
  }
  // todo: implement the rest of these
  return undefined;
};

const getGoToReversiblePathCriteria = (
  task: Task & { goToReversiblePath: GoToReversiblePathTask }
): TaskStateDetails | undefined => {
  const taskState: TaskStateDetails = {};
  setTractorStateCriteria(taskState, task);
  if (task.goToReversiblePath.tolerances) {
    taskState.headingValid = {
      state: true,
      data: task.goToReversiblePath.tolerances?.heading,
    };
    taskState.posXteValid = {
      state: true,
      data: task.goToReversiblePath.tolerances?.crosstrack,
    };
    taskState.posDistValid = {
      state: true,
      data: task.goToReversiblePath.tolerances?.distance,
    };
  }

  return taskState;
};

const getLaserWeedCriteria = (
  task: Task & { laserWeed: LaserWeedTask }
): TaskStateDetails | undefined => {
  const taskState: TaskStateDetails = {};
  setTractorStateCriteria(taskState, task);
  if (task.laserWeed.tolerances) {
    taskState.headingValid = {
      state: true,
      data: task.laserWeed.tolerances?.heading,
    };
    taskState.posXteValid = {
      state: true,
      data: task.laserWeed.tolerances?.crosstrack,
    };
    taskState.posDistValid = {
      state: true,
      data: task.laserWeed.tolerances?.distance,
    };
  }

  return taskState;
};

const setTractorStateCriteria = (
  taskState: TaskStateDetails,
  task: Task
): void => {
  for (const state of task.expectedTractorState) {
    if (state.gear) {
      taskState.gearStateValid = { state: true, data: state.gear };
    }
    if (state.hitch && state.hitch.command) {
      taskState.hitchStateValid = { state: true, data: state.hitch.command };
    }
  }
};

// returns the display value for the value of a start/end criteria on the task.
export const getDisplayValue = (
  val: TaskStateData,
  name: keyof TaskStateDetails,
  expectedCriteria?: TaskStateDetails
): number | string | boolean => {
  let displayVal: string = "";
  let expectedVal: string | undefined;
  let operator: string = "/";
  if (name === "gearStateValid") {
    operator = "=";
    const gear = val.data as Gear;
    const expectedGear = expectedCriteria?.[name]?.data;
    displayVal = GEAR_INFO[gear].abbreviation;
    expectedVal = expectedGear
      ? GEAR_INFO[expectedGear as Gear].abbreviation
      : undefined;
  } else if (name === "hitchStateValid") {
    displayVal = `${val.data}`; // todo: these are inconsistent
    // eslint-disable-next-line camelcase
    expectedVal = expectedCriteria?.[name]?.data
      ? hitchState_HitchCommandToJSON(expectedCriteria?.[name]?.data)
      : undefined;
  } else {
    operator = "<";
    if (Number.isInteger(val.data)) {
      displayVal = `${val.data}`;
    } else if (typeof val.data === "number") {
      displayVal = `${val.data.toFixed(1)}`;
    }
    if (typeof val.data === "string") {
      displayVal = val.data;
    }
    expectedVal = expectedCriteria?.[name]?.data
      ? `${expectedCriteria?.[name]?.data}`
      : undefined;
  }
  if (name === "unsupported") {
    expectedCriteria = undefined;
    displayVal = "";
  }
  if (!expectedCriteria) {
    return displayVal;
  }
  return `${displayVal} ${operator} ${expectedVal}`;
};
