import { hex6ToRgba } from "./theme";
import { OCEAN_PALETTE } from "common/theme/colors";

describe("theme", () => {
  describe("hex6ToRgba", () => {
    test("white", () => {
      const result = hex6ToRgba("#ffffff", 0.5);
      expect(result).toEqual([255, 255, 255, 0.5]);
    });
    test("ocean blue", () => {
      const result = hex6ToRgba(OCEAN_PALETTE[500], 0.5);
      expect(result).toEqual([3, 74, 206, 0.5]);
    });
  });
});
